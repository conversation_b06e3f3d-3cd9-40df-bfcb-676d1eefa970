'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { useForm, useFieldArray } from 'react-hook-form'
import { ArrowLeft, Plus, X } from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface ServiceFormData {
  name: string
  description: string
  category: string
  type: string
  base_price: string
  currency: string
  duration_minutes: number
  max_passengers: number
  available_airports: string[]
  requirements: string[]
  inclusions: string[]
  status: string
}

export default function EditServicePage() {
  const router = useRouter()
  const params = useParams()
  const queryClient = useQueryClient()
  const serviceId = params?.id as string

  // Fetch existing service data
  const { data: serviceData, isLoading: isLoadingService, error } = useQuery(
    ['service', serviceId],
    () => servicesAPI.getById(serviceId),
    {
      enabled: !!serviceId,
    }
  )

  const service = serviceData?.data?.data

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ServiceFormData>({
    defaultValues: {
      name: '',
      description: '',
      category: 'meet_greet',
      type: 'arrival',
      base_price: '',
      currency: 'INR',
      duration_minutes: 60,
      max_passengers: 1,
      available_airports: [],
      requirements: [],
      inclusions: [],
      status: 'active',
    },
  })

  // Populate form with existing data
  useEffect(() => {
    if (service) {
      reset({
        name: service.name || '',
        description: service.description || '',
        category: service.category || 'meet_greet',
        type: service.type || 'arrival',
        base_price: service.base_price || '',
        currency: service.currency || 'INR',
        duration_minutes: service.duration_minutes || 60,
        max_passengers: service.max_passengers || 1,
        available_airports: service.available_airports || [],
        requirements: service.requirements || [],
        inclusions: service.inclusions || [],
        status: service.status || 'active',
      })
    }
  }, [service, reset])

  const {
    fields: airportFields,
    append: appendAirport,
    remove: removeAirport,
  } = useFieldArray({
    control,
    name: 'available_airports',
  })

  const {
    fields: requirementFields,
    append: appendRequirement,
    remove: removeRequirement,
  } = useFieldArray({
    control,
    name: 'requirements',
  })

  const {
    fields: inclusionFields,
    append: appendInclusion,
    remove: removeInclusion,
  } = useFieldArray({
    control,
    name: 'inclusions',
  })

  const updateServiceMutation = useMutation(
    (data: any) => servicesAPI.update(serviceId, data),
    {
      onSuccess: () => {
        toast.success('Service updated successfully!')
        queryClient.invalidateQueries(['services'])
        queryClient.invalidateQueries(['service', serviceId])
        router.push('/dashboard/services')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update service')
      },
    }
  )

  const onSubmit = (data: ServiceFormData) => {
    // Transform data to match backend API expectations
    const formattedData = {
      name: data.name,
      description: data.description,
      category: data.category,
      type: data.type,
      basePrice: parseFloat(data.base_price),
      currency: data.currency,
      durationMinutes: Number(data.duration_minutes),
      maxPassengers: Number(data.max_passengers),
      availableAirports: data.available_airports.filter(airport => airport.trim() !== ''),
      requirements: data.requirements.filter(req => req.trim() !== ''),
      inclusions: data.inclusions.filter(inc => inc.trim() !== ''),
      status: data.status,
    }

    updateServiceMutation.mutate(formattedData)
  }

  if (isLoadingService) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load service</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Service not found</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Services
          </Link>
        </div>
        <h1 className="text-2xl font-bold text-gray-900">Edit Service</h1>
        <p className="text-gray-600">Update your service details and configuration.</p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Service Name *
              </label>
              <input
                type="text"
                id="name"
                {...register('name', { required: 'Service name is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., Premium Meet & Greet"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                Category *
              </label>
              <select
                id="category"
                {...register('category', { required: 'Category is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="meet_greet">Meet & Greet</option>
                <option value="lounge_access">Lounge Access</option>
                <option value="fast_track">Fast Track</option>
                <option value="vip_terminal">VIP Terminal</option>
                <option value="concierge">Concierge</option>
              </select>
              {errors.category && (
                <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
              )}
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                id="description"
                rows={3}
                {...register('description', { required: 'Description is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="Describe your service..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Service Details */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Service Details</h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Service Type *
              </label>
              <select
                id="type"
                {...register('type', { required: 'Service type is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="arrival">Arrival</option>
                <option value="departure">Departure</option>
                <option value="both">Both</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="base_price" className="block text-sm font-medium text-gray-700">
                Base Price (INR) *
              </label>
              <input
                type="number"
                step="0.01"
                id="base_price"
                {...register('base_price', { 
                  required: 'Base price is required',
                  min: { value: 0, message: 'Price must be positive' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
              {errors.base_price && (
                <p className="mt-1 text-sm text-red-600">{errors.base_price.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="duration_minutes" className="block text-sm font-medium text-gray-700">
                Duration (minutes) *
              </label>
              <input
                type="number"
                id="duration_minutes"
                {...register('duration_minutes', { 
                  required: 'Duration is required',
                  min: { value: 1, message: 'Duration must be at least 1 minute' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="60"
              />
              {errors.duration_minutes && (
                <p className="mt-1 text-sm text-red-600">{errors.duration_minutes.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="max_passengers" className="block text-sm font-medium text-gray-700">
                Max Passengers *
              </label>
              <input
                type="number"
                id="max_passengers"
                {...register('max_passengers', { 
                  required: 'Max passengers is required',
                  min: { value: 1, message: 'Must allow at least 1 passenger' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="1"
              />
              {errors.max_passengers && (
                <p className="mt-1 text-sm text-red-600">{errors.max_passengers.message}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Status *
            </label>
            <select
              id="status"
              {...register('status', { required: 'Status is required' })}
              className="mt-1 block w-full sm:w-48 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>
        </div>

        {/* Available Airports */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Available Airports</h2>

          <div className="space-y-3">
            {airportFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`available_airports.${index}` as const, {
                    required: 'Airport code is required',
                    pattern: {
                      value: /^[A-Z]{3}$/,
                      message: 'Airport code must be 3 uppercase letters (e.g., JFK)'
                    }
                  })}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., JFK"
                />
                <button
                  type="button"
                  onClick={() => removeAirport(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendAirport('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Airport
            </button>
          </div>
        </div>

        {/* Requirements */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Requirements</h2>

          <div className="space-y-3">
            {requirementFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`requirements.${index}` as const, {
                    required: 'Requirement is required'
                  })}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Valid passport"
                />
                <button
                  type="button"
                  onClick={() => removeRequirement(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendRequirement('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Requirement
            </button>
          </div>
        </div>

        {/* Inclusions */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Inclusions</h2>

          <div className="space-y-3">
            {inclusionFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`inclusions.${index}` as const, {
                    required: 'Inclusion is required'
                  })}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Personal greeter"
                />
                <button
                  type="button"
                  onClick={() => removeInclusion(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendInclusion('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Inclusion
            </button>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting || updateServiceMutation.isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting || updateServiceMutation.isLoading ? (
              <>
                <LoadingSpinner className="h-4 w-4 mr-2" />
                Updating...
              </>
            ) : (
              'Update Service'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
