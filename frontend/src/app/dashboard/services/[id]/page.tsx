'use client'

import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useQuery } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { ArrowLeft, Edit, Copy, Trash2 } from 'lucide-react'
import Link from 'next/link'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { formatCurrency } from '@/lib/utils'

export default function ServiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const serviceId = params?.id as string

  const { data: serviceData, isLoading, error } = useQuery(
    ['service', serviceId],
    () => servicesAPI.getById(serviceId),
    {
      enabled: !!serviceId,
    }
  )

  const service = serviceData?.data?.data

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load service</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Service not found</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Services
          </Link>
        </div>
        
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
            <p className="text-gray-600 capitalize">{service.category?.replace('_', ' ')}</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Link
              href={`/dashboard/services/${serviceId}/edit`}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </div>
        </div>
      </div>

      {/* Service Details */}
      <div className="bg-white shadow-soft rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Service Details</h2>
        
        <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div>
            <dt className="text-sm font-medium text-gray-500">Description</dt>
            <dd className="mt-1 text-sm text-gray-900">{service.description}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Category</dt>
            <dd className="mt-1 text-sm text-gray-900 capitalize">{service.category?.replace('_', ' ')}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Type</dt>
            <dd className="mt-1 text-sm text-gray-900 capitalize">{service.type}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Base Price</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {formatCurrency(parseFloat(service.base_price), service.currency)}
            </dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Duration</dt>
            <dd className="mt-1 text-sm text-gray-900">{service.duration_minutes} minutes</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Max Passengers</dt>
            <dd className="mt-1 text-sm text-gray-900">{service.max_passengers}</dd>
          </div>
          
          <div>
            <dt className="text-sm font-medium text-gray-500">Status</dt>
            <dd className="mt-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                service.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {service.status}
              </span>
            </dd>
          </div>
        </dl>
      </div>

      {/* Available Airports */}
      {service.available_airports && service.available_airports.length > 0 && (
        <div className="bg-white shadow-soft rounded-lg p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Available Airports</h2>
          <div className="flex flex-wrap gap-2">
            {service.available_airports.map((airport: string, index: number) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {airport}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Requirements */}
      {service.requirements && service.requirements.length > 0 && (
        <div className="bg-white shadow-soft rounded-lg p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Requirements</h2>
          <ul className="list-disc list-inside space-y-1">
            {service.requirements.map((requirement: string, index: number) => (
              <li key={index} className="text-sm text-gray-700">{requirement}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Inclusions */}
      {service.inclusions && service.inclusions.length > 0 && (
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Inclusions</h2>
          <ul className="list-disc list-inside space-y-1">
            {service.inclusions.map((inclusion: string, index: number) => (
              <li key={index} className="text-sm text-gray-700">{inclusion}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
