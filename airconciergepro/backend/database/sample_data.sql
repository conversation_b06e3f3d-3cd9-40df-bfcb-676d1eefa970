-- Sample data for AirConcierge Pro
-- This file contains sample data for development and testing

-- Sample airports
INSERT INTO airports (iata_code, icao_code, name, city, country, timezone) VALUES
('JFK', 'KJFK', 'John F. Kennedy International Airport', 'New York', 'United States', 'America/New_York'),
('LAX', 'KLAX', 'Los Angeles International Airport', 'Los Angeles', 'United States', 'America/Los_Angeles'),
('LHR', 'EGLL', 'Heathrow Airport', 'London', 'United Kingdom', 'Europe/London'),
('DXB', 'OMDB', 'Dubai International Airport', 'Dubai', 'United Arab Emirates', 'Asia/Dubai'),
('SIN', 'WSSS', 'Singapore Changi Airport', 'Singapore', 'Singapore', 'Asia/Singapore'),
('CDG', 'LFPG', 'Charles de Gaulle Airport', 'Paris', 'France', 'Europe/Paris'),
('NRT', 'RJAA', 'Narita International Airport', 'Tokyo', 'Japan', 'Asia/Tokyo'),
('FRA', 'EDDF', 'Frankfurt Airport', 'Frankfurt', 'Germany', 'Europe/Berlin'),
('BOM', 'VABB', 'Chhatrapati Shivaji International Airport', 'Mumbai', 'India', 'Asia/Kolkata'),
('DEL', 'VIDP', 'Indira Gandhi International Airport', 'Delhi', 'India', 'Asia/Kolkata');

-- Sample tenant (Demo company)
INSERT INTO tenants (id, name, subdomain, plan, status, settings) VALUES
('550e8400-e29b-41d4-a716-************', 
 'Demo VIP Services', 
 'demo', 
 'professional', 
 'active',
 '{"branding": {"primaryColor": "#2563eb", "secondaryColor": "#1e40af"}, "features": ["booking_management", "customer_management", "analytics", "mobile_app"], "limits": {"monthly_bookings": 2000, "api_calls": 10000, "locations": 5}}'
);

-- Sample admin user
INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, status, permissions) VALUES
('550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 '$2a$12$LQv3c1yqBCFcXz7WhCXeUeHQrS1QGnqU1VAoxeqM.VhyNlGnOSTKG', -- password: admin123
 'John',
 'Smith',
 'company_admin',
 'active',
 '["all"]'
);

-- Sample operations manager
INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, status, permissions) VALUES
('550e8400-e29b-41d4-a716-446655440002',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 '$2a$12$LQv3c1yqBCFcXz7WhCXeUeHQrS1QGnqU1VAoxeqM.VhyNlGnOSTKG', -- password: admin123
 'Sarah',
 'Johnson',
 'operations_manager',
 'active',
 '["view_bookings", "create_booking", "update_booking", "assign_booking", "view_customers", "view_analytics"]'
);

-- Sample field agents
INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, status, permissions) VALUES
('550e8400-e29b-41d4-a716-446655440003',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 '$2a$12$LQv3c1yqBCFcXz7WhCXeUeHQrS1QGnqU1VAoxeqM.VhyNlGnOSTKG', -- password: admin123
 'Michael',
 'Brown',
 'field_agent',
 'active',
 '["view_assignments", "update_booking_status", "mobile_access"]'
),
('550e8400-e29b-41d4-a716-446655440004',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 '$2a$12$LQv3c1yqBCFcXz7WhCXeUeHQrS1QGnqU1VAoxeqM.VhyNlGnOSTKG', -- password: admin123
 'Emily',
 'Davis',
 'field_agent',
 'active',
 '["view_assignments", "update_booking_status", "mobile_access"]'
);

-- Sample agents data
INSERT INTO agents (id, tenant_id, user_id, employee_id, status, specializations, languages, airports, rating, total_services) VALUES
('550e8400-e29b-41d4-a716-446655440005',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440003',
 'AGT001',
 'available',
 '["vip_services", "special_assistance", "wheelchair_support"]',
 '["English", "Spanish", "French"]',
 '["JFK", "LHR", "CDG"]',
 4.8,
 156
),
('550e8400-e29b-41d4-a716-446655440006',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440004',
 'AGT002',
 'available',
 '["fast_track", "lounge_access", "business_travelers"]',
 '["English", "German", "Italian"]',
 '["LAX", "FRA", "DXB"]',
 4.9,
 203
);

-- Sample customers
INSERT INTO customers (id, tenant_id, email, first_name, last_name, phone, preferences, loyalty_points, total_bookings, total_spent) VALUES
('550e8400-e29b-41d4-a716-446655440007',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 'Robert',
 'Wilson',
 '******-0123',
 '{"language": "English", "specialRequirements": ["wheelchair_assistance"], "communicationPreferences": ["email", "sms"]}',
 850,
 12,
 2400.00
),
('550e8400-e29b-41d4-a716-446655440008',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 'Maria',
 'Garcia',
 '******-0124',
 '{"language": "Spanish", "specialRequirements": ["dietary_restrictions"], "communicationPreferences": ["email"]}',
 450,
 6,
 1200.00
),
('550e8400-e29b-41d4-a716-446655440009',
 '550e8400-e29b-41d4-a716-************',
 '<EMAIL>',
 'James',
 'Chen',
 '******-0125',
 '{"language": "English", "specialRequirements": [], "communicationPreferences": ["email", "whatsapp"]}',
 1200,
 18,
 3600.00
);

-- Sample services
INSERT INTO services (id, tenant_id, name, description, category, type, base_price, currency, duration_minutes, max_passengers, available_airports, requirements, inclusions) VALUES
('550e8400-e29b-41d4-a716-446655440010',
 '550e8400-e29b-41d4-a716-************',
 'Premium Meet & Greet - Arrival',
 'Personalized meet and greet service with fast-track immigration and baggage assistance',
 'meet_greet',
 'arrival',
 150.00,
 'USD',
 60,
 4,
 '["JFK", "LHR", "CDG", "DXB", "LAX"]',
 '["Valid passport", "Flight confirmation"]',
 '["Personal greeter", "Fast-track immigration", "Baggage assistance", "Ground transportation coordination"]'
),
('550e8400-e29b-41d4-a716-446655440011',
 '550e8400-e29b-41d4-a716-************',
 'VIP Departure Service',
 'Complete departure assistance with check-in, security fast-track, and lounge access',
 'meet_greet',
 'departure',
 180.00,
 'USD',
 90,
 4,
 '["JFK", "LHR", "CDG", "DXB", "LAX", "FRA"]',
 '["Valid passport", "Flight confirmation", "Advance check-in"]',
 '["Personal assistant", "Check-in assistance", "Fast-track security", "Lounge access", "Boarding assistance"]'
),
('550e8400-e29b-41d4-a716-446655440012',
 '550e8400-e29b-41d4-a716-************',
 'Fast Track Security',
 'Express security screening for hassle-free airport experience',
 'fast_track',
 'departure',
 75.00,
 'USD',
 30,
 6,
 '["JFK", "LHR", "CDG", "DXB", "LAX", "FRA", "SIN"]',
 '["Valid boarding pass"]',
 '["Priority security screening", "Dedicated lanes"]'
),
('550e8400-e29b-41d4-a716-446655440013',
 '550e8400-e29b-41d4-a716-************',
 'Business Lounge Access',
 'Access to premium airport lounges with refreshments and amenities',
 'lounge_access',
 'departure',
 95.00,
 'USD',
 180,
 2,
 '["JFK", "LHR", "CDG", "DXB", "LAX", "FRA", "SIN", "NRT"]',
 '["Valid boarding pass for same day travel"]',
 '["Lounge access", "Complimentary food and beverages", "WiFi", "Business facilities"]'
);

-- Sample bookings
INSERT INTO bookings (
    id, tenant_id, customer_id, service_id, booking_reference, status,
    flight_number, airline, departure_airport, arrival_airport, flight_date,
    estimated_arrival, service_type, passenger_count, passengers,
    special_requirements, meeting_point, assigned_agent_id,
    base_price, total_price, currency, payment_status
) VALUES
('550e8400-e29b-41d4-a716-446655440014',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440007',
 '550e8400-e29b-41d4-a716-446655440010',
 'ACG123ABC',
 'confirmed',
 'BA175',
 'British Airways',
 'LHR',
 'JFK',
 CURRENT_DATE + INTERVAL '2 days',
 CURRENT_DATE + INTERVAL '2 days' + INTERVAL '14 hours',
 'arrival',
 2,
 '[{"firstName": "Robert", "lastName": "Wilson", "specialRequirements": ["wheelchair_assistance"]}, {"firstName": "Helen", "lastName": "Wilson", "age": 65}]',
 '["wheelchair_assistance", "priority_assistance"]',
 'Terminal 4 - Arrivals Hall',
 '550e8400-e29b-41d4-a716-446655440003',
 300.00,
 300.00,
 'USD',
 'paid'
),
('550e8400-e29b-41d4-a716-446655440015',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440008',
 '550e8400-e29b-41d4-a716-446655440011',
 'ACG124DEF',
 'in_progress',
 'AA101',
 'American Airlines',
 'JFK',
 'LAX',
 CURRENT_DATE + INTERVAL '1 day',
 CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours',
 'departure',
 1,
 '[{"firstName": "Maria", "lastName": "Garcia", "specialRequirements": ["dietary_restrictions"]}]',
 '["dietary_restrictions"]',
 'Terminal 8 - Departure Level',
 '550e8400-e29b-41d4-a716-446655440004',
 180.00,
 180.00,
 'USD',
 'paid'
),
('550e8400-e29b-41d4-a716-446655440016',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440009',
 '550e8400-e29b-41d4-a716-446655440012',
 'ACG125GHI',
 'completed',
 'EK205',
 'Emirates',
 'DXB',
 'JFK',
 CURRENT_DATE - INTERVAL '1 day',
 CURRENT_DATE - INTERVAL '1 day' + INTERVAL '15 hours',
 'departure',
 1,
 '[{"firstName": "James", "lastName": "Chen"}]',
 '[]',
 'Terminal 3 - Security Checkpoint',
 '550e8400-e29b-41d4-a716-446655440003',
 75.00,
 75.00,
 'USD',
 'paid'
);

-- Sample booking activities
INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description) VALUES
('550e8400-e29b-41d4-a716-446655440017',
 '550e8400-e29b-41d4-a716-446655440014',
 '550e8400-e29b-41d4-a716-446655440002',
 'created',
 'Booking created for BA175 - arrival service'
),
('550e8400-e29b-41d4-a716-446655440018',
 '550e8400-e29b-41d4-a716-446655440014',
 '550e8400-e29b-41d4-a716-446655440002',
 'assigned',
 'Booking assigned to agent Michael Brown'
),
('550e8400-e29b-41d4-a716-446655440019',
 '550e8400-e29b-41d4-a716-446655440015',
 '550e8400-e29b-41d4-a716-446655440004',
 'started',
 'Service started - customer checked in at Terminal 8'
),
('550e8400-e29b-41d4-a716-446655440020',
 '550e8400-e29b-41d4-a716-446655440016',
 '550e8400-e29b-41d4-a716-446655440003',
 'completed',
 'Service completed successfully'
);

-- Sample API key for partner integration
INSERT INTO api_keys (id, tenant_id, name, key_hash, permissions, usage_limit, usage_count, expires_at, status) VALUES
('550e8400-e29b-41d4-a716-446655440021',
 '550e8400-e29b-41d4-a716-************',
 'Demo Travel Agency API',
 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3', -- SHA256 of 'demo_api_key_123'
 '["partner_booking", "partner_query", "inventory_access"]',
 10000,
 1250,
 CURRENT_DATE + INTERVAL '1 year',
 'active'
);

-- Sample payments
INSERT INTO payments (id, tenant_id, booking_id, amount, currency, payment_method, payment_provider, provider_transaction_id, status) VALUES
('550e8400-e29b-41d4-a716-446655440022',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440014',
 300.00,
 'USD',
 'credit_card',
 'stripe',
 'pi_**********abcdef',
 'completed'
),
('550e8400-e29b-41d4-a716-446655440023',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440015',
 180.00,
 'USD',
 'credit_card',
 'stripe',
 'pi_0987654321fedcba',
 'completed'
),
('550e8400-e29b-41d4-a716-446655440024',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440016',
 75.00,
 'USD',
 'paypal',
 'paypal',
 'PAY-**********',
 'completed'
);

-- Sample notifications
INSERT INTO notifications (id, tenant_id, booking_id, customer_id, type, channel, recipient, subject, message, status, sent_at) VALUES
('550e8400-e29b-41d4-a716-446655440025',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440014',
 '550e8400-e29b-41d4-a716-446655440007',
 'email',
 'smtp',
 '<EMAIL>',
 'Booking Confirmation - ACG123ABC',
 'Your meet and greet service has been confirmed for BA175 arrival at JFK.',
 'delivered',
 NOW() - INTERVAL '1 day'
),
('550e8400-e29b-41d4-a716-446655440026',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440015',
 '550e8400-e29b-41d4-a716-446655440008',
 'sms',
 'twilio',
 '******-0124',
 NULL,
 'Your departure service is starting soon. Agent Emily will meet you at Terminal 8.',
 'delivered',
 NOW() - INTERVAL '2 hours'
);
