-- Migration: Enhance airports table with AviationStack integration fields
-- Date: 2025-07-08
-- Description: Add latitude, longitude, and other enhanced fields to airports table

-- Add new columns to airports table
ALTER TABLE airports 
ADD COLUMN IF NOT EXISTS country_iso2 VARCHAR(2),
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS phone_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS facilities JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS meeting_points JSONB DEFAULT '[]';

-- Create flight cache table for real-time flight data
CREATE TABLE IF NOT EXISTS flight_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flight_number VARCHAR(10) NOT NULL,
    flight_date DATE NOT NULL,
    status VARCHAR(50),
    airline_name VA<PERSON>HA<PERSON>(100),
    airline_iata VARCHAR(3),
    departure_airport VARCHAR(3),
    departure_scheduled TIMESTAMP WITH TIME ZONE,
    departure_estimated TIMESTAMP WITH TIME ZONE,
    departure_actual TIMESTAMP WITH TIME ZONE,
    departure_terminal VARCHAR(10),
    departure_gate VARCHAR(10),
    departure_delay INTEGER DEFAULT 0,
    arrival_airport VARCHAR(3),
    arrival_scheduled TIMESTAMP WITH TIME ZONE,
    arrival_estimated TIMESTAMP WITH TIME ZONE,
    arrival_actual TIMESTAMP WITH TIME ZONE,
    arrival_terminal VARCHAR(10),
    arrival_gate VARCHAR(10),
    arrival_baggage VARCHAR(10),
    arrival_delay INTEGER DEFAULT 0,
    aircraft_registration VARCHAR(20),
    aircraft_type VARCHAR(10),
    live_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(flight_number, flight_date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_airports_latitude_longitude ON airports(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_airports_country_iso2 ON airports(country_iso2);
CREATE INDEX IF NOT EXISTS idx_flight_cache_flight_number ON flight_cache(flight_number);
CREATE INDEX IF NOT EXISTS idx_flight_cache_flight_date ON flight_cache(flight_date);
CREATE INDEX IF NOT EXISTS idx_flight_cache_departure_airport ON flight_cache(departure_airport);
CREATE INDEX IF NOT EXISTS idx_flight_cache_arrival_airport ON flight_cache(arrival_airport);

-- Add some enhanced data to existing airports if they don't have coordinates
UPDATE airports SET 
    latitude = CASE 
        WHEN iata_code = 'JFK' THEN 40.6413
        WHEN iata_code = 'LAX' THEN 33.9425
        WHEN iata_code = 'LHR' THEN 51.4700
        WHEN iata_code = 'CDG' THEN 49.0097
        WHEN iata_code = 'DXB' THEN 25.2532
        WHEN iata_code = 'NRT' THEN 35.7720
        WHEN iata_code = 'SIN' THEN 1.3644
        WHEN iata_code = 'FRA' THEN 50.0379
        WHEN iata_code = 'BOM' THEN 19.0896
        WHEN iata_code = 'DEL' THEN 28.5562
        ELSE latitude
    END,
    longitude = CASE 
        WHEN iata_code = 'JFK' THEN -73.7781
        WHEN iata_code = 'LAX' THEN -118.4081
        WHEN iata_code = 'LHR' THEN -0.4543
        WHEN iata_code = 'CDG' THEN 2.5479
        WHEN iata_code = 'DXB' THEN 55.3657
        WHEN iata_code = 'NRT' THEN 140.3929
        WHEN iata_code = 'SIN' THEN 103.9915
        WHEN iata_code = 'FRA' THEN 8.5622
        WHEN iata_code = 'BOM' THEN 72.8656
        WHEN iata_code = 'DEL' THEN 77.1000
        ELSE longitude
    END,
    country_iso2 = CASE 
        WHEN iata_code = 'JFK' THEN 'US'
        WHEN iata_code = 'LAX' THEN 'US'
        WHEN iata_code = 'LHR' THEN 'GB'
        WHEN iata_code = 'CDG' THEN 'FR'
        WHEN iata_code = 'DXB' THEN 'AE'
        WHEN iata_code = 'NRT' THEN 'JP'
        WHEN iata_code = 'SIN' THEN 'SG'
        WHEN iata_code = 'FRA' THEN 'DE'
        WHEN iata_code = 'BOM' THEN 'IN'
        WHEN iata_code = 'DEL' THEN 'IN'
        ELSE country_iso2
    END
WHERE latitude IS NULL OR longitude IS NULL OR country_iso2 IS NULL;

-- Add enhanced agent location tracking columns
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS location_accuracy DECIMAL(8, 2),
ADD COLUMN IF NOT EXISTS location_updated_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS status_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create indexes for agent location queries
CREATE INDEX IF NOT EXISTS idx_agents_location_updated_at ON agents(location_updated_at);
CREATE INDEX IF NOT EXISTS idx_agents_status_updated_at ON agents(status_updated_at);

-- Add meeting points table for airports
CREATE TABLE IF NOT EXISTS meeting_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    airport_id UUID NOT NULL REFERENCES airports(id) ON DELETE CASCADE,
    terminal VARCHAR(10),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    coordinates JSONB,
    instructions TEXT,
    accessibility_features TEXT[],
    capacity INTEGER DEFAULT 1,
    equipment_available TEXT[],
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for meeting points
CREATE INDEX IF NOT EXISTS idx_meeting_points_airport_id ON meeting_points(airport_id);
CREATE INDEX IF NOT EXISTS idx_meeting_points_terminal ON meeting_points(terminal);
CREATE INDEX IF NOT EXISTS idx_meeting_points_status ON meeting_points(status);

-- Add airport terminals table for better terminal management
CREATE TABLE IF NOT EXISTS airport_terminals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    airport_id UUID NOT NULL REFERENCES airports(id) ON DELETE CASCADE,
    terminal_code VARCHAR(10) NOT NULL,
    terminal_name VARCHAR(100) NOT NULL,
    facilities JSONB DEFAULT '{}',
    gates JSONB DEFAULT '[]',
    services JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(airport_id, terminal_code)
);

-- Create indexes for airport terminals
CREATE INDEX IF NOT EXISTS idx_airport_terminals_airport_id ON airport_terminals(airport_id);
CREATE INDEX IF NOT EXISTS idx_airport_terminals_terminal_code ON airport_terminals(terminal_code);

-- Insert some sample meeting points for major airports
INSERT INTO meeting_points (airport_id, terminal, name, description, instructions, capacity) 
SELECT 
    a.id,
    'T1',
    'Arrivals Hall Meeting Point',
    'Main meeting point in arrivals hall',
    'Look for AirConcierge Pro agent with your name sign near the information desk',
    10
FROM airports a 
WHERE a.iata_code IN ('JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'NRT', 'SIN', 'FRA', 'BOM', 'DEL')
ON CONFLICT DO NOTHING;

-- Insert sample terminal data
INSERT INTO airport_terminals (airport_id, terminal_code, terminal_name, facilities)
SELECT 
    a.id,
    'T1',
    'Terminal 1',
    '{"restaurants": ["Food Court", "Starbucks"], "shops": ["Duty Free", "News Stand"], "services": ["WiFi", "Charging Stations"]}'::jsonb
FROM airports a 
WHERE a.iata_code IN ('JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'NRT', 'SIN', 'FRA', 'BOM', 'DEL')
ON CONFLICT DO NOTHING;

-- Update schema version
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('002_enhance_airports_table', NOW())
ON CONFLICT (version) DO NOTHING;
