-- Migration: Add payment_intent_id to payments table and update constraints
-- This migration adds support for Razorpay and other payment providers

-- Add payment_intent_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'payments' AND column_name = 'payment_intent_id'
    ) THEN
        ALTER TABLE payments ADD COLUMN payment_intent_id VARCHAR(255);
    END IF;
END $$;

-- Update payment_method constraint to include more options
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_payment_method_check;
ALTER TABLE payments ADD CONSTRAINT payments_payment_method_check 
    CHECK (payment_method IN (
        'credit_card', 'debit_card', 'paypal', 'stripe', 'bank_transfer',
        'upi', 'net_banking', 'wallet', 'razorpay'
    ));

-- Update status constraint to include more statuses
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_status_check;
ALTER TABLE payments ADD CONSTRAINT payments_status_check 
    CHECK (status IN (
        'pending', 'completed', 'failed', 'refunded', 'cancelled',
        'requires_action', 'requires_payment_method', 'succeeded'
    ));

-- Add index on payment_intent_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_payments_payment_intent_id ON payments(payment_intent_id);

-- Add index on payment_provider for analytics
CREATE INDEX IF NOT EXISTS idx_payments_provider ON payments(payment_provider);

-- Add composite index for tenant and status
CREATE INDEX IF NOT EXISTS idx_payments_tenant_status ON payments(tenant_id, status);

COMMENT ON COLUMN payments.payment_intent_id IS 'Payment intent/order ID from payment provider (Stripe, Razorpay, etc.)';
COMMENT ON COLUMN payments.payment_provider IS 'Payment provider name (stripe, razorpay, paypal, etc.)';
COMMENT ON COLUMN payments.payment_method IS 'Payment method used (credit_card, upi, net_banking, etc.)';
