-- Migration: Enhance flight information in bookings table
-- Date: 2025-07-09
-- Description: Add comprehensive flight details for AviationStack integration

-- Add new flight information columns to bookings table
DO $$ 
BEGIN
    -- Add airline_code column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'airline_code') THEN
        ALTER TABLE bookings ADD COLUMN airline_code VARCHAR(3);
    END IF;

    -- Rename airline to airline_name for clarity
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'bookings' AND column_name = 'airline') THEN
        ALTER TABLE bookings RENAME COLUMN airline TO airline_name;
    END IF;

    -- Add departure_terminal column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'departure_terminal') THEN
        ALTER TABLE bookings ADD COLUMN departure_terminal VARCHAR(10);
    END IF;

    -- Add arrival_terminal column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'arrival_terminal') THEN
        ALTER TABLE bookings ADD COLUMN arrival_terminal VARCHAR(10);
    END IF;

    -- Add scheduled_departure column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'scheduled_departure') THEN
        ALTER TABLE bookings ADD COLUMN scheduled_departure TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add scheduled_arrival column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'scheduled_arrival') THEN
        ALTER TABLE bookings ADD COLUMN scheduled_arrival TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add estimated_departure column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'estimated_departure') THEN
        ALTER TABLE bookings ADD COLUMN estimated_departure TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add actual_departure column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'actual_departure') THEN
        ALTER TABLE bookings ADD COLUMN actual_departure TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add flight_status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'flight_status') THEN
        ALTER TABLE bookings ADD COLUMN flight_status VARCHAR(50);
    END IF;

    -- Add aircraft_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'aircraft_type') THEN
        ALTER TABLE bookings ADD COLUMN aircraft_type VARCHAR(50);
    END IF;

    -- Add gate column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'gate') THEN
        ALTER TABLE bookings ADD COLUMN gate VARCHAR(10);
    END IF;

    -- Add baggage_belt column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'baggage_belt') THEN
        ALTER TABLE bookings ADD COLUMN baggage_belt VARCHAR(10);
    END IF;

    -- Add flight_details JSONB column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'bookings' AND column_name = 'flight_details') THEN
        ALTER TABLE bookings ADD COLUMN flight_details JSONB DEFAULT '{}';
    END IF;

    -- Make flight_number optional (not all bookings require flight info)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'bookings' AND column_name = 'flight_number' AND is_nullable = 'NO') THEN
        ALTER TABLE bookings ALTER COLUMN flight_number DROP NOT NULL;
    END IF;
END $$;

-- Create indexes for flight-related queries
DO $$
BEGIN
    -- Index for flight number lookups
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_bookings_flight_number') THEN
        CREATE INDEX idx_bookings_flight_number ON bookings(flight_number) WHERE flight_number IS NOT NULL;
    END IF;
    
    -- Index for flight date queries
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_bookings_flight_date') THEN
        CREATE INDEX idx_bookings_flight_date ON bookings(flight_date);
    END IF;
    
    -- Index for airline code queries
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_bookings_airline_code') THEN
        CREATE INDEX idx_bookings_airline_code ON bookings(airline_code) WHERE airline_code IS NOT NULL;
    END IF;
    
    -- Index for flight status queries
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_bookings_flight_status') THEN
        CREATE INDEX idx_bookings_flight_status ON bookings(flight_status) WHERE flight_status IS NOT NULL;
    END IF;
    
    -- Composite index for airport and date queries
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_bookings_airport_date') THEN
        CREATE INDEX idx_bookings_airport_date ON bookings(arrival_airport, flight_date);
    END IF;
END $$;

-- Create a function to automatically update flight details from AviationStack
CREATE OR REPLACE FUNCTION update_flight_details()
RETURNS TRIGGER AS $$
BEGIN
    -- If flight_number is provided but flight details are empty, mark for API update
    IF NEW.flight_number IS NOT NULL AND NEW.flight_number != '' 
       AND (NEW.flight_details IS NULL OR NEW.flight_details = '{}') THEN
        NEW.flight_details = jsonb_build_object(
            'needs_update', true,
            'last_update_attempt', NULL,
            'api_source', 'aviationstack'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically mark flights for API updates
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_update_flight_details') THEN
        CREATE TRIGGER trigger_update_flight_details
            BEFORE INSERT OR UPDATE ON bookings
            FOR EACH ROW
            EXECUTE FUNCTION update_flight_details();
    END IF;
END $$;
