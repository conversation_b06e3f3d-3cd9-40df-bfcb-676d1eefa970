-- Migration: Add terminal mapping to services
-- Date: 2025-07-09
-- Description: Add available_terminals field to services table for better terminal-service mapping

-- Add available_terminals column to services table
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS available_terminals JSONB DEFAULT '{}';

-- Add comment to explain the structure
COMMENT ON COLUMN services.available_terminals IS 'Terminal mapping per airport: {"DXB": ["T1", "T3"], "DEL": ["T1", "T2"]}';

-- Create index for terminal queries
CREATE INDEX IF NOT EXISTS idx_services_available_terminals ON services USING GIN (available_terminals);

-- Update existing services to include terminal mapping based on their available airports
-- For now, map all services to T1 terminal for each airport they serve
UPDATE services 
SET available_terminals = (
    SELECT jsonb_object_agg(
        airport_code, 
        '["T1"]'::jsonb
    )
    FROM jsonb_array_elements_text(available_airports) AS airport_code
)
WHERE available_airports IS NOT NULL 
  AND jsonb_array_length(available_airports) > 0
  AND (available_terminals IS NULL OR available_terminals = '{}'::jsonb);

-- Add more terminals for major airports to make the booking flow more realistic
UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{DXB}',
    '["T1", "T2", "T3"]'::jsonb
)
WHERE available_airports::jsonb ? 'DXB';

UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{DEL}',
    '["T1", "T2"]'::jsonb
)
WHERE available_airports::jsonb ? 'DEL';

UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{BOM}',
    '["T1", "T2"]'::jsonb
)
WHERE available_airports::jsonb ? 'BOM';

UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{COK}',
    '["T1"]'::jsonb
)
WHERE available_airports::jsonb ? 'COK';

UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{CCJ}',
    '["T1"]'::jsonb
)
WHERE available_airports::jsonb ? 'CCJ';

UPDATE services 
SET available_terminals = jsonb_set(
    available_terminals,
    '{TRV}',
    '["T1"]'::jsonb
)
WHERE available_airports::jsonb ? 'TRV';

-- Update schema version
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('003_add_terminal_mapping_to_services', NOW())
ON CONFLICT (version) DO NOTHING;
