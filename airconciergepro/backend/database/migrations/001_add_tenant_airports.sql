-- Migration: Add tenant airports management
-- Date: 2025-07-09
-- Description: Add tenant_airports table and update airports table with status field

-- Add status field to airports table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'airports' AND column_name = 'status') THEN
        ALTER TABLE airports ADD COLUMN status VARCHAR(50) NOT NULL DEFAULT 'active' 
        CHECK (status IN ('active', 'inactive'));
    END IF;
END $$;

-- Create tenant_airports table if it doesn't exist
CREATE TABLE IF NOT EXISTS tenant_airports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    airport_id UUID NOT NULL REFERENCES airports(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    operational_hours JSONB DEFAULT '{}', -- Tenant-specific operational hours
    contact_info JSONB DEFAULT '{}', -- Tenant-specific contact information
    meeting_instructions TEXT, -- Tenant-specific meeting instructions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, airport_id)
);

-- Add indexes if they don't exist
DO $$
BEGIN
    -- Check and create indexes for airports table
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_airports_iata_code') THEN
        CREATE INDEX idx_airports_iata_code ON airports(iata_code);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_airports_status') THEN
        CREATE INDEX idx_airports_status ON airports(status);
    END IF;
    
    -- Check and create indexes for tenant_airports table
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tenant_airports_tenant_id') THEN
        CREATE INDEX idx_tenant_airports_tenant_id ON tenant_airports(tenant_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tenant_airports_airport_id') THEN
        CREATE INDEX idx_tenant_airports_airport_id ON tenant_airports(airport_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tenant_airports_status') THEN
        CREATE INDEX idx_tenant_airports_status ON tenant_airports(status);
    END IF;
END $$;

-- Add triggers if they don't exist
DO $$
BEGIN
    -- Check and create trigger for airports table
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_airports_updated_at') THEN
        CREATE TRIGGER update_airports_updated_at 
        BEFORE UPDATE ON airports 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Check and create trigger for tenant_airports table
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_tenant_airports_updated_at') THEN
        CREATE TRIGGER update_tenant_airports_updated_at 
        BEFORE UPDATE ON tenant_airports 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Migrate existing service available_airports to tenant_airports
-- This will create tenant_airports entries for all airports currently used in services
INSERT INTO tenant_airports (tenant_id, airport_id, status)
SELECT DISTINCT 
    s.tenant_id,
    a.id as airport_id,
    'active' as status
FROM services s
CROSS JOIN LATERAL jsonb_array_elements_text(s.available_airports) AS airport_code
JOIN airports a ON a.iata_code = airport_code
WHERE NOT EXISTS (
    SELECT 1 FROM tenant_airports ta 
    WHERE ta.tenant_id = s.tenant_id AND ta.airport_id = a.id
)
ON CONFLICT (tenant_id, airport_id) DO NOTHING;

-- Add some common airports to all existing tenants if they don't have any airports yet
INSERT INTO tenant_airports (tenant_id, airport_id, status)
SELECT 
    t.id as tenant_id,
    a.id as airport_id,
    'active' as status
FROM tenants t
CROSS JOIN airports a
WHERE a.iata_code IN ('JFK', 'LHR', 'CDG', 'DXB', 'LAX', 'BOM', 'DEL', 'COK')
AND NOT EXISTS (
    SELECT 1 FROM tenant_airports ta WHERE ta.tenant_id = t.id
)
AND NOT EXISTS (
    SELECT 1 FROM tenant_airports ta2 WHERE ta2.tenant_id = t.id AND ta2.airport_id = a.id
)
ON CONFLICT (tenant_id, airport_id) DO NOTHING;
