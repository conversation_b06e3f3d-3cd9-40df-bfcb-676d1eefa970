const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('booking_activities').del();
  await knex('payments').del();
  await knex('notifications').del();
  await knex('bookings').del();
  await knex('users').del()
  await knex('tenants').del()

  const tenantId = uuidv4();
  const userId = uuidv4();
  const passwordHash = '$2a$12$wFhhRExtIwL1TKKixosCP.jI6yvS01jOd48jw8GjXibJHjt3Sw38i'; // Hash for "admin123"

  // Inserts tenant
  await knex('tenants').insert([
    {
      id: tenantId,
      name: 'Demo Tenant',
      subdomain: 'demo',
      plan: 'starter',
      status: 'active',
      settings: JSON.stringify({
        features: ['basic_booking', 'customer_management'],
        limits: {
          monthly_bookings: 100,
          api_calls: 1000,
          locations: 1
        }
      }),
      created_at: new Date(),
      updated_at: new Date()
    }
  ]);

  // Inserts demo user
  await knex('users').insert([
    {
      id: userId,
      tenant_id: tenantId,
      email: '<EMAIL>',
      password_hash: passwordHash,
      first_name: 'Demo',
      last_name: 'Admin',
      role: 'company_admin',
      status: 'active',
      permissions: JSON.stringify(['all']),
      created_at: new Date(),
      updated_at: new Date()
    }
  ]);
};
