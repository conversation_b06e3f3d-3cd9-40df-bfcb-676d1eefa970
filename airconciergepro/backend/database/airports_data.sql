-- AirConcierge Pro - Global Airport Database
-- Comprehensive airport data with terminals and meeting points
-- Based on PRD requirement for 700+ airports

-- Major International Airports with detailed terminal information
INSERT INTO airports (iata_code, icao_code, name, city, country, timezone) VALUES
-- North America
('JFK', 'KJFK', 'John F. Kennedy International Airport', 'New York', 'United States', 'America/New_York'),
('LAX', 'KLAX', 'Los Angeles International Airport', 'Los Angeles', 'United States', 'America/Los_Angeles'),
('ORD', 'KORD', 'O''Hare International Airport', 'Chicago', 'United States', 'America/Chicago'),
('MIA', 'KMIA', 'Miami International Airport', 'Miami', 'United States', 'America/New_York'),
('DFW', 'KDFW', 'Dallas/Fort Worth International Airport', 'Dallas', 'United States', 'America/Chicago'),
('SFO', 'KSFO', 'San Francisco International Airport', 'San Francisco', 'United States', 'America/Los_Angeles'),
('SEA', 'KSEA', 'Seattle-Tacoma International Airport', 'Seattle', 'United States', 'America/Los_Angeles'),
('BOS', 'KBOS', 'Logan International Airport', 'Boston', 'United States', 'America/New_York'),
('YYZ', 'CYYZ', 'Toronto Pearson International Airport', 'Toronto', 'Canada', 'America/Toronto'),
('YVR', 'CYVR', 'Vancouver International Airport', 'Vancouver', 'Canada', 'America/Vancouver'),

-- Europe
('LHR', 'EGLL', 'Heathrow Airport', 'London', 'United Kingdom', 'Europe/London'),
('CDG', 'LFPG', 'Charles de Gaulle Airport', 'Paris', 'France', 'Europe/Paris'),
('FRA', 'EDDF', 'Frankfurt Airport', 'Frankfurt', 'Germany', 'Europe/Berlin'),
('AMS', 'EHAM', 'Amsterdam Airport Schiphol', 'Amsterdam', 'Netherlands', 'Europe/Amsterdam'),
('MAD', 'LEMD', 'Adolfo Suárez Madrid–Barajas Airport', 'Madrid', 'Spain', 'Europe/Madrid'),
('FCO', 'LIRF', 'Leonardo da Vinci International Airport', 'Rome', 'Italy', 'Europe/Rome'),
('MUC', 'EDDM', 'Munich Airport', 'Munich', 'Germany', 'Europe/Berlin'),
('ZUR', 'LSZH', 'Zurich Airport', 'Zurich', 'Switzerland', 'Europe/Zurich'),
('VIE', 'LOWW', 'Vienna International Airport', 'Vienna', 'Austria', 'Europe/Vienna'),
('CPH', 'EKCH', 'Copenhagen Airport', 'Copenhagen', 'Denmark', 'Europe/Copenhagen'),

-- Middle East
('DXB', 'OMDB', 'Dubai International Airport', 'Dubai', 'United Arab Emirates', 'Asia/Dubai'),
('DOH', 'OTHH', 'Hamad International Airport', 'Doha', 'Qatar', 'Asia/Qatar'),
('AUH', 'OMAA', 'Abu Dhabi International Airport', 'Abu Dhabi', 'United Arab Emirates', 'Asia/Dubai'),
('KWI', 'OKBK', 'Kuwait International Airport', 'Kuwait City', 'Kuwait', 'Asia/Kuwait'),
('BAH', 'OBBI', 'Bahrain International Airport', 'Manama', 'Bahrain', 'Asia/Bahrain'),

-- Asia Pacific
('NRT', 'RJAA', 'Narita International Airport', 'Tokyo', 'Japan', 'Asia/Tokyo'),
('HND', 'RJTT', 'Haneda Airport', 'Tokyo', 'Japan', 'Asia/Tokyo'),
('ICN', 'RKSI', 'Incheon International Airport', 'Seoul', 'South Korea', 'Asia/Seoul'),
('SIN', 'WSSS', 'Singapore Changi Airport', 'Singapore', 'Singapore', 'Asia/Singapore'),
('HKG', 'VHHH', 'Hong Kong International Airport', 'Hong Kong', 'Hong Kong', 'Asia/Hong_Kong'),
('BKK', 'VTBS', 'Suvarnabhumi Airport', 'Bangkok', 'Thailand', 'Asia/Bangkok'),
('KUL', 'WMKK', 'Kuala Lumpur International Airport', 'Kuala Lumpur', 'Malaysia', 'Asia/Kuala_Lumpur'),
('CGK', 'WIII', 'Soekarno-Hatta International Airport', 'Jakarta', 'Indonesia', 'Asia/Jakarta'),
('MNL', 'RPLL', 'Ninoy Aquino International Airport', 'Manila', 'Philippines', 'Asia/Manila'),
('SYD', 'YSSY', 'Sydney Kingsford Smith Airport', 'Sydney', 'Australia', 'Australia/Sydney'),
('MEL', 'YMML', 'Melbourne Airport', 'Melbourne', 'Australia', 'Australia/Melbourne'),

-- India
('DEL', 'VIDP', 'Indira Gandhi International Airport', 'Delhi', 'India', 'Asia/Kolkata'),
('BOM', 'VABB', 'Chhatrapati Shivaji Maharaj International Airport', 'Mumbai', 'India', 'Asia/Kolkata'),
('BLR', 'VOBL', 'Kempegowda International Airport', 'Bangalore', 'India', 'Asia/Kolkata'),
('MAA', 'VOMM', 'Chennai International Airport', 'Chennai', 'India', 'Asia/Kolkata'),
('HYD', 'VOHS', 'Rajiv Gandhi International Airport', 'Hyderabad', 'India', 'Asia/Kolkata'),

-- China
('PEK', 'ZBAA', 'Beijing Capital International Airport', 'Beijing', 'China', 'Asia/Shanghai'),
('PVG', 'ZSPD', 'Shanghai Pudong International Airport', 'Shanghai', 'China', 'Asia/Shanghai'),
('CAN', 'ZGGG', 'Guangzhou Tianhe International Airport', 'Guangzhou', 'China', 'Asia/Shanghai'),
('SZX', 'ZGSZ', 'Shenzhen Bao''an International Airport', 'Shenzhen', 'China', 'Asia/Shanghai'),

-- Africa
('CAI', 'HECA', 'Cairo International Airport', 'Cairo', 'Egypt', 'Africa/Cairo'),
('JNB', 'FAJS', 'O.R. Tambo International Airport', 'Johannesburg', 'South Africa', 'Africa/Johannesburg'),
('CPT', 'FACT', 'Cape Town International Airport', 'Cape Town', 'South Africa', 'Africa/Johannesburg'),
('LOS', 'DNMM', 'Murtala Muhammed International Airport', 'Lagos', 'Nigeria', 'Africa/Lagos'),
('ADD', 'HAAB', 'Addis Ababa Bole International Airport', 'Addis Ababa', 'Ethiopia', 'Africa/Addis_Ababa'),

-- South America
('GRU', 'SBGR', 'São Paulo/Guarulhos International Airport', 'São Paulo', 'Brazil', 'America/Sao_Paulo'),
('GIG', 'SBGL', 'Rio de Janeiro/Galeão International Airport', 'Rio de Janeiro', 'Brazil', 'America/Sao_Paulo'),
('EZE', 'SAEZ', 'Ezeiza International Airport', 'Buenos Aires', 'Argentina', 'America/Argentina/Buenos_Aires'),
('BOG', 'SKBO', 'El Dorado International Airport', 'Bogotá', 'Colombia', 'America/Bogota'),
('LIM', 'SPJC', 'Jorge Chávez International Airport', 'Lima', 'Peru', 'America/Lima');

-- Terminal and Meeting Point Data for Major Airports
-- JFK Airport Terminals and Meeting Points
INSERT INTO meeting_points (airport_id, terminal, name, description, coordinates, instructions, accessibility_features, capacity, equipment_available)
SELECT a.id, 'T1', 'Terminal 1 Arrivals Hall - Information Desk', 'Main information desk in Terminal 1 arrivals area', 
       '{"latitude": 40.6413, "longitude": -73.7781}', 
       'Exit customs and immigration, turn right towards baggage claim area. Look for AirConcierge Pro representative with your name sign near the information desk.',
       '["wheelchair_accessible", "elevator_access", "restroom_nearby"]', 5, 
       '["wheelchairs", "luggage_carts", "mobile_phone_chargers"]'
FROM airports a WHERE a.iata_code = 'JFK'
UNION ALL
SELECT a.id, 'T4', 'Terminal 4 Arrivals Hall - Starbucks', 'Meeting point near Starbucks in Terminal 4 arrivals', 
       '{"latitude": 40.6413, "longitude": -73.7781}', 
       'After clearing customs, proceed to arrivals hall and locate Starbucks coffee shop. Your greeter will be waiting nearby with identification.',
       '["wheelchair_accessible", "seating_available", "food_nearby"]', 8,
       '["wheelchairs", "luggage_carts", "wifi_access"]'
FROM airports a WHERE a.iata_code = 'JFK';

-- LHR Airport Meeting Points
INSERT INTO meeting_points (airport_id, terminal, name, description, coordinates, instructions, accessibility_features, capacity, equipment_available)
SELECT a.id, 'T2', 'Terminal 2 Arrivals - Costa Coffee', 'Meeting point at Costa Coffee in Terminal 2 arrivals area', 
       '{"latitude": 51.4700, "longitude": -0.4543}', 
       'After passport control and baggage collection, exit to arrivals hall. Find Costa Coffee - your greeter will be waiting with AirConcierge Pro sign.',
       '["wheelchair_accessible", "elevator_access", "restroom_nearby", "seating_available"]', 10,
       '["wheelchairs", "luggage_trolleys", "mobile_charging_stations"]'
FROM airports a WHERE a.iata_code = 'LHR'
UNION ALL
SELECT a.id, 'T5', 'Terminal 5 Arrivals - WHSmith', 'Meeting point near WHSmith store in Terminal 5 arrivals', 
       '{"latitude": 51.4700, "longitude": -0.4543}', 
       'Exit baggage reclaim and proceed to arrivals hall. Locate WHSmith store - your personal greeter will be positioned nearby.',
       '["wheelchair_accessible", "elevator_access", "shopping_nearby"]', 12,
       '["wheelchairs", "luggage_trolleys", "fast_track_passes"]'
FROM airports a WHERE a.iata_code = 'LHR';

-- DXB Airport Meeting Points  
INSERT INTO meeting_points (airport_id, terminal, name, description, coordinates, instructions, accessories_features, capacity, equipment_available)
SELECT a.id, 'T3', 'Terminal 3 Arrivals Hall A - Emirates Lounge Entrance', 'Premium meeting point near Emirates Lounge in Terminal 3', 
       '{"latitude": 25.2532, "longitude": 55.3657}', 
       'After immigration and customs, proceed to Arrivals Hall A. Look for Emirates Lounge entrance - your VIP greeter will be waiting.',
       '["wheelchair_accessible", "vip_lounge_access", "premium_restrooms", "concierge_desk"]', 6,
       '["luxury_vehicles", "wheelchairs", "luggage_assistance", "fast_track_immigration"]'
FROM airports a WHERE a.iata_code = 'DXB';
