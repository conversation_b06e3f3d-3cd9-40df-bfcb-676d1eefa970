-- Seed services for tenant fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff
-- This will create services for major airports so the customer portal shows only airports with active services

-- Insert services for DXB (Dubai International Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'VIP Meet & Greet - Dubai',
    'Premium meet and greet service at Dubai International Airport',
    'meet_greet',
    'arrival',
    2500,
    'INR',
    60,
    4,
    'active',
    '["DXB"]',
    '["Personal agent", "Fast track", "Baggage assistance", "VIP lounge access"]',
    '["Valid passport", "Flight details"]'
),
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Fast Track Immigration - Dubai',
    'Skip the queues with our fast track service at Dubai Airport',
    'fast_track',
    'arrival',
    1500,
    'INR',
    30,
    2,
    'active',
    '["DXB"]',
    '["Priority lane access", "Escort service"]',
    '["Valid passport"]'
),
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Departure Lounge Access - Dubai',
    'Relax in premium airport lounge before departure',
    'lounge',
    'departure',
    3000,
    'INR',
    180,
    6,
    'active',
    '["DXB"]',
    '["Food & beverages", "WiFi", "Comfortable seating", "Shower facilities"]',
    '["Valid boarding pass"]'
);

-- Insert services for BOM (Mumbai Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'VIP Meet & Greet - Mumbai',
    'Premium meet and greet service at Mumbai Airport',
    'meet_greet',
    'arrival',
    2000,
    'INR',
    60,
    4,
    'active',
    '["BOM"]',
    '["Personal agent", "Fast track", "Baggage assistance"]',
    '["Valid passport", "Flight details"]'
),
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Airport Transfer - Mumbai',
    'Comfortable transfer service from Mumbai Airport',
    'transfer',
    'arrival',
    1800,
    'INR',
    45,
    8,
    'active',
    '["BOM"]',
    '["Private vehicle", "Professional driver", "Meet & greet"]',
    '["Valid ID", "Destination address"]'
);

-- Insert services for DEL (Delhi Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'VIP Meet & Greet - Delhi',
    'Premium meet and greet service at Delhi Airport',
    'meet_greet',
    'arrival',
    2200,
    'INR',
    60,
    4,
    'active',
    '["DEL"]',
    '["Personal agent", "Fast track", "Baggage assistance"]',
    '["Valid passport", "Flight details"]'
),
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Business Lounge Access - Delhi',
    'Access to premium business lounge at Delhi Airport',
    'lounge',
    'departure',
    2800,
    'INR',
    120,
    4,
    'active',
    '["DEL"]',
    '["Food & beverages", "WiFi", "Business facilities"]',
    '["Valid boarding pass"]'
);

-- Insert services for BLR (Bangalore Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Meet & Greet - Bangalore',
    'Professional meet and greet service at Bangalore Airport',
    'meet_greet',
    'arrival',
    1800,
    'INR',
    45,
    4,
    'active',
    '["BLR"]',
    '["Personal agent", "Baggage assistance"]',
    '["Valid passport", "Flight details"]'
);

-- Insert services for MAA (Chennai Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'VIP Service - Chennai',
    'VIP meet and greet service at Chennai Airport',
    'meet_greet',
    'arrival',
    1900,
    'INR',
    50,
    4,
    'active',
    '["MAA"]',
    '["Personal agent", "Fast track", "Baggage assistance"]',
    '["Valid passport", "Flight details"]'
);

-- Insert services for CCU (Kolkata Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Airport Assistance - Kolkata',
    'Professional airport assistance at Kolkata Airport',
    'assistance',
    'arrival',
    1600,
    'INR',
    40,
    6,
    'active',
    '["CCU"]',
    '["Personal agent", "Information assistance"]',
    '["Valid ID"]'
);

-- Insert services for HYD (Hyderabad Airport)
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Premium Service - Hyderabad',
    'Premium airport service at Hyderabad Airport',
    'meet_greet',
    'arrival',
    2000,
    'INR',
    55,
    4,
    'active',
    '["HYD"]',
    '["Personal agent", "Fast track", "Baggage assistance"]',
    '["Valid passport", "Flight details"]'
);

-- Add some departure services as well
INSERT INTO services (
    id, tenant_id, name, description, category, type, base_price, currency, 
    duration_minutes, max_passengers, status, available_airports, inclusions, requirements
) VALUES 
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Departure Assistance - Mumbai',
    'Professional departure assistance at Mumbai Airport',
    'assistance',
    'departure',
    1500,
    'INR',
    90,
    4,
    'active',
    '["BOM"]',
    '["Check-in assistance", "Security fast track", "Boarding assistance"]',
    '["Valid boarding pass", "Valid ID"]'
),
(
    gen_random_uuid(),
    'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff',
    'Departure VIP - Delhi',
    'VIP departure service at Delhi Airport',
    'meet_greet',
    'departure',
    2500,
    'INR',
    120,
    4,
    'active',
    '["DEL"]',
    '["Personal agent", "Check-in assistance", "Lounge access", "Priority boarding"]',
    '["Valid boarding pass", "Valid ID"]'
);
