-- Enhanced sample data for new features

-- User Groups for different pricing tiers
INSERT INTO user_groups (id, tenant_id, name, description, pricing_tier, discount_percentage, features) VALUES
('650e8400-e29b-41d4-a716-446655440030',
 '550e8400-e29b-41d4-a716-************',
 'Standard Customers',
 'Regular individual customers with standard pricing',
 'standard',
 0,
 '["basic_booking", "email_notifications"]'
),
('650e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 'Premium Members',
 'Premium customers with enhanced services and priority support',
 'premium',
 10,
 '["priority_booking", "sms_notifications", "lounge_access", "priority_support"]'
),
('650e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 'VIP Corporate',
 'VIP corporate accounts with maximum benefits',
 'vip',
 25,
 '["instant_booking", "dedicated_agent", "all_notifications", "vip_lounges", "24_7_support", "custom_services"]'
);

-- Equipment for service delivery
INSERT INTO equipment (id, tenant_id, type, identifier, location, status, specifications) VALUES
('650e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 'buggy',
 'BGY-JFK-001',
 'JFK Terminal 4',
 'available',
 '{"capacity": 4, "wheelchair_accessible": true, "electric": true}'
),
('650e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 'wheelchair',
 'WC-JFK-001',
 'JFK Terminal 4',
 'available',
 '{"type": "manual", "weight_capacity": "120kg", "foldable": true}'
),
('650e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-************',
 'vehicle',
 'VH-LHR-001',
 'LHR Terminal 5',
 'available',
 '{"type": "luxury_sedan", "capacity": 4, "brand": "Mercedes", "model": "S-Class"}'
);

-- Meeting points for airports
INSERT INTO meeting_points (id, airport_id, terminal, name, description, coordinates, instructions, accessibility_features) VALUES
('650e8400-e29b-41d4-a716-446655440036',
 (SELECT id FROM airports WHERE iata_code = 'JFK'),
 'T4',
 'Arrivals Hall Central',
 'Main meeting point in Terminal 4 arrivals area',
 '{"latitude": 40.6413, "longitude": -73.7781}',
 'Exit customs and immigration, turn right towards the central seating area',
 '["wheelchair_accessible", "elevator_access", "clear_signage"]'
),
('650e8400-e29b-41d4-a716-446655440037',
 (SELECT id FROM airports WHERE iata_code = 'LHR'),
 'T5',
 'Departure Lounge Level 2',
 'Premium meeting point for departing passengers',
 '{"latitude": 51.4700, "longitude": -0.4543}',
 'After security, take elevator to Level 2, meet near Costa Coffee',
 '["wheelchair_accessible", "elevator_access", "quiet_zone"]'
);

-- Training modules
INSERT INTO training_modules (id, tenant_id, title, description, content, duration_minutes, required_for_roles, certification_required) VALUES
('650e8400-e29b-41d4-a716-446655440038',
 '550e8400-e29b-41d4-a716-************',
 'Airport Security Protocols',
 'Essential security procedures and protocols for airport operations',
 '{"sections": [{"title": "Security Basics", "content": "Understanding airport security zones and procedures"}, {"title": "Emergency Procedures", "content": "What to do in emergency situations"}], "quiz": [{"question": "What are the main security zones in an airport?", "options": ["Public, Sterile, SIDA", "Entry, Exit, Transit", "Domestic, International"], "correct": 0}]}',
 45,
 '["field_agent", "operations_manager"]',
 true
),
('650e8400-e29b-41d4-a716-446655440039',
 '550e8400-e29b-41d4-a716-************',
 'Customer Service Excellence',
 'Best practices for delivering exceptional customer service',
 '{"sections": [{"title": "Communication Skills", "content": "Effective communication with diverse customers"}, {"title": "Problem Resolution", "content": "Handling customer complaints and issues"}], "quiz": [{"question": "What is the first step in handling a customer complaint?", "options": ["Listen actively", "Offer a solution", "Escalate to manager"], "correct": 0}]}',
 60,
 '["field_agent", "customer_service"]',
 true
);

-- Flight information samples
INSERT INTO flight_information (flight_number, airline, departure_airport, arrival_airport, scheduled_departure, scheduled_arrival, estimated_arrival, status, gate, terminal) VALUES
('BA175', 'British Airways', 'LHR', 'JFK', 
 CURRENT_DATE + INTERVAL '2 days' + INTERVAL '8 hours',
 CURRENT_DATE + INTERVAL '2 days' + INTERVAL '14 hours',
 CURRENT_DATE + INTERVAL '2 days' + INTERVAL '14 hours 15 minutes',
 'delayed', 'A12', 'T4'
),
('AA101', 'American Airlines', 'JFK', 'LAX',
 CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours',
 CURRENT_DATE + INTERVAL '1 day' + INTERVAL '16 hours',
 CURRENT_DATE + INTERVAL '1 day' + INTERVAL '16 hours',
 'on_time', 'B24', 'T8'
);

-- Pricing rules for dynamic pricing
INSERT INTO pricing_rules (id, tenant_id, service_id, rule_name, rule_type, conditions, adjustment_type, adjustment_value, priority) VALUES
('650e8400-e29b-41d4-a716-446655440040',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440010',
 'Peak Hours Surcharge',
 'time_based',
 '{"hours": ["06:00-09:00", "17:00-21:00"], "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}',
 'percentage',
 20.00,
 1
),
('650e8400-e29b-41d4-a716-446655440041',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440010',
 'Group Discount',
 'group_discount',
 '{"min_passengers": 5, "max_passengers": 10}',
 'percentage',
 -15.00,
 2
),
('650e8400-e29b-41d4-a716-446655440042',
 '550e8400-e29b-41d4-a716-************',
 NULL,
 'High Demand Surge',
 'demand_based',
 '{"booking_threshold": 20, "time_window_hours": 24}',
 'percentage',
 30.00,
 3
);

-- White-label configuration sample
INSERT INTO white_label_configs (id, tenant_id, partner_name, subdomain, branding, features, commission_rate) VALUES
('650e8400-e29b-41d4-a716-446655440043',
 '550e8400-e29b-41d4-a716-************',
 'Luxury Travel Agency',
 'luxurytravel',
 '{"logo": "https://example.com/logo.png", "primary_color": "#d4af37", "secondary_color": "#1a1a1a", "font_family": "Playfair Display"}',
 '["custom_branding", "dedicated_support", "priority_booking", "vip_services"]',
 15.00
);

-- Service Level Agreements
INSERT INTO service_level_agreements (id, tenant_id, customer_id, sla_type, target_value, measurement_unit, penalty_amount, valid_from, valid_to) VALUES
('650e8400-e29b-41d4-a716-446655440044',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440009',
 'response_time',
 5.00,
 'minutes',
 50.00,
 CURRENT_DATE,
 CURRENT_DATE + INTERVAL '1 year'
),
('650e8400-e29b-41d4-a716-446655440045',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440009',
 'completion_time',
 60.00,
 'minutes',
 100.00,
 CURRENT_DATE,
 CURRENT_DATE + INTERVAL '1 year'
);

-- Update existing customers with user groups
UPDATE customers SET user_group_id = '650e8400-e29b-41d4-a716-446655440030' WHERE id = '550e8400-e29b-41d4-a716-446655440007';
UPDATE customers SET user_group_id = '650e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-446655440008';
UPDATE customers SET user_group_id = '650e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-446655440009';

-- User training progress samples
INSERT INTO user_training_progress (user_id, training_module_id, status, score, started_at, completed_at, certificate_issued_at) VALUES
('550e8400-e29b-41d4-a716-446655440003',
 '650e8400-e29b-41d4-a716-446655440038',
 'completed',
 92,
 NOW() - INTERVAL '2 days',
 NOW() - INTERVAL '1 day',
 NOW() - INTERVAL '1 day'
),
('550e8400-e29b-41d4-a716-446655440004',
 '650e8400-e29b-41d4-a716-446655440039',
 'in_progress',
 NULL,
 NOW() - INTERVAL '1 hour',
 NULL,
 NULL
);

-- Sample documents
INSERT INTO documents (id, tenant_id, booking_id, customer_id, filename, original_filename, file_type, file_size, storage_path, document_type, metadata) VALUES
('650e8400-e29b-41d4-a716-446655440046',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440014',
 '550e8400-e29b-41d4-a716-446655440007',
 'passport_scan_123.pdf',
 'passport_robert_wilson.pdf',
 'application/pdf',
 245760,
 '/documents/tenant_550e8400/passport_scan_123.pdf',
 'passport',
 '{"page_count": 2, "scan_quality": "high", "verified": true}'
);
