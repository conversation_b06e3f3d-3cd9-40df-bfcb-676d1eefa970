-- AirConcierge Pro Database Schema
-- PostgreSQL Database Schema for Meet & Greet SaaS Platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tenants table (Multi-tenant architecture)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(50) UNIQUE NOT NULL,
    plan VARCHAR(50) NOT NULL DEFAULT 'starter' CHECK (plan IN ('starter', 'professional', 'enterprise', 'enterprise_plus')),
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'trial')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'operations_manager', 'field_agent', 'customer_service')),
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    permissions JSONB DEFAULT '[]',
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(email)
);

-- Customers table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    date_of_birth DATE,
    preferences JSONB DEFAULT '{}',
    loyalty_points INTEGER DEFAULT 0,
    total_bookings INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, email)
);

-- Airports table (Global reference data with AviationStack integration)
CREATE TABLE airports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    iata_code VARCHAR(3) UNIQUE NOT NULL,
    icao_code VARCHAR(4) UNIQUE,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(255) NOT NULL,
    country VARCHAR(255) NOT NULL,
    country_iso2 VARCHAR(2),
    timezone VARCHAR(50) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    phone_number VARCHAR(50),
    terminals JSONB DEFAULT '[]',
    facilities JSONB DEFAULT '{}',
    meeting_points JSONB DEFAULT '[]',
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tenant airports table (Which airports each tenant operates in)
CREATE TABLE tenant_airports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    airport_id UUID NOT NULL REFERENCES airports(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    operational_hours JSONB DEFAULT '{}', -- Tenant-specific operational hours
    contact_info JSONB DEFAULT '{}', -- Tenant-specific contact information
    meeting_instructions TEXT, -- Tenant-specific meeting instructions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, airport_id)
);

-- Flight cache table for real-time flight data
CREATE TABLE flight_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flight_number VARCHAR(10) NOT NULL,
    flight_date DATE NOT NULL,
    status VARCHAR(50),
    airline_name VARCHAR(100),
    airline_iata VARCHAR(3),
    departure_airport VARCHAR(3),
    departure_scheduled TIMESTAMP WITH TIME ZONE,
    departure_estimated TIMESTAMP WITH TIME ZONE,
    departure_actual TIMESTAMP WITH TIME ZONE,
    departure_terminal VARCHAR(10),
    departure_gate VARCHAR(10),
    departure_delay INTEGER DEFAULT 0,
    arrival_airport VARCHAR(3),
    arrival_scheduled TIMESTAMP WITH TIME ZONE,
    arrival_estimated TIMESTAMP WITH TIME ZONE,
    arrival_actual TIMESTAMP WITH TIME ZONE,
    arrival_terminal VARCHAR(10),
    arrival_gate VARCHAR(10),
    arrival_baggage VARCHAR(10),
    arrival_delay INTEGER DEFAULT 0,
    aircraft_registration VARCHAR(20),
    aircraft_type VARCHAR(10),
    live_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(flight_number, flight_date)
);

-- Services table
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('meet_greet', 'fast_track', 'lounge_access', 'vip_terminal', 'transfer', 'porter')),
    type VARCHAR(50) NOT NULL CHECK (type IN ('arrival', 'departure', 'transit')),
    base_price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    duration_minutes INTEGER NOT NULL,
    max_passengers INTEGER NOT NULL DEFAULT 1,
    available_airports JSONB DEFAULT '[]',
    available_terminals JSONB DEFAULT '{}', -- New field: {"DXB": ["T1", "T3"], "DEL": ["T1", "T2"]}
    requirements JSONB DEFAULT '[]',
    inclusions JSONB DEFAULT '[]',
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'offline' CHECK (status IN ('available', 'busy', 'offline')),
    current_location JSONB,
    specializations JSONB DEFAULT '[]',
    languages JSONB DEFAULT '[]',
    airports JSONB DEFAULT '[]',
    rating DECIMAL(3,2) DEFAULT 5.0,
    total_services INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, user_id)
);

-- Bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(id),
    service_id UUID NOT NULL REFERENCES services(id),
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'in_progress', 'completed', 'cancelled', 'no_show')),
    
    -- Flight details (Enhanced for AviationStack integration)
    flight_number VARCHAR(20),
    airline_code VARCHAR(3),
    airline_name VARCHAR(100),
    departure_airport VARCHAR(3) NOT NULL,
    arrival_airport VARCHAR(3) NOT NULL,
    departure_terminal VARCHAR(10),
    arrival_terminal VARCHAR(10),
    flight_date DATE NOT NULL,
    scheduled_departure TIMESTAMP WITH TIME ZONE,
    scheduled_arrival TIMESTAMP WITH TIME ZONE,
    estimated_departure TIMESTAMP WITH TIME ZONE,
    estimated_arrival TIMESTAMP WITH TIME ZONE,
    actual_departure TIMESTAMP WITH TIME ZONE,
    actual_arrival TIMESTAMP WITH TIME ZONE,
    flight_status VARCHAR(50), -- scheduled, active, landed, cancelled, delayed, etc.
    aircraft_type VARCHAR(50),
    gate VARCHAR(10),
    baggage_belt VARCHAR(10),
    flight_details JSONB DEFAULT '{}', -- Store complete AviationStack response
    
    -- Service details
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('arrival', 'departure', 'transit')),
    passenger_count INTEGER NOT NULL DEFAULT 1,
    passengers JSONB NOT NULL,
    special_requirements JSONB DEFAULT '[]',
    meeting_point VARCHAR(255),
    
    -- Assignment
    assigned_agent_id UUID REFERENCES users(id),
    assignment_time TIMESTAMP WITH TIME ZONE,
    
    -- Pricing
    base_price DECIMAL(10,2) NOT NULL,
    additional_charges DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Payment
    payment_status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'refunded', 'failed')),
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    
    -- Service timing
    service_start_time TIMESTAMP WITH TIME ZONE,
    service_end_time TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Booking activities table (Audit log)
CREATE TABLE booking_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN ('created', 'updated', 'assigned', 'started', 'completed', 'cancelled', 'feedback')),
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    booking_id UUID NOT NULL REFERENCES bookings(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('credit_card', 'debit_card', 'paypal', 'stripe', 'bank_transfer')),
    payment_provider VARCHAR(100) NOT NULL,
    provider_transaction_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Keys table (Partner integration)
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    permissions JSONB DEFAULT '[]',
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'revoked')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Partner bookings table (Track partner-originated bookings)
CREATE TABLE partner_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
    partner_booking_id VARCHAR(255) NOT NULL,
    api_key_id UUID NOT NULL REFERENCES api_keys(id),
    commission_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(partner_booking_id, api_key_id)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES bookings(id),
    user_id UUID REFERENCES users(id),
    customer_id UUID REFERENCES customers(id),
    type VARCHAR(50) NOT NULL CHECK (type IN ('email', 'sms', 'push', 'webhook')),
    channel VARCHAR(50) NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'delivered')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX idx_customers_email ON customers(tenant_id, email);
CREATE INDEX idx_services_tenant_id ON services(tenant_id);
CREATE INDEX idx_services_category_type ON services(category, type);
CREATE INDEX idx_agents_tenant_id ON agents(tenant_id);
CREATE INDEX idx_agents_user_id ON agents(user_id);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_bookings_tenant_id ON bookings(tenant_id);
CREATE INDEX idx_bookings_customer_id ON bookings(customer_id);
CREATE INDEX idx_bookings_service_id ON bookings(service_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_flight_date ON bookings(flight_date);
CREATE INDEX idx_bookings_assigned_agent ON bookings(assigned_agent_id);
CREATE INDEX idx_booking_activities_booking_id ON booking_activities(booking_id);
CREATE INDEX idx_payments_booking_id ON payments(booking_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX idx_partner_bookings_booking_id ON partner_bookings(booking_id);
CREATE INDEX idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX idx_notifications_booking_id ON notifications(booking_id);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Meeting points for airports
CREATE TABLE meeting_points (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  airport_id UUID REFERENCES airports(id) ON DELETE CASCADE,
  terminal VARCHAR(10),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  coordinates JSONB, -- {latitude, longitude}
  instructions TEXT,
  accessibility_features TEXT[],
  capacity INTEGER DEFAULT 1,
  equipment_available TEXT[],
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Airport terminals information
CREATE TABLE airport_terminals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  airport_id UUID REFERENCES airports(id) ON DELETE CASCADE,
  terminal_code VARCHAR(10) NOT NULL,
  terminal_name VARCHAR(255) NOT NULL,
  gates TEXT[], -- Array of gate numbers
  facilities TEXT[], -- Array of available facilities
  operating_hours JSONB, -- {open: "05:00", close: "23:00"}
  contact_info JSONB, -- {phone, email, etc}
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(airport_id, terminal_code)
);

-- Flight information cache table
CREATE TABLE flight_information (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  flight_number VARCHAR(20) NOT NULL,
  airline VARCHAR(255),
  departure_airport VARCHAR(3) NOT NULL,
  arrival_airport VARCHAR(3) NOT NULL,
  scheduled_departure TIMESTAMP WITH TIME ZONE NOT NULL,
  scheduled_arrival TIMESTAMP WITH TIME ZONE NOT NULL,
  estimated_departure TIMESTAMP WITH TIME ZONE,
  estimated_arrival TIMESTAMP WITH TIME ZONE,
  actual_departure TIMESTAMP WITH TIME ZONE,
  actual_arrival TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) DEFAULT 'scheduled',
  gate VARCHAR(10),
  terminal VARCHAR(10),
  aircraft_type VARCHAR(50),
  delay_reason TEXT,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(flight_number, scheduled_departure)
);

-- Airport operational data
CREATE TABLE airport_operations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  airport_id UUID REFERENCES airports(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  total_arrivals INTEGER DEFAULT 0,
  total_departures INTEGER DEFAULT 0,
  delayed_flights INTEGER DEFAULT 0,
  cancelled_flights INTEGER DEFAULT 0,
  average_delay_minutes INTEGER DEFAULT 0,
  weather_conditions VARCHAR(100),
  operational_status VARCHAR(50) DEFAULT 'normal', -- normal, disrupted, closed
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(airport_id, date)
);

-- Additional indexes for new tables
CREATE INDEX idx_airports_iata_code ON airports(iata_code);
CREATE INDEX idx_airports_status ON airports(status);
CREATE INDEX idx_tenant_airports_tenant_id ON tenant_airports(tenant_id);
CREATE INDEX idx_tenant_airports_airport_id ON tenant_airports(airport_id);
CREATE INDEX idx_tenant_airports_status ON tenant_airports(status);
CREATE INDEX idx_meeting_points_airport_id ON meeting_points(airport_id);
CREATE INDEX idx_meeting_points_terminal ON meeting_points(terminal);
CREATE INDEX idx_airport_terminals_airport_id ON airport_terminals(airport_id);
CREATE INDEX idx_flight_information_flight_number ON flight_information(flight_number);
CREATE INDEX idx_flight_information_departure_airport ON flight_information(departure_airport);
CREATE INDEX idx_flight_information_arrival_airport ON flight_information(arrival_airport);
CREATE INDEX idx_flight_information_scheduled_departure ON flight_information(scheduled_departure);
CREATE INDEX idx_airport_operations_airport_id ON airport_operations(airport_id);
CREATE INDEX idx_airport_operations_date ON airport_operations(date);

-- Additional triggers for new tables
CREATE TRIGGER update_airports_updated_at BEFORE UPDATE ON airports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_airports_updated_at BEFORE UPDATE ON tenant_airports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meeting_points_updated_at BEFORE UPDATE ON meeting_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_airport_terminals_updated_at BEFORE UPDATE ON airport_terminals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_airport_operations_updated_at BEFORE UPDATE ON airport_operations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
