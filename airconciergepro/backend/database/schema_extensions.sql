-- User Groups and Pricing Management Extension
-- Add user groups table
CREATE TABLE user_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    pricing_tier VARCHAR(50) NOT NULL CHECK (pricing_tier IN ('standard', 'premium', 'vip')),
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    features JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Equipment table for tracking buggies, wheelchairs, etc.
CREATE TABLE equipment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('buggy', 'wheelchair', 'cart', 'vehicle', 'other')),
    identifier VARCHAR(50) NOT NULL,
    location VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'available' CHECK (status IN ('available', 'in_use', 'maintenance', 'out_of_service')),
    current_booking_id UUID REFERENCES bookings(id),
    specifications JSONB DEFAULT '{}',
    maintenance_schedule JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, identifier)
);

-- Flight information table for real-time data
CREATE TABLE flight_information (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    flight_number VARCHAR(20) NOT NULL,
    airline VARCHAR(100) NOT NULL,
    departure_airport VARCHAR(3) NOT NULL,
    arrival_airport VARCHAR(3) NOT NULL,
    scheduled_departure TIMESTAMP WITH TIME ZONE,
    scheduled_arrival TIMESTAMP WITH TIME ZONE,
    actual_departure TIMESTAMP WITH TIME ZONE,
    actual_arrival TIMESTAMP WITH TIME ZONE,
    estimated_departure TIMESTAMP WITH TIME ZONE,
    estimated_arrival TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'scheduled',
    gate VARCHAR(10),
    terminal VARCHAR(10),
    aircraft_type VARCHAR(50),
    delay_reason TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Training modules table
CREATE TABLE training_modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    duration_minutes INTEGER,
    required_for_roles JSONB DEFAULT '[]',
    certification_required BOOLEAN DEFAULT false,
    passing_score INTEGER DEFAULT 80,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User training progress
CREATE TABLE user_training_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    training_module_id UUID NOT NULL REFERENCES training_modules(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'failed')),
    score INTEGER,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    certificate_issued_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, training_module_id)
);

-- Meeting points table for detailed airport mapping
CREATE TABLE meeting_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    airport_id UUID NOT NULL REFERENCES airports(id) ON DELETE CASCADE,
    terminal VARCHAR(10),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    coordinates JSONB,
    instructions TEXT,
    accessibility_features JSONB DEFAULT '[]',
    capacity INTEGER DEFAULT 1,
    equipment_available JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table for file management
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    document_type VARCHAR(50) CHECK (document_type IN ('passport', 'visa', 'ticket', 'id', 'certificate', 'invoice', 'other')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pricing rules table for dynamic pricing
CREATE TABLE pricing_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('time_based', 'demand_based', 'group_discount', 'corporate_rate', 'surge_pricing')),
    conditions JSONB NOT NULL,
    adjustment_type VARCHAR(20) NOT NULL CHECK (adjustment_type IN ('percentage', 'fixed_amount')),
    adjustment_value DECIMAL(10,2) NOT NULL,
    priority INTEGER DEFAULT 0,
    valid_from DATE,
    valid_to DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- White-label configurations
CREATE TABLE white_label_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    partner_name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE,
    custom_domain VARCHAR(255),
    branding JSONB NOT NULL DEFAULT '{}',
    features JSONB DEFAULT '[]',
    api_endpoints JSONB DEFAULT '{}',
    commission_rate DECIMAL(5,2) DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Service level agreements
CREATE TABLE service_level_agreements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id),
    sla_type VARCHAR(50) NOT NULL CHECK (sla_type IN ('response_time', 'completion_time', 'availability', 'quality_score')),
    target_value DECIMAL(10,2) NOT NULL,
    measurement_unit VARCHAR(20) NOT NULL,
    penalty_amount DECIMAL(10,2),
    valid_from DATE NOT NULL,
    valid_to DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for new tables
CREATE INDEX idx_user_groups_tenant_id ON user_groups(tenant_id);
CREATE INDEX idx_equipment_tenant_id ON equipment(tenant_id);
CREATE INDEX idx_equipment_status ON equipment(status);
CREATE INDEX idx_flight_information_number ON flight_information(flight_number);
CREATE INDEX idx_flight_information_airports ON flight_information(departure_airport, arrival_airport);
CREATE INDEX idx_training_modules_tenant_id ON training_modules(tenant_id);
CREATE INDEX idx_user_training_progress_user_id ON user_training_progress(user_id);
CREATE INDEX idx_meeting_points_airport_id ON meeting_points(airport_id);
CREATE INDEX idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX idx_documents_booking_id ON documents(booking_id);
CREATE INDEX idx_pricing_rules_tenant_id ON pricing_rules(tenant_id);
CREATE INDEX idx_pricing_rules_service_id ON pricing_rules(service_id);
CREATE INDEX idx_white_label_configs_tenant_id ON white_label_configs(tenant_id);
CREATE INDEX idx_sla_tenant_id ON service_level_agreements(tenant_id);

-- Add user_group_id to customers table
ALTER TABLE customers ADD COLUMN user_group_id UUID REFERENCES user_groups(id);

-- Add equipment tracking to bookings
ALTER TABLE bookings ADD COLUMN assigned_equipment JSONB DEFAULT '[]';

-- Add white-label config to bookings
ALTER TABLE bookings ADD COLUMN white_label_config_id UUID REFERENCES white_label_configs(id);

-- Add SLA tracking to bookings
ALTER TABLE bookings ADD COLUMN sla_requirements JSONB DEFAULT '{}';
ALTER TABLE bookings ADD COLUMN sla_compliance JSONB DEFAULT '{}';

-- Update triggers for new tables
CREATE TRIGGER update_user_groups_updated_at BEFORE UPDATE ON user_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_modules_updated_at BEFORE UPDATE ON training_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_training_progress_updated_at BEFORE UPDATE ON user_training_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meeting_points_updated_at BEFORE UPDATE ON meeting_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pricing_rules_updated_at BEFORE UPDATE ON pricing_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_white_label_configs_updated_at BEFORE UPDATE ON white_label_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sla_updated_at BEFORE UPDATE ON service_level_agreements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
