#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { aviationStackService } from '../src/services/aviationStackService';
import { logger } from '../src/services/logger';
import { query } from '../src/services/database';

// Load environment variables
dotenv.config();

async function syncAirports() {
  try {
    logger.info('Starting airport synchronization from AviationStack API...');
    
    // Check current airport count
    const beforeResult = await query('SELECT COUNT(*) as count FROM airports', []);
    const beforeCount = parseInt(beforeResult.rows[0].count);
    
    logger.info(`Current airports in database: ${beforeCount}`);
    
    // Sync airports in batches
    const batchSize = 100;
    let offset = 0;
    let totalSynced = 0;
    
    while (true) {
      logger.info(`Syncing batch: offset ${offset}, limit ${batchSize}`);
      
      try {
        await aviationStackService.syncAirports(batchSize, offset);
        
        // Check if we got a full batch (indicating more data available)
        const currentResult = await query('SELECT COUNT(*) as count FROM airports', []);
        const currentCount = parseInt(currentResult.rows[0].count);
        const batchSynced = currentCount - beforeCount - totalSynced;
        
        if (batchSynced === 0) {
          logger.info('No more airports to sync');
          break;
        }
        
        totalSynced += batchSynced;
        offset += batchSize;
        
        logger.info(`Synced ${batchSynced} airports in this batch. Total synced: ${totalSynced}`);
        
        // Add delay to respect API rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Stop after a reasonable number to avoid hitting API limits
        if (offset >= 1000) {
          logger.info('Reached sync limit of 1000 airports');
          break;
        }
        
      } catch (error) {
        logger.error(`Error syncing batch at offset ${offset}:`, error);
        break;
      }
    }
    
    // Final count
    const afterResult = await query('SELECT COUNT(*) as count FROM airports', []);
    const afterCount = parseInt(afterResult.rows[0].count);
    
    logger.info(`Airport synchronization completed!`);
    logger.info(`Before: ${beforeCount} airports`);
    logger.info(`After: ${afterCount} airports`);
    logger.info(`Total synced: ${afterCount - beforeCount} airports`);
    
    // Show some sample airports
    const sampleResult = await query(
      'SELECT iata_code, name, city, country FROM airports ORDER BY name LIMIT 10',
      []
    );
    
    logger.info('Sample airports:');
    sampleResult.rows.forEach((airport: any) => {
      logger.info(`  ${airport.iata_code} - ${airport.name}, ${airport.city}, ${airport.country}`);
    });
    
  } catch (error) {
    logger.error('Airport synchronization failed:', error);
    process.exit(1);
  }
}

async function testFlightData() {
  try {
    logger.info('Testing flight data retrieval...');
    
    // Test with some common flights
    const testFlights = ['BA175', 'AA101', 'EK205', 'LH401', 'AF83'];
    
    for (const flightNumber of testFlights) {
      try {
        logger.info(`Testing flight: ${flightNumber}`);
        const flightInfo = await aviationStackService.getFlightInfo(flightNumber);
        
        if (flightInfo) {
          logger.info(`  ✓ Found data for ${flightNumber}: ${flightInfo.airline.name} - ${flightInfo.flight_status}`);
        } else {
          logger.info(`  ✗ No data found for ${flightNumber}`);
        }
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        logger.error(`  ✗ Error testing ${flightNumber}:`, error);
      }
    }
    
  } catch (error) {
    logger.error('Flight data test failed:', error);
  }
}

async function searchAirports() {
  try {
    logger.info('Testing airport search...');
    
    const searchTerms = ['London', 'New York', 'Dubai', 'Tokyo', 'Paris'];
    
    for (const term of searchTerms) {
      try {
        logger.info(`Searching for: ${term}`);
        const airports = await aviationStackService.searchAirports(term, 5);
        
        if (airports.length > 0) {
          logger.info(`  Found ${airports.length} airports:`);
          airports.forEach((airport: any) => {
            logger.info(`    ${airport.iata_code} - ${airport.airport_name}, ${airport.country_name}`);
          });
        } else {
          logger.info(`  No airports found for ${term}`);
        }
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        logger.error(`  Error searching for ${term}:`, error);
      }
    }
    
  } catch (error) {
    logger.error('Airport search test failed:', error);
  }
}

async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'sync':
      await syncAirports();
      break;
    case 'test-flights':
      await testFlightData();
      break;
    case 'search':
      await searchAirports();
      break;
    case 'all':
      await syncAirports();
      await testFlightData();
      await searchAirports();
      break;
    default:
      logger.info('Usage: npm run sync-airports [command]');
      logger.info('Commands:');
      logger.info('  sync        - Sync airports from AviationStack API');
      logger.info('  test-flights - Test flight data retrieval');
      logger.info('  search      - Test airport search functionality');
      logger.info('  all         - Run all commands');
      process.exit(1);
  }
  
  process.exit(0);
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  logger.error('Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

// Run the script
main().catch((error) => {
  logger.error('Script failed:', error);
  process.exit(1);
});
