{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.108Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.108Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.108Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.108Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.109Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.109Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.109Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.109Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.109Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.111Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.112Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.112Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.116Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.117Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.118Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.118Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.119Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.120Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.121Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.122Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.123Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.124Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.124Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.124Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.124Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.125Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.126Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.127Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.128Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.128Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.128Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.128Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.128Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.129Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.129Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.129Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.130Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.130Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.130Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.130Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.131Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:11:49 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:11:49.131Z"}
{"level":"info","message":"Razorpay order created: order_QrHHCwJliCjQ6H, amount: 415000 paise (₹4150)","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:00.743Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:00 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 520 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:00.745Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.079Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.080Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.080Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.081Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.082Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.083Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.083Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.084Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.084Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.085Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.085Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.085Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.085Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.086Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.086Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.086Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.087Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.087Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.087Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.088Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.088Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.088Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.088Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.089Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.090Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.090Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.090Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.090Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.091Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.091Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.091Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.092Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.092Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.092Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.093Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:19 +0000] \"POST /api/customer/v1/availability/check HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:19.093Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:24 +0000] \"POST /api/customer/v1/payments/verify-payment HTTP/1.1\" 200 58 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:24.286Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:24 +0000] \"POST /api/customer/v1/bookings HTTP/1.1\" 400 81 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:24.493Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:24 +0000] \"POST /api/customer/v1/bookings HTTP/1.1\" 400 81 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:24.495Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:12:24 +0000] \"POST /api/customer/v1/bookings HTTP/1.1\" 400 81 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:12:24.550Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.804Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.808Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.809Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:48 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.809Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.812Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.813Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.813Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:48 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:48.813Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.209Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/customer/v1/bookings/reference/AC31544289 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.209Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.212Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/customer/v1/bookings/reference/AC30711793 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.213Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.305Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.312Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.314Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.315Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.704Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.705Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/customer/v1/bookings/reference/AC30711793 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.706Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.706Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.707Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.707Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.718Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:20:49 +0000] \"GET /api/customer/v1/bookings/reference/AC31544289 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:20:49.718Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.589Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.593Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.593Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:49:33 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.594Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.596Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.597Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.597Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:49:33 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:33.598Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:40.146Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:49:40 +0000] \"GET /api/customer/v1/bookings/reference/AC30711793 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:49:40.147Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:50:00.225Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:50:00 +0000] \"GET /api/customer/v1/bookings/reference/AC30711793 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:50:00.225Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:50:00.243Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:50:00 +0000] \"GET /api/customer/v1/bookings/reference/AC30711793 HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:50:00.243Z"}
{"level":"info","message":"Executed query in 12ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.403Z"}
{"level":"info","message":"Executed query in 22ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.425Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.432Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:51:00 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.433Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.443Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.444Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.445Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:51:00 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:00.445Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.309Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.310Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:51:02 +0000] \"GET /api/customer/v1/bookings/reference/PAR2627966RT HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.311Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.323Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.323Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:51:02 +0000] \"GET /api/customer/v1/bookings/reference/PAR2627966RT HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:51:02.324Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.718Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.720Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:55:59 +0000] \"GET /api/customer/v1/bookings/reference/PAR2627966RT HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.720Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.731Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.732Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:07:55:59 +0000] \"GET /api/customer/v1/bookings/reference/PAR2627966RT HTTP/1.1\" 200 - \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T07:55:59.732Z"}
{"level":"info","message":"Executed query in 10ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.095Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.099Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.099Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:05 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.100Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.102Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.104Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.104Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:05 +0000] \"GET /api/customer/v1/airports/available?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:05.105Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:12.646Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:12.648Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:12.648Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:12 +0000] \"GET /api/customer/v1/airports/available HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:12.649Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.719Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.723Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.724Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:14 +0000] \"GET /api/customer/v1/airports/BOM/terminals?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.725Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.727Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.729Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.729Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:14 +0000] \"GET /api/customer/v1/airports/BOM/terminals?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:14.730Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:16.949Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:16 +0000] \"GET /api/customer/v1/airports/BOM/terminals/T1/services?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:16.950Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:16.954Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:09:57:16 +0000] \"GET /api/customer/v1/airports/BOM/terminals/T1/services?type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T09:57:16.955Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.255Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.258Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.258Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:01:11 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.259Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.260Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.261Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.261Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:01:11 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:01:11.261Z"}
{"level":"info","message":"Executed query in 15ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.264Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.266Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.266Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:02:00 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.267Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.268Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.269Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.269Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:02:00 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:02:00.269Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.155Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.155Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.156Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:03:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.156Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.158Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.158Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.158Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:03:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.158Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.303Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.303Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.304Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:03:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.304Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.308Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.308Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.308Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:03:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:03:01.308Z"}
{"level":"info","message":"Executed query in 18ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.480Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.490Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.490Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:07:26 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.491Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.496Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.497Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.497Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:07:26 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:07:26.499Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.940Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.944Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.944Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:08:44 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.945Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.947Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.948Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.948Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:10:08:44 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T10:08:44.949Z"}
{"level":"info","message":"Executed query in 13ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:12.417Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:12.421Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:12.427Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:12.429Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:12 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:12.431Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.444Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.445Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.447Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.448Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:15 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=departure&passengers=1 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.450Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.982Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.990Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.992Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.993Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:15 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:15.994Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.601Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.602Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.602Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:32 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.602Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.604Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.605Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.605Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:32 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:32.605Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.248Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.249Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.249Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:38 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.250Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.254Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.255Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.256Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:38 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:38.256Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:43.056Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:43.057Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:43.058Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:43.059Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:43 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:43.060Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:44.150Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:44.151Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:44.151Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:44.152Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:44 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=departure&passengers=1 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:44.153Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:45.457Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:45.458Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:45.460Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:45.461Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:45 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:45.462Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:46.879Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:46.880Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:46.881Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:46.882Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:46 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:46.883Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:47.940Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:47.941Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:47.942Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:47.943Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:06:47 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:06:47.944Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.489Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.489Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.490Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:07:09 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.490Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.491Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.492Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.492Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:12:07:09 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T12:07:09.492Z"}
{"level":"info","message":"Executed query in 10ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.232Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.235Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.235Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:28 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.236Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.238Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.239Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.239Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:28 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:28.239Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:29.601Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:29.603Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:29.605Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:29.606Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:29 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:29.608Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:44.041Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:44.042Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:44.043Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:44.044Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:44 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=2 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:44.045Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.052Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.053Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.054Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.055Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:47 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=3 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.056Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.783Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.784Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.785Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.785Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:47 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=4 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:47.786Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:49.094Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:49.095Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:49.096Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:49.097Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:11:49 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=3 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:11:49.098Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.382Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.382Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.382Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:10 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.383Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.385Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.385Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.385Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:10 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:10.385Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:15.774Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:15.776Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:15.777Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:15.778Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:15 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:15.780Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:17.574Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:17.575Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:17.577Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:17.578Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:17 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=departure&passengers=1 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:17.580Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.047Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.048Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.050Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.050Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:20 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=departure&passengers=2 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.051Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.896Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.897Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.898Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.899Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:20 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:20.900Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:21.681Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:21.682Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:21.683Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:21.684Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:21 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:21.685Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:22.567Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:22.568Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:22.569Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:22.570Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:22 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=2 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:22.571Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:23.450Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:23.451Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:23.451Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:23.452Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:12:23 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:12:23.452Z"}
{"level":"info","message":"Executed query in 17ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.039Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.045Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.045Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:37:40 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.047Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.048Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.049Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.049Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:37:40 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:40.049Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:44.447Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:44.455Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:44.458Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:44.459Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:37:44 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:44.460Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:46.998Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.000Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.001Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.002Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:37:47 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=departure&passengers=1 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.003Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.957Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.958Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.959Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.960Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:14:37:47 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T14:37:47.961Z"}
{"level":"info","message":"Executed query in 14ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.040Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.044Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.045Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:09:52 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.045Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.048Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.049Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.049Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:09:52 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:52.050Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.063Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.063Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.063Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:09:57 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.064Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.065Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.065Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.066Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:09:57 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:57.066Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:58.957Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:58.960Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:58.966Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:58.968Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:09:58 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:09:58.968Z"}
{"level":"info","message":"Razorpay order created: order_QrPQPyk2i0QXBu, amount: 99900 paise (₹999)","service":"airconciergepro-api","timestamp":"2025-07-10T15:10:16.748Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:10:16 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 498 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:10:16.749Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:12:36 +0000] \"POST /api/customer/v1/payments/verify-payment HTTP/1.1\" 200 58 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:12:36.726Z"}
{"level":"info","message":"Executed query in 23ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:12:36.974Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:12:36 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:12:36.975Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-10T15:12:36.992Z"}
{"level":"info","message":"::1 - - [10/Jul/2025:15:12:36 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-10T15:12:36.993Z"}
