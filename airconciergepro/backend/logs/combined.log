{"level":"info","message":"Email transporter initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.433Z"}
{"level":"info","message":"Razorpay Key ID: Present","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.589Z"}
{"level":"info","message":"Razorpay Key Secret: Present","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.590Z"}
{"level":"info","message":"<PERSON><PERSON>pay initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.595Z"}
{"level":"info","message":"Razorpay Key ID: Present","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.603Z"}
{"level":"info","message":"Razorpay Key Secret: Present","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.603Z"}
{"level":"info","message":"Razorpay initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.603Z"}
{"level":"info","message":"WebSocket service initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.799Z"}
{"level":"info","message":"🚀 AirConcierge Pro API Server started","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"🌐 Server running on 0.0.0.0:8000","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"📝 Environment: development","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"🔗 API Documentation: http://0.0.0.0:8000/api","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"❤️  Health Check: http://0.0.0.0:8000/health","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"🔧 Initializing services...","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.801Z"}
{"level":"info","message":"Initializing Flight Information Service...","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.802Z"}
{"level":"warn","message":"Flight API key not configured, using mock data","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.802Z"}
{"level":"info","message":"Flight Information Service initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.802Z"}
{"level":"info","message":"✅ Flight Information Service initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.802Z"}
{"level":"info","message":"Initializing Airport Management Service...","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.802Z"}
{"level":"info","message":"Connected to Redis","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.804Z"}
{"level":"info","message":"Executed query in 24ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"Airport Management Service initialized with 13 airports","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"✅ Airport Management Service initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"Initializing Notification Service...","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"Email transporter initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"Notification Service initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"✅ Notification Service initialized","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"✅ Database connection verified","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"🎉 All services initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-11T10:10:35.826Z"}
{"level":"info","message":"Executed query in 13ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.822Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.826Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.826Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:30 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.827Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.833Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.834Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.834Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:30 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:30.834Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:36.151Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:36.156Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:36.158Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:36.161Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:36 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:36.162Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:38.839Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:38.840Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:38.841Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:38.841Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:38 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:38.841Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:39.662Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:39.664Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:39.664Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:39.665Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:39 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:39.667Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:40.562Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:40.564Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:40.565Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:40.566Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:40 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:40.567Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:41.158Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:41.159Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:41.161Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:41.162Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:11:41 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:11:41.164Z"}
{"level":"info","message":"Razorpay order created: order_Qrisa16QBcH3cA, amount: 99900 paise (₹999)","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:07.169Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:07 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 497 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:07.171Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:36 +0000] \"POST /api/customer/v1/payments/verify-payment HTTP/1.1\" 200 58 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:36.256Z"}
{"level":"info","message":"Executed query in 16ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:36.861Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:36 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:36.862Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:36.870Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:36 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:36.870Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:49.160Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:49 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:49.161Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:49.171Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:12:49 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:12:49.172Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/bookings HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.446Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.447Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.447Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.449Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.450Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.451Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.453Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/bookings HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.458Z"}
{"level":"info","message":"Executed query in 13ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.462Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.462Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.464Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.465Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.467Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.467Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.861Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.862Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.865Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.866Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.939Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.941Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.941Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.943Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.943Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.944Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.944Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.945Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.945Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.946Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.948Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 200 321 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.949Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.950Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.951Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.954Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:18 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:18.954Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.391Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.393Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.393Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.396Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:20 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.396Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.398Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.398Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.399Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.400Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:20 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:20.401Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.680Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.681Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.681Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.682Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:34 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.684Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.688Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.688Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.688Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.689Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:34 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:34.689Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:43.530Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:43.530Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:43.531Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:43.531Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:43 +0000] \"GET /api/v1/services HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:43.531Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.939Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.939Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.939Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.940Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.940Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.940Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.942Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:47 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 688 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.942Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.944Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.944Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.944Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.945Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.945Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:47 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.945Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.947Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:47 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.948Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.969Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.970Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.971Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.972Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:13:47 +0000] \"POST /api/v1/terminals/airports/batch HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:13:47.972Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.592Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.595Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.605Z"}
{"level":"info","message":"Executed query in 41ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.647Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:11 +0000] \"PUT /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 608 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.649Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.655Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.655Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.657Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.659Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:11 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 698 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.660Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.700Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.701Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.701Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.702Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:11 +0000] \"GET /api/v1/services HTTP/1.1\" 200 702 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:11.703Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.086Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.088Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.088Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:21 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.089Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.091Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.091Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.091Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:21 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:21.091Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:23.546Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:23.549Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:23.552Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:23.554Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:23 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:23.555Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.259Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.260Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.261Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.262Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:34 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=2 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.264Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.479Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.480Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.481Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.481Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:34 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=3 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.482Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.679Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.681Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.682Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.683Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:34 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=4 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.684Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.896Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.897Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.898Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.899Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:34 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=5 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:34.900Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.097Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.098Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.099Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.100Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:35 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=6 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.101Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.291Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.292Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.293Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.293Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:15:35 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=7 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:15:35.294Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.908Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.909Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.910Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.920Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.924Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:16:02 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:02.925Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:12.561Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:12.561Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:12.564Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:12.565Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:16:12 +0000] \"GET /api/v1/terminals/airport/58e1deff-a38b-4492-be5a-83eb57524faf HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:12.566Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.807Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.808Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.810Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.816Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.818Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:16:24 +0000] \"GET /api/v1/airports/global?search=DC&status=active HTTP/1.1\" 200 118 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:24.819Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.950Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.950Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.952Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.953Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.954Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:16:26 +0000] \"GET /api/v1/airports/global?search=DU&status=active HTTP/1.1\" 200 549 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:26.955Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.105Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.107Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.111Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.113Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:16:27 +0000] \"GET /api/v1/airports/global?search=DUB&status=active HTTP/1.1\" 200 549 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:16:27.114Z"}
{"level":"info","message":"Executed query in 12ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.638Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.639Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.640Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:17:47 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.640Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.641Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.642Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.642Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:17:47 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:47.642Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:49.154Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:49.155Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:49.157Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:49.159Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:17:49 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 294 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:17:49.160Z"}
{"level":"info","message":"Razorpay order created: order_Qriz5rolDBpVv9, amount: 99900 paise (₹999)","service":"airconciergepro-api","timestamp":"2025-07-11T10:18:17.108Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:18:17 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 489 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:18:17.109Z"}
{"level":"info","message":"Razorpay order created: order_QrizWD3qLRHGF0, amount: 99900 paise (₹999)","service":"airconciergepro-api","timestamp":"2025-07-11T10:18:41.204Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:18:41 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 489 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:18:41.207Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:19:17 +0000] \"POST /api/customer/v1/payments/verify-payment HTTP/1.1\" 200 58 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:19:17.422Z"}
{"level":"info","message":"Executed query in 22ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:19:17.640Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:19:17 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:19:17.640Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:19:17.651Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:19:17 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:19:17.651Z"}
{"level":"info","message":"Executed query in 32ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.589Z"}
{"level":"info","message":"Executed query in 30ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.589Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.593Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.595Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.595Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.596Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.603Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.604Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.606Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:14 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.609Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.612Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:14 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:14.614Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.016Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.016Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.017Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.019Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:18 +0000] \"GET /api/v1/bookings HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.019Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.021Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.021Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.021Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.023Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:18 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:18.023Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:46.049Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:46 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 401 31 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:46.049Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:46.058Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:46 +0000] \"GET /api/partner/v1/bookings?page=1&limit=20 HTTP/1.1\" 401 31 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:46.058Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:47.797Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:47 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:47.798Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:47.810Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:47 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:47.810Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.649Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.653Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.653Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:49 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.653Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.654Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.655Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.655Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:20:49 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:20:49.655Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.065Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.066Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.070Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.071Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:21:06 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 200 755 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.072Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.074Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.074Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.075Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.076Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:21:06 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:21:06.077Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:25:29 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:25:29.420Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:25:29 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:25:29.427Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:10:25:29 +0000] \"GET /api/v1/auth/me HTTP/1.1\" - - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T10:25:29.434Z"}
{"level":"info","message":"Executed query in 15ms","service":"airconciergepro-api","timestamp":"2025-07-11T10:25:29.438Z"}
{"level":"info","message":"Executed query in 14ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:53.382Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:53.385Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:20:53 +0000] \"GET /api/v1/tenants/settings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:53.386Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.554Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.554Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.557Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.557Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.558Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.559Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.560Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:20:58 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.560Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.560Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.563Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.565Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:20:58 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:20:58.566Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.330Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.331Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.332Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.333Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:01 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.334Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.337Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.337Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.338Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.339Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:01 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:01.339Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.254Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.254Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.254Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.255Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:06 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.255Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.256Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.257Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.257Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.257Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:06 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:06.257Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:11.031Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:11.032Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:11.032Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:11.032Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:11 +0000] \"GET /api/v1/services HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:11.033Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.781Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.781Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.781Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.782Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.782Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.782Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.783Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:12 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.783Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.784Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.785Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.785Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.785Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.786Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:12 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.786Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.786Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:12 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.786Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.791Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.792Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.792Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.794Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:21:12 +0000] \"POST /api/v1/terminals/airports/batch HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:21:12.795Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.489Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.490Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.493Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.500Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:12 +0000] \"PUT /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 602 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.501Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.505Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.505Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.506Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.508Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:12 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 692 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.508Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.541Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.542Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.542Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.543Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:12 +0000] \"GET /api/v1/services HTTP/1.1\" 200 696 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:12.543Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.348Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.348Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.349Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.350Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.352Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:14 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:14.353Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:23.108Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:23.110Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:23.112Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:23.113Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:23 +0000] \"GET /api/v1/terminals/airport/11111111-1111-1111-1111-111111111111 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:23.114Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.447Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.447Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.450Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.450Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:32 +0000] \"GET /api/v1/agents?queryKey[]=agents&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.451Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.452Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.453Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.454Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.454Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:32 +0000] \"GET /api/v1/agents?queryKey[]=agents&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:32.455Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.807Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.808Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.808Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.810Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.812Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:34 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:34.812Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.615Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.615Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.615Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.616Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:38 +0000] \"GET /api/v1/bookings HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.617Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.618Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.619Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.619Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.621Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:38 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:38.621Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.212Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.213Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.219Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.219Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:54 +0000] \"GET /api/v1/payments?queryKey[]=payments&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.219Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.220Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.221Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.222Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.222Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:22:54 +0000] \"GET /api/v1/payments?queryKey[]=payments&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:22:54.222Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.224Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.225Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.227Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.227Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:23:00 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.227Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.229Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.229Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.230Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.230Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:11:23:00 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T11:23:00.230Z"}
{"level":"info","message":"Executed query in 10ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.027Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.032Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.032Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:37 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.033Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.036Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.037Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.037Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:37 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:37.037Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:40.833Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:40.837Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:40.839Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:40.842Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:40 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 288 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:40.843Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:41.510Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:41.511Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:41.514Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:41.515Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:41 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 288 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:41.516Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.139Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.140Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.141Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.142Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:42 +0000] \"GET /api/customer/v1/airports/CCJ/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 288 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.143Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.568Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.570Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.571Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.572Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:42 +0000] \"GET /api/customer/v1/airports/DEL/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 288 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:42.572Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.268Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.270Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.271Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.272Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:43 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.273Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.766Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.767Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.768Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.769Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:43 +0000] \"GET /api/customer/v1/airports/TRV/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 288 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:43.770Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.043Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.044Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.045Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.046Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:46 +0000] \"GET /api/customer/v1/airports/TRV/services?service_type=departure&passengers=1 HTTP/1.1\" 200 26 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.047Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.742Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.743Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.744Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.746Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:04:46 +0000] \"GET /api/customer/v1/airports/TRV/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:04:46.747Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.845Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.846Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.848Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 314 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.849Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.851Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.851Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.853Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.853Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.855Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:01 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:01.855Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.331Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.332Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.334Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.335Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.410Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.410Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.412Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.412Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.416Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.416Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.417Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.418Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.418Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.421Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.421Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.422Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.422Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.423Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.423Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:02 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:02.423Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:04.080Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:04.081Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:04.082Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:04.083Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:04 +0000] \"GET /api/v1/services HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:04.083Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.531Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.531Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.531Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.532Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.532Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.532Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.533Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:05 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.533Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.533Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.534Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.534Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.535Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.535Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:05 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.535Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.535Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:05 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.535Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.550Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.550Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.551Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.552Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:05:05 +0000] \"POST /api/v1/terminals/airports/batch HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:05:05.553Z"}
{"level":"info","message":"Executed query in 10ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.504Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.506Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.508Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.516Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:09:08 +0000] \"PUT /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.519Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.523Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.523Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.524Z"}
{"level":"info","message":"Executed query in 6ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.531Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:09:08 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.532Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.566Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.566Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.567Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.568Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:09:08 +0000] \"GET /api/v1/services HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:09:08.569Z"}
{"level":"info","message":"Executed query in 15ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.721Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.723Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.729Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:47 +0000] \"POST /api/v1/services HTTP/1.1\" 201 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.731Z"}
{"level":"info","message":"Executed query in 11ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.787Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.788Z"}
{"level":"info","message":"Executed query in 24ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.812Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.814Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:47 +0000] \"GET /api/v1/services HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:47.814Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.844Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.845Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.846Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.846Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.850Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.851Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.856Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.857Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.858Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:55 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:55.858Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.199Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.200Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.202Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.203Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.282Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.282Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.284Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.284Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.288Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.289Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.289Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.292Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.293Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.295Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.295Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.295Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.296Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.296Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.296Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:10:56 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 200 321 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:10:56.297Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.956Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.960Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.960Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:04 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.960Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.962Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.962Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.962Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:04 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:04.963Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:06.942Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:06.945Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:06.946Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:06.948Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:06 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:06.949Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.498Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.504Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.506Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.506Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.507Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.507Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.507Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.507Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.508Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.508Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.509Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.510Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:25 +0000] \"POST /api/v1/terminals/airports/batch HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.510Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.511Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:25 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.511Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:25 +0000] \"GET /api/v1/services/7d7a5432-ed20-49a2-a06f-e30522811560 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.512Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.513Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.514Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.514Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.515Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:25 +0000] \"GET /api/v1/services/7d7a5432-ed20-49a2-a06f-e30522811560 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:25.515Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.281Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.281Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.284Z"}
{"level":"info","message":"Executed query in 11ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.296Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:34 +0000] \"PUT /api/v1/services/7d7a5432-ed20-49a2-a06f-e30522811560 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.298Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.302Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.303Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.304Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.304Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:34 +0000] \"GET /api/v1/services/7d7a5432-ed20-49a2-a06f-e30522811560 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.305Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.345Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.346Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.347Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.347Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:34 +0000] \"GET /api/v1/services HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:34.348Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.023Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.024Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.024Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.025Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:39 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.025Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.027Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.027Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.027Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.028Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:39 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:39.028Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.426Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.427Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.427Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.429Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:51 +0000] \"PUT /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.429Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.434Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.434Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.435Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.436Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:51 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.436Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.453Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.454Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.455Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.455Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:51 +0000] \"GET /api/v1/services HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:51.457Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.437Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.437Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/services HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.438Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.441Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.441Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.444Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.444Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.446Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.447Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.828Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.830Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.832Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.833Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.949Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.949Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.950Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.951Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.952Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.953Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.953Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.954Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.954Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.954Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.954Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.955Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.955Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.955Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.956Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:11:54 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:11:54.957Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.152Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.154Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.154Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:12:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.154Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.155Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.155Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.156Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:12:01 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:01.156Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:03.838Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:03.839Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:03.841Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:03.843Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:12:03 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:12:03.846Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:07.235Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:07.237Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:07.237Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:07.239Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:13:07 +0000] \"GET /api/customer/v1/airports/BOM/services?service_type=arrival&passengers=2 HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:07.240Z"}
{"level":"info","message":"Razorpay order created: order_QrkwlyQV1jmlg2, amount: 199798 paise (₹1997.98)","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:28.543Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:13:28 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 511 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:28.546Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:13:55 +0000] \"POST /api/customer/v1/payments/verify-payment HTTP/1.1\" 200 58 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:55.558Z"}
{"level":"info","message":"Executed query in 17ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:55.805Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:13:55 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:55.806Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:55.816Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:13:55 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:13:55.816Z"}
{"level":"info","message":"Executed query in 24ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.214Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.217Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.217Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:16:41 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.220Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.222Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.223Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.223Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:16:41 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.223Z"}
{"level":"info","message":"Executed query in 13ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.320Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:16:41 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.322Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.364Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:16:41 +0000] \"GET /api/customer/v1/bookings/reference/[object%20Object] HTTP/1.1\" 404 45 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:16:41.364Z"}
{"level":"info","message":"Executed query in 14ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.460Z"}
{"level":"info","message":"Executed query in 7ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.468Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.468Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:23:29 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.470Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.471Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.472Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.472Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:23:29 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:23:29.472Z"}
{"level":"info","message":"Executed query in 11ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.952Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.957Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.957Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:06 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.958Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.960Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.960Z"}
{"level":"info","message":"Available airports query returned 5 rows","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.960Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:06 +0000] \"GET /api/customer/v1/airports/available? HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:06.961Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:13.305Z"}
{"level":"info","message":"Executed query in 3ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:13.308Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:13.310Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:13.313Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:13 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 200 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:13.315Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:16.232Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:16.233Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:16.234Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:16.236Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:16 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:16.237Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:17.391Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:17.392Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:17.393Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:17.394Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:17 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:17.396Z"}
{"level":"info","message":"Razorpay order created: order_QrlX95kgVe4a2E, amount: 99899 paise (₹998.99)","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:54.437Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:47:54 +0000] \"POST /api/customer/v1/payments/create-payment-intent HTTP/1.1\" 200 495 \"-\" \"node\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:47:54.439Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.340Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.341Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.345Z"}
{"level":"info","message":"Executed query in 4ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.349Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:03 +0000] \"GET /api/v1/bookings HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.350Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.351Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.351Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.352Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.353Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:03 +0000] \"GET /api/v1/bookings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:03.354Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.084Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.084Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.085Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.085Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:06 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.086Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.089Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:06 +0000] \"GET /api/v1/customers?queryKey[]=customers&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:06.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:07.920Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:07.921Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:07.922Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:07.923Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:07 +0000] \"GET /api/v1/services HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:07.923Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.036Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.037Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.038Z"}
{"level":"info","message":"Executed query in 5ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.040Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.041Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.042Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.050Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:10 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.050Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.051Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.051Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.052Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.052Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:10 +0000] \"GET /api/v1/services/ba1fa529-1188-4538-878c-67753b26ad85 HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.052Z"}
{"level":"info","message":"Executed query in 16ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.055Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.057Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:10 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.057Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.068Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.068Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.069Z"}
{"level":"info","message":"Executed query in 15ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.084Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:10 +0000] \"POST /api/v1/terminals/airports/batch HTTP/1.1\" 200 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:10.084Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.459Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.460Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.461Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.461Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.462Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:16 +0000] \"GET /api/v1/airports/tenant HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:16.463Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:19.004Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:19.005Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:19.007Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:19.008Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:19 +0000] \"GET /api/v1/terminals/airport/58e1deff-a38b-4492-be5a-83eb57524faf HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:19.008Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.739Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.739Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.741Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.742Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:25 +0000] \"GET /api/v1/agents?queryKey[]=agents&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.742Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.744Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.744Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.745Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.745Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:25 +0000] \"GET /api/v1/agents?queryKey[]=agents&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:25.746Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.675Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.675Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.676Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.677Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.678Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:26 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:26.678Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.089Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.089Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.091Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.091Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:29 +0000] \"GET /api/v1/payments?queryKey[]=payments&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.092Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.093Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.093Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.094Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.094Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:29 +0000] \"GET /api/v1/payments?queryKey[]=payments&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:29.095Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.016Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.017Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.019Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.020Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:30 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.020Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.022Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.022Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.023Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.024Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:30 +0000] \"GET /api/v1/partners?queryKey[]=partners&signal=[object+AbortSignal] HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:30.024Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:34.665Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:34.666Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:34 +0000] \"GET /api/v1/tenants/settings HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:34.666Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.941Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.941Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.942Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.943Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.943Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.944Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.944Z"}
{"level":"info","message":"Executed query in 0ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.945Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.945Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:35 +0000] \"GET /api/v1/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.945Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.946Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:48:35 +0000] \"GET /api/v1/tenants/stats HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:48:35.946Z"}
{"level":"info","message":"Executed query in 8ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:50.336Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:50.338Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:50.340Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:50.342Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:50:50 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:50.343Z"}
{"level":"info","message":"Executed query in 2ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:52.860Z"}
{"level":"info","message":"Executed query in 9ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:52.869Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:52.871Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:52.872Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:50:52 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=departure&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:52.873Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:53.532Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:53.534Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:53.536Z"}
{"level":"info","message":"Executed query in 1ms","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:53.537Z"}
{"level":"info","message":"::1 - - [11/Jul/2025:12:50:53 +0000] \"GET /api/customer/v1/airports/COK/services?service_type=arrival&passengers=1 HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"airconciergepro-api","timestamp":"2025-07-11T12:50:53.539Z"}
{"level":"info","message":"Email transporter initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:52.465Z"}
{"level":"info","message":"Razorpay Key ID: Present","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.556Z"}
{"level":"info","message":"Razorpay Key Secret: Present","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.558Z"}
{"level":"info","message":"Razorpay initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.589Z"}
{"level":"info","message":"Razorpay Key ID: Present","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.638Z"}
{"level":"info","message":"Razorpay Key Secret: Present","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.639Z"}
{"level":"info","message":"Razorpay initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:53.642Z"}
{"level":"info","message":"WebSocket service initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.189Z"}
{"level":"info","message":"🚀 AirConcierge Pro API Server started","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.202Z"}
{"level":"info","message":"🌐 Server running on 0.0.0.0:8000","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.204Z"}
{"level":"info","message":"📝 Environment: development","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.204Z"}
{"level":"info","message":"🔗 API Documentation: http://0.0.0.0:8000/api","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.204Z"}
{"level":"info","message":"❤️  Health Check: http://0.0.0.0:8000/health","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.205Z"}
{"level":"info","message":"🔧 Initializing services...","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.205Z"}
{"level":"info","message":"Initializing Flight Information Service...","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.205Z"}
{"level":"warn","message":"Flight API key not configured, using mock data","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.205Z"}
{"level":"info","message":"Flight Information Service initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.205Z"}
{"level":"info","message":"✅ Flight Information Service initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.206Z"}
{"level":"info","message":"Initializing Airport Management Service...","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.206Z"}
{"level":"info","message":"Connected to Redis","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.226Z"}
{"level":"info","message":"Executed query in 190ms","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.396Z"}
{"level":"info","message":"Airport Management Service initialized with 13 airports","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.396Z"}
{"level":"info","message":"✅ Airport Management Service initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.397Z"}
{"level":"info","message":"Initializing Notification Service...","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.397Z"}
{"level":"info","message":"Email transporter initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.397Z"}
{"level":"info","message":"Notification Service initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.398Z"}
{"level":"info","message":"✅ Notification Service initialized","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.398Z"}
{"level":"info","message":"✅ Database connection verified","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.398Z"}
{"level":"info","message":"🎉 All services initialized successfully","service":"airconciergepro-api","timestamp":"2025-07-12T10:08:55.398Z"}
