import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { query } from '../services/database';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendInternalServerError,
  calculatePagination,
  parsePaginationParams
} from '../utils/response';

const router = express.Router();

// Validation schemas
const createCustomerSchema = Joi.object({
  email: Joi.string().email().required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  phone: Joi.string().required(),
  dateOfBirth: Joi.date().iso(),
  preferences: Joi.object({
    language: Joi.string(),
    specialRequirements: Joi.array().items(Joi.string()),
    communicationPreferences: Joi.array().items(Joi.string())
  })
});

const updateCustomerSchema = Joi.object({
  email: Joi.string().email(),
  firstName: Joi.string(),
  lastName: Joi.string(),
  phone: Joi.string(),
  dateOfBirth: Joi.date().iso(),
  preferences: Joi.object({
    language: Joi.string(),
    specialRequirements: Joi.array().items(Joi.string()),
    communicationPreferences: Joi.array().items(Joi.string())
  })
});

// Create new customer
router.post('/',
  requirePermission('create_customer'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { error, value } = createCustomerSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    // Check if customer with email already exists
    const existingCustomer = await query(
      'SELECT id FROM customers WHERE email = $1 AND tenant_id = $2',
      [value.email, req.user!.tenant_id]
    );

    if (existingCustomer.rows.length > 0) {
      return sendValidationError(res, 'Customer with this email already exists');
    }

    const customerId = uuidv4();
    const result = await query(
      `INSERT INTO customers (
        id, tenant_id, email, first_name, last_name, phone, date_of_birth,
        preferences, loyalty_points, total_bookings, total_spent, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW()) RETURNING *`,
      [
        customerId,
        req.user!.tenant_id,
        value.email,
        value.firstName,
        value.lastName,
        value.phone,
        value.dateOfBirth || null,
        JSON.stringify(value.preferences || {}),
        0, // Initial loyalty points
        0, // Initial booking count
        0  // Initial total spent
      ]
    );

    return sendSuccess(res, result.rows[0], 201);
  })
);

// Get all customers with filtering and pagination
router.get('/', asyncHandler(async (req: TenantRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;

  // Build filter conditions
  let filters = ['tenant_id = $1'];
  let params: any[] = [req.user!.tenant_id];
  let paramIndex = 2;

  if (search) {
    filters.push(`(
      LOWER(first_name) LIKE LOWER($${paramIndex}) OR 
      LOWER(last_name) LIKE LOWER($${paramIndex}) OR 
      LOWER(email) LIKE LOWER($${paramIndex})
    )`);
    params.push(`%${search}%`);
    paramIndex++;
  }

  const whereClause = `WHERE ${filters.join(' AND ')}`;

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total FROM customers ${whereClause}`,
    params
  );

  // Get customers
  const customersResult = await query(
    `SELECT * FROM customers ${whereClause}
     ORDER BY created_at DESC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
    [...params, limit, offset]
  );

  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      customers: customersResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
}));

// Get single customer
router.get('/:id', asyncHandler(async (req: TenantRequest, res, next) => {
  const customerResult = await query(
    'SELECT * FROM customers WHERE id = $1 AND tenant_id = $2',
    [req.params.id, req.user!.tenant_id]
  );

  if (customerResult.rows.length === 0) {
    res.status(404).json({ error: 'Customer not found' });
    return;
  }

  res.json({
    success: true,
    data: customerResult.rows[0]
  });
}));

// Update customer
router.put('/:id',
  requirePermission('update_customer'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { error, value } = updateCustomerSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    // Check if customer exists
    const existingCustomer = await query(
      'SELECT id FROM customers WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (existingCustomer.rows.length === 0) {
      return sendNotFoundError(res, 'Customer not found');
    }

    // Check email uniqueness if email is being updated
    if (value.email) {
      const emailCheck = await query(
        'SELECT id FROM customers WHERE email = $1 AND tenant_id = $2 AND id != $3',
        [value.email, req.user!.tenant_id, req.params.id]
      );

      if (emailCheck.rows.length > 0) {
        return sendValidationError(res, 'Email already exists for another customer');
      }
    }

    // Build update query dynamically
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    Object.entries(value).forEach(([key, val]) => {
      if (val !== undefined) {
        const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        if (dbKey === 'preferences') {
          updates.push(`${dbKey} = $${paramIndex}`);
          params.push(JSON.stringify(val));
        } else {
          updates.push(`${dbKey} = $${paramIndex}`);
          params.push(val);
        }
        paramIndex++;
      }
    });

    if (updates.length === 0) {
      return sendValidationError(res, 'No valid fields to update');
    }

    updates.push('updated_at = NOW()');
    params.push(req.params.id, req.user!.tenant_id);

    const updateQuery = `
      UPDATE customers
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
      RETURNING *
    `;

    const result = await query(updateQuery, params);

    return sendSuccess(res, result.rows[0]);
  })
);

// Get customer booking history
router.get('/:id/bookings', asyncHandler(async (req: TenantRequest, res, next) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;

  // Check if customer exists
  const customerCheck = await query(
    'SELECT id FROM customers WHERE id = $1 AND tenant_id = $2',
    [req.params.id, req.user!.tenant_id]
  );

  if (customerCheck.rows.length === 0) {
    res.status(404).json({ error: 'Customer not found' });
    return;
  }

  // Get booking count
  const countResult = await query(
    'SELECT COUNT(*) as total FROM bookings WHERE customer_id = $1',
    [req.params.id]
  );

  // Get bookings with service details
  const bookingsResult = await query(
    `SELECT b.*, s.name as service_name, s.category as service_category
     FROM bookings b
     LEFT JOIN services s ON b.service_id = s.id
     WHERE b.customer_id = $1
     ORDER BY b.created_at DESC
     LIMIT $2 OFFSET $3`,
    [req.params.id, limit, offset]
  );

  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      bookings: bookingsResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
}));

// Delete customer
router.delete('/:id',
  requirePermission('delete_customer'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    // Check if customer has any bookings
    const bookingsCheck = await query(
      'SELECT COUNT(*) as count FROM bookings WHERE customer_id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (parseInt(bookingsCheck.rows[0].count) > 0) {
      return sendValidationError(res, 'Cannot delete customer with existing bookings');
    }

    const result = await query(
      'DELETE FROM customers WHERE id = $1 AND tenant_id = $2 RETURNING id',
      [req.params.id, req.user!.tenant_id]
    );

    if (result.rows.length === 0) {
      return sendNotFoundError(res, 'Customer not found');
    }

    return sendSuccess(res, { message: 'Customer deleted successfully' });
  })
);

// Search customers
router.get('/search/:query', asyncHandler(async (req: TenantRequest, res, next) => {
  const searchQuery = req.params.query;
  const limit = parseInt(req.query.limit as string) || 10;

  const result = await query(
    `SELECT id, first_name, last_name, email, phone
     FROM customers 
     WHERE tenant_id = $1 AND (
       LOWER(first_name) LIKE LOWER($2) OR 
       LOWER(last_name) LIKE LOWER($2) OR 
       LOWER(email) LIKE LOWER($2) OR
       phone LIKE $2
     )
     ORDER BY first_name, last_name
     LIMIT $3`,
    [req.user!.tenant_id, `%${searchQuery}%`, limit]
  );

  res.json({
    success: true,
    data: result.rows
  });
}));

export default router;
