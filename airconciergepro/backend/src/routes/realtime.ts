import express from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { query } from '../services/database';
import { logger } from '../services/logger';
import { flightInformationService } from '../services/flightInformation';
import { aviationStackService } from '../services/aviationStackService';
import { 
  sendSuccess, 
  sendError, 
  sendValidationError, 
  sendNotFoundError, 
  sendInternalServerError 
} from '../utils/response';

const router = express.Router();

/**
 * @route GET /api/v1/realtime/dashboard
 * @desc Get real-time dashboard data
 * @access Private
 */
router.get('/dashboard', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const tenantId = req.user.tenant_id;

    // Get active bookings with real-time status
    const activeBookingsResult = await query(
      `SELECT b.*, 
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              s.name as service_name, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name,
              a.status as agent_status, a.current_location as agent_location
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       LEFT JOIN services s ON b.service_id = s.id
       LEFT JOIN users u ON b.assigned_agent_id = u.id
       LEFT JOIN agents a ON u.id = a.user_id
       WHERE b.tenant_id = $1 
         AND b.status IN ('confirmed', 'in_progress', 'agent_assigned')
         AND DATE(b.flight_date) = CURRENT_DATE
       ORDER BY b.flight_date ASC`,
      [tenantId]
    );

    // Get agent status summary
    const agentStatusResult = await query(
      `SELECT 
         a.status,
         COUNT(*) as count
       FROM agents a
       JOIN users u ON a.user_id = u.id
       WHERE u.tenant_id = $1 AND u.status = 'active'
       GROUP BY a.status`,
      [tenantId]
    );

    // Get today's statistics
    const todayStatsResult = await query(
      `SELECT 
         COUNT(*) as total_bookings,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
         COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_bookings,
         COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
         SUM(total_price) as total_revenue
       FROM bookings 
       WHERE tenant_id = $1 AND DATE(created_at) = CURRENT_DATE`,
      [tenantId]
    );

    // Get flight updates for today's bookings
    const flightUpdates: any[] = [];
    for (const booking of activeBookingsResult.rows) {
      if (booking.flight_number) {
        try {
          const flightInfo = await flightInformationService.getFlightInfo(
            booking.flight_number, 
            new Date(booking.flight_date)
          );
          if (flightInfo) {
            flightUpdates.push({
              bookingId: booking.id,
              flightNumber: booking.flight_number,
              status: flightInfo.status,
              gate: flightInfo.gate,
              terminal: flightInfo.terminal,
              estimatedArrival: flightInfo.estimatedArrival,
              delay: flightInfo.delayReason ? true : false
            });
          }
        } catch (error) {
          logger.error(`Error fetching flight info for ${booking.flight_number}:`, error);
        }
      }
    }

    // Get WebSocket service for agent locations
    const wsService = (global as any).websocketService;
    const agentLocations = wsService ? wsService.getAgentLocations(tenantId) : [];

    return sendSuccess(res, {
      activeBookings: activeBookingsResult.rows,
      agentStatus: agentStatusResult.rows.reduce((acc: any, row: any) => {
        acc[row.status] = parseInt(row.count);
        return acc;
      }, {}),
      todayStats: todayStatsResult.rows[0],
      flightUpdates,
      agentLocations,
      lastUpdated: new Date()
    });
  } catch (error) {
    logger.error('Error fetching dashboard data:', error);
    return sendInternalServerError(res, 'Failed to fetch dashboard data');
  }
}));

/**
 * @route GET /api/v1/realtime/agents
 * @desc Get real-time agent status and locations
 * @access Private
 */
router.get('/agents', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const tenantId = req.user.tenant_id;

    // Get agents with current status and location
    const agentsResult = await query(
      `SELECT 
         u.id, u.first_name, u.last_name, u.email,
         a.status, a.current_location, a.specializations, a.languages,
         a.rating, a.total_services,
         COUNT(b.id) as active_bookings
       FROM users u
       JOIN agents a ON u.id = a.user_id
       LEFT JOIN bookings b ON u.id = b.assigned_agent_id 
         AND b.status IN ('confirmed', 'in_progress', 'agent_assigned')
       WHERE u.tenant_id = $1 AND u.role = 'field_agent' AND u.status = 'active'
       GROUP BY u.id, u.first_name, u.last_name, u.email, a.status, 
                a.current_location, a.specializations, a.languages, a.rating, a.total_services
       ORDER BY a.status, u.first_name`,
      [tenantId]
    );

    // Get real-time locations from WebSocket service
    const wsService = (global as any).websocketService;
    const realtimeLocations = wsService ? wsService.getAgentLocations(tenantId) : [];

    // Merge database data with real-time data
    const agents = agentsResult.rows.map((agent: any) => {
      const realtimeLocation = realtimeLocations.find((loc: any) => loc.agentId === agent.id);
      return {
        ...agent,
        realtimeLocation: realtimeLocation || null,
        lastLocationUpdate: realtimeLocation?.timestamp || null
      };
    });

    return sendSuccess(res, {
      agents,
      summary: {
        total: agents.length,
        available: agents.filter((a: any) => a.status === 'available').length,
        busy: agents.filter((a: any) => a.status === 'busy').length,
        offline: agents.filter((a: any) => a.status === 'offline').length
      }
    });
  } catch (error) {
    logger.error('Error fetching agent data:', error);
    return sendInternalServerError(res, 'Failed to fetch agent data');
  }
}));

/**
 * @route GET /api/v1/realtime/flights
 * @desc Get real-time flight information for today's bookings
 * @access Private
 */
router.get('/flights', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const tenantId = req.user.tenant_id;
    const date = req.query.date ? new Date(req.query.date as string) : new Date();

    // Get unique flight numbers for the date
    const flightNumbersResult = await query(
      `SELECT DISTINCT flight_number, flight_date
       FROM bookings 
       WHERE tenant_id = $1 
         AND flight_number IS NOT NULL 
         AND DATE(flight_date) = DATE($2)`,
      [tenantId, date]
    );

    const flightUpdates: any[] = [];

    for (const row of flightNumbersResult.rows) {
      try {
        const flightInfo = await flightInformationService.getFlightInfo(
          row.flight_number, 
          new Date(row.flight_date)
        );
        
        if (flightInfo) {
          // Get bookings for this flight
          const bookingsResult = await query(
            `SELECT id, booking_reference, customer_id, assigned_agent_id, status
             FROM bookings 
             WHERE tenant_id = $1 AND flight_number = $2 AND DATE(flight_date) = DATE($3)`,
            [tenantId, row.flight_number, row.flight_date]
          );

          flightUpdates.push({
            flightNumber: row.flight_number,
            flightDate: row.flight_date,
            flightInfo,
            affectedBookings: bookingsResult.rows
          });
        }
      } catch (error) {
        logger.error(`Error fetching flight info for ${row.flight_number}:`, error);
      }
    }

    return sendSuccess(res, {
      date: date.toISOString().split('T')[0],
      flights: flightUpdates,
      lastUpdated: new Date()
    });
  } catch (error) {
    logger.error('Error fetching flight data:', error);
    return sendInternalServerError(res, 'Failed to fetch flight data');
  }
}));

/**
 * @route POST /api/v1/realtime/notifications
 * @desc Send real-time notification to specific users or groups
 * @access Private
 */
router.post('/notifications', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const tenantId = req.user.tenant_id;
    const { type, title, message, targetUsers, targetRoles, data } = req.body;

    if (!type || !title || !message) {
      return sendValidationError(res, 'Type, title, and message are required');
    }

    const wsService = (global as any).websocketService;
    if (!wsService) {
      return sendError(res, 'Real-time service not available', 503);
    }

    const notification = {
      type,
      title,
      message,
      data: data || {},
      sentBy: req.user.id,
      sentAt: new Date()
    };

    // Send to specific users
    if (targetUsers && Array.isArray(targetUsers)) {
      // Implementation would target specific user sockets
      // For now, broadcast to tenant
      wsService.broadcastSystemNotification(tenantId, notification);
    }

    // Send to specific roles
    if (targetRoles && Array.isArray(targetRoles)) {
      // Implementation would target role-specific rooms
      wsService.broadcastSystemNotification(tenantId, notification);
    }

    // If no specific targets, broadcast to entire tenant
    if (!targetUsers && !targetRoles) {
      wsService.broadcastSystemNotification(tenantId, notification);
    }

    return sendSuccess(res, {
      message: 'Notification sent successfully',
      notification
    });
  } catch (error) {
    logger.error('Error sending notification:', error);
    return sendInternalServerError(res, 'Failed to send notification');
  }
}));

/**
 * @route GET /api/v1/realtime/status
 * @desc Get real-time service status
 * @access Private
 */
router.get('/status', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const wsService = (global as any).websocketService;
    
    return sendSuccess(res, {
      websocketConnected: !!wsService,
      connectedClients: wsService ? wsService.getConnectedClientsCount() : 0,
      services: {
        flightInformation: true,
        aviationStack: true,
        notifications: true
      },
      lastUpdated: new Date()
    });
  } catch (error) {
    logger.error('Error fetching status:', error);
    return sendInternalServerError(res, 'Failed to fetch status');
  }
}));

export default router;
