import express, { Request, Response } from 'express';
import Joi from 'joi';
import { TenantRequest } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';
import { logger } from '../services/logger';
import { availabilityService } from '../services/availability';

const router = express.Router();

// Validation schemas
const checkAvailabilitySchema = Joi.object({
  serviceId: Joi.string().uuid().required(),
  date: Joi.date().iso().required(),
  timeSlot: Joi.string().optional(),
  passengerCount: Joi.number().integer().min(1).required(),
  duration: Joi.number().integer().min(15).max(480).optional(), // 15 minutes to 8 hours
  airportCode: Joi.string().length(3).optional()
});

const holdTimeSlotSchema = Joi.object({
  serviceId: Joi.string().uuid().required(),
  date: Joi.date().iso().required(),
  timeSlot: Joi.string().required(),
  passengerCount: Joi.number().integer().min(1).required(),
  customerId: Joi.string().uuid().optional()
});

const calendarSchema = Joi.object({
  serviceId: Joi.string().uuid().required(),
  month: Joi.number().integer().min(1).max(12).required(),
  year: Joi.number().integer().min(2024).max(2030).required()
});

/**
 * Check availability for a service
 * POST /api/v1/availability/check
 */
router.post('/check', 
  requirePermission('view_availability'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { error, value } = checkAvailabilitySchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const availability = await availabilityService.checkAvailability({
        tenantId: req.user!.tenant_id,
        ...value
      });

      res.json({
        success: true,
        data: availability
      });
      return;
    } catch (error) {
      logger.error('Check availability error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Get availability calendar for a month
 * GET /api/v1/availability/calendar
 */
router.get('/calendar',
  requirePermission('view_availability'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { error, value } = calendarSchema.validate(req.query);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const calendar = await availabilityService.getAvailabilityCalendar(
        req.user!.tenant_id,
        value.serviceId,
        value.month,
        value.year
      );

      res.json({
        success: true,
        data: calendar
      });
      return;
    } catch (error) {
      logger.error('Get availability calendar error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Hold a time slot temporarily
 * POST /api/v1/availability/hold
 */
router.post('/hold',
  requirePermission('create_booking'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { error, value } = holdTimeSlotSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const hold = await availabilityService.holdTimeSlot(
        req.user!.tenant_id,
        value.serviceId,
        new Date(value.date),
        value.timeSlot,
        value.passengerCount,
        value.customerId
      );

      if (hold.success) {
        res.json({
          success: true,
          data: {
            holdId: hold.holdId,
            expiresAt: hold.expiresAt
          }
        });
      } else {
        res.status(409).json({ 
          error: 'Time slot is no longer available',
          code: 'SLOT_UNAVAILABLE'
        });
      }
      return;
    } catch (error) {
      logger.error('Hold time slot error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Get capacity utilization metrics
 * GET /api/v1/availability/metrics
 */
router.get('/metrics',
  requirePermission('view_analytics'),
  async (req: TenantRequest, res: Response) => {
    try {
      const serviceId = req.query.serviceId as string;
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      if (!serviceId || !startDate || !endDate) {
        return res.status(400).json({ 
          error: 'serviceId, startDate, and endDate are required' 
        });
      }

      const metrics = await availabilityService.getCapacityMetrics(
        req.user!.tenant_id,
        serviceId,
        new Date(startDate),
        new Date(endDate)
      );

      res.json({
        success: true,
        data: metrics
      });
      return;
    } catch (error) {
      logger.error('Get capacity metrics error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Get real-time availability for dashboard
 * GET /api/v1/availability/realtime
 */
router.get('/realtime',
  requirePermission('view_operations'),
  async (req: TenantRequest, res: Response) => {
    try {
      const realTimeData = await availabilityService.getRealTimeAvailability(
        req.user!.tenant_id
      );

      res.json({
        success: true,
        data: realTimeData
      });
      return;
    } catch (error) {
      logger.error('Get real-time availability error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Get availability status for multiple services
 * POST /api/v1/availability/bulk-check
 */
router.post('/bulk-check',
  requirePermission('view_availability'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { serviceIds, date, passengerCount } = req.body;

      if (!Array.isArray(serviceIds) || !date || !passengerCount) {
        return res.status(400).json({ 
          error: 'serviceIds (array), date, and passengerCount are required' 
        });
      }

      const results = await Promise.allSettled(
        serviceIds.map(serviceId => 
          availabilityService.checkAvailability({
            tenantId: req.user!.tenant_id,
            serviceId,
            date: new Date(date),
            passengerCount
          })
        )
      );

      const availability = serviceIds.map((serviceId, index) => {
        const result = results[index];
        return {
          serviceId,
          success: result.status === 'fulfilled',
          data: result.status === 'fulfilled' ? result.value : null,
          error: result.status === 'rejected' ? result.reason.message : null
        };
      });

      res.json({
        success: true,
        data: availability
      });
      return;
    } catch (error) {
      logger.error('Bulk availability check error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Get availability summary for operations dashboard
 * GET /api/v1/availability/summary
 */
router.get('/summary',
  requirePermission('view_operations'),
  async (req: TenantRequest, res: Response) => {
    try {
      const date = req.query.date as string || new Date().toISOString().split('T')[0];
      
      // This would aggregate availability across all services for a tenant
      // For now, return real-time data
      const summary = await availabilityService.getRealTimeAvailability(
        req.user!.tenant_id
      );

      res.json({
        success: true,
        data: {
          date,
          summary
        }
      });
      return;
    } catch (error) {
      logger.error('Get availability summary error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

/**
 * Release expired holds manually (admin endpoint)
 * POST /api/v1/availability/release-holds
 */
router.post('/release-holds',
  requirePermission('manage_system'),
  async (req: TenantRequest, res: Response) => {
    try {
      const releasedCount = await availabilityService.releaseExpiredHolds();
      
      res.json({
        success: true,
        data: {
          releasedHolds: releasedCount,
          message: `Released ${releasedCount} expired holds`
        }
      });
      return;
    } catch (error) {
      logger.error('Release holds error:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  }
);

export default router;
