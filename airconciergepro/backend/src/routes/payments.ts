import express from 'express';
import Strip<PERSON> from 'stripe';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { tenantMiddleware } from '../middleware/tenant';
import { query } from '../services/database';
import { logger } from '../services/logger';
import { paymentService, PaymentProvider } from '../services/paymentService';

const router = express.Router();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-08-16',
});

/**
 * Get all payments for the tenant
 */
router.get('/', asyncHandler(async (req: AuthRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;

  const paymentsResult = await query(
    `SELECT
       p.id, p.booking_id, p.amount, p.currency, p.payment_method,
       p.payment_provider, p.status, p.created_at,
       b.booking_reference, b.flight_number, b.airline,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       s.name as service_name
     FROM payments p
     JOIN bookings b ON p.booking_id = b.id
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE p.tenant_id = $1
     ORDER BY p.created_at DESC
     LIMIT $2 OFFSET $3`,
    [req.user!.tenant_id, limit, offset]
  );

  const countResult = await query(
    'SELECT COUNT(*) as total FROM payments WHERE tenant_id = $1',
    [req.user!.tenant_id]
  );

  const totalItems = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(totalItems / limit);

  res.json({
    success: true,
    data: {
      payments: paymentsResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit
      }
    }
  });
}));

/**
 * Create a payment intent for a booking (supports multiple providers)
 */
router.post('/create-payment-intent', authMiddleware, tenantMiddleware, asyncHandler(async (req: AuthRequest, res) => {
  const { bookingId, provider = 'stripe', customer_info } = req.body;

  if (!bookingId) {
    return res.status(400).json({ error: 'Booking ID is required' });
  }

  if (!['stripe', 'razorpay'].includes(provider)) {
    return res.status(400).json({ error: 'Invalid payment provider' });
  }

  const bookingResult = await query(
    'SELECT total_price, currency, customer_id FROM bookings WHERE id = $1 AND tenant_id = $2',
    [bookingId, req.user!.tenant_id]
  );

  if (bookingResult.rows.length === 0) {
    return res.status(404).json({ error: 'Booking not found' });
  }

  const booking = bookingResult.rows[0];

  const paymentIntent = await paymentService.createPaymentIntent({
    amount: booking.total_price,
    currency: booking.currency,
    provider: provider as PaymentProvider,
    metadata: {
      booking_id: bookingId,
      tenant_id: req.user!.tenant_id,
    },
    customer_info,
  });

  // Save payment record
  await paymentService.savePaymentRecord({
    tenant_id: req.user!.tenant_id,
    booking_id: bookingId,
    payment_intent_id: paymentIntent.id,
    provider: provider as PaymentProvider,
    amount: booking.total_price,
    currency: booking.currency,
    status: paymentIntent.status,
    metadata: paymentIntent.metadata,
  });

  res.json({
    success: true,
    data: {
      payment_intent: paymentIntent,
      provider,
    },
  });
  return;
}));

/**
 * Verify payment (supports multiple providers)
 */
router.post('/verify-payment', asyncHandler(async (req: any, res) => {
  const { payment_id, provider, verification_data } = req.body;

  if (!payment_id || !provider) {
    return res.status(400).json({ error: 'Payment ID and provider are required' });
  }

  const isValid = await paymentService.verifyPayment(payment_id, provider, verification_data);

  if (isValid) {
    // Update payment status in database
    await paymentService.updatePaymentStatus(payment_id, 'succeeded');

    return res.json({
      success: true,
      message: 'Payment verified successfully',
    });
  } else {
    return res.status(400).json({
      success: false,
      error: 'Payment verification failed',
    });
  }
}));

/**
 * Stripe Webhook Handler
 */
router.post('/webhook/stripe', express.raw({ type: 'application/json' }), asyncHandler(async (req: any, res) => {
    const sig = req.headers['stripe-signature'] as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    } catch (err: any) {
        logger.error(`Stripe webhook error: ${err.message}`);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        await paymentService.updatePaymentStatus(paymentIntent.id, 'succeeded', paymentIntent.metadata);
        logger.info(`Payment succeeded: ${paymentIntent.id}`);
        break;
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        await paymentService.updatePaymentStatus(failedPayment.id, 'failed', failedPayment.metadata);
        logger.info(`Payment failed: ${failedPayment.id}`);
        break;
      default:
        logger.info(`Unhandled Stripe event type: ${event.type}`);
    }

    res.json({ received: true });
    return;
}));

/**
 * Razorpay Webhook Handler
 */
router.post('/webhook/razorpay', express.raw({ type: 'application/json' }), asyncHandler(async (req: any, res) => {
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET!;
    const crypto = require('crypto');

    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(JSON.stringify(req.body))
      .digest('hex');

    const actualSignature = req.headers['x-razorpay-signature'];

    if (expectedSignature !== actualSignature) {
      logger.error('Razorpay webhook signature verification failed');
      return res.status(400).send('Webhook signature verification failed');
    }

    const event = req.body;

    // Handle the event
    switch (event.event) {
      case 'payment.captured':
        await paymentService.updatePaymentStatus(event.payload.payment.entity.order_id, 'succeeded');
        logger.info(`Razorpay payment captured: ${event.payload.payment.entity.id}`);
        break;
      case 'payment.failed':
        await paymentService.updatePaymentStatus(event.payload.payment.entity.order_id, 'failed');
        logger.info(`Razorpay payment failed: ${event.payload.payment.entity.id}`);
        break;
      default:
        logger.info(`Unhandled Razorpay event type: ${event.event}`);
    }

    res.json({ received: true });
    return;
}));

export default router;
