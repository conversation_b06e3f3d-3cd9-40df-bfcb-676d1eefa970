import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { query } from '../services/database';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest, checkTenantLimits } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';
import { logger } from '../services/logger';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendInternalServerError,
  calculatePagination,
  parsePaginationParams
} from '../utils/response';

const router = express.Router();

// Validation schemas
const createBookingSchema = Joi.object({
  customerId: Joi.string().uuid().required(),
  serviceId: Joi.string().uuid().required(),
  flightNumber: Joi.string().required(),
  airline: Joi.string().required(),
  departureAirport: Joi.string().length(3).required(),
  arrivalAirport: Joi.string().length(3).required(),
  flightDate: Joi.date().iso().required(),
  estimatedArrival: Joi.date().iso(),
  serviceType: Joi.string().valid('arrival', 'departure', 'transit').required(),
  passengerCount: Joi.number().integer().min(1).required(),
  passengers: Joi.array().items(
    Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      age: Joi.number().integer().min(0).max(120),
      specialRequirements: Joi.array().items(Joi.string()),
      contactNumber: Joi.string()
    })
  ).required(),
  specialRequirements: Joi.array().items(Joi.string()),
  meetingPoint: Joi.string().required()
});

const updateBookingSchema = Joi.object({
  status: Joi.string().valid('confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'),
  assignedAgentId: Joi.string().uuid(),
  estimatedArrival: Joi.date().iso(),
  actualArrival: Joi.date().iso(),
  serviceStartTime: Joi.date().iso(),
  serviceEndTime: Joi.date().iso(),
  specialRequirements: Joi.array().items(Joi.string()),
  meetingPoint: Joi.string()
});

// Generate unique booking reference
const generateBookingReference = (): string => {
  const prefix = 'ACG';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `${prefix}${timestamp}${random}`;
};

// Create new booking
router.post('/', 
  checkTenantLimits('bookings'),
  requirePermission('create_booking'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { error, value } = createBookingSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const bookingId = uuidv4();
    const bookingReference = generateBookingReference();

    // Get service details for pricing
    const serviceResult = await query(
      'SELECT * FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3',
      [value.serviceId, req.user!.tenant_id, 'active']
    );

    if (serviceResult.rows.length === 0) {
      return res.status(404).json({ error: 'Service not found or inactive' });
    }

    const service = serviceResult.rows[0];
    const basePrice = service.base_price * value.passengerCount;

    // Create booking
    const bookingResult = await query(
      `INSERT INTO bookings (
        id, tenant_id, customer_id, service_id, booking_reference, status,
        flight_number, airline, departure_airport, arrival_airport, flight_date,
        estimated_arrival, service_type, passenger_count, passengers,
        special_requirements, meeting_point, base_price, total_price,
        currency, payment_status, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, NOW(), NOW()
      ) RETURNING *`,
      [
        bookingId,
        req.user!.tenant_id,
        value.customerId,
        value.serviceId,
        bookingReference,
        'confirmed',
        value.flightNumber,
        value.airline,
        value.departureAirport,
        value.arrivalAirport,
        value.flightDate,
        value.estimatedArrival,
        value.serviceType,
        value.passengerCount,
        JSON.stringify(value.passengers),
        JSON.stringify(value.specialRequirements || []),
        value.meetingPoint,
        basePrice,
        basePrice, // No additional charges for now
        service.currency,
        'pending'
      ]
    );

    // Log booking activity
    await query(
      `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, created_at)
       VALUES ($1, $2, $3, $4, $5, NOW())`,
      [
        uuidv4(),
        bookingId,
        req.user!.id,
        'created',
        `Booking created for ${value.flightNumber} - ${value.serviceType}`
      ]
    );

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`tenant_${req.user!.tenant_id}`).emit('booking_created', {
      booking: bookingResult.rows[0]
    });

    res.status(201).json({
      success: true,
      data: bookingResult.rows[0]
    });
  })
);

// Get all bookings with filtering and pagination
router.get('/', asyncHandler(async (req: TenantRequest, res) => {
  const { page, limit } = parsePaginationParams(req.query);
  const offset = (page - 1) * limit;

  // Build filter conditions
  const filters: string[] = ['b.tenant_id = $1'];
  const params: any[] = [req.user!.tenant_id];
  let paramIndex = 2;

  if (req.query.status) {
    filters.push(`b.status = $${paramIndex}`);
    params.push(req.query.status);
    paramIndex++;
  }

  if (req.query.serviceType) {
    filters.push(`b.service_type = $${paramIndex}`);
    params.push(req.query.serviceType);
    paramIndex++;
  }

  if (req.query.flightDate) {
    filters.push(`DATE(b.flight_date) = $${paramIndex}`);
    params.push(req.query.flightDate);
    paramIndex++;
  }

  if (req.query.airport) {
    filters.push(`(b.departure_airport = $${paramIndex} OR b.arrival_airport = $${paramIndex})`);
    params.push(req.query.airport);
    paramIndex++;
  }

  if (req.query.assignedAgentId) {
    filters.push(`b.assigned_agent_id = $${paramIndex}`);
    params.push(req.query.assignedAgentId);
    paramIndex++;
  }

  const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total FROM bookings b ${whereClause}`,
    params
  );

  // Get bookings with customer and service details
  const bookingsResult = await query(
    `SELECT b.*, 
            c.first_name as customer_first_name, c.last_name as customer_last_name, c.email as customer_email,
            s.name as service_name, s.category as service_category,
            u.first_name as agent_first_name, u.last_name as agent_last_name
     FROM bookings b
     LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
     LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
     LEFT JOIN users u ON b.assigned_agent_id = u.id AND u.tenant_id = b.tenant_id
     ${whereClause}
     ORDER BY b.created_at DESC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
    [...params, limit, offset]
  );

  const total = parseInt(countResult.rows[0].total);
  const pagination = calculatePagination(total, page, limit);

  return sendSuccess(res, {
    bookings: bookingsResult.rows
  }, 200, pagination);
}));

// Get single booking
router.get('/:id', asyncHandler(async (req: TenantRequest, res): Promise<any> => {
  const bookingResult = await query(
    `SELECT b.*, 
            c.first_name as customer_first_name, c.last_name as customer_last_name, 
            c.email as customer_email, c.phone as customer_phone,
            s.name as service_name, s.description as service_description, s.category as service_category,
            u.first_name as agent_first_name, u.last_name as agent_last_name, u.email as agent_email
     FROM bookings b
     LEFT JOIN customers c ON b.customer_id = c.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN users u ON b.assigned_agent_id = u.id
     WHERE b.id = $1 AND b.tenant_id = $2`,
    [req.params.id, req.user!.tenant_id]
  );

  if (bookingResult.rows.length === 0) {
    return sendNotFoundError(res, 'Booking not found');
  }

  // Get booking activities
  const activitiesResult = await query(
    `SELECT ba.*, u.first_name, u.last_name, u.role
     FROM booking_activities ba
     LEFT JOIN users u ON ba.user_id = u.id
     WHERE ba.booking_id = $1
     ORDER BY ba.created_at DESC`,
    [req.params.id]
  );

  return sendSuccess(res, {
    booking: bookingResult.rows[0],
    activities: activitiesResult.rows
  });
}));

// Update booking
router.put('/:id',
  requirePermission('update_booking'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { error, value } = updateBookingSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if booking exists
    const existingBooking = await query(
      'SELECT * FROM bookings WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (existingBooking.rows.length === 0) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Build update query dynamically
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    Object.entries(value).forEach(([key, val]) => {
      if (val !== undefined) {
        const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        updates.push(`${dbKey} = $${paramIndex}`);
        params.push(val);
        paramIndex++;
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updates.push('updated_at = NOW()');
    params.push(req.params.id, req.user!.tenant_id);

    const updateQuery = `
      UPDATE bookings 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
      RETURNING *
    `;

    const result = await query(updateQuery, params);

    // Log activity
    await query(
      `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
      [
        uuidv4(),
        req.params.id,
        req.user!.id,
        'updated',
        `Booking updated`,
        JSON.stringify(value)
      ]
    );

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`tenant_${req.user!.tenant_id}`).emit('booking_updated', {
      booking: result.rows[0]
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Assign agent to booking
router.post('/:id/assign',
  requirePermission('assign_booking'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { agentId } = req.body;

    if (!agentId) {
      return res.status(400).json({ error: 'Agent ID is required' });
    }

    // Check if agent exists and is available
    const agentResult = await query(
      'SELECT * FROM agents WHERE user_id = $1 AND tenant_id = $2 AND status = $3',
      [agentId, req.user!.tenant_id, 'available']
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({ error: 'Agent not found or not available' });
    }

    // Update booking
    const result = await query(
      `UPDATE bookings 
       SET assigned_agent_id = $1, assignment_time = NOW(), updated_at = NOW()
       WHERE id = $2 AND tenant_id = $3
       RETURNING *`,
      [agentId, req.params.id, req.user!.tenant_id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Log activity
    await query(
      `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, created_at)
       VALUES ($1, $2, $3, $4, $5, NOW())`,
      [
        uuidv4(),
        req.params.id,
        req.user!.id,
        'assigned',
        `Booking assigned to agent ${agentId}`
      ]
    );

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`tenant_${req.user!.tenant_id}`).emit('booking_assigned', {
      booking: result.rows[0],
      agentId
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Cancel booking
router.post('/:id/cancel',
  requirePermission('cancel_booking'),
  asyncHandler(async (req: TenantRequest, res): Promise<any> => {
    const { reason } = req.body;

    const result = await query(
      `UPDATE bookings 
       SET status = 'cancelled', updated_at = NOW()
       WHERE id = $1 AND tenant_id = $2 AND status != 'completed'
       RETURNING *`,
      [req.params.id, req.user!.tenant_id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Booking not found or cannot be cancelled' });
    }

    // Log activity
    await query(
      `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
      [
        uuidv4(),
        req.params.id,
        req.user!.id,
        'cancelled',
        `Booking cancelled`,
        JSON.stringify({ reason })
      ]
    );

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`tenant_${req.user!.tenant_id}`).emit('booking_cancelled', {
      booking: result.rows[0]
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

export default router;
