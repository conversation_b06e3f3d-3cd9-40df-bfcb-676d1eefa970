import express from 'express';
import jwt from 'jsonwebtoken';
import { query } from '../services/database';
import { asyncHandler } from '../middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../services/logger';
import { flightInformationService } from '../services/flightInformation';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendInternalServerError
} from '../utils/response';

const router = express.Router();

// Mobile app authentication
const mobileAuth = async (req: any, res: any, next: any) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret') as any;
    
    const userResult = await query(
      `SELECT u.*, a.status as agent_status, a.current_location, a.specializations
       FROM users u
       LEFT JOIN agents a ON u.id = a.user_id
       WHERE u.id = $1 AND u.role = 'field_agent' AND u.status = 'active'`,
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid token or user not authorized for mobile app' });
    }

    req.user = userResult.rows[0];
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

// Agent login (same as web but returns mobile-specific data)
router.post('/login', asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  // Implementation would be similar to web login but with mobile-specific response
  res.json({ success: true, message: 'Mobile login endpoint - implement based on web auth' });
}));

// Get agent assignments
router.get('/assignments', mobileAuth, asyncHandler(async (req: any, res) => {
  const status = req.query.status || 'confirmed';
  
  const result = await query(
    `SELECT 
       b.id, b.booking_reference, b.status, b.flight_number, b.airline,
       b.departure_airport, b.arrival_airport, b.estimated_arrival,
       b.service_type, b.passenger_count, b.meeting_point, b.special_requirements,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       c.phone as customer_phone, c.email as customer_email,
       s.name as service_name, s.duration_minutes
     FROM bookings b
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE b.assigned_agent_id = $1 
       AND ($2 = 'all' OR b.status = $2)
       AND DATE(b.flight_date) >= CURRENT_DATE
     ORDER BY b.estimated_arrival ASC`,
    [req.user.id, status]
  );

  res.json({
    success: true,
    data: result.rows
  });
}));

// Update agent location
router.post('/location', mobileAuth, asyncHandler(async (req: any, res) => {
  const { latitude, longitude, accuracy } = req.body;

  if (!latitude || !longitude) {
    return res.status(400).json({ error: 'Latitude and longitude required' });
  }

  await query(
    `UPDATE agents 
     SET current_location = $1, updated_at = NOW()
     WHERE user_id = $2`,
    [
      JSON.stringify({
        latitude,
        longitude,
        accuracy: accuracy || 0,
        timestamp: new Date()
      }),
      req.user.id
    ]
  );

  return res.json({
    success: true,
    message: 'Location updated successfully'
  });
}));

// Check in customer
router.post('/checkin/:bookingId', mobileAuth, asyncHandler(async (req: any, res) => {
  const { bookingId } = req.params;
  const { location, notes } = req.body;

  // Verify booking is assigned to this agent
  const bookingResult = await query(
    'SELECT * FROM bookings WHERE id = $1 AND assigned_agent_id = $2',
    [bookingId, req.user.id]
  );

  if (bookingResult.rows.length === 0) {
    return res.status(404).json({ error: 'Booking not found or not assigned to you' });
  }

  // Update booking status
  await query(
    `UPDATE bookings 
     SET status = 'in_progress', service_start_time = NOW(), updated_at = NOW()
     WHERE id = $1`,
    [bookingId]
  );

  // Log activity
  await query(
    `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
     VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
    [
      uuidv4(),
      bookingId,
      req.user.id,
      'started',
      'Service started - customer checked in',
      JSON.stringify({ location, notes })
    ]
  );

  return res.json({
    success: true,
    message: 'Customer checked in successfully'
  });
}));

// Complete service
router.post('/complete/:bookingId', mobileAuth, asyncHandler(async (req: any, res) => {
  const { bookingId } = req.params;
  const { notes, rating } = req.body;

  // Verify booking is assigned to this agent and in progress
  const bookingResult = await query(
    'SELECT * FROM bookings WHERE id = $1 AND assigned_agent_id = $2 AND status = $3',
    [bookingId, req.user.id, 'in_progress']
  );

  if (bookingResult.rows.length === 0) {
    return res.status(404).json({ error: 'Booking not found, not assigned to you, or not in progress' });
  }

  // Update booking status
  await query(
    `UPDATE bookings 
     SET status = 'completed', service_end_time = NOW(), updated_at = NOW()
     WHERE id = $1`,
    [bookingId]
  );

  // Log activity
  await query(
    `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
     VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
    [
      uuidv4(),
      bookingId,
      req.user.id,
      'completed',
      'Service completed successfully',
      JSON.stringify({ notes, rating })
    ]
  );

  // Update agent statistics
  await query(
    `UPDATE agents 
     SET total_services = total_services + 1, 
         rating = CASE 
           WHEN rating IS NULL THEN $2
           ELSE (rating * (total_services - 1) + $2) / total_services
         END,
         updated_at = NOW()
     WHERE user_id = $1`,
    [req.user.id, rating || 5]
  );

  return res.json({
    success: true,
    message: 'Service completed successfully'
  });
}));

// Update booking status
router.post('/status/:bookingId', mobileAuth, asyncHandler(async (req: any, res) => {
  const { bookingId } = req.params;
  const { status, notes } = req.body;

  const validStatuses = ['in_progress', 'completed', 'no_show'];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({ error: 'Invalid status' });
  }

  // Verify booking is assigned to this agent
  const bookingResult = await query(
    'SELECT * FROM bookings WHERE id = $1 AND assigned_agent_id = $2',
    [bookingId, req.user.id]
  );

  if (bookingResult.rows.length === 0) {
    return res.status(404).json({ error: 'Booking not found or not assigned to you' });
  }

  const updateFields = ['status = $2', 'updated_at = NOW()'];
  const params = [bookingId, status];

  if (status === 'in_progress') {
    updateFields.push('service_start_time = NOW()');
  } else if (status === 'completed') {
    updateFields.push('service_end_time = NOW()');
  }

  await query(
    `UPDATE bookings SET ${updateFields.join(', ')} WHERE id = $1`,
    params
  );

  // Log activity
  await query(
    `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
     VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
    [
      uuidv4(),
      bookingId,
      req.user.id,
      'updated',
      `Status updated to ${status}`,
      JSON.stringify({ notes })
    ]
  );

  return res.json({
    success: true,
    message: `Status updated to ${status}`
  });
}));

// Submit service feedback
router.post('/feedback/:bookingId', mobileAuth, asyncHandler(async (req: any, res) => {
  const { bookingId } = req.params;
  const { feedback, issues } = req.body;

  // Log activity
  await query(
    `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
     VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
    [
      uuidv4(),
      bookingId,
      req.user.id,
      'feedback',
      'Agent feedback submitted',
      JSON.stringify({ feedback, issues })
    ]
  );

  res.json({
    success: true,
    message: 'Feedback submitted successfully'
  });
}));

// Get agent profile and stats
router.get('/profile', mobileAuth, asyncHandler(async (req: any, res) => {
  const statsResult = await query(
    `SELECT 
       COUNT(*) as total_assignments,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_services,
       COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_shows,
       AVG(CASE WHEN status = 'completed' AND service_start_time IS NOT NULL AND service_end_time IS NOT NULL 
         THEN EXTRACT(EPOCH FROM (service_end_time - service_start_time))/60 
       END) as avg_service_duration_minutes
     FROM bookings 
     WHERE assigned_agent_id = $1`,
    [req.user.id]
  );

  res.json({
    success: true,
    data: {
      user: {
        id: req.user.id,
        firstName: req.user.first_name,
        lastName: req.user.last_name,
        email: req.user.email
      },
      agent: {
        status: req.user.agent_status,
        specializations: req.user.specializations,
        currentLocation: req.user.current_location
      },
      statistics: statsResult.rows[0]
    }
  });
}));

// Update agent status (available/busy/offline)
router.post('/status', mobileAuth, asyncHandler(async (req: any, res) => {
  const { status } = req.body;
  
  const validStatuses = ['available', 'busy', 'offline'];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({ error: 'Invalid status' });
  }

  await query(
    'UPDATE agents SET status = $1, updated_at = NOW() WHERE user_id = $2',
    [status, req.user.id]
  );

  return res.json({
    success: true,
    message: `Status updated to ${status}`
  });
}));

/**
 * @route POST /api/v1/mobile/location/update
 * @desc Update agent's current location
 * @access Private (Mobile App)
 */
router.post('/location/update', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const { latitude, longitude, accuracy, timestamp } = req.body;
    const agentId = req.user.id;

    if (!latitude || !longitude) {
      return sendValidationError(res, 'Latitude and longitude are required');
    }

    // Update agent location in database
    await query(
      `UPDATE agents SET
         current_location = $1,
         location_accuracy = $2,
         location_updated_at = $3
       WHERE user_id = $4`,
      [
        JSON.stringify({ latitude, longitude }),
        accuracy || null,
        timestamp ? new Date(timestamp) : new Date(),
        agentId
      ]
    );

    // Broadcast location update via WebSocket
    const wsService = (global as any).websocketService;
    if (wsService) {
      wsService.broadcastAgentLocationUpdate(req.user.tenant_id, agentId, {
        agentId,
        latitude,
        longitude,
        timestamp: new Date(),
        status: req.user.agent_status || 'available'
      });
    }

    return sendSuccess(res, {
      message: 'Location updated successfully',
      timestamp: new Date()
    });
  } catch (error) {
    logger.error('Error updating agent location:', error);
    return sendInternalServerError(res, 'Failed to update location');
  }
}));

/**
 * @route POST /api/v1/mobile/status/update
 * @desc Update agent's availability status
 * @access Private (Mobile App)
 */
router.post('/status/update', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const { status } = req.body;
    const agentId = req.user.id;

    if (!['available', 'busy', 'offline', 'break'].includes(status)) {
      return sendValidationError(res, 'Invalid status. Must be: available, busy, offline, or break');
    }

    // Update agent status
    await query(
      'UPDATE agents SET status = $1, status_updated_at = NOW() WHERE user_id = $2',
      [status, agentId]
    );

    // Broadcast status update via WebSocket
    const wsService = (global as any).websocketService;
    if (wsService) {
      wsService.broadcastSystemNotification(req.user.tenant_id, {
        type: 'info',
        title: 'Agent Status Update',
        message: `Agent ${req.user.first_name} ${req.user.last_name} is now ${status}`,
        data: { agentId, status }
      });
    }

    return sendSuccess(res, {
      message: 'Status updated successfully',
      status,
      timestamp: new Date()
    });
  } catch (error) {
    logger.error('Error updating agent status:', error);
    return sendInternalServerError(res, 'Failed to update status');
  }
}));

/**
 * @route GET /api/v1/mobile/bookings/nearby
 * @desc Get nearby bookings based on agent's location
 * @access Private (Mobile App)
 */
router.get('/bookings/nearby', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const { latitude, longitude, radius = 50 } = req.query; // radius in km
    const agentId = req.user.id;
    const tenantId = req.user.tenant_id;

    if (!latitude || !longitude) {
      return sendValidationError(res, 'Current location (latitude, longitude) is required');
    }

    // Get nearby bookings using PostGIS distance calculation
    // For now, we'll use a simple bounding box approach
    const latRadius = parseFloat(radius) / 111; // Rough conversion: 1 degree ≈ 111 km
    const lngRadius = latRadius / Math.cos(parseFloat(latitude) * Math.PI / 180);

    const nearbyBookingsResult = await query(
      `SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              c.phone as customer_phone, c.email as customer_email,
              s.name as service_name, s.category as service_category,
              a.name as airport_name, a.iata_code as airport_code
       FROM bookings b
       JOIN customers c ON b.customer_id = c.id
       JOIN services s ON b.service_id = s.id
       JOIN airports a ON (b.departure_airport = a.iata_code OR b.arrival_airport = a.iata_code)
       WHERE b.tenant_id = $1
         AND b.status IN ('confirmed', 'agent_assigned')
         AND (b.assigned_agent_id IS NULL OR b.assigned_agent_id = $2)
         AND DATE(b.flight_date) = CURRENT_DATE
         AND a.latitude BETWEEN $3 AND $4
         AND a.longitude BETWEEN $5 AND $6
       ORDER BY b.flight_date ASC
       LIMIT 20`,
      [
        tenantId,
        agentId,
        parseFloat(latitude) - latRadius,
        parseFloat(latitude) + latRadius,
        parseFloat(longitude) - lngRadius,
        parseFloat(longitude) + lngRadius
      ]
    );

    // Get flight information for each booking
    const bookingsWithFlightInfo = await Promise.all(
      nearbyBookingsResult.rows.map(async (booking: any) => {
        let flightInfo: any = null;
        if (booking.flight_number) {
          try {
            flightInfo = await flightInformationService.getFlightInfo(
              booking.flight_number,
              new Date(booking.flight_date)
            );
          } catch (error) {
            logger.error(`Error fetching flight info for ${booking.flight_number}:`, error);
          }
        }

        return {
          ...booking,
          flightInfo,
          distance: null // Would calculate actual distance in production
        };
      })
    );

    return sendSuccess(res, {
      bookings: bookingsWithFlightInfo,
      agentLocation: { latitude: parseFloat(latitude), longitude: parseFloat(longitude) },
      searchRadius: parseFloat(radius)
    });
  } catch (error) {
    logger.error('Error fetching nearby bookings:', error);
    return sendInternalServerError(res, 'Failed to fetch nearby bookings');
  }
}));

/**
 * @route POST /api/v1/mobile/bookings/:id/checkin
 * @desc Check in to a booking location
 * @access Private (Mobile App)
 */
router.post('/bookings/:id/checkin', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const { id } = req.params;
    const { latitude, longitude, notes } = req.body;
    const agentId = req.user.id;

    if (!latitude || !longitude) {
      return sendValidationError(res, 'Current location is required for check-in');
    }

    // Verify booking exists and is assigned to this agent
    const bookingResult = await query(
      `SELECT b.*, c.first_name, c.last_name, c.phone
       FROM bookings b
       JOIN customers c ON b.customer_id = c.id
       WHERE b.id = $1 AND b.assigned_agent_id = $2`,
      [id, agentId]
    );

    if (bookingResult.rows.length === 0) {
      return sendNotFoundError(res, 'Booking not found or not assigned to you');
    }

    const booking = bookingResult.rows[0];

    // Update booking status
    await query(
      'UPDATE bookings SET status = $1, updated_at = NOW() WHERE id = $2',
      ['in_progress', id]
    );

    // Create check-in activity
    await query(
      `INSERT INTO booking_activities (
        id, booking_id, user_id, activity_type, description,
        location_data, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
      [
        uuidv4(),
        id,
        agentId,
        'agent_checkin',
        notes || `Agent checked in at location`,
        JSON.stringify({ latitude, longitude, accuracy: req.body.accuracy })
      ]
    );

    // Broadcast update via WebSocket
    const wsService = (global as any).websocketService;
    if (wsService) {
      wsService.broadcastSystemNotification(req.user.tenant_id, {
        type: 'success',
        title: 'Agent Check-in',
        message: `${req.user.first_name} ${req.user.last_name} checked in for booking ${booking.booking_reference}`,
        data: { bookingId: id, agentId, status: 'in_progress' }
      });
    }

    return sendSuccess(res, {
      message: 'Successfully checked in to booking',
      booking: {
        id: booking.id,
        reference: booking.booking_reference,
        customer: `${booking.first_name} ${booking.last_name}`,
        status: 'in_progress'
      },
      checkinTime: new Date()
    });
  } catch (error) {
    logger.error('Error checking in to booking:', error);
    return sendInternalServerError(res, 'Failed to check in');
  }
}));

/**
 * @route POST /api/v1/mobile/bookings/:id/complete
 * @desc Mark booking as completed
 * @access Private (Mobile App)
 */
router.post('/bookings/:id/complete', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const { id } = req.params;
    const { notes, customerSignature, rating } = req.body;
    const agentId = req.user.id;

    // Verify booking exists and is assigned to this agent
    const bookingResult = await query(
      'SELECT * FROM bookings WHERE id = $1 AND assigned_agent_id = $2',
      [id, agentId]
    );

    if (bookingResult.rows.length === 0) {
      return sendNotFoundError(res, 'Booking not found or not assigned to you');
    }

    // Update booking status
    await query(
      `UPDATE bookings SET
         status = $1,
         completed_at = NOW(),
         completion_notes = $2,
         customer_rating = $3,
         updated_at = NOW()
       WHERE id = $4`,
      ['completed', notes, rating, id]
    );

    // Create completion activity
    await query(
      `INSERT INTO booking_activities (
        id, booking_id, user_id, activity_type, description, created_at
      ) VALUES ($1, $2, $3, $4, $5, NOW())`,
      [
        uuidv4(),
        id,
        agentId,
        'service_completed',
        notes || 'Service completed successfully'
      ]
    );

    // Update agent statistics
    await query(
      `UPDATE agents SET
         total_services = total_services + 1,
         last_service_date = NOW()
       WHERE user_id = $1`,
      [agentId]
    );

    // Broadcast completion via WebSocket
    const wsService = (global as any).websocketService;
    if (wsService) {
      wsService.broadcastSystemNotification(req.user.tenant_id, {
        type: 'success',
        title: 'Service Completed',
        message: `Booking ${bookingResult.rows[0].booking_reference} completed by ${req.user.first_name} ${req.user.last_name}`,
        data: { bookingId: id, agentId, status: 'completed', rating }
      });
    }

    return sendSuccess(res, {
      message: 'Booking completed successfully',
      completionTime: new Date(),
      rating: rating || null
    });
  } catch (error) {
    logger.error('Error completing booking:', error);
    return sendInternalServerError(res, 'Failed to complete booking');
  }
}));

/**
 * @route GET /api/v1/mobile/agent/stats
 * @desc Get agent performance statistics
 * @access Private (Mobile App)
 */
router.get('/agent/stats', mobileAuth, asyncHandler(async (req: any, res): Promise<void> => {
  try {
    const agentId = req.user.id;
    const period = req.query.period || 'today'; // today, week, month

    let dateFilter = '';
    switch (period) {
      case 'week':
        dateFilter = "AND DATE(b.completed_at) >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'month':
        dateFilter = "AND DATE(b.completed_at) >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      default:
        dateFilter = "AND DATE(b.completed_at) = CURRENT_DATE";
    }

    // Get performance statistics
    const statsResult = await query(
      `SELECT
         COUNT(*) as total_completed,
         AVG(customer_rating) as average_rating,
         SUM(total_price) as total_revenue,
         COUNT(CASE WHEN customer_rating >= 4 THEN 1 END) as positive_ratings
       FROM bookings b
       WHERE b.assigned_agent_id = $1
         AND b.status = 'completed'
         ${dateFilter}`,
      [agentId]
    );

    // Get today's schedule
    const todayScheduleResult = await query(
      `SELECT b.*, c.first_name, c.last_name, s.name as service_name
       FROM bookings b
       JOIN customers c ON b.customer_id = c.id
       JOIN services s ON b.service_id = s.id
       WHERE b.assigned_agent_id = $1
         AND DATE(b.flight_date) = CURRENT_DATE
         AND b.status IN ('confirmed', 'in_progress', 'agent_assigned')
       ORDER BY b.flight_date ASC`,
      [agentId]
    );

    const stats = statsResult.rows[0];

    return sendSuccess(res, {
      period,
      statistics: {
        totalCompleted: parseInt(stats.total_completed) || 0,
        averageRating: parseFloat(stats.average_rating) || 0,
        totalRevenue: parseFloat(stats.total_revenue) || 0,
        positiveRatings: parseInt(stats.positive_ratings) || 0,
        satisfactionRate: stats.total_completed > 0
          ? Math.round((stats.positive_ratings / stats.total_completed) * 100)
          : 0
      },
      todaySchedule: todayScheduleResult.rows,
      agentInfo: {
        name: `${req.user.first_name} ${req.user.last_name}`,
        status: req.user.agent_status,
        specializations: req.user.specializations || []
      }
    });
  } catch (error) {
    logger.error('Error fetching agent stats:', error);
    return sendInternalServerError(res, 'Failed to fetch statistics');
  }
}));

export default router;
