import express from 'express';
import { query } from '../services/database';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenant';
import { requireRole } from '../middleware/auth';

const router = express.Router();

// Get tenant settings and info
router.get('/settings', asyncHandler(async (req: TenantRequest, res) => {
  const tenantResult = await query(
    'SELECT id, name, subdomain, plan, status, settings, created_at FROM tenants WHERE id = $1',
    [req.user!.tenant_id]
  );

  if (tenantResult.rows.length === 0) {
    return res.status(404).json({ error: 'Tenant not found' });
  }

  return res.json({
    success: true,
    data: tenantResult.rows[0]
  });
}));

// Update tenant settings (company admin only)
router.put('/settings',
  requireRole(['company_admin']),
  asyncHandler(async (req: TenantRequest, res) => {
    const { name, settings } = req.body;

    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (name) {
      updates.push(`name = $${paramIndex}`);
      params.push(name);
      paramIndex++;
    }

    if (settings) {
      updates.push(`settings = $${paramIndex}`);
      params.push(JSON.stringify(settings));
      paramIndex++;
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updates.push('updated_at = NOW()');
    params.push(req.user!.tenant_id);

    const updateQuery = `
      UPDATE tenants 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(updateQuery, params);

    return res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Get tenant users
router.get('/users', asyncHandler(async (req: TenantRequest, res) => {
  const result = await query(
    `SELECT id, email, first_name, last_name, role, status, permissions, last_login, created_at
     FROM users 
     WHERE tenant_id = $1
     ORDER BY created_at DESC`,
    [req.user!.tenant_id]
  );

  return res.json({
    success: true,
    data: result.rows
  });
}));

// Get tenant statistics
router.get('/stats', asyncHandler(async (req: TenantRequest, res) => {
  // Get various statistics for the tenant
  const bookingStatsResult = await query(
    `SELECT 
       COUNT(*) as total_bookings,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
       COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
       COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_bookings,
       SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as total_revenue
     FROM bookings 
     WHERE tenant_id = $1`,
    [req.user!.tenant_id]
  );

  const customerStatsResult = await query(
    `SELECT 
       COUNT(*) as total_customers,
       COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as new_customers_today
     FROM customers 
     WHERE tenant_id = $1`,
    [req.user!.tenant_id]
  );

  const serviceStatsResult = await query(
    `SELECT 
       COUNT(*) as total_services,
       COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services
     FROM services 
     WHERE tenant_id = $1`,
    [req.user!.tenant_id]
  );

  const agentStatsResult = await query(
    `SELECT 
       COUNT(*) as total_agents,
       COUNT(CASE WHEN status = 'available' THEN 1 END) as available_agents
     FROM agents 
     WHERE tenant_id = $1`,
    [req.user!.tenant_id]
  );

  res.json({
    success: true,
    data: {
      bookings: bookingStatsResult.rows[0],
      customers: customerStatsResult.rows[0],
      services: serviceStatsResult.rows[0],
      agents: agentStatsResult.rows[0]
    }
  });
}));

export default router;
