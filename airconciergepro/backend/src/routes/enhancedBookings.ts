import express, { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { query } from '../services/database';
import { TenantRequest, checkTenantLimits } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';
import { logger } from '../services/logger';
import { dynamicPricingService } from '../services/dynamicPricing';
import { notificationService } from '../services/notification';
import { flightInformationService } from '../services/flightInformation';

const router = express.Router();

// Enhanced validation schema
const createBookingSchema = Joi.object({
  customerId: Joi.string().uuid().required(),
  serviceId: Joi.string().uuid().required(),
  flightNumber: Joi.string().required(),
  airline: Joi.string().required(),
  departureAirport: Joi.string().length(3).required(),
  arrivalAirport: Joi.string().length(3).required(),
  flightDate: Joi.date().iso().required(),
  estimatedArrival: Joi.date().iso(),
  serviceType: Joi.string().valid('arrival', 'departure', 'transit').required(),
  passengerCount: Joi.number().integer().min(1).required(),
  passengers: Joi.array().items(
    Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      age: Joi.number().integer().min(0).max(120),
      specialRequirements: Joi.array().items(Joi.string()),
      contactNumber: Joi.string()
    })
  ).required(),
  specialRequirements: Joi.array().items(Joi.string()),
  meetingPoint: Joi.string(),
  whitelabelConfigId: Joi.string().uuid(),
  requestedEquipment: Joi.array().items(Joi.string()),
  applyDynamicPricing: Joi.boolean().default(true)
});

const updateBookingSchema = Joi.object({
  status: Joi.string().valid('confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'),
  assignedAgentId: Joi.string().uuid(),
  estimatedArrival: Joi.date().iso(),
  actualArrival: Joi.date().iso(),
  serviceStartTime: Joi.date().iso(),
  serviceEndTime: Joi.date().iso(),
  specialRequirements: Joi.array().items(Joi.string()),
  meetingPoint: Joi.string(),
  assignedEquipment: Joi.array().items(Joi.string())
});

// Generate unique booking reference
const generateBookingReference = (): string => {
  const prefix = 'ACG';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `${prefix}${timestamp}${random}`;
};

// Enhanced create booking with dynamic pricing and real-time flight data
router.post('/', 
  checkTenantLimits('bookings'),
  requirePermission('create_booking'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { error, value } = createBookingSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const bookingId = uuidv4();
      const bookingReference = generateBookingReference();

      // Get customer details including group for pricing
      const customerResult = await query(
        'SELECT id, user_group_id, email, phone, first_name, last_name FROM customers WHERE id = $1 AND tenant_id = $2',
        [value.customerId, req.user!.tenant_id]
      );

      if (customerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Customer not found' });
      }

      const customer = customerResult.rows[0];

      // Get real-time flight information
      const flightInfo = await flightInformationService.getFlightInfo(
        value.flightNumber, 
        new Date(value.flightDate)
      );

      let estimatedArrival = value.estimatedArrival;
      if (flightInfo && flightInfo.estimatedArrival) {
        estimatedArrival = flightInfo.estimatedArrival;
      }

      // Calculate dynamic pricing
      let finalPrice: number;
      let pricingBreakdown: any = null;

      if (value.applyDynamicPricing) {
        const pricingContext = {
          serviceId: value.serviceId,
          tenantId: req.user!.tenant_id,
          passengerCount: value.passengerCount,
          bookingDate: new Date(),
          serviceDate: new Date(value.flightDate),
          customerGroupId: customer.user_group_id,
          isWhiteLabel: !!value.whitelabelConfigId,
          whitelabelPartnerId: value.whitelabelConfigId
        };

        const pricing = await dynamicPricingService.calculatePrice(pricingContext);
        finalPrice = pricing.finalPrice;
        pricingBreakdown = pricing;
      } else {
        // Get base service price
        const serviceResult = await query(
          'SELECT base_price FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3',
          [value.serviceId, req.user!.tenant_id, 'active']
        );

        if (serviceResult.rows.length === 0) {
          return res.status(404).json({ error: 'Service not found or inactive' });
        }

        finalPrice = serviceResult.rows[0].base_price * value.passengerCount;
      }

      // Get service currency
      const serviceResult = await query(
        'SELECT currency FROM services WHERE id = $1',
        [value.serviceId]
      );
      const currency = serviceResult.rows[0]?.currency || 'USD';

      // Create booking with enhanced data
      const bookingResult = await query(
        `INSERT INTO bookings (
          id, tenant_id, customer_id, service_id, booking_reference, status,
          flight_number, airline, departure_airport, arrival_airport, flight_date,
          estimated_arrival, actual_arrival, service_type, passenger_count, passengers,
          special_requirements, meeting_point, base_price, total_price,
          currency, payment_status, white_label_config_id, assigned_equipment,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, NOW(), NOW()
        ) RETURNING *`,
        [
          bookingId,
          req.user!.tenant_id,
          value.customerId,
          value.serviceId,
          bookingReference,
          'confirmed',
          value.flightNumber,
          value.airline,
          value.departureAirport,
          value.arrivalAirport,
          value.flightDate,
          estimatedArrival,
          flightInfo?.actualArrival || null,
          value.serviceType,
          value.passengerCount,
          JSON.stringify(value.passengers),
          JSON.stringify(value.specialRequirements || []),
          value.meetingPoint || 'Main Terminal',
          pricingBreakdown?.basePrice || finalPrice,
          finalPrice,
          currency,
          'pending',
          value.whitelabelConfigId || null,
          JSON.stringify(value.requestedEquipment || [])
        ]
      );

      // Log booking activity
      await query(
        `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
        [
          uuidv4(),
          bookingId,
          req.user!.id,
          'created',
          `Booking created for ${value.flightNumber} - ${value.serviceType}`,
          JSON.stringify({
            flightInfo: flightInfo ? {
              status: flightInfo.status,
              gate: flightInfo.gate,
              terminal: flightInfo.terminal
            } : null,
            pricing: pricingBreakdown
          })
        ]
      );

      // Send booking confirmation notification
      await notificationService.sendBookingConfirmation(
        req.user!.tenant_id,
        bookingId,
        customer.email,
        {
          bookingReference,
          flightNumber: value.flightNumber,
          flightDate: value.flightDate,
          serviceName: 'Meet & Greet Service',
          meetingPoint: value.meetingPoint || 'Main Terminal'
        }
      );

      // Emit real-time update
      const io = req.app.get('io');
      io.to(`tenant_${req.user!.tenant_id}`).emit('booking_created', {
        booking: bookingResult.rows[0],
        flightInfo,
        pricing: pricingBreakdown
      });

      return res.status(201).json({
        success: true,
        data: {
          booking: bookingResult.rows[0],
          flightInfo,
          pricing: pricingBreakdown
        }
      });
    } catch (error) {
      logger.error('Enhanced booking creation error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get all bookings with enhanced filtering
router.get('/', async (req: TenantRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    // Build filter conditions
    const filters: string[] = ['b.tenant_id = $1'];
    const params: any[] = [req.user!.tenant_id];
    let paramIndex = 2;

    if (req.query.status) {
      filters.push(`b.status = $${paramIndex}`);
      params.push(req.query.status);
      paramIndex++;
    }

    if (req.query.serviceType) {
      filters.push(`b.service_type = $${paramIndex}`);
      params.push(req.query.serviceType);
      paramIndex++;
    }

    if (req.query.flightDate) {
      filters.push(`DATE(b.flight_date) = $${paramIndex}`);
      params.push(req.query.flightDate);
      paramIndex++;
    }

    if (req.query.airport) {
      filters.push(`(b.departure_airport = $${paramIndex} OR b.arrival_airport = $${paramIndex})`);
      params.push(req.query.airport);
      paramIndex++;
    }

    if (req.query.assignedAgentId) {
      filters.push(`b.assigned_agent_id = $${paramIndex}`);
      params.push(req.query.assignedAgentId);
      paramIndex++;
    }

    if (req.query.userGroup) {
      filters.push(`c.user_group_id = $${paramIndex}`);
      params.push(req.query.userGroup);
      paramIndex++;
    }

    const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total 
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       ${whereClause}`,
      params
    );

    // Get bookings with enhanced data
    const bookingsResult = await query(
      `SELECT b.*, 
              c.first_name as customer_first_name, c.last_name as customer_last_name, 
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name,
              ug.name as customer_group_name, ug.pricing_tier,
              wl.partner_name as white_label_partner
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       LEFT JOIN services s ON b.service_id = s.id
       LEFT JOIN users u ON b.assigned_agent_id = u.id
       LEFT JOIN user_groups ug ON c.user_group_id = ug.id
       LEFT JOIN white_label_configs wl ON b.white_label_config_id = wl.id
       ${whereClause}
       ORDER BY b.created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...params, limit, offset]
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return res.json({
      success: true,
      data: {
        bookings: bookingsResult.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    logger.error('Get bookings error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Enhanced assign agent with equipment
router.post('/:id/assign',
  requirePermission('assign_booking'),
  async (req: TenantRequest, res: Response) => {
    try {
      const { agentId, equipmentIds } = req.body;

      if (!agentId) {
        return res.status(400).json({ error: 'Agent ID is required' });
      }

      // Check if agent exists and is available
      const agentResult = await query(
        'SELECT * FROM agents WHERE user_id = $1 AND tenant_id = $2',
        [agentId, req.user!.tenant_id]
      );

      if (agentResult.rows.length === 0) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Reserve equipment if specified
      if (equipmentIds && equipmentIds.length > 0) {
        await query(
          `UPDATE equipment 
           SET status = 'in_use', current_booking_id = $1, updated_at = NOW()
           WHERE id = ANY($2) AND tenant_id = $3 AND status = 'available'`,
          [req.params.id, equipmentIds, req.user!.tenant_id]
        );
      }

      // Update booking
      const result = await query(
        `UPDATE bookings 
         SET assigned_agent_id = $1, assignment_time = NOW(), 
             assigned_equipment = $2, updated_at = NOW()
         WHERE id = $3 AND tenant_id = $4
         RETURNING *`,
        [agentId, JSON.stringify(equipmentIds || []), req.params.id, req.user!.tenant_id]
      );

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Booking not found' });
      }

      // Get agent details for notification
      const userResult = await query(
        'SELECT email, first_name, last_name FROM users WHERE id = $1',
        [agentId]
      );

      if (userResult.rows.length > 0) {
        const booking = result.rows[0];
        await notificationService.sendAgentAssignment(
          req.user!.tenant_id,
          req.params.id,
          userResult.rows[0].email,
          {
            bookingReference: booking.booking_reference,
            customerName: 'Customer', // You might want to get this from customer table
            flightNumber: booking.flight_number,
            flightDate: booking.flight_date,
            meetingPoint: booking.meeting_point
          }
        );
      }

      // Log activity
      await query(
        `INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
        [
          uuidv4(),
          req.params.id,
          req.user!.id,
          'assigned',
          `Booking assigned to agent ${agentId}`,
          JSON.stringify({ equipmentIds })
        ]
      );

      // Emit real-time update
      const io = req.app.get('io');
      io.to(`tenant_${req.user!.tenant_id}`).emit('booking_assigned', {
        booking: result.rows[0],
        agentId,
        equipmentIds
      });

      return res.json({
        success: true,
        data: result.rows[0]
      });
    } catch (error) {
      logger.error('Assign booking error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get price quote
router.post('/quote', async (req: TenantRequest, res: Response) => {
  try {
    const { serviceId, passengerCount, flightDate, customerId } = req.body;

    // Get customer group if provided
    let customerGroupId: string | undefined;
    if (customerId) {
      const customerResult = await query(
        'SELECT user_group_id FROM customers WHERE id = $1',
        [customerId]
      );
      customerGroupId = customerResult.rows[0]?.user_group_id;
    }

    const pricingContext = {
      serviceId,
      tenantId: req.user!.tenant_id,
      passengerCount: passengerCount || 1,
      bookingDate: new Date(),
      serviceDate: new Date(flightDate),
      customerGroupId
    };

    const quote = await dynamicPricingService.getQuote(pricingContext);

    return res.json({
      success: true,
      data: quote
    });
  } catch (error) {
    logger.error('Quote generation error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Update flight information for booking
router.post('/:id/update-flight',
  requirePermission('update_booking'),
  async (req: TenantRequest, res: Response) => {
    try {
      const updated = await flightInformationService.updateBookingWithFlightInfo(req.params.id);
      
      if (updated) {
        return res.json({
          success: true,
          message: 'Flight information updated'
        });
      } else {
        return res.status(404).json({ error: 'Booking not found or flight information unavailable' });
      }
    } catch (error) {
      logger.error('Update flight info error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
);

export default router;
