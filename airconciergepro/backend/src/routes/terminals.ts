import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { query } from '../services/database';

const router = Router();

// Get all terminals for an airport
router.get('/airport/:airportId', authenticateToken, async (req, res) => {
  try {
    const { airportId } = req.params;
    
    const result = await query(`
      SELECT
        t.id,
        t.airport_id,
        t.terminal_code,
        t.terminal_name,
        t.facilities,
        t.gates,
        t.services,
        t.created_at,
        t.updated_at,
        a.iata_code,
        a.name as airport_name
      FROM airport_terminals t
      JOIN airports a ON t.airport_id = a.id
      WHERE t.airport_id = $1
      ORDER BY t.terminal_code
    `, [airportId]);

    res.json({
      success: true,
      data: {
        terminals: result.rows
      }
    });
  } catch (error) {
    console.error('Error fetching terminals:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch terminals'
    });
  }
});

// Get terminals for multiple airports (for services)
router.post('/airports/batch', authenticateToken, async (req, res) => {
  try {
    const { airportIds } = req.body;
    
    if (!airportIds || !Array.isArray(airportIds)) {
      return res.status(400).json({
        success: false,
        error: 'Airport IDs array is required'
      });
    }

    const result = await query(`
      SELECT
        t.id,
        t.airport_id,
        t.terminal_code,
        t.terminal_name,
        t.facilities,
        t.gates,
        t.services,
        a.iata_code,
        a.name as airport_name
      FROM airport_terminals t
      JOIN airports a ON t.airport_id = a.id
      WHERE t.airport_id = ANY($1)
      ORDER BY a.iata_code, t.terminal_code
    `, [airportIds]);

    // Group terminals by airport
    const terminalsByAirport = result.rows.reduce((acc, terminal) => {
      const airportCode = terminal.iata_code;
      if (!acc[airportCode]) {
        acc[airportCode] = [];
      }
      acc[airportCode].push(terminal);
      return acc;
    }, {});

    return res.json({
      success: true,
      data: {
        terminals: terminalsByAirport
      }
    });
  } catch (error) {
    console.error('Error fetching terminals for airports:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch terminals'
    });
  }
});

// Create or update terminal
router.post('/airport/:airportId', authenticateToken, async (req, res) => {
  try {
    const { airportId } = req.params;
    const { terminal_code, terminal_name, facilities, gates, services } = req.body;

    if (!terminal_code || !terminal_name) {
      return res.status(400).json({
        success: false,
        error: 'Terminal code and name are required'
      });
    }

    // Check if terminal exists
    const existingTerminal = await query(
      'SELECT id FROM airport_terminals WHERE airport_id = $1 AND terminal_code = $2',
      [airportId, terminal_code]
    );

    let result;
    if (existingTerminal.rows.length > 0) {
      // Update existing terminal
      result = await query(`
        UPDATE airport_terminals
        SET terminal_name = $1, facilities = $2, gates = $3, services = $4, updated_at = NOW()
        WHERE airport_id = $5 AND terminal_code = $6
        RETURNING *
      `, [terminal_name, facilities || {}, gates || [], services || [], airportId, terminal_code]);
    } else {
      // Create new terminal
      result = await query(`
        INSERT INTO airport_terminals (airport_id, terminal_code, terminal_name, facilities, gates, services)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `, [airportId, terminal_code, terminal_name, facilities || {}, gates || [], services || []]);
    }

    return res.json({
      success: true,
      data: {
        terminal: result.rows[0]
      }
    });
  } catch (error) {
    console.error('Error saving terminal:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to save terminal'
    });
  }
});

// Update terminal
router.put('/:terminalId', authenticateToken, async (req, res) => {
  try {
    const { terminalId } = req.params;
    const { terminal_name, facilities, gates, services } = req.body;

    const result = await query(`
      UPDATE airport_terminals
      SET terminal_name = $1, facilities = $2, gates = $3, services = $4, updated_at = NOW()
      WHERE id = $5
      RETURNING *
    `, [terminal_name, facilities || {}, gates || [], services || [], terminalId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Terminal not found'
      });
    }

    return res.json({
      success: true,
      data: {
        terminal: result.rows[0]
      }
    });
  } catch (error) {
    console.error('Error updating terminal:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update terminal'
    });
  }
});

// Delete terminal
router.delete('/:terminalId', authenticateToken, async (req, res) => {
  try {
    const { terminalId } = req.params;

    const result = await query(
      'DELETE FROM airport_terminals WHERE id = $1 RETURNING *',
      [terminalId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Terminal not found'
      });
    }

    return res.json({
      success: true,
      data: {
        message: 'Terminal deleted successfully'
      }
    });
  } catch (error) {
    console.error('Error deleting terminal:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete terminal'
    });
  }
});

export default router;
