import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { authenticateToken, requirePermission } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenant';
import { airportManagementService } from '../services/airportManagement';
import { flightInformationService } from '../services/flightInformation';
import { aviationStackService } from '../services/aviationStackService';
import { query } from '../services/database';
import { logger } from '../services/logger';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendInternalServerError
} from '../utils/response';

const router = express.Router();

/**
 * @route GET /api/v1/airports
 * @desc Get all airports with pagination
 * @access Private
 */
router.get('/', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 50;

  const result = await airportManagementService.getAirports(page, limit);

  res.json({
    success: true,
    data: result
  });
}));

// ===== TENANT-SPECIFIC AIRPORT MANAGEMENT ENDPOINTS =====
// These must come BEFORE the generic /:code route to avoid conflicts

// Validation schemas for tenant airport management
const tenantAirportSchema = Joi.object({
  airport_id: Joi.string().uuid().required(),
  status: Joi.string().valid('active', 'inactive').default('active'),
  operational_hours: Joi.object().default({}),
  contact_info: Joi.object().default({}),
  meeting_instructions: Joi.string().allow('').optional()
});

const updateTenantAirportSchema = Joi.object({
  status: Joi.string().valid('active', 'inactive'),
  operational_hours: Joi.object(),
  contact_info: Joi.object(),
  meeting_instructions: Joi.string().allow('')
});

/**
 * @route GET /api/v1/airports/tenant
 * @desc Get tenant's available airports
 * @access Private
 */
router.get('/tenant', authenticateToken, asyncHandler(async (req: TenantRequest, res): Promise<void> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const status = req.query.status as string;

  let whereClause = 'WHERE ta.tenant_id = $1';
  const params: any[] = [req.user!.tenant_id];
  let paramIndex = 2;

  if (search) {
    whereClause += ` AND (a.iata_code ILIKE $${paramIndex} OR a.name ILIKE $${paramIndex} OR a.city ILIKE $${paramIndex} OR a.country ILIKE $${paramIndex})`;
    params.push(`%${search}%`);
    paramIndex++;
  }

  if (status) {
    whereClause += ` AND ta.status = $${paramIndex}`;
    params.push(status);
    paramIndex++;
  }

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total
     FROM tenant_airports ta
     JOIN airports a ON ta.airport_id = a.id
     ${whereClause}`,
    params
  );

  // Get tenant airports with airport details and terminals
  const airportsResult = await query(
    `SELECT ta.*, a.iata_code, a.icao_code, a.name, a.city, a.country,
            a.country_iso2, a.timezone, a.latitude, a.longitude, a.phone_number,
            a.facilities, a.meeting_points, a.status as airport_status,
            COALESCE(
              JSON_AGG(
                JSON_BUILD_OBJECT(
                  'id', at.id,
                  'terminal_code', at.terminal_code,
                  'terminal_name', at.terminal_name,
                  'facilities', at.facilities,
                  'gates', at.gates,
                  'services', at.services
                ) ORDER BY at.terminal_code
              ) FILTER (WHERE at.id IS NOT NULL),
              '[]'::json
            ) as terminals
     FROM tenant_airports ta
     JOIN airports a ON ta.airport_id = a.id
     LEFT JOIN airport_terminals at ON a.id = at.airport_id
     ${whereClause}
     GROUP BY ta.id, ta.tenant_id, ta.airport_id, ta.status, ta.operational_hours,
              ta.contact_info, ta.meeting_instructions, ta.created_at, ta.updated_at,
              a.iata_code, a.icao_code, a.name, a.city, a.country, a.country_iso2,
              a.timezone, a.latitude, a.longitude, a.phone_number, a.facilities,
              a.meeting_points, a.status
     ORDER BY a.name ASC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
    [...params, limit, offset]
  );

  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      airports: airportsResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
}));

/**
 * @route GET /api/v1/airports/available
 * @desc Get airports available for booking (used by customer portal)
 * @access Private
 */
router.get('/available', authenticateToken, asyncHandler(async (req: TenantRequest, res): Promise<void> => {
  const category = req.query.category as string;
  const type = req.query.type as string;

  let serviceFilter = '';
  const params: any[] = [req.user!.tenant_id];
  let paramIndex = 2;

  if (category) {
    serviceFilter += ` AND s.category = $${paramIndex}`;
    params.push(category);
    paramIndex++;
  }

  if (type) {
    serviceFilter += ` AND s.type = $${paramIndex}`;
    params.push(type);
    paramIndex++;
  }

  // Get airports that have active services
  const airportsResult = await query(
    `SELECT DISTINCT a.iata_code, a.name, a.city, a.country, a.timezone
     FROM airports a
     JOIN tenant_airports ta ON a.id = ta.airport_id
     JOIN services s ON s.tenant_id = ta.tenant_id
     WHERE ta.tenant_id = $1
       AND ta.status = 'active'
       AND a.status = 'active'
       AND s.status = 'active'
       AND s.available_airports::jsonb ? a.iata_code
       ${serviceFilter}
     ORDER BY a.name ASC`,
    params
  );

  res.json({
    success: true,
    data: airportsResult.rows
  });
}));

/**
 * @route GET /api/v1/airports/global
 * @desc Get all global airports (for admin to choose from)
 * @access Private (requires view_airports permission)
 */
router.get('/global',
  authenticateToken,
  requirePermission('view_airports'),
  asyncHandler(async (req: TenantRequest, res): Promise<void> => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = (page - 1) * limit;
    const search = req.query.search as string;
    const status = req.query.status as string;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (search) {
      whereClause += ` AND (iata_code ILIKE $${paramIndex} OR name ILIKE $${paramIndex} OR city ILIKE $${paramIndex} OR country ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (status) {
      whereClause += ` AND status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM airports ${whereClause}`,
      params
    );

    // Get airports
    const airportsResult = await query(
      `SELECT * FROM airports ${whereClause}
       ORDER BY name ASC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...params, limit, offset]
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        airports: airportsResult.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit
        }
      }
    });
  })
);

/**
 * @route GET /api/v1/airports/search
 * @desc Search airports by name, code, or city
 * @access Private
 */
router.get('/search', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const query = req.query.q as string;

  if (!query || query.length < 2) {
    res.status(400).json({
      success: false,
      error: 'Search query must be at least 2 characters long'
    });
    return;
  }

  const airports = await airportManagementService.searchAirports(query);

  res.json({
    success: true,
    data: airports
  });
}));

/**
 * @route GET /api/v1/airports/:code
 * @desc Get airport details by IATA code
 * @access Private
 */
router.get('/:code', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;

  if (!code || code.length !== 3) {
    res.status(400).json({
      success: false,
      error: 'Valid IATA code required (3 characters)'
    });
    return;
  }

  const airport = await airportManagementService.getAirport(code);

  if (!airport) {
    res.status(404).json({
      success: false,
      error: 'Airport not found'
    });
    return;
  }

  res.json({
    success: true,
    data: airport
  });
}));

/**
 * @route GET /api/v1/airports/:code/terminals
 * @desc Get airport terminals
 * @access Private
 */
router.get('/:code/terminals', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;

  const airport = await airportManagementService.getAirport(code);
  if (!airport) {
    res.status(404).json({
      success: false,
      error: 'Airport not found'
    });
    return;
  }

  const terminals = await airportManagementService.getAirportTerminals(airport.id);

  res.json({
    success: true,
    data: terminals
  });
}));

/**
 * @route GET /api/v1/airports/:code/meeting-points
 * @desc Get airport meeting points
 * @access Private
 */
router.get('/:code/meeting-points', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;
  const terminal = req.query.terminal as string;

  const airport = await airportManagementService.getAirport(code);
  if (!airport) {
    res.status(404).json({
      success: false,
      error: 'Airport not found'
    });
    return;
  }

  const meetingPoints = await airportManagementService.getAirportMeetingPoints(airport.id, terminal);

  res.json({
    success: true,
    data: meetingPoints
  });
}));

/**
 * @route POST /api/v1/airports/:code/meeting-points
 * @desc Create or update meeting point
 * @access Private (Admin only)
 */
router.post('/:code/meeting-points', authenticateToken, asyncHandler(async (req: any, res): Promise<void> => {
  const { code } = req.params;
  const { terminal, name, description, coordinates, instructions, accessibilityFeatures, capacity, equipmentAvailable } = req.body;

  // Check if user has admin permissions
  if (req.user.role !== 'super_admin' && req.user.role !== 'company_admin') {
    res.status(403).json({
      success: false,
      error: 'Admin permissions required'
    });
    return;
  }

  const airport = await airportManagementService.getAirport(code);
  if (!airport) {
    res.status(404).json({
      success: false,
      error: 'Airport not found'
    });
    return;
  }

  if (!name || !instructions) {
    res.status(400).json({
      success: false,
      error: 'Name and instructions are required'
    });
    return;
  }

  const meetingPointId = await airportManagementService.upsertMeetingPoint({
    airportId: airport.id,
    terminal,
    name,
    description,
    coordinates,
    instructions,
    accessibilityFeatures: accessibilityFeatures || [],
    capacity: capacity || 1,
    equipmentAvailable: equipmentAvailable || [],
    status: 'active'
  });

  if (!meetingPointId) {
    res.status(500).json({
      success: false,
      error: 'Failed to create meeting point'
    });
    return;
  }

  res.status(201).json({
    success: true,
    data: { id: meetingPointId }
  });
}));

/**
 * @route GET /api/v1/airports/:code/operations
 * @desc Get airport operational data
 * @access Private
 */
router.get('/:code/operations', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;
  const date = req.query.date ? new Date(req.query.date as string) : undefined;

  const airport = await airportManagementService.getAirport(code);
  if (!airport) {
    res.status(404).json({
      success: false,
      error: 'Airport not found'
    });
    return;
  }

  const operations = await airportManagementService.getAirportOperations(airport.id, date);

  res.json({
    success: true,
    data: operations
  });
}));

/**
 * @route GET /api/v1/airports/:code/delays
 * @desc Get airport delay information
 * @access Private
 */
router.get('/:code/delays', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;

  const delayInfo = await flightInformationService.getAirportDelays(code);

  res.json({
    success: true,
    data: delayInfo
  });
}));

/**
 * @route GET /api/v1/airports/:code/flights
 * @desc Get flights for a specific airport
 * @access Private
 */
router.get('/:code/flights', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  const { code } = req.params;
  const date = req.query.date as string;

  // This would typically integrate with flight information APIs
  // For now, return a placeholder response
  res.json({
    success: true,
    data: {
      airport: code,
      date: date || new Date().toISOString().split('T')[0],
      flights: [],
      message: 'Flight data integration in progress'
    }
  });
}));

/**
 * @route GET /api/v1/airports/stats/summary
 * @desc Get airport statistics summary
 * @access Private
 */
router.get('/stats/summary', authenticateToken, asyncHandler(async (req, res): Promise<void> => {
  try {
    const flightStatusSummary = await flightInformationService.getFlightStatusSummary();

    res.json({
      success: true,
      data: {
        flightStatus: flightStatusSummary,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting airport stats summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get airport statistics'
    });
  }
}));

/**
 * @route POST /api/v1/airports/sync
 * @desc Sync airports from AviationStack API
 * @access Private (Admin only)
 */
router.post('/sync', asyncHandler(async (req: any, res): Promise<void> => {
  try {
    // Check if user has admin permissions
    if (!req.user || !['super_admin', 'company_admin'].includes(req.user.role)) {
      return sendError(res, 'Insufficient permissions', 403);
    }

    const { limit = 100, offset = 0 } = req.body;

    logger.info(`Starting airport sync with limit: ${limit}, offset: ${offset}`);

    await aviationStackService.syncAirports(limit, offset);

    // Get updated count
    const countResult = await query('SELECT COUNT(*) as total FROM airports', []);
    const total = parseInt(countResult.rows[0].total);

    return sendSuccess(res, {
      message: 'Airport sync completed successfully',
      totalAirports: total
    });
  } catch (error) {
    logger.error('Error syncing airports:', error);
    return sendInternalServerError(res, 'Failed to sync airports');
  }
}));

/**
 * @route GET /api/v1/airports/:code/flights
 * @desc Get real-time flights for an airport
 * @access Private
 */
router.get('/:code/flights', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { code } = req.params;
    const type = req.query.type as 'arrival' | 'departure' || 'arrival';
    const limit = parseInt(req.query.limit as string) || 20;

    if (!code || code.length !== 3) {
      return sendValidationError(res, 'Valid IATA code (3 characters) is required');
    }

    // Get flights from AviationStack
    const flights = await aviationStackService.getFlightsByAirport(
      code.toUpperCase(),
      type,
      limit
    );

    return sendSuccess(res, {
      airport: code.toUpperCase(),
      type,
      flights: flights.map(flight => ({
        flightNumber: flight.flight.iata,
        airline: flight.airline.name,
        status: flight.flight_status,
        scheduled: type === 'arrival' ? flight.arrival.scheduled : flight.departure.scheduled,
        estimated: type === 'arrival' ? flight.arrival.estimated : flight.departure.estimated,
        actual: type === 'arrival' ? flight.arrival.actual : flight.departure.actual,
        terminal: type === 'arrival' ? flight.arrival.terminal : flight.departure.terminal,
        gate: type === 'arrival' ? flight.arrival.gate : flight.departure.gate,
        delay: type === 'arrival' ? flight.arrival.delay : flight.departure.delay,
        origin: flight.departure.iata,
        destination: flight.arrival.iata
      }))
    });
  } catch (error) {
    logger.error('Error fetching airport flights:', error);
    return sendInternalServerError(res, 'Failed to fetch flights');
  }
}));

/**
 * @route GET /api/v1/airports/search/external
 * @desc Search airports using AviationStack API
 * @access Private
 */
router.get('/search/external', asyncHandler(async (req, res): Promise<void> => {
  try {
    const searchQuery = req.query.q as string;
    const limit = parseInt(req.query.limit as string) || 20;

    if (!searchQuery || searchQuery.length < 2) {
      return sendValidationError(res, 'Search query must be at least 2 characters');
    }

    const airports = await aviationStackService.searchAirports(searchQuery, limit);

    return sendSuccess(res, {
      query: searchQuery,
      airports: airports.map(airport => ({
        iataCode: airport.iata_code,
        icaoCode: airport.icao_code,
        name: airport.airport_name,
        city: airport.city_iata_code,
        country: airport.country_name,
        timezone: airport.timezone
      }))
    });
  } catch (error) {
    logger.error('Error searching airports:', error);
    return sendInternalServerError(res, 'Failed to search airports');
  }
}));



// Additional tenant-specific routes (POST, PUT, DELETE) for managing tenant airports
/**
 * @route POST /api/v1/airports/tenant
 * @desc Add airport to tenant
 * @access Private (requires manage_airports permission)
 */
router.post('/tenant',
  authenticateToken,
  requirePermission('manage_airports'),
  asyncHandler(async (req: TenantRequest, res): Promise<void> => {
    const { error, value } = tenantAirportSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: error.details[0].message
      });
      return;
    }

    // Check if airport exists
    const airportExists = await query(
      'SELECT id FROM airports WHERE id = $1 AND status = $2',
      [value.airport_id, 'active']
    );

    if (airportExists.rows.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Airport not found or inactive'
      });
      return;
    }

    // Check if already added to tenant
    const existingTenantAirport = await query(
      'SELECT id FROM tenant_airports WHERE tenant_id = $1 AND airport_id = $2',
      [req.user!.tenant_id, value.airport_id]
    );

    if (existingTenantAirport.rows.length > 0) {
      res.status(409).json({
        success: false,
        error: 'Airport already added to your account'
      });
      return;
    }

    const tenantAirportId = uuidv4();
    const result = await query(
      `INSERT INTO tenant_airports (
        id, tenant_id, airport_id, status, operational_hours,
        contact_info, meeting_instructions, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW()) RETURNING *`,
      [
        tenantAirportId,
        req.user!.tenant_id,
        value.airport_id,
        value.status,
        JSON.stringify(value.operational_hours),
        JSON.stringify(value.contact_info),
        value.meeting_instructions
      ]
    );

    res.status(201).json({
      success: true,
      data: result.rows[0]
    });
  })
);

/**
 * @route PUT /api/v1/airports/tenant/:id
 * @desc Update tenant airport settings
 * @access Private (requires manage_airports permission)
 */
router.put('/tenant/:id',
  authenticateToken,
  requirePermission('manage_airports'),
  asyncHandler(async (req: TenantRequest, res): Promise<void> => {
    const { error, value } = updateTenantAirportSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: error.details[0].message
      });
      return;
    }

    // Check if tenant airport exists
    const existingTenantAirport = await query(
      'SELECT id FROM tenant_airports WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (existingTenantAirport.rows.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Tenant airport not found'
      });
      return;
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (value.status !== undefined) {
      updateFields.push(`status = $${paramIndex}`);
      updateValues.push(value.status);
      paramIndex++;
    }

    if (value.operational_hours !== undefined) {
      updateFields.push(`operational_hours = $${paramIndex}`);
      updateValues.push(JSON.stringify(value.operational_hours));
      paramIndex++;
    }

    if (value.contact_info !== undefined) {
      updateFields.push(`contact_info = $${paramIndex}`);
      updateValues.push(JSON.stringify(value.contact_info));
      paramIndex++;
    }

    if (value.meeting_instructions !== undefined) {
      updateFields.push(`meeting_instructions = $${paramIndex}`);
      updateValues.push(value.meeting_instructions);
      paramIndex++;
    }

    updateFields.push(`updated_at = NOW()`);

    const result = await query(
      `UPDATE tenant_airports SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
       RETURNING *`,
      [...updateValues, req.params.id, req.user!.tenant_id]
    );

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

/**
 * @route DELETE /api/v1/airports/tenant/:id
 * @desc Remove airport from tenant
 * @access Private (requires manage_airports permission)
 */
router.delete('/tenant/:id',
  authenticateToken,
  requirePermission('manage_airports'),
  asyncHandler(async (req: TenantRequest, res): Promise<void> => {
    // Check if tenant airport exists
    const existingTenantAirport = await query(
      'SELECT id, airport_id FROM tenant_airports WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (existingTenantAirport.rows.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Tenant airport not found'
      });
      return;
    }

    // Check if airport is used in any active services
    const airportInUse = await query(
      `SELECT COUNT(*) as count FROM services
       WHERE tenant_id = $1 AND status = 'active'
       AND available_airports::jsonb ? (
         SELECT iata_code FROM airports WHERE id = $2
       )`,
      [req.user!.tenant_id, existingTenantAirport.rows[0].airport_id]
    );

    if (parseInt(airportInUse.rows[0].count) > 0) {
      res.status(400).json({
        success: false,
        error: 'Cannot remove airport that is used in active services. Please update or deactivate the services first.'
      });
      return;
    }

    await query(
      'DELETE FROM tenant_airports WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    res.json({
      success: true,
      message: 'Airport removed from your account successfully'
    });
  })
);

export default router;
