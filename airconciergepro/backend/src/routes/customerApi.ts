import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { airportManagementService } from '../services/airportManagement';
import { query } from '../services/database';
import { logger } from '../services/logger';
import { PaymentService } from '../services/paymentService';
import { flightInformationService } from '../services/flightInformationService';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendInternalServerError
} from '../utils/response';

const router = express.Router();
const paymentService = new PaymentService();

/**
 * Public Customer API Routes
 * These endpoints don't require authentication and are used by the customer portal
 */

/**
 * @route GET /api/customer/v1/airports
 * @desc Get all airports for customer portal (public)
 * @access Public
 */
router.get('/airports', asyncHandler(async (req, res): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 100;
    
    const result = await airportManagementService.getAirports(page, limit);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error fetching airports for customer portal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch airports'
    });
  }
}));

/**
 * @route GET /api/customer/v1/airports/search
 * @desc Search airports by name, city, or IATA code (public)
 * @access Public
 */
router.get('/airports/search', asyncHandler(async (req, res): Promise<void> => {
  try {
    const query_param = req.query.q as string;
    
    if (!query_param || query_param.length < 2) {
      res.status(400).json({
        success: false,
        error: 'Search query must be at least 2 characters'
      });
      return;
    }
    
    const result = await query(
      `SELECT id, iata_code as code, name, city, country, timezone
       FROM airports
       WHERE LOWER(name) LIKE LOWER($1)
          OR LOWER(city) LIKE LOWER($1)
          OR LOWER(iata_code) LIKE LOWER($1)
          OR LOWER(country) LIKE LOWER($1)
       ORDER BY
         CASE
           WHEN LOWER(iata_code) = LOWER($2) THEN 1
           WHEN LOWER(iata_code) LIKE LOWER($1) THEN 2
           WHEN LOWER(name) LIKE LOWER($1) THEN 3
           WHEN LOWER(city) LIKE LOWER($1) THEN 4
           ELSE 5
         END,
         name ASC
       LIMIT 20`,
      [`%${query_param}%`, query_param]
    );
    
    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    logger.error('Error searching airports:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search airports'
    });
  }
}));

/**
 * @route GET /api/customer/v1/airports/available
 * @desc Get airports available for booking with service information
 * @access Public
 */
router.get('/airports/available', asyncHandler(async (req, res): Promise<void> => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    const category = req.query.category as string;
    const type = req.query.type as string;
    const passengers = parseInt(req.query.passengers as string) || 1;

    if (!tenantId) {
      res.status(400).json({
        success: false,
        error: 'Tenant ID is required'
      });
      return;
    }

    let serviceFilter = '';
    const params: any[] = [];

    if (category) {
      serviceFilter += ` AND s.category = $${params.length + 1}`;
      params.push(category);
    }

    if (type) {
      serviceFilter += ` AND s.type = $${params.length + 1}`;
      params.push(type);
    }

    // Get airports where the tenant has active services
    // First, let's get the airports that have services for this tenant
    const serviceAirports = await query(
      `SELECT DISTINCT jsonb_array_elements_text(s.available_airports) as iata_code
       FROM services s
       WHERE s.tenant_id = $1::uuid
         AND s.status = 'active'`,
      [tenantId]
    );

    if (serviceAirports.rows.length === 0) {
      return sendNotFoundError(res, 'No airports available for this tenant');
    }

    const airportCodes = serviceAirports.rows.map(row => row.iata_code);
    const placeholders = airportCodes.map((_, index) => `$${index + 1}`).join(',');

    // Now get the airport details for these codes
    const result = await query(
      `SELECT
         a.id,
         a.iata_code,
         a.name,
         a.city,
         a.country,
         a.timezone,
         1 as available_services,
         2500 as starting_price,
         'INR' as currency
       FROM airports a
       WHERE a.iata_code IN (${placeholders})
       ORDER BY a.name ASC`,
      airportCodes
    );

    logger.info(`Available airports query returned ${result.rows.length} rows`);

    if (result.rows.length === 0) {
      res.status(404).json({
        success: false,
        error: 'No airports available'
      });
      return;
    }

    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    logger.error('Error fetching available airports:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}));

/**
 * @route GET /api/customer/v1/airports/:code
 * @desc Get airport details by IATA code or ID (public)
 * @access Public
 */
router.get('/airports/:code', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { code } = req.params;

    if (!code) {
      res.status(400).json({
        success: false,
        error: 'Airport code or ID required'
      });
      return;
    }

    let airport;

    if (code.length === 36 && code.includes('-')) {
      // It's a UUID (airport ID)
      const airportResult = await query(
        'SELECT * FROM airports WHERE id = $1',
        [code]
      );

      if (airportResult.rows.length === 0) {
        res.status(404).json({
          success: false,
          error: 'Airport not found'
        });
        return;
      }

      const row = airportResult.rows[0];
      airport = {
        id: row.id,
        iataCode: row.iata_code,
        icaoCode: row.icao_code,
        name: row.name,
        city: row.city,
        country: row.country,
        timezone: row.timezone
      };
    } else {
      // It's an IATA code
      airport = await airportManagementService.getAirport(code);
    }

    if (!airport) {
      res.status(404).json({
        success: false,
        error: 'Airport not found'
      });
      return;
    }

    res.json({
      success: true,
      data: airport
    });
  } catch (error) {
    logger.error('Error fetching airport details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch airport details'
    });
  }
}));

/**
 * @route GET /api/customer/v1/airports/:code/services
 * @desc Get available services for an airport (public)
 * @access Public
 */
router.get('/airports/:code/services', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { code } = req.params;
    const serviceType = req.query.service_type as string;
    const passengerCount = parseInt(req.query.passengers as string) || 1;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    // Check if code is UUID (airport ID) or IATA code
    let airport;
    let airportCode;

    if (code.length === 36 && code.includes('-')) {
      // It's a UUID (airport ID)
      const airportResult = await query(
        'SELECT * FROM airports WHERE id = $1',
        [code]
      );

      if (airportResult.rows.length === 0) {
        res.status(404).json({
          success: false,
          error: 'Airport not found'
        });
        return;
      }

      airport = airportResult.rows[0];
      airportCode = airport.iata_code;
    } else {
      // It's an IATA code
      airport = await airportManagementService.getAirport(code);
      if (!airport) {
        res.status(404).json({
          success: false,
          error: 'Airport not found'
        });
        return;
      }
      airportCode = code;
    }

    // Build query conditions - filter by tenant for SaaS isolation
    let whereConditions = ['s.status = $1', 's.tenant_id = $2', 's.available_airports::jsonb ? $3'];
    let queryParams: any[] = ['active', tenantId, airportCode];
    let paramIndex = 4;

    if (serviceType) {
      whereConditions.push(`s.type = $${paramIndex}`);
      queryParams.push(serviceType);
      paramIndex++;
    }

    if (passengerCount > 1) {
      whereConditions.push(`s.max_passengers >= $${paramIndex}`);
      queryParams.push(passengerCount);
      paramIndex++;
    }

    const result = await query(
      `SELECT s.id, s.name, s.description, s.category, s.type, s.base_price, s.currency,
              s.duration_minutes, s.max_passengers, s.inclusions, s.requirements
       FROM services s
       WHERE ${whereConditions.join(' AND ')}
       ORDER BY s.category, s.base_price ASC`,
      queryParams
    );

    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    logger.error('Error fetching airport services:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch services'
    });
  }
}));

/**
 * @route GET /api/customer/v1/popular-airports
 * @desc Get popular airports for quick selection (public)
 * @access Public
 */
router.get('/popular-airports', asyncHandler(async (req, res): Promise<void> => {
  try {
    // Get airports with most bookings or predefined popular ones
    const result = await query(
      `SELECT a.id, a.iata_code as code, a.name, a.city, a.country, a.timezone,
              COUNT(b.id) as booking_count
       FROM airports a
       LEFT JOIN bookings b ON a.iata_code = b.departure_airport OR a.iata_code = b.arrival_airport
       GROUP BY a.id, a.iata_code, a.name, a.city, a.country, a.timezone
       ORDER BY booking_count DESC, a.name ASC
       LIMIT 12`
    );

    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    logger.error('Error fetching popular airports:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch popular airports'
    });
  }
}));

/**
 * @route GET /api/customer/v1/services/:id
 * @desc Get service details by ID (public)
 * @access Public
 */
router.get('/services/:id', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { id } = req.params;

    const result = await query(
      `SELECT s.id, s.name, s.description, s.category, s.type, s.base_price, s.currency,
              s.duration_minutes, s.max_passengers, s.inclusions, s.requirements
       FROM services s
       WHERE s.id = $1 AND s.status = 'active'`,
      [id]
    );

    if (result.rows.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Service not found'
      });
      return;
    }

    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    logger.error('Error fetching service details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch service details'
    });
  }
}));

/**
 * @route POST /api/customer/v1/availability/check
 * @desc Check availability for services (public)
 * @access Public
 */
router.post('/availability/check', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { airport_id, service_ids, datetime } = req.body;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!airport_id || !service_ids || !datetime) {
      return sendValidationError(res, 'Missing required fields: airport_id, service_ids, datetime');
    }

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    // Get airport details - airport_id can be either UUID or IATA code
    let airportResult;
    let airportIataCode: string = '';
    let airportName: string = '';

    // Check if airport_id is a UUID (contains hyphens) or IATA code (3 letters)
    if (airport_id.length === 3 && /^[A-Z]{3}$/.test(airport_id)) {
      // It's an IATA code
      airportResult = await query(
        `SELECT iata_code, name FROM airports WHERE iata_code = $1`,
        [airport_id]
      );
      airportIataCode = airport_id;
    } else {
      // It's a UUID
      airportResult = await query(
        `SELECT iata_code, name FROM airports WHERE id = $1`,
        [airport_id]
      );
    }

    if (airportResult.rows.length === 0) {
      return sendNotFoundError(res, 'Airport not found');
    }

    if (!airportIataCode) {
      airportIataCode = airportResult.rows[0].iata_code;
    }
    airportName = airportResult.rows[0].name;

    // Check if services exist and are active for this airport
    const servicesResult = await query(
      `SELECT s.*
       FROM services s
       WHERE s.id = ANY($1) AND s.tenant_id = $2 AND s.status = 'active'
         AND (s.available_airports = '[]'::jsonb OR s.available_airports @> $3)`,
      [service_ids, tenantId, JSON.stringify([airportIataCode])]
    );

    if (servicesResult.rows.length === 0) {
      return sendNotFoundError(res, 'No active services found');
    }

    // Check existing bookings for the date to calculate availability
    const bookingDate = new Date(datetime);
    const dateStr = bookingDate.toISOString().split('T')[0];

    const existingBookingsResult = await query(
      `SELECT COUNT(*) as booking_count, SUM(passenger_count) as total_passengers
       FROM bookings
       WHERE service_id = ANY($1)
         AND tenant_id = $2
         AND DATE(flight_date) = $3
         AND status IN ('confirmed', 'in_progress', 'agent_assigned')`,
      [service_ids, tenantId, dateStr]
    );

    const existingBookings = existingBookingsResult.rows[0];
    const totalBookedPassengers = parseInt(existingBookings.total_passengers) || 0;

    // Calculate capacity (for now, use a base capacity per service)
    const totalCapacity = servicesResult.rows.length * 100; // 100 slots per service
    const availableCapacity = Math.max(0, totalCapacity - totalBookedPassengers);
    const available = availableCapacity > 0;

    // Generate time slots for the day (every 30 minutes from 6 AM to 10 PM)
    const timeSlots: any[] = [];
    for (let hour = 6; hour <= 22; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

        // For each time slot, check if there are bookings
        const slotBookingsResult = await query(
          `SELECT COUNT(*) as slot_bookings, SUM(passenger_count) as slot_passengers
           FROM bookings
           WHERE service_id = ANY($1)
             AND tenant_id = $2
             AND DATE(flight_date) = $3
             AND EXTRACT(HOUR FROM flight_date) = $4
             AND EXTRACT(MINUTE FROM flight_date) = $5
             AND status IN ('confirmed', 'in_progress', 'agent_assigned')`,
          [service_ids, tenantId, dateStr, hour, minute]
        );

        const slotBookings = slotBookingsResult.rows[0];
        const slotPassengers = parseInt(slotBookings.slot_passengers) || 0;
        const slotCapacity = 20; // 20 slots per time slot
        const slotAvailable = slotPassengers < slotCapacity;

        timeSlots.push({
          time: timeString,
          available: slotAvailable,
          capacity: slotCapacity,
          booked: slotPassengers,
          waitlist: 0
        });
      }
    }

    // Find next available date if current date is not available
    let nextAvailableDate: string | null = null;
    if (!available) {
      const futureDate = new Date(bookingDate);
      futureDate.setDate(futureDate.getDate() + 1);
      nextAvailableDate = futureDate.toISOString().split('T')[0];
    }

    return sendSuccess(res, {
      date: dateStr,
      available,
      totalCapacity,
      availableCapacity,
      timeSlots,
      nextAvailableDate,
      restrictions: [],
      lastUpdated: new Date().toISOString(),
      services: servicesResult.rows.map(service => ({
        id: service.id,
        name: service.name,
        airport: airportName,
        airport_code: airportIataCode
      }))
    });
  } catch (error) {
    logger.error('Error checking availability:', error);
    return sendInternalServerError(res, 'Failed to check availability');
  }
}));

/**
 * @route POST /api/customer/v1/pricing/calculate
 * @desc Calculate pricing for services (public)
 * @access Public
 */
router.post('/pricing/calculate', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { service_ids, passenger_count, datetime } = req.body;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    if (!service_ids || !passenger_count) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: service_ids, passenger_count'
      });
      return;
    }

    // Get service prices with tenant filtering
    const result = await query(
      `SELECT id, name, base_price, currency
       FROM services
       WHERE id = ANY($1) AND status = 'active' AND tenant_id = $2`,
      [service_ids, tenantId]
    );

    let total = 0;
    const breakdown = result.rows.map(service => {
      const serviceTotal = service.base_price * passenger_count;
      total += serviceTotal;
      return {
        service_id: service.id,
        service_name: service.name,
        base_price: service.base_price,
        passenger_count,
        total: serviceTotal,
        currency: service.currency
      };
    });

    res.json({
      success: true,
      data: {
        total,
        currency: result.rows[0]?.currency || 'USD',
        breakdown
      }
    });
  } catch (error) {
    logger.error('Error calculating pricing:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate pricing'
    });
  }
}));

// Validation schema for booking creation
const createBookingSchema = Joi.object({
  customerId: Joi.string().uuid().optional(),
  serviceId: Joi.string().uuid().required(),
  flightNumber: Joi.string().required(),
  airline: Joi.string().required(),
  departureAirport: Joi.string().length(3).required(),
  arrivalAirport: Joi.string().length(3).required(),
  flightDate: Joi.date().iso().required(),
  estimatedArrival: Joi.date().iso(),
  serviceType: Joi.string().valid('arrival', 'departure', 'transit').required(),
  passengerCount: Joi.number().integer().min(1).required(),
  passengers: Joi.array().items(
    Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      age: Joi.number().integer().min(0).max(120),
      specialRequirements: Joi.array().items(Joi.string()),
      contactNumber: Joi.string()
    })
  ).required(),
  specialRequirements: Joi.array().items(Joi.string()),
  meetingPoint: Joi.string().required()
});

// Generate unique booking reference
const generateBookingReference = (): string => {
  const prefix = 'ACG';
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * @route POST /api/customer/v1/bookings
 * @desc Create a new booking (public)
 * @access Public
 */
router.post('/bookings', asyncHandler(async (req, res): Promise<void> => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    const { error, value } = createBookingSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    // Verify service exists and is active
    const serviceResult = await query(
      'SELECT * FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3',
      [value.serviceId, tenantId, 'active']
    );

    if (serviceResult.rows.length === 0) {
      return sendNotFoundError(res, 'Service not found or inactive');
    }

    const service = serviceResult.rows[0];
    const bookingId = uuidv4();
    const bookingReference = generateBookingReference();

    // Create customer on the fly if customerId is provided but doesn't exist
    // Or generate a new customer from passenger information
    let customerId = value.customerId;

    if (customerId) {
      // Check if customer exists
      const customerCheck = await query(
        'SELECT id FROM customers WHERE id = $1',
        [customerId]
      );

      if (customerCheck.rows.length === 0) {
        // Customer doesn't exist, create it
        const primaryPassenger = value.passengers[0];
        await query(
          `INSERT INTO customers (id, tenant_id, first_name, last_name, email, phone, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())`,
          [
            customerId,
            tenantId,
            primaryPassenger.firstName,
            primaryPassenger.lastName,
            `${primaryPassenger.firstName.toLowerCase()}.${primaryPassenger.lastName.toLowerCase()}@customer.temp`,
            primaryPassenger.contactNumber || '******-000-0000'
          ]
        );
      }
    } else {
      // Generate new customer from passenger information
      customerId = uuidv4();
      const primaryPassenger = value.passengers[0];
      await query(
        `INSERT INTO customers (id, tenant_id, first_name, last_name, email, phone, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())`,
        [
          customerId,
          tenantId,
          primaryPassenger.firstName,
          primaryPassenger.lastName,
          `${primaryPassenger.firstName.toLowerCase()}.${primaryPassenger.lastName.toLowerCase()}@customer.temp`,
          primaryPassenger.contactNumber || '******-000-0000'
        ]
      );
    }

    // Create booking in database
    const bookingResult = await query(
      `INSERT INTO bookings (
        id, tenant_id, customer_id, service_id, booking_reference, status,
        flight_number, airline, departure_airport, arrival_airport, flight_date,
        estimated_arrival, service_type, passenger_count, passengers,
        special_requirements, meeting_point, base_price, total_price,
        currency, payment_status, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, NOW(), NOW()
      ) RETURNING *`,
      [
        bookingId,
        tenantId,
        customerId,
        value.serviceId,
        bookingReference,
        'confirmed',
        value.flightNumber,
        value.airline,
        value.departureAirport,
        value.arrivalAirport,
        value.flightDate,
        value.estimatedArrival,
        value.serviceType,
        value.passengerCount,
        JSON.stringify(value.passengers),
        JSON.stringify(value.specialRequirements || []),
        value.meetingPoint,
        service.base_price,
        service.base_price * value.passengerCount,
        service.currency,
        'pending'
      ]
    );

    return sendSuccess(res, bookingResult.rows[0], 201);
  } catch (error) {
    logger.error('Error creating booking:', error);
    return sendInternalServerError(res, 'Failed to create booking');
  }
}));

/**
 * @route GET /api/customer/v1/bookings
 * @desc Get customer bookings by customer ID or email (public)
 * @access Public
 */
router.get('/bookings', asyncHandler(async (req, res): Promise<void> => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    const { customerId, email } = req.query;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    if (!customerId && !email) {
      return sendValidationError(res, 'Customer ID or email is required');
    }

    let whereClause = 'b.tenant_id = $1';
    let params: any[] = [tenantId];

    if (customerId) {
      whereClause += ' AND b.customer_id = $2';
      params.push(customerId);
    } else if (email) {
      whereClause += ' AND c.email = $2';
      params.push(email);
    }

    const bookingsResult = await query(
      `SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name, c.email as customer_email,
              s.name as service_name, s.category as service_category
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       WHERE ${whereClause}
       ORDER BY b.created_at DESC`,
      params
    );

    return sendSuccess(res, bookingsResult.rows);
  } catch (error) {
    logger.error('Error fetching bookings:', error);
    return sendInternalServerError(res, 'Failed to fetch bookings');
  }
}));

/**
 * @route GET /api/customer/v1/bookings/:id
 * @desc Get booking details by ID (public)
 * @access Public
 */
router.get('/bookings/:id', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { id } = req.params;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    const bookingResult = await query(
      `SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.description as service_description, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name, u.email as agent_email
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       LEFT JOIN users u ON b.assigned_agent_id = u.id AND u.tenant_id = b.tenant_id
       WHERE b.id = $1 AND b.tenant_id = $2`,
      [id, tenantId]
    );

    if (bookingResult.rows.length === 0) {
      return sendNotFoundError(res, 'Booking not found');
    }

    // Get booking activities
    const activitiesResult = await query(
      `SELECT ba.*, u.first_name, u.last_name, u.role
       FROM booking_activities ba
       LEFT JOIN users u ON ba.user_id = u.id
       WHERE ba.booking_id = $1
       ORDER BY ba.created_at DESC`,
      [id]
    );

    return sendSuccess(res, {
      booking: bookingResult.rows[0],
      activities: activitiesResult.rows
    });
  } catch (error) {
    logger.error('Error fetching booking:', error);
    return sendInternalServerError(res, 'Failed to fetch booking');
  }
}));

/**
 * @route POST /api/customer/v1/payments/create-payment-intent
 * @desc Create payment intent for customer booking (public)
 * @access Public
 */
router.post('/payments/create-payment-intent', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { amount, currency, provider, booking_data } = req.body;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    if (!amount || !currency || !provider) {
      res.status(400).json({
        success: false,
        error: 'Amount, currency, and provider are required'
      });
      return;
    }

    // Create payment intent using the payment service
    const paymentIntent = await paymentService.createPaymentIntent({
      amount: parseFloat(amount),
      currency: currency.toLowerCase(),
      provider: provider as 'stripe' | 'razorpay',
      metadata: {
        tenant_id: tenantId,
        booking_data: JSON.stringify(booking_data)
      }
    });

    res.json({
      success: true,
      data: paymentIntent
    });
  } catch (error) {
    logger.error('Error creating payment intent:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment intent'
    });
  }
}));

/**
 * @route POST /api/customer/v1/payments/verify-payment
 * @desc Verify payment for customer booking (public)
 * @access Public
 */
router.post('/payments/verify-payment', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { payment_id, provider, verification_data } = req.body;

    if (!payment_id || !provider) {
      res.status(400).json({
        success: false,
        error: 'Payment ID and provider are required'
      });
      return;
    }

    const isValid = await paymentService.verifyPayment(payment_id, provider, verification_data);

    if (isValid) {
      res.json({
        success: true,
        message: 'Payment verified successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Payment verification failed'
      });
    }
  } catch (error) {
    logger.error('Error verifying payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify payment'
    });
  }
}));

/**
 * @route GET /api/customer/v1/bookings/reference/:reference
 * @desc Get booking details by reference (public)
 * @access Public
 */
router.get('/bookings/reference/:reference', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { reference } = req.params;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    if (!reference) {
      return sendValidationError(res, 'Booking reference is required');
    }

    const bookingResult = await query(
      `SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.description as service_description, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name, u.email as agent_email
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       LEFT JOIN users u ON b.assigned_agent_id = u.id AND u.tenant_id = b.tenant_id
       WHERE b.booking_reference = $1 AND b.tenant_id = $2`,
      [reference, tenantId]
    );

    if (bookingResult.rows.length === 0) {
      return sendNotFoundError(res, 'Booking not found');
    }

    // Get booking activities
    const activitiesResult = await query(
      `SELECT ba.*, u.first_name, u.last_name, u.role
       FROM booking_activities ba
       LEFT JOIN users u ON ba.user_id = u.id
       WHERE ba.booking_id = $1
       ORDER BY ba.created_at DESC`,
      [bookingResult.rows[0].id]
    );

    return sendSuccess(res, {
      booking: bookingResult.rows[0],
      activities: activitiesResult.rows
    });
  } catch (error) {
    logger.error('Error fetching booking by reference:', error);
    return sendInternalServerError(res, 'Failed to fetch booking');
  }
}));



/**
 * @route GET /api/customer/v1/airports/:code/terminals
 * @desc Get available terminals for a specific airport and service type
 * @access Public
 */
router.get('/airports/:code/terminals', asyncHandler(async (req, res): Promise<void> => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    const { code } = req.params;
    const serviceType = req.query.type as string; // arrival or departure

    if (!tenantId) {
      res.status(400).json({
        success: false,
        error: 'Tenant ID is required'
      });
      return;
    }

    if (!code || code.length !== 3) {
      res.status(400).json({
        success: false,
        error: 'Valid airport code is required'
      });
      return;
    }

    let serviceTypeFilter = '';
    const params: any[] = [tenantId, code.toUpperCase()];
    let paramIndex = 3;

    if (serviceType) {
      serviceTypeFilter += ` AND s.type = $${paramIndex}`;
      params.push(serviceType);
      paramIndex++;
    }

    // Get airport ID first
    const airportResult = await query(
      'SELECT id FROM airports WHERE iata_code = $1',
      [code.toUpperCase()]
    );

    if (airportResult.rows.length === 0) {
      return sendNotFoundError(res, 'Airport not found');
    }

    const airportId = airportResult.rows[0].id;

    // Get terminals that have services for this airport and tenant
    let terminalsQuery = `
      SELECT DISTINCT
         at.terminal_code,
         at.terminal_name,
         at.facilities,
         at.gates,
         COUNT(s.id) as service_count,
         MIN(s.base_price) as min_price
       FROM airport_terminals at
       LEFT JOIN services s ON s.tenant_id = $1::uuid
         AND s.status = 'active'
         AND s.available_airports::jsonb ? $2
         AND s.available_terminals->>$2 IS NOT NULL
         AND s.available_terminals->>$2::text LIKE '%' || at.terminal_code || '%'`;

    let queryParams = [tenantId, code.toUpperCase()];
    let terminalParamIndex = 3;

    if (serviceType) {
      terminalsQuery += ` AND s.type = $${terminalParamIndex}`;
      queryParams.push(serviceType);
      terminalParamIndex++;
    }

    terminalsQuery += `
       WHERE at.airport_id = $${terminalParamIndex}::uuid
       GROUP BY at.terminal_code, at.terminal_name, at.facilities, at.gates
       HAVING COUNT(s.id) > 0
       ORDER BY at.terminal_code`;

    queryParams.push(airportId);

    const terminalsResult = await query(terminalsQuery, queryParams);

    // If no terminals found with services, get all terminals for this airport
    let allTerminalsResult = terminalsResult;
    if (terminalsResult.rows.length === 0) {
      allTerminalsResult = await query(
        `SELECT
           at.terminal_code,
           at.terminal_name,
           at.facilities,
           at.gates,
           0 as service_count,
           2500 as min_price
         FROM airport_terminals at
         WHERE at.airport_id = $1::uuid
         ORDER BY at.terminal_code`,
        [airportId]
      );
    }

    // Use the appropriate result set
    const finalResult = terminalsResult.rows.length > 0 ? terminalsResult : allTerminalsResult;

    // If no terminals found in database, provide default terminals
    let result: any[];
    if (finalResult.rows.length === 0) {
      const defaultTerminals = [
        { terminal_code: 'T1', terminal_name: 'Terminal 1', facilities: {}, gates: [], service_count: 0, min_price: 2500 },
        { terminal_code: 'T2', terminal_name: 'Terminal 2', facilities: {}, gates: [], service_count: 0, min_price: 2500 },
        { terminal_code: 'T3', terminal_name: 'Terminal 3', facilities: {}, gates: [], service_count: 0, min_price: 2500 }
      ];

      result = defaultTerminals.map((terminal: any) => ({
        terminal_code: terminal.terminal_code,
        terminal_name: terminal.terminal_name,
        facilities: terminal.facilities,
        gates: terminal.gates,
        service_count: terminal.service_count,
        min_price: terminal.min_price,
        currency: 'INR'
      }));
    } else {
      result = finalResult.rows.map((terminal: any) => ({
        terminal_code: terminal.terminal_code,
        terminal_name: terminal.terminal_name,
        facilities: terminal.facilities || {},
        gates: terminal.gates || [],
        service_count: parseInt(terminal.service_count) || 0,
        min_price: parseFloat(terminal.min_price) || 2500,
        currency: 'INR'
      }));
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error fetching airport terminals:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}));

/**
 * @route GET /api/customer/v1/airports/:code/terminals/:terminal/services
 * @desc Get available services for a specific airport, terminal, and service type
 * @access Public
 */
router.get('/airports/:code/terminals/:terminal/services', asyncHandler(async (req, res): Promise<void> => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    const { code, terminal } = req.params;
    const category = req.query.category as string;
    const type = req.query.type as string;
    const passengers = parseInt(req.query.passengers as string) || 1;

    if (!tenantId) {
      res.status(400).json({
        success: false,
        error: 'Tenant ID is required'
      });
      return;
    }

    if (!code || code.length !== 3) {
      res.status(400).json({
        success: false,
        error: 'Valid airport code is required'
      });
      return;
    }

    let serviceFilter = '';
    const params: any[] = [tenantId, code.toUpperCase(), terminal.toUpperCase()];
    let paramIndex = 4;

    if (category) {
      serviceFilter += ` AND s.category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (type) {
      serviceFilter += ` AND s.type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    if (passengers > 1) {
      serviceFilter += ` AND s.max_passengers >= $${paramIndex}`;
      params.push(passengers);
      paramIndex++;
    }

    // Get services available at this specific airport and terminal
    let servicesQuery = `
      SELECT DISTINCT
         s.id, s.name, s.description, s.category, s.type,
         s.base_price, s.currency, s.duration_minutes, s.max_passengers,
         s.inclusions, s.requirements
       FROM services s
       WHERE s.tenant_id = $1::uuid
         AND s.status = 'active'
         AND s.available_airports::jsonb ? $2
         AND s.available_terminals->>$2 IS NOT NULL
         AND s.available_terminals->>$2::text LIKE '%' || $3 || '%'
         AND s.max_passengers >= $4::integer`;

    let servicesParams = [tenantId, code.toUpperCase(), terminal, passengers];
    let servicesParamIndex = 5;

    if (category) {
      servicesQuery += ` AND s.category = $${servicesParamIndex}`;
      servicesParams.push(category);
      servicesParamIndex++;
    }

    if (type) {
      servicesQuery += ` AND s.type = $${servicesParamIndex}`;
      servicesParams.push(type);
      servicesParamIndex++;
    }

    servicesQuery += ` ORDER BY s.category, s.base_price ASC`;

    const result = await query(servicesQuery, servicesParams);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    logger.error('Error fetching terminal services:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}));

/**
 * @route POST /api/customer/v1/flights/lookup
 * @desc Get flight information and time slot suggestions
 * @access Public
 */
router.post('/flights/lookup', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { flight_number, flight_date, service_type } = req.body;

    if (!flight_number) {
      res.status(400).json({
        success: false,
        error: 'Flight number is required'
      });
      return;
    }

    if (!service_type || !['arrival', 'departure'].includes(service_type)) {
      res.status(400).json({
        success: false,
        error: 'Valid service type (arrival or departure) is required'
      });
      return;
    }

    const result = await flightInformationService.processFlightInformation(
      flight_number,
      flight_date,
      service_type
    );

    if (result.error) {
      res.status(404).json({
        success: false,
        error: result.error
      });
      return;
    }

    res.json({
      success: true,
      data: {
        flight: result.flightData,
        time_slot_suggestions: result.timeSlotSuggestions
      }
    });
  } catch (error) {
    logger.error('Error in flight lookup:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}));

/**
 * @route GET /api/customer/v1/flights/:flightNumber/status
 * @desc Get real-time flight status
 * @access Public
 */
router.get('/flights/:flightNumber/status', asyncHandler(async (req, res): Promise<void> => {
  try {
    const { flightNumber } = req.params;
    const date = req.query.date as string;

    const flightData = await flightInformationService.fetchFlightInfo(flightNumber, date);

    if (!flightData) {
      res.status(404).json({
        success: false,
        error: 'Flight not found'
      });
      return;
    }

    res.json({
      success: true,
      data: {
        flight_number: flightData.flight_number,
        airline: flightData.airline_name,
        status: flightData.flight_status,
        scheduled_departure: flightData.scheduled_departure,
        scheduled_arrival: flightData.scheduled_arrival,
        estimated_departure: flightData.estimated_departure,
        estimated_arrival: flightData.estimated_arrival,
        actual_departure: flightData.actual_departure,
        actual_arrival: flightData.actual_arrival,
        departure_airport: flightData.departure_airport,
        arrival_airport: flightData.arrival_airport,
        departure_terminal: flightData.departure_terminal,
        arrival_terminal: flightData.arrival_terminal,
        gate: flightData.gate,
        baggage_belt: flightData.baggage_belt
      }
    });
  } catch (error) {
    logger.error('Error fetching flight status:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}));

export default router;
