import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { query } from '../services/database';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';

const router = express.Router();

// Validation schemas
const createAgentSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  employeeId: Joi.string().optional(),
  specializations: Joi.array().items(Joi.string()).default([]),
  languages: Joi.array().items(Joi.string()).default([]),
  airports: Joi.array().items(Joi.string().length(3)).default([])
});

const updateAgentSchema = Joi.object({
  employeeId: Joi.string().optional(),
  status: Joi.string().valid('available', 'busy', 'offline').optional(),
  specializations: Joi.array().items(Joi.string()).optional(),
  languages: Joi.array().items(Joi.string()).optional(),
  airports: Joi.array().items(Joi.string().length(3)).optional(),
  currentLocation: Joi.object().optional()
});

/**
 * Get all agents for the tenant
 */
router.get('/', asyncHandler(async (req: TenantRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const status = req.query.status as string;

  let whereClause = 'WHERE a.tenant_id = $1';
  const params = [req.user!.tenant_id];

  if (status) {
    whereClause += ' AND a.status = $2';
    params.push(status);
  }

  const agentsResult = await query(
    `SELECT 
       a.id, a.employee_id, a.status, a.current_location, a.specializations,
       a.languages, a.airports, a.rating, a.total_services, a.created_at,
       u.first_name, u.last_name, u.email,
       COUNT(b.id) as active_bookings
     FROM agents a
     JOIN users u ON a.user_id = u.id
     LEFT JOIN bookings b ON a.user_id = b.assigned_agent_id AND b.status IN ('confirmed', 'in_progress')
     ${whereClause}
     GROUP BY a.id, u.id
     ORDER BY a.created_at DESC
     LIMIT $${params.length + 1} OFFSET $${params.length + 2}`,
    [...params, limit, offset]
  );

  const countResult = await query(
    `SELECT COUNT(*) as total FROM agents a WHERE a.tenant_id = $1`,
    [req.user!.tenant_id]
  );

  const totalItems = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(totalItems / limit);

  res.json({
    success: true,
    data: {
      agents: agentsResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit
      }
    }
  });
}));

/**
 * Get single agent details
 */
router.get('/:id', asyncHandler(async (req: TenantRequest, res) => {
  const agentResult = await query(
    `SELECT
       a.*, u.first_name, u.last_name, u.email
     FROM agents a
     JOIN users u ON a.user_id = u.id
     WHERE a.id = $1 AND a.tenant_id = $2`,
    [req.params.id, req.user!.tenant_id]
  );

  if (agentResult.rows.length === 0) {
    return res.status(404).json({ error: 'Agent not found' });
  }

  // Get recent bookings for this agent
  const bookingsResult = await query(
    `SELECT 
       b.id, b.booking_reference, b.status, b.flight_number, b.airline,
       b.flight_date, b.total_price, b.created_at,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       s.name as service_name
     FROM bookings b
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE b.assigned_agent_id = $1
     ORDER BY b.created_at DESC
     LIMIT 10`,
    [agentResult.rows[0].user_id]
  );

  return res.json({
    success: true,
    data: {
      agent: agentResult.rows[0],
      recentBookings: bookingsResult.rows
    }
  });
}));

/**
 * Create new agent
 */
router.post('/',
  requirePermission('create_agent'),
  asyncHandler(async (req: TenantRequest, res) => {
    const { error, value } = createAgentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if user exists and is in the same tenant
    const userResult = await query(
      'SELECT id, role FROM users WHERE id = $1 AND tenant_id = $2',
      [value.userId, req.user!.tenant_id]
    );

    if (userResult.rows.length === 0) {
      return res.status(400).json({ error: 'User not found or not in the same tenant' });
    }

    // Check if agent already exists for this user
    const existingAgent = await query(
      'SELECT id FROM agents WHERE user_id = $1 AND tenant_id = $2',
      [value.userId, req.user!.tenant_id]
    );

    if (existingAgent.rows.length > 0) {
      return res.status(400).json({ error: 'Agent already exists for this user' });
    }

    const agentId = uuidv4();
    const result = await query(
      `INSERT INTO agents (
        id, tenant_id, user_id, employee_id, status, specializations,
        languages, airports, rating, total_services, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()) RETURNING *`,
      [
        agentId,
        req.user!.tenant_id,
        value.userId,
        value.employeeId || null,
        'offline',
        JSON.stringify(value.specializations),
        JSON.stringify(value.languages),
        JSON.stringify(value.airports),
        5.0, // Default rating
        0    // Initial service count
      ]
    );

    return res.status(201).json({
      success: true,
      data: result.rows[0]
    });
  })
);

/**
 * Update agent
 */
router.put('/:id',
  requirePermission('update_agent'),
  asyncHandler(async (req: TenantRequest, res) => {
    const { error, value } = updateAgentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const updateFields: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (value.employeeId !== undefined) {
      updateFields.push(`employee_id = $${paramIndex++}`);
      params.push(value.employeeId);
    }

    if (value.status) {
      updateFields.push(`status = $${paramIndex++}`);
      params.push(value.status);
    }

    if (value.specializations) {
      updateFields.push(`specializations = $${paramIndex++}`);
      params.push(JSON.stringify(value.specializations));
    }

    if (value.languages) {
      updateFields.push(`languages = $${paramIndex++}`);
      params.push(JSON.stringify(value.languages));
    }

    if (value.airports) {
      updateFields.push(`airports = $${paramIndex++}`);
      params.push(JSON.stringify(value.airports));
    }

    if (value.currentLocation) {
      updateFields.push(`current_location = $${paramIndex++}`);
      params.push(JSON.stringify(value.currentLocation));
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updateFields.push(`updated_at = NOW()`);
    params.push(req.params.id, req.user!.tenant_id);

    const result = await query(
      `UPDATE agents SET ${updateFields.join(', ')} 
       WHERE id = $${paramIndex++} AND tenant_id = $${paramIndex++} 
       RETURNING *`,
      params
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    return res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

/**
 * Delete agent
 */
router.delete('/:id',
  requirePermission('delete_agent'),
  asyncHandler(async (req: TenantRequest, res) => {
    // Check if agent has any active bookings
    const activeBookingsCheck = await query(
      `SELECT COUNT(*) as count FROM bookings b
       JOIN agents a ON b.assigned_agent_id = a.user_id
       WHERE a.id = $1 AND b.status IN ('confirmed', 'in_progress')`,
      [req.params.id]
    );

    if (parseInt(activeBookingsCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete agent with active bookings' 
      });
    }

    const result = await query(
      'DELETE FROM agents WHERE id = $1 AND tenant_id = $2 RETURNING id',
      [req.params.id, req.user!.tenant_id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    return res.json({
      success: true,
      message: 'Agent deleted successfully'
    });
  })
);

export default router;
