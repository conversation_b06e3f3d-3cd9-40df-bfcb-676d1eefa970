import express, { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { query } from '../services/database';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenant';
import { requirePermission } from '../middleware/auth';

const router = express.Router();

// Validation schemas
const createServiceSchema = Joi.object({
  name: Joi.string().required(),
  description: Joi.string().required(),
  category: Joi.string().valid('meet_greet', 'fast_track', 'lounge_access', 'vip_terminal', 'transfer', 'porter').required(),
  type: Joi.string().valid('arrival', 'departure', 'transit').required(),
  basePrice: Joi.number().positive().required(),
  currency: Joi.string().length(3).required(),
  durationMinutes: Joi.number().integer().positive().required(),
  maxPassengers: Joi.number().integer().positive().required(),
  availableAirports: Joi.array().items(Joi.string().length(3)).required(),
  availableTerminals: Joi.object().pattern(Joi.string().length(3), Joi.array().items(Joi.string())).default({}),
  requirements: Joi.array().items(Joi.string()),
  inclusions: Joi.array().items(Joi.string())
});

const updateServiceSchema = Joi.object({
  name: Joi.string(),
  description: Joi.string(),
  category: Joi.string().valid('meet_greet', 'fast_track', 'lounge_access', 'vip_terminal', 'transfer', 'porter'),
  type: Joi.string().valid('arrival', 'departure', 'transit'),
  basePrice: Joi.number().positive(),
  currency: Joi.string().length(3),
  durationMinutes: Joi.number().integer().positive(),
  maxPassengers: Joi.number().integer().positive(),
  availableAirports: Joi.array().items(Joi.string().length(3)),
  availableTerminals: Joi.object().pattern(Joi.string().length(3), Joi.array().items(Joi.string())),
  requirements: Joi.array().items(Joi.string()),
  inclusions: Joi.array().items(Joi.string()),
  status: Joi.string().valid('active', 'inactive')
});

// Create new service
router.post('/',
  requirePermission('create_service'),
  asyncHandler(async (req: TenantRequest, res, next): Promise<void | Response> => {
    const { error, value } = createServiceSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const serviceId = uuidv4();
    const result = await query(
      `INSERT INTO services (
        id, tenant_id, name, description, category, type, base_price, currency,
        duration_minutes, max_passengers, available_airports, available_terminals,
        requirements, inclusions, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW()) RETURNING *`,
      [
        serviceId,
        req.user!.tenant_id,
        value.name,
        value.description,
        value.category,
        value.type,
        value.basePrice,
        value.currency,
        value.durationMinutes,
        value.maxPassengers,
        JSON.stringify(value.availableAirports),
        JSON.stringify(value.availableTerminals || {}),
        JSON.stringify(value.requirements || []),
        JSON.stringify(value.inclusions || []),
        'active'
      ]
    );

    res.status(201).json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Get all services with filtering
router.get('/', asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;

  // Build filter conditions
  const filters: string[] = ['tenant_id = $1'];
  const params: any[] = [req.user!.tenant_id];
  let paramIndex = 2;

  if (req.query.category) {
    filters.push(`category = $${paramIndex}`);
    params.push(req.query.category);
    paramIndex++;
  }

  if (req.query.type) {
    filters.push(`type = $${paramIndex}`);
    params.push(req.query.type);
    paramIndex++;
  }

  if (req.query.status) {
    filters.push(`status = $${paramIndex}`);
    params.push(req.query.status);
    paramIndex++;
  }

  if (req.query.airport) {
    filters.push(`available_airports::jsonb ? $${paramIndex}`);
    params.push(req.query.airport);
    paramIndex++;
  }

  const whereClause = `WHERE ${filters.join(' AND ')}`;

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total FROM services ${whereClause}`,
    params
  );

  // Get services
  const servicesResult = await query(
    `SELECT * FROM services ${whereClause}
     ORDER BY created_at DESC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
    [...params, limit, offset]
  );

  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      services: servicesResult.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
}));

// Get single service
router.get('/:id', asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
  const serviceResult = await query(
    'SELECT * FROM services WHERE id = $1 AND tenant_id = $2',
    [req.params.id, req.user!.tenant_id]
  );

  if (serviceResult.rows.length === 0) {
    return res.status(404).json({ error: 'Service not found' });
  }

  // Get recent bookings for this service
  const bookingsResult = await query(
    `SELECT COUNT(*) as total_bookings,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
            AVG(CASE WHEN status = 'completed' THEN total_price END) as avg_price
     FROM bookings 
     WHERE service_id = $1`,
    [req.params.id]
  );

  res.json({
    success: true,
    data: {
      service: serviceResult.rows[0],
      statistics: bookingsResult.rows[0]
    }
  });
}));

// Update service
router.put('/:id',
  requirePermission('update_service'),
  asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
    const { error, value } = updateServiceSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if service exists
    const existingService = await query(
      'SELECT id FROM services WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (existingService.rows.length === 0) {
      return res.status(404).json({ error: 'Service not found' });
    }

    // Build update query dynamically
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    Object.entries(value).forEach(([key, val]) => {
      if (val !== undefined) {
        const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        if (['available_airports', 'available_terminals', 'requirements', 'inclusions'].includes(dbKey)) {
          updates.push(`${dbKey} = $${paramIndex}`);
          params.push(JSON.stringify(val));
        } else {
          updates.push(`${dbKey} = $${paramIndex}`);
          params.push(val);
        }
        paramIndex++;
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updates.push('updated_at = NOW()');
    params.push(req.params.id, req.user!.tenant_id);

    const updateQuery = `
      UPDATE services 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
      RETURNING *
    `;

    const result = await query(updateQuery, params);

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Delete service
router.delete('/:id',
  requirePermission('delete_service'),
  asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
    // Check if service has any bookings
    const bookingsCheck = await query(
      'SELECT COUNT(*) as count FROM bookings WHERE service_id = $1',
      [req.params.id]
    );

    if (parseInt(bookingsCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete service with existing bookings. Set status to inactive instead.' 
      });
    }

    const result = await query(
      'DELETE FROM services WHERE id = $1 AND tenant_id = $2 RETURNING id',
      [req.params.id, req.user!.tenant_id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Service not found' });
    }

    res.json({
      success: true,
      message: 'Service deleted successfully'
    });
  })
);

// Get available services for booking (public endpoint for booking widget)
router.get('/available/:airport/:type', asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
  const { airport, type } = req.params;
  const passengerCount = parseInt(req.query.passengers as string) || 1;

  const result = await query(
    `SELECT id, name, description, category, base_price, currency, duration_minutes, 
            max_passengers, inclusions, requirements
     FROM services 
     WHERE tenant_id = $1 
       AND status = 'active' 
       AND type = $2 
       AND available_airports::jsonb ? $3
       AND max_passengers >= $4
     ORDER BY base_price ASC`,
    [req.user!.tenant_id, type, airport, passengerCount]
  );

  res.json({
    success: true,
    data: result.rows
  });
}));

// Get service categories and statistics
router.get('/stats/categories', asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
  const result = await query(
    `SELECT 
       category,
       COUNT(*) as total_services,
       COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services,
       AVG(base_price) as avg_price,
       MIN(base_price) as min_price,
       MAX(base_price) as max_price
     FROM services 
     WHERE tenant_id = $1
     GROUP BY category
     ORDER BY category`,
    [req.user!.tenant_id]
  );

  res.json({
    success: true,
    data: result.rows
  });
}));

// Duplicate service
router.post('/:id/duplicate',
  requirePermission('create_service'),
  asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
    // Get original service
    const originalService = await query(
      'SELECT * FROM services WHERE id = $1 AND tenant_id = $2',
      [req.params.id, req.user!.tenant_id]
    );

    if (originalService.rows.length === 0) {
      return res.status(404).json({ error: 'Service not found' });
    }

    const service = originalService.rows[0];
    const newServiceId = uuidv4();

    // Create duplicate with modified name
    const result = await query(
      `INSERT INTO services (
        id, tenant_id, name, description, category, type, base_price, currency,
        duration_minutes, max_passengers, available_airports, requirements,
        inclusions, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()) RETURNING *`,
      [
        newServiceId,
        req.user!.tenant_id,
        `${service.name} (Copy)`,
        service.description,
        service.category,
        service.type,
        service.base_price,
        service.currency,
        service.duration_minutes,
        service.max_passengers,
        service.available_airports,
        service.requirements,
        service.inclusions,
        'inactive' // Start as inactive for review
      ]
    );

    res.status(201).json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Get airports available for specific service filters (optimized for customer portal)
router.get('/airports/available',
  asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
    const category = req.query.category as string;
    const type = req.query.type as string;
    const passengerCount = parseInt(req.query.passengers as string) || 1;

    let serviceFilter = '';
    const params: any[] = [req.user!.tenant_id];
    let paramIndex = 2;

    if (category) {
      serviceFilter += ` AND s.category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (type) {
      serviceFilter += ` AND s.type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    if (passengerCount > 1) {
      serviceFilter += ` AND s.max_passengers >= $${paramIndex}`;
      params.push(passengerCount);
      paramIndex++;
    }

    // Get airports that have active services matching the criteria
    const airportsResult = await query(
      `SELECT DISTINCT
         a.iata_code,
         a.name,
         a.city,
         a.country,
         a.timezone,
         COUNT(s.id) as service_count,
         MIN(s.base_price) as min_price,
         MAX(s.base_price) as max_price,
         s.currency
       FROM airports a
       JOIN tenant_airports ta ON a.id = ta.airport_id
       JOIN services s ON s.tenant_id = ta.tenant_id
       WHERE ta.tenant_id = $1
         AND ta.status = 'active'
         AND a.status = 'active'
         AND s.status = 'active'
         AND s.available_airports::jsonb ? a.iata_code
         ${serviceFilter}
       GROUP BY a.iata_code, a.name, a.city, a.country, a.timezone, s.currency
       ORDER BY a.name ASC`,
      params
    );

    res.json({
      success: true,
      data: airportsResult.rows
    });
  })
);

// Get service categories and types available for a specific airport
router.get('/filters/:airport',
  asyncHandler(async (req: TenantRequest, res): Promise<void | Response> => {
    const airport = req.params.airport.toUpperCase();
    const passengerCount = parseInt(req.query.passengers as string) || 1;

    // Validate airport code
    if (airport.length !== 3) {
      return res.status(400).json({
        success: false,
        error: 'Invalid airport code. Must be 3 characters.'
      });
    }

    // Get available categories and types for this airport
    const filtersResult = await query(
      `SELECT
         s.category,
         s.type,
         COUNT(*) as service_count,
         MIN(s.base_price) as min_price,
         MAX(s.base_price) as max_price,
         s.currency
       FROM services s
       JOIN tenant_airports ta ON s.tenant_id = ta.tenant_id
       JOIN airports a ON ta.airport_id = a.id
       WHERE s.tenant_id = $1
         AND s.status = 'active'
         AND ta.status = 'active'
         AND a.status = 'active'
         AND a.iata_code = $2
         AND s.available_airports::jsonb ? $2
         AND s.max_passengers >= $3
       GROUP BY s.category, s.type, s.currency
       ORDER BY s.category, s.type`,
      [req.user!.tenant_id, airport, passengerCount]
    );

    // Group by category
    const categories: any = {};
    filtersResult.rows.forEach(row => {
      if (!categories[row.category]) {
        categories[row.category] = {
          category: row.category,
          types: [],
          total_services: 0,
          price_range: { min: row.min_price, max: row.max_price, currency: row.currency }
        };
      }

      categories[row.category].types.push({
        type: row.type,
        service_count: parseInt(row.service_count),
        min_price: row.min_price,
        max_price: row.max_price,
        currency: row.currency
      });

      categories[row.category].total_services += parseInt(row.service_count);

      // Update price range
      if (row.min_price < categories[row.category].price_range.min) {
        categories[row.category].price_range.min = row.min_price;
      }
      if (row.max_price > categories[row.category].price_range.max) {
        categories[row.category].price_range.max = row.max_price;
      }
    });

    res.json({
      success: true,
      data: {
        airport: airport,
        categories: Object.values(categories)
      }
    });
  })
);

export default router;
