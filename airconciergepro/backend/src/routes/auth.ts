import express from 'express';
import bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { query } from '../services/database';
import { logger } from '../services/logger';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendUnauthorizedError,
  sendInternalServerError
} from '../utils/response';
import Joi from 'joi';

const router = express.Router();

// Validation schemas
const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  tenantName: Joi.string().required(),
  subdomain: Joi.string().alphanum().min(3).max(20).required()
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

// Generate JWT token
const generateToken = (userId: string): string => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  const expiresIn = process.env.JWT_EXPIRE || '7d';

  return (jwt as any).sign(
    { userId },
    secret,
    { expiresIn }
  );
};

// Register new tenant and admin user
router.post('/register', async (req, res): Promise<any> => {
  try {
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    const { email, password, firstName, lastName, tenantName, subdomain } = value;

    // Check if email already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return sendValidationError(res, 'Email already registered');
    }

    // Check if subdomain already exists
    const existingTenant = await query('SELECT id FROM tenants WHERE subdomain = $1', [subdomain]);
    if (existingTenant.rows.length > 0) {
      return sendValidationError(res, 'Subdomain already taken');
    }

    // Hash password
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(password, salt);

    // Create tenant
    const tenantId = uuidv4();
    const tenantResult = await query(
      `INSERT INTO tenants (id, name, subdomain, plan, status, settings, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW()) RETURNING *`,
      [
        tenantId,
        tenantName,
        subdomain,
        'trial',
        'active',
        JSON.stringify({
          features: ['basic_booking', 'customer_management'],
          limits: {
            monthly_bookings: 100,
            api_calls: 1000,
            locations: 1
          }
        })
      ]
    );

    // Create admin user
    const userId = uuidv4();
    const userResult = await query(
      `INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, status, permissions, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()) RETURNING id, email, first_name, last_name, role`,
      [
        userId,
        tenantId,
        email,
        passwordHash,
        firstName,
        lastName,
        'company_admin',
        'active',
        JSON.stringify(['all'])
      ]
    );

    const token = generateToken(userId);

    return sendSuccess(res, {
      token,
      user: userResult.rows[0],
      tenant: tenantResult.rows[0]
    }, 201);
  } catch (error) {
    logger.error('Registration error:', error);
    return sendInternalServerError(res, 'Registration failed');
  }
});

// Login
router.post('/login', async (req, res): Promise<any> => {
  try {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    const { email, password } = value;

    // Get user with tenant info
    const userResult = await query(
      `SELECT u.*, t.name as tenant_name, t.subdomain, t.plan, t.status as tenant_status
       FROM users u
       JOIN tenants t ON u.tenant_id = t.id
       WHERE u.email = $1`,
      [email]
    );

    if (userResult.rows.length === 0) {
      return sendUnauthorizedError(res, 'Invalid email or password');
    }

    const user = userResult.rows[0];

    // Check password
    logger.info(`Password received: ${password}`);
    logger.info(`Password hash from database: ${user.password_hash}`);
    
    // Verify hash generation
    const testHash = await bcrypt.hash(password, 12);
    logger.info(`Generated test hash: ${testHash}`);
    
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    logger.info(`bcrypt.compare result: ${isValidPassword}`);
    logger.info(`Hash matches: ${testHash === user.password_hash}`);
    if (!isValidPassword) {
      return sendUnauthorizedError(res, 'Invalid email or password');
    }

    // Check user status
    if (user.status !== 'active') {
      return sendUnauthorizedError(res, 'Account is not active');
    }

    // Check tenant status
    if (user.tenant_status === 'suspended') {
      return sendUnauthorizedError(res, 'Account is suspended');
    }

    // Update last login
    await query('UPDATE users SET last_login = NOW() WHERE id = $1', [user.id]);

    const token = generateToken(user.id);

    return sendSuccess(res, {
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        permissions: user.permissions
      },
      tenant: {
        id: user.tenant_id,
        name: user.tenant_name,
        subdomain: user.subdomain,
        plan: user.plan
      }
    });
  } catch (error) {
    logger.error('Login error:', error);
    return sendInternalServerError(res, 'Login failed');
  }
});

// Get current user
router.get('/me', async (req, res): Promise<any> => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = (jwt as any).verify(token, process.env.JWT_SECRET || 'fallback_secret') as any;
    
    const userResult = await query(
      `SELECT u.id, u.email, u.first_name, u.last_name, u.role, u.permissions,
              t.id as tenant_id, t.name as tenant_name, t.subdomain, t.plan
       FROM users u
       JOIN tenants t ON u.tenant_id = t.id
       WHERE u.id = $1 AND u.status = 'active'`,
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const user = userResult.rows[0];

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          permissions: user.permissions
        },
        tenant: {
          id: user.tenant_id,
          name: user.tenant_name,
          subdomain: user.subdomain,
          plan: user.plan
        }
      }
    });
  } catch (error) {
    logger.error('Get user error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
});

// Customer authentication endpoints for customer portal

// Customer login
router.post('/customer/login', async (req, res): Promise<any> => {
  try {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    const { email, password } = value;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    // Get customer with tenant info
    const customerResult = await query(
      `SELECT c.*, t.name as tenant_name, t.subdomain, t.plan, t.status as tenant_status
       FROM customers c
       JOIN tenants t ON c.tenant_id = t.id
       WHERE c.email = $1 AND c.tenant_id = $2`,
      [email, tenantId]
    );

    if (customerResult.rows.length === 0) {
      return sendUnauthorizedError(res, 'Invalid email or password');
    }

    const customer = customerResult.rows[0];

    // For customers, we'll use a simple password check or create a customer-specific auth system
    // For now, we'll allow login with any password for customers (this should be enhanced)

    // Check tenant status
    if (customer.tenant_status === 'suspended') {
      return sendUnauthorizedError(res, 'Service is temporarily unavailable');
    }

    const token = generateToken(customer.id);

    return sendSuccess(res, {
      token,
      customer: {
        id: customer.id,
        email: customer.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        phone: customer.phone
      },
      tenant: {
        id: customer.tenant_id,
        name: customer.tenant_name,
        subdomain: customer.subdomain,
        plan: customer.plan
      }
    });
  } catch (error) {
    logger.error('Customer login error:', error);
    return sendInternalServerError(res, 'Login failed');
  }
});

// Customer registration
router.post('/customer/register', async (req, res): Promise<any> => {
  try {
    const customerSchema = Joi.object({
      email: Joi.string().email().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      phone: Joi.string().required(),
      password: Joi.string().min(6).required()
    });

    const { error, value } = customerSchema.validate(req.body);
    if (error) {
      return sendValidationError(res, error.details[0].message);
    }

    const { email, firstName, lastName, phone, password } = value;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      return sendValidationError(res, 'Tenant ID is required');
    }

    // Check if customer already exists
    const existingCustomer = await query(
      'SELECT id FROM customers WHERE email = $1 AND tenant_id = $2',
      [email, tenantId]
    );

    if (existingCustomer.rows.length > 0) {
      return sendValidationError(res, 'Customer with this email already exists');
    }

    // Create customer (password is optional for customers in this system)
    const customerId = uuidv4();
    const result = await query(
      `INSERT INTO customers (
        id, tenant_id, email, first_name, last_name, phone,
        preferences, loyalty_points, total_bookings, total_spent, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()) RETURNING *`,
      [
        customerId,
        tenantId,
        email,
        firstName,
        lastName,
        phone,
        JSON.stringify({}),
        0,
        0,
        0
      ]
    );

    const token = generateToken(customerId);

    return sendSuccess(res, {
      token,
      customer: result.rows[0],
      message: 'Customer account created successfully'
    }, 201);
  } catch (error) {
    logger.error('Customer registration error:', error);
    return sendInternalServerError(res, 'Registration failed');
  }
});

export default router;
