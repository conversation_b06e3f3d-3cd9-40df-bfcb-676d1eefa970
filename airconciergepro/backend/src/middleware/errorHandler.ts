import { Request, Response, NextFunction } from 'express';
import { logger } from '../services/logger';

export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}

export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (err: AppError, req: Request, res: Response, next: NextFunction) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error('Error Handler:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // PostgreSQL errors
  if (err.name === 'error' && (err as any).code) {
    switch ((err as any).code) {
      case '23505': // Unique violation
        error = new AppError('Duplicate entry', 400);
        break;
      case '23503': // Foreign key violation
        error = new AppError('Referenced resource does not exist', 400);
        break;
      case '23514': // Check violation
        error = new AppError('Invalid data provided', 400);
        break;
      default:
        error = new AppError('Database error', 500);
    }
  }

  // Default to 500 server error
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';

  res.status(statusCode).json({
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

import { Response as ExpressResponse } from 'express';

export const asyncHandler = <T = void>(
  fn: (req: Request, res: ExpressResponse, next: NextFunction) => Promise<T>
) => (req: Request, res: ExpressResponse, next: NextFunction): Promise<T | void> => {
  return Promise.resolve(fn(req, res, next)).catch(next);
};
