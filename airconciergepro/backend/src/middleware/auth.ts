import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { query } from '../services/database';
import { logger } from '../services/logger';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    tenant_id: string;
    role: string;
    permissions: string[];
  };
}

export const authMiddleware = async (req: AuthRequest, res: Response, next: NextFunction): Promise<any> => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret') as any;
    
    // Get user details from database
    const userResult = await query(
      'SELECT id, tenant_id, role, permissions, status FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid token. User not found.' });
    }

    const user = userResult.rows[0];
    
    if (user.status !== 'active') {
      return res.status(401).json({ error: 'Account is not active.' });
    }

    req.user = {
      id: user.id,
      tenant_id: user.tenant_id,
      role: user.role,
      permissions: user.permissions || []
    };

    next();
  } catch (error) {
    logger.error('Auth middleware error:', error);
    res.status(401).json({ error: 'Invalid token.' });
  }
};

export const requirePermission = (permission: string) => {
  return (req: AuthRequest, res: Response, next: NextFunction): any => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    // Check if user has the specific permission, "all" permission, or is super_admin
    if (!req.user.permissions.includes(permission) &&
        !req.user.permissions.includes('all') &&
        req.user.role !== 'super_admin') {
      return res.status(403).json({ error: 'Insufficient permissions.' });
    }

    next();
  };
};

export const requireRole = (roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): any => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient role permissions.' });
    }

    next();
  };
};

// Export alias for backward compatibility
export const authenticateToken = authMiddleware;
