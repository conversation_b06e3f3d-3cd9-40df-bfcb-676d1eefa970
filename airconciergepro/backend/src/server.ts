// Load environment variables first, before any other imports
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { WebSocketService, websocketService } from './services/websocketService';
import { Server } from 'socket.io';
import compression from 'compression';
import morgan from 'morgan';

// Route imports
import authRoutes from './routes/auth';
import bookingRoutes from './routes/bookings';
import enhancedBookingRoutes from './routes/enhancedBookings';
import customerRoutes from './routes/customers';
import customerApiRoutes from './routes/customerApi';
import serviceRoutes from './routes/services';
import tenantRoutes from './routes/tenants';
import analyticsRoutes from './routes/analytics';
import partnerRoutes from './routes/partners';
import mobileRoutes from './routes/mobile';
import airportRoutes from './routes/airports';
import terminalRoutes from './routes/terminals';
import realtimeRoutes from './routes/realtime';
import paymentsRoutes from './routes/payments';
import agentsRoutes from './routes/agents';

// Middleware imports
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware, AuthRequest } from './middleware/auth';
import { tenantMiddleware } from './middleware/tenant';
import { logger } from './services/logger';

// Service imports
import { dynamicPricingService } from './services/dynamicPricing';
import { flightInformationService } from './services/flightInformation';
import { airportManagementService } from './services/airportManagement';
import { notificationService } from './services/notification';

const app = express();
const server = createServer(app);
// Parse frontend URLs from environment variable
const frontendUrls = process.env.FRONTEND_URL
  ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
  : ["http://localhost:3000", "http://localhost:3001"];

// Initialize enhanced WebSocket service (this replaces the basic Socket.IO setup)
const wsService = new WebSocketService(server);
(global as any).websocketService = wsService;

// Make the Socket.IO instance available globally for backward compatibility
const io = wsService.io;
(global as any).io = io;

// Trust proxy for rate limiting behind load balancer
app.set('trust proxy', 1);

// Enhanced rate limiting with different tiers
const createRateLimit = (windowMs: number, max: number, message: string) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message, retryAfter: windowMs / 1000 },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}, endpoint: ${req.originalUrl}`);
      res.status(429).json({
        error: message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// Different rate limits for different endpoints
const generalLimiter = createRateLimit(15 * 60 * 1000, 100, 'Too many requests from this IP, please try again later.');
const authLimiter = createRateLimit(15 * 60 * 1000, 100, 'Too many authentication attempts, please try again later.');
const apiLimiter = createRateLimit(15 * 60 * 1000, 1000, 'API rate limit exceeded.');
const partnerLimiter = createRateLimit(15 * 60 * 1000, 8000, 'Partner API rate limit exceeded.');

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

app.use(cors({
  origin: frontendUrls,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID']
}));

app.use(compression());
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoints
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

app.get('/health/detailed', async (req, res) => {
  try {
    const checks = {
      database: false,
      flightAPI: false,
      pricing: false,
      notifications: false
    };

    // Add your health checks here
    // checks.database = await databaseHealthCheck();
    // checks.flightAPI = await flightInformationService.healthCheck();
    
    const allHealthy = Object.values(checks).every(check => check === true);
    
    res.status(allHealthy ? 200 : 503).json({
      status: allHealthy ? 'OK' : 'DEGRADED',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      checks
    });
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

// API versioning and documentation
app.get('/api', (req, res) => {
  res.json({
    name: 'AirConcierge Pro API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      v1: '/api/v1',
      partner: '/api/partner/v1',
      mobile: '/api/mobile/v1'
    }
  });
});

// API v1 health endpoint
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Enhanced API Routes with specific rate limiting

// Authentication routes (stricter rate limiting)
app.use('/api/v1/auth', authLimiter, authRoutes);

// Core API routes (standard rate limiting)
app.use('/api/v1/bookings', generalLimiter, authMiddleware, tenantMiddleware, bookingRoutes);
app.use('/api/v1/enhanced-bookings', generalLimiter, authMiddleware, tenantMiddleware, enhancedBookingRoutes);
app.use('/api/v1/customers', generalLimiter, authMiddleware, tenantMiddleware, customerRoutes);
app.use('/api/v1/services', generalLimiter, authMiddleware, tenantMiddleware, serviceRoutes);
app.use('/api/v1/tenants', generalLimiter, authMiddleware, tenantRoutes);
app.use('/api/v1/analytics', generalLimiter, authMiddleware, tenantMiddleware, analyticsRoutes);
app.use('/api/v1/airports', generalLimiter, authMiddleware, tenantMiddleware, airportRoutes);
app.use('/api/v1/terminals', generalLimiter, authMiddleware, tenantMiddleware, terminalRoutes);
app.use('/api/v1/payments', generalLimiter, authMiddleware, tenantMiddleware, paymentsRoutes);
app.use('/api/v1/agents', generalLimiter, authMiddleware, tenantMiddleware, agentsRoutes);
app.use('/api/v1/partners', generalLimiter, authMiddleware, tenantMiddleware, partnerRoutes);
app.use('/api/v1/realtime', generalLimiter, authMiddleware, tenantMiddleware, realtimeRoutes);

// Partner API routes (higher rate limits)
app.use('/api/partner/v1', partnerLimiter, partnerRoutes);

// Mobile API routes
app.use('/api/mobile/v1', apiLimiter, mobileRoutes);

// Customer API routes (public, no authentication required)
app.use('/api/customer/v1', generalLimiter, customerApiRoutes);

// New enhanced endpoints based on PRD requirements

// Availability API
app.post('/api/v1/availability/check', generalLimiter, authMiddleware, tenantMiddleware, async (req, res) => {
  try {
    const { serviceId, date, passengerCount } = req.body;
    
    // Implementation for availability checking
    res.json({
      success: true,
      data: {
        available: true,
        slots: [], // Available time slots
        capacity: 100
      }
    });
  } catch (error) {
    logger.error('Availability check error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Quote generation endpoint
app.post('/api/v1/quotes', generalLimiter, authMiddleware, tenantMiddleware, async (req: AuthRequest, res) => {
  try {
    const { serviceId, passengerCount, flightDate, customerId } = req.body;

    const pricingContext = {
      serviceId,
      tenantId: req.user!.tenant_id,
      passengerCount: passengerCount || 1,
      bookingDate: new Date(),
      serviceDate: new Date(flightDate),
      customerId
    };

    const quote = await dynamicPricingService.getQuote(pricingContext);

    res.json({
      success: true,
      data: quote
    });
  } catch (error) {
    logger.error('Quote generation error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Flight information endpoint
app.get('/api/v1/flights/:flightNumber', generalLimiter, authMiddleware, async (req, res) => {
  try {
    const { flightNumber } = req.params;
    const { date } = req.query;
    
    const flightInfo = await flightInformationService.getFlightInfo(
      flightNumber, 
      date ? new Date(date as string) : new Date()
    );
    
    res.json({
      success: true,
      data: flightInfo
    });
  } catch (error) {
    logger.error('Flight info error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Real-time operations dashboard endpoint
app.get('/api/v1/operations/dashboard', generalLimiter, authMiddleware, tenantMiddleware, async (req, res) => {
  try {
    // Implementation for real-time dashboard data
    res.json({
      success: true,
      data: {
        activeBookings: 0,
        completedToday: 0,
        revenue: 0,
        activeAgents: 0
      }
    });
  } catch (error) {
    logger.error('Dashboard data error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Enhanced WebSocket for real-time updates
io.use((socket, next) => {
  // Add authentication middleware for WebSocket connections
  const token = socket.handshake.auth.token;
  if (token) {
    // Verify token and attach user data
    next();
  } else {
    // Allow unauthenticated connections for customer portal
    console.log('WebSocket connection without authentication token - allowing for customer portal');
    next();
  }
});

io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  // Enhanced room management
  socket.on('join_tenant', (tenantId) => {
    socket.join(`tenant_${tenantId}`);
    socket.join(`operations_${tenantId}`);
    logger.info(`Socket ${socket.id} joined tenant ${tenantId}`);
  });

  socket.on('join_booking', (bookingId) => {
    socket.join(`booking_${bookingId}`);
    logger.info(`Socket ${socket.id} joined booking ${bookingId}`);
  });

  socket.on('join_agent', (agentId) => {
    socket.join(`agent_${agentId}`);
    logger.info(`Socket ${socket.id} joined agent channel ${agentId}`);
  });

  // Agent location tracking
  socket.on('agent_location_update', (data) => {
    socket.to(`tenant_${data.tenantId}`).emit('agent_location', {
      agentId: data.agentId,
      location: data.location,
      timestamp: new Date().toISOString()
    });
  });

  // Service status updates
  socket.on('service_status_update', (data) => {
    socket.to(`booking_${data.bookingId}`).emit('service_status', {
      bookingId: data.bookingId,
      status: data.status,
      timestamp: new Date().toISOString()
    });
  });

  // Flight updates
  socket.on('flight_status_update', (data) => {
    socket.to(`tenant_${data.tenantId}`).emit('flight_update', {
      flightNumber: data.flightNumber,
      status: data.status,
      gate: data.gate,
      terminal: data.terminal,
      timestamp: new Date().toISOString()
    });
  });

  socket.on('disconnect', (reason) => {
    logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
  });

  socket.on('error', (error) => {
    logger.error(`Socket error for ${socket.id}:`, error);
  });
});

// Make io available to routes
app.set('io', io);

// Global error handler with enhanced logging
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  if (error.type === 'entity.too.large') {
    return res.status(413).json({ error: 'Request entity too large' });
  }

  return res.status(500).json({
    error: 'Internal server error',
    requestId: req.headers['x-request-id'] || 'unknown'
  });
});

// Enhanced error handling middleware
app.use(errorHandler);

// 404 handler with logging
app.use('*', (req, res) => {
  logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ 
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
});

const PORT = process.env.PORT || 8000;
const HOST = process.env.HOST || '0.0.0.0';

// Graceful shutdown handling
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully...');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully...');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

server.listen(PORT, () => {
  logger.info(`🚀 AirConcierge Pro API Server started`);
  logger.info(`🌐 Server running on ${HOST}:${PORT}`);
  logger.info(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🔗 API Documentation: http://${HOST}:${PORT}/api`);
  logger.info(`❤️  Health Check: http://${HOST}:${PORT}/health`);

  // Initialize services
  initializeServices();
});

async function initializeServices() {
  try {
    logger.info('🔧 Initializing services...');
    
    // Initialize flight information service
    await flightInformationService.initialize();
    logger.info('✅ Flight Information Service initialized');

    // Initialize airport management service
    await airportManagementService.initialize();
    logger.info('✅ Airport Management Service initialized');

    // Initialize notification service
    await notificationService.initialize();
    logger.info('✅ Notification Service initialized');
    
    // Test database connection
    // await testDatabaseConnection();
    logger.info('✅ Database connection verified');
    
    logger.info('🎉 All services initialized successfully');
  } catch (error) {
    logger.error('❌ Service initialization failed:', error);
  }
}

export default app;
