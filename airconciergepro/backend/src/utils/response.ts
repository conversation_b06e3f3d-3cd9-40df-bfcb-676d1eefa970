import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface PaginationOptions {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

/**
 * Send a successful response with data
 */
export const sendSuccess = <T>(
  res: Response,
  data: T,
  statusCode: number = 200,
  pagination?: PaginationOptions
): void => {
  const response: ApiResponse<T> = {
    success: true,
    data,
  };

  if (pagination) {
    response.pagination = pagination;
  }

  res.status(statusCode).json(response);
};

/**
 * Send an error response
 */
export const sendError = (
  res: Response,
  error: string,
  statusCode: number = 400
): void => {
  const response: ApiResponse = {
    success: false,
    error,
  };

  res.status(statusCode).json(response);
};

/**
 * Send a validation error response
 */
export const sendValidationError = (
  res: Response,
  error: string
): void => {
  sendError(res, error, 400);
};

/**
 * Send an unauthorized error response
 */
export const sendUnauthorizedError = (
  res: Response,
  error: string = 'Unauthorized'
): void => {
  sendError(res, error, 401);
};

/**
 * Send a forbidden error response
 */
export const sendForbiddenError = (
  res: Response,
  error: string = 'Forbidden'
): void => {
  sendError(res, error, 403);
};

/**
 * Send a not found error response
 */
export const sendNotFoundError = (
  res: Response,
  error: string = 'Resource not found'
): void => {
  sendError(res, error, 404);
};

/**
 * Send an internal server error response
 */
export const sendInternalServerError = (
  res: Response,
  error: string = 'Internal server error'
): void => {
  sendError(res, error, 500);
};

/**
 * Calculate pagination metadata
 */
export const calculatePagination = (
  totalItems: number,
  page: number = 1,
  limit: number = 10
): PaginationOptions => {
  const totalPages = Math.ceil(totalItems / limit);
  const currentPage = Math.max(1, Math.min(page, totalPages));

  return {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage: limit,
  };
};

/**
 * Parse pagination parameters from query
 */
export const parsePaginationParams = (query: any): { page: number; limit: number } => {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.max(1, Math.min(100, parseInt(query.limit) || 10));

  return { page, limit };
};
