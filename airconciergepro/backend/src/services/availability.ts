import { query } from './database';
import { logger } from './logger';

interface AvailabilityRequest {
  tenantId: string;
  serviceId: string;
  date: Date;
  timeSlot?: string;
  passengerCount: number;
  duration?: number; // in minutes
  airportCode?: string;
}

interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
  capacity: number;
  bookings: number;
  price?: number;
}

interface AvailabilityResponse {
  date: Date;
  available: boolean;
  totalCapacity: number;
  availableCapacity: number;
  timeSlots: TimeSlot[];
  restrictions?: string[];
}

interface DailyData {
  date: Date;
  bookings: number;
  capacity: number;
  utilization: number;
}

export class AvailabilityService {
  /**
   * Check availability for a specific service and date
   */
  async checkAvailability(request: AvailabilityRequest): Promise<AvailabilityResponse> {
    try {
      // Get service details and capacity
      const serviceResult = await query(
        `SELECT s.*, sc.capacity_per_hour, sc.operating_hours_start, sc.operating_hours_end,
                sc.advance_booking_hours, sc.max_booking_days
         FROM services s
         LEFT JOIN service_capacity sc ON s.id = sc.service_id
         WHERE s.id = $1 AND s.tenant_id = $2 AND s.status = 'active'`,
        [request.serviceId, request.tenantId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service not found or inactive');
      }

      const service = serviceResult.rows[0];
      const capacityPerHour = service.capacity_per_hour || 10;
      const operatingStart = service.operating_hours_start || '06:00';
      const operatingEnd = service.operating_hours_end || '23:00';

      // Check if date is within booking window
      const requestDate = new Date(request.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      requestDate.setHours(0, 0, 0, 0);

      const daysInAdvance = Math.ceil((requestDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      const maxBookingDays = service.max_booking_days || 90;
      const minBookingHours = service.advance_booking_hours || 24;

      const restrictions: string[] = [];

      if (daysInAdvance > maxBookingDays) {
        restrictions.push(`Bookings can only be made up to ${maxBookingDays} days in advance`);
      }

      if (daysInAdvance === 0) {
        const hoursUntilService = (requestDate.getTime() - today.getTime()) / (1000 * 60 * 60);
        if (hoursUntilService < minBookingHours) {
          restrictions.push(`Bookings must be made at least ${minBookingHours} hours in advance`);
        }
      }

      // Generate time slots
      const timeSlots = await this.generateTimeSlots(
        request.tenantId,
        request.serviceId,
        requestDate,
        operatingStart,
        operatingEnd,
        capacityPerHour
      );

      // Calculate total and available capacity
      const totalCapacity = timeSlots.reduce((sum, slot) => sum + slot.capacity, 0);
      const availableCapacity = timeSlots.reduce((sum, slot) => 
        sum + (slot.capacity - slot.bookings), 0
      );

      const available = availableCapacity >= request.passengerCount && restrictions.length === 0;

      return {
        date: requestDate,
        available,
        totalCapacity,
        availableCapacity,
        timeSlots: timeSlots.filter(slot => slot.available || slot.bookings > 0),
        restrictions: restrictions.length > 0 ? restrictions : undefined
      };

    } catch (error) {
      logger.error('Availability check error:', error);
      throw error;
    }
  }

  /**
   * Generate time slots for a given date
   */
  private async generateTimeSlots(
    tenantId: string,
    serviceId: string,
    date: Date,
    startTime: string,
    endTime: string,
    capacityPerHour: number
  ): Promise<TimeSlot[]> {
    const slots: TimeSlot[] = [];
    
    // Convert time strings to Date objects
    const startHour = parseInt(startTime.split(':')[0]);
    const startMinute = parseInt(startTime.split(':')[1]);
    const endHour = parseInt(endTime.split(':')[0]);
    const endMinute = parseInt(endTime.split(':')[1]);

    // Get existing bookings for the date
    const bookingsResult = await query(
      `SELECT 
         EXTRACT(HOUR FROM estimated_arrival) as hour,
         COUNT(*) as booking_count,
         SUM(passenger_count) as total_passengers
       FROM bookings
       WHERE tenant_id = $1 
         AND service_id = $2
         AND DATE(flight_date) = $3
         AND status NOT IN ('cancelled', 'no_show')
       GROUP BY EXTRACT(HOUR FROM estimated_arrival)`,
      [tenantId, serviceId, date.toISOString().split('T')[0]]
    );

    const bookingsByHour: { [hour: number]: { count: number; passengers: number } } = {};
    bookingsResult.rows.forEach(row => {
      const hour = parseInt(row.hour);
      bookingsByHour[hour] = {
        count: parseInt(row.booking_count),
        passengers: parseInt(row.total_passengers)
      };
    });

    // Generate hourly slots
    for (let hour = startHour; hour <= endHour; hour++) {
      // Skip if it's the end hour and we're past the end minute
      if (hour === endHour && startMinute > endMinute) {
        break;
      }

      const slotStart = `${hour.toString().padStart(2, '0')}:00`;
      const slotEnd = hour === endHour 
        ? `${hour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`
        : `${(hour + 1).toString().padStart(2, '0')}:00`;

      const bookingData = bookingsByHour[hour] || { count: 0, passengers: 0 };
      const availableCapacity = capacityPerHour - bookingData.passengers;

      slots.push({
        startTime: slotStart,
        endTime: slotEnd,
        available: availableCapacity > 0,
        capacity: capacityPerHour,
        bookings: bookingData.passengers
      });
    }

    return slots;
  }

  /**
   * Hold a time slot temporarily
   */
  async holdTimeSlot(
    tenantId: string,
    serviceId: string,
    date: Date,
    timeSlot: string,
    passengerCount: number,
    customerId?: string
  ): Promise<{ success: boolean; holdId?: string; expiresAt?: Date }> {
    try {
      // Check if slot is still available
      const availability = await this.checkAvailability({
        tenantId,
        serviceId,
        date,
        timeSlot,
        passengerCount
      });

      if (!availability.available) {
        return { success: false };
      }

      // Create a temporary hold
      const holdId = `hold_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

      await query(
        `INSERT INTO booking_holds (
          id, tenant_id, service_id, date, time_slot, passenger_count, 
          customer_id, expires_at, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())`,
        [holdId, tenantId, serviceId, date, timeSlot, passengerCount, customerId, expiresAt]
      );

      return { success: true, holdId, expiresAt };

    } catch (error) {
      logger.error('Hold time slot error:', error);
      return { success: false };
    }
  }

  /**
   * Release expired holds
   */
  async releaseExpiredHolds(): Promise<number> {
    try {
      const result = await query(
        'DELETE FROM booking_holds WHERE expires_at < NOW()',
        []
      );

      const releasedCount = result.rowCount || 0;
      if (releasedCount > 0) {
        logger.info(`Released ${releasedCount} expired booking holds`);
      }

      return releasedCount;
    } catch (error) {
      logger.error('Release expired holds error:', error);
      return 0;
    }
  }

  /**
   * Get availability calendar for a month
   */
  async getAvailabilityCalendar(
    tenantId: string,
    serviceId: string,
    month: number,
    year: number
  ): Promise<{
    month: number;
    year: number;
    days: Array<{
      date: Date;
      available: boolean;
      capacity: number;
      bookings: number;
      availabilityPercentage: number;
    }>;
  }> {
    try {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      const days: Array<{
        date: Date;
        available: boolean;
        capacity: number;
        bookings: number;
        availabilityPercentage: number;
      }> = [];

      // Get service capacity
      const serviceResult = await query(
        `SELECT sc.capacity_per_hour, sc.operating_hours_start, sc.operating_hours_end
         FROM services s
         LEFT JOIN service_capacity sc ON s.id = sc.service_id
         WHERE s.id = $1 AND s.tenant_id = $2`,
        [serviceId, tenantId]
      );

      const service = serviceResult.rows[0];
      const capacityPerHour = service?.capacity_per_hour || 10;
      const operatingStart = service?.operating_hours_start || '06:00';
      const operatingEnd = service?.operating_hours_end || '23:00';

      // Calculate daily capacity
      const startHour = parseInt(operatingStart.split(':')[0]);
      const endHour = parseInt(operatingEnd.split(':')[0]);
      const dailyCapacity = (endHour - startHour) * capacityPerHour;

      // Get bookings for the month
      const bookingsResult = await query(
        `SELECT 
           DATE(flight_date) as booking_date,
           SUM(passenger_count) as total_passengers
         FROM bookings
         WHERE tenant_id = $1 
           AND service_id = $2
           AND DATE(flight_date) BETWEEN $3 AND $4
           AND status NOT IN ('cancelled', 'no_show')
         GROUP BY DATE(flight_date)`,
        [tenantId, serviceId, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]
      );

      const bookingsByDate: { [date: string]: number } = {};
      bookingsResult.rows.forEach(row => {
        bookingsByDate[row.booking_date] = parseInt(row.total_passengers);
      });

      // Generate calendar days
      for (let day = 1; day <= endDate.getDate(); day++) {
        const currentDate = new Date(year, month - 1, day);
        const dateKey = currentDate.toISOString().split('T')[0];
        const bookings = bookingsByDate[dateKey] || 0;
        const availableCapacity = dailyCapacity - bookings;
        const availabilityPercentage = dailyCapacity > 0 ? (availableCapacity / dailyCapacity) * 100 : 0;

        days.push({
          date: currentDate,
          available: availableCapacity > 0,
          capacity: dailyCapacity,
          bookings,
          availabilityPercentage: Math.round(availabilityPercentage)
        });
      }

      return {
        month,
        year,
        days
      };

    } catch (error) {
      logger.error('Availability calendar error:', error);
      throw error;
    }
  }

  /**
   * Get capacity utilization metrics
   */
  async getCapacityMetrics(
    tenantId: string,
    serviceId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalCapacity: number;
    totalBookings: number;
    utilizationPercentage: number;
    peakUtilizationDay: { date: Date; percentage: number };
    lowUtilizationDay: { date: Date; percentage: number };
    averageDailyBookings: number;
    trendsData: Array<{ date: Date; bookings: number; capacity: number; utilization: number }>;
  }> {
    try {
      // Get service capacity details
      const serviceResult = await query(
        `SELECT sc.capacity_per_hour, sc.operating_hours_start, sc.operating_hours_end
         FROM services s
         LEFT JOIN service_capacity sc ON s.id = sc.service_id
         WHERE s.id = $1 AND s.tenant_id = $2`,
        [serviceId, tenantId]
      );

      const service = serviceResult.rows[0];
      const capacityPerHour = service?.capacity_per_hour || 10;
      const operatingStart = service?.operating_hours_start || '06:00';
      const operatingEnd = service?.operating_hours_end || '23:00';

      // Calculate daily capacity
      const startHour = parseInt(operatingStart.split(':')[0]);
      const endHour = parseInt(operatingEnd.split(':')[0]);
      const dailyCapacity = (endHour - startHour) * capacityPerHour;

      // Get daily bookings
      const bookingsResult = await query(
        `SELECT 
           DATE(flight_date) as booking_date,
           SUM(passenger_count) as daily_bookings
         FROM bookings
         WHERE tenant_id = $1 
           AND service_id = $2
           AND DATE(flight_date) BETWEEN $3 AND $4
           AND status NOT IN ('cancelled', 'no_show')
         GROUP BY DATE(flight_date)
         ORDER BY booking_date`,
        [tenantId, serviceId, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]
      );

      const dailyData = bookingsResult.rows.map(row => ({
        date: new Date(row.booking_date),
        bookings: parseInt(row.daily_bookings),
        capacity: dailyCapacity,
        utilization: dailyCapacity > 0 ? (parseInt(row.daily_bookings) / dailyCapacity) * 100 : 0
      }));

      // Calculate metrics
      const totalBookings = dailyData.reduce((sum, day) => sum + day.bookings, 0);
      const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      const totalCapacity = dailyCapacity * totalDays;
      const utilizationPercentage = totalCapacity > 0 ? (totalBookings / totalCapacity) * 100 : 0;

      // Find peak and low utilization days
      const peakDay = dailyData.reduce((peak, day) => 
        day.utilization > peak.utilization ? day : peak
      , { utilization: 0, date: startDate, bookings: 0, capacity: 0 });

      const lowDay = dailyData.reduce((low, day) => 
        day.utilization < low.utilization ? day : low
      , { utilization: 100, date: startDate, bookings: 0, capacity: 0 });

      const averageDailyBookings = dailyData.length > 0 
        ? dailyData.reduce((sum, day) => sum + day.bookings, 0) / dailyData.length
        : 0;

      return {
        totalCapacity,
        totalBookings,
        utilizationPercentage: Math.round(utilizationPercentage * 100) / 100,
        peakUtilizationDay: {
          date: peakDay.date,
          percentage: Math.round(peakDay.utilization * 100) / 100
        },
        lowUtilizationDay: {
          date: lowDay.date,
          percentage: Math.round(lowDay.utilization * 100) / 100
        },
        averageDailyBookings: Math.round(averageDailyBookings * 100) / 100,
        trendsData: dailyData
      };

    } catch (error) {
      logger.error('Capacity metrics error:', error);
      throw error;
    }
  }

  /**
   * Get real-time availability for operations dashboard
   */
  async getRealTimeAvailability(tenantId: string): Promise<{
    today: {
      totalCapacity: number;
      currentBookings: number;
      availableCapacity: number;
      utilizationPercentage: number;
    };
    tomorrow: {
      totalCapacity: number;
      currentBookings: number;
      availableCapacity: number;
      utilizationPercentage: number;
    };
    upcomingSlots: Array<{
      time: string;
      available: boolean;
      capacity: number;
      bookings: number;
    }>;
  }> {
    try {
      const today = new Date();
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

      // Get today's data
      const todayResult = await query(
        `SELECT 
           s.id as service_id,
           sc.capacity_per_hour,
           sc.operating_hours_start,
           sc.operating_hours_end,
           COALESCE(SUM(b.passenger_count), 0) as total_bookings
         FROM services s
         LEFT JOIN service_capacity sc ON s.id = sc.service_id
         LEFT JOIN bookings b ON s.id = b.service_id 
           AND DATE(b.flight_date) = CURRENT_DATE 
           AND b.status NOT IN ('cancelled', 'no_show')
         WHERE s.tenant_id = $1 AND s.status = 'active'
         GROUP BY s.id, sc.capacity_per_hour, sc.operating_hours_start, sc.operating_hours_end`,
        [tenantId]
      );

      // Get tomorrow's data
      const tomorrowResult = await query(
        `SELECT 
           s.id as service_id,
           sc.capacity_per_hour,
           sc.operating_hours_start,
           sc.operating_hours_end,
           COALESCE(SUM(b.passenger_count), 0) as total_bookings
         FROM services s
         LEFT JOIN service_capacity sc ON s.id = sc.service_id
         LEFT JOIN bookings b ON s.id = b.service_id 
           AND DATE(b.flight_date) = CURRENT_DATE + INTERVAL '1 day'
           AND b.status NOT IN ('cancelled', 'no_show')
         WHERE s.tenant_id = $1 AND s.status = 'active'
         GROUP BY s.id, sc.capacity_per_hour, sc.operating_hours_start, sc.operating_hours_end`,
        [tenantId]
      );

      const calculateDayMetrics = (results: any[]) => {
        let totalCapacity = 0;
        let currentBookings = 0;

        results.forEach(row => {
          const capacityPerHour = row.capacity_per_hour || 10;
          const startHour = parseInt((row.operating_hours_start || '06:00').split(':')[0]);
          const endHour = parseInt((row.operating_hours_end || '23:00').split(':')[0]);
          const dailyCapacity = (endHour - startHour) * capacityPerHour;

          totalCapacity += dailyCapacity;
          currentBookings += parseInt(row.total_bookings) || 0;
        });

        const availableCapacity = totalCapacity - currentBookings;
        const utilizationPercentage = totalCapacity > 0 ? (currentBookings / totalCapacity) * 100 : 0;

        return {
          totalCapacity,
          currentBookings,
          availableCapacity,
          utilizationPercentage: Math.round(utilizationPercentage * 100) / 100
        };
      };

      // Get upcoming time slots for next 4 hours
      const currentHour = new Date().getHours();
      const upcomingSlotsResult = await query(
        `SELECT 
           EXTRACT(HOUR FROM estimated_arrival) as hour,
           SUM(passenger_count) as bookings
         FROM bookings
         WHERE tenant_id = $1
           AND DATE(flight_date) = CURRENT_DATE
           AND EXTRACT(HOUR FROM estimated_arrival) BETWEEN $2 AND $3
           AND status NOT IN ('cancelled', 'no_show')
         GROUP BY EXTRACT(HOUR FROM estimated_arrival)
         ORDER BY hour`,
        [tenantId, currentHour, currentHour + 4]
      );

      const upcomingSlots: Array<{
        time: string;
        available: boolean;
        capacity: number;
        bookings: number;
      }> = [];
      for (let hour = currentHour; hour <= currentHour + 4; hour++) {
        const slotData = upcomingSlotsResult.rows.find(row => parseInt(row.hour) === hour);
        const bookings = slotData ? parseInt(slotData.bookings) : 0;
        const capacity = 10; // Default capacity per hour
        
        upcomingSlots.push({
          time: `${hour.toString().padStart(2, '0')}:00`,
          available: bookings < capacity,
          capacity,
          bookings
        });
      }

      return {
        today: calculateDayMetrics(todayResult.rows),
        tomorrow: calculateDayMetrics(tomorrowResult.rows),
        upcomingSlots
      };

    } catch (error) {
      logger.error('Real-time availability error:', error);
      throw error;
    }
  }

  /**
   * Initialize availability monitoring
   */
  async initialize(): Promise<void> {
    logger.info('Initializing Availability Service...');
    
    // Start periodic cleanup of expired holds
    setInterval(async () => {
      await this.releaseExpiredHolds();
    }, 5 * 60 * 1000); // Every 5 minutes

    logger.info('Availability Service initialized');
  }
}

export const availabilityService = new AvailabilityService();
