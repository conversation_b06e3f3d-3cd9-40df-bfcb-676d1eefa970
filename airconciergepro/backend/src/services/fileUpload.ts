import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { query } from './database';
import { logger } from './logger';

interface UploadResult {
  success: boolean;
  fileId?: string;
  filePath?: string;
  error?: string;
}

export class FileUploadService {
  private uploadDir: string;
  private maxFileSize: number;
  private allowedTypes: string[];

  constructor() {
    this.uploadDir = process.env.UPLOAD_DIR || './uploads';
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB default
    this.allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,pdf,doc,docx').split(',');
    
    this.ensureUploadDir();
  }

  /**
   * Ensure upload directory exists
   */
  private ensureUploadDir(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
      logger.info(`Created upload directory: ${this.uploadDir}`);
    }
  }

  /**
   * Configure multer storage
   */
  getMulterConfig() {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        // Create tenant-specific directory
        const tenantId = (req as any).user?.tenant_id || 'default';
        const tenantDir = path.join(this.uploadDir, tenantId);
        
        if (!fs.existsSync(tenantDir)) {
          fs.mkdirSync(tenantDir, { recursive: true });
        }
        
        cb(null, tenantDir);
      },
      filename: (req, file, cb) => {
        // Generate unique filename
        const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
      }
    });

    const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
      
      if (this.allowedTypes.includes(fileExt)) {
        cb(null, true);
      } else {
        cb(new Error(`File type ${fileExt} not allowed. Allowed types: ${this.allowedTypes.join(', ')}`));
      }
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: this.maxFileSize
      }
    });
  }

  /**
   * Save file metadata to database
   */
  async saveFileMetadata(
    tenantId: string,
    file: Express.Multer.File,
    documentType?: string,
    bookingId?: string,
    customerId?: string,
    userId?: string
  ): Promise<UploadResult> {
    try {
      const fileId = uuidv4();
      const relativePath = path.relative(this.uploadDir, file.path);

      await query(
        `INSERT INTO documents (
          id, tenant_id, booking_id, customer_id, user_id, filename, 
          original_filename, file_type, file_size, storage_path, 
          document_type, metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())`,
        [
          fileId,
          tenantId,
          bookingId || null,
          customerId || null,
          userId || null,
          file.filename,
          file.originalname,
          file.mimetype,
          file.size,
          relativePath,
          documentType || 'other',
          JSON.stringify({
            uploadDate: new Date().toISOString(),
            originalSize: file.size,
            encoding: file.encoding
          })
        ]
      );

      return {
        success: true,
        fileId,
        filePath: relativePath
      };
    } catch (error) {
      logger.error('Error saving file metadata:', error);
      
      // Clean up uploaded file if database save fails
      try {
        fs.unlinkSync(file.path);
      } catch (unlinkError) {
        logger.error('Error cleaning up file:', unlinkError);
      }

      return {
        success: false,
        error: 'Failed to save file metadata'
      };
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(fileId: string, tenantId: string): Promise<any> {
    try {
      const result = await query(
        'SELECT * FROM documents WHERE id = $1 AND tenant_id = $2',
        [fileId, tenantId]
      );

      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting file metadata:', error);
      return null;
    }
  }

  /**
   * Get file path for download
   */
  async getFilePath(fileId: string, tenantId: string): Promise<string | null> {
    try {
      const metadata = await this.getFileMetadata(fileId, tenantId);
      if (!metadata) {
        return null;
      }

      const fullPath = path.join(this.uploadDir, metadata.storage_path);
      
      // Check if file exists
      if (fs.existsSync(fullPath)) {
        return fullPath;
      }

      logger.warn(`File not found: ${fullPath}`);
      return null;
    } catch (error) {
      logger.error('Error getting file path:', error);
      return null;
    }
  }

  /**
   * Delete file
   */
  async deleteFile(fileId: string, tenantId: string): Promise<boolean> {
    try {
      const metadata = await this.getFileMetadata(fileId, tenantId);
      if (!metadata) {
        return false;
      }

      const fullPath = path.join(this.uploadDir, metadata.storage_path);
      
      // Delete physical file
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }

      // Delete metadata
      await query(
        'DELETE FROM documents WHERE id = $1 AND tenant_id = $2',
        [fileId, tenantId]
      );

      return true;
    } catch (error) {
      logger.error('Error deleting file:', error);
      return false;
    }
  }

  /**
   * Get files for booking
   */
  async getBookingFiles(bookingId: string, tenantId: string): Promise<any[]> {
    try {
      const result = await query(
        `SELECT id, filename, original_filename, file_type, file_size, 
                document_type, created_at
         FROM documents 
         WHERE booking_id = $1 AND tenant_id = $2
         ORDER BY created_at DESC`,
        [bookingId, tenantId]
      );

      return result.rows;
    } catch (error) {
      logger.error('Error getting booking files:', error);
      return [];
    }
  }

  /**
   * Get files for customer
   */
  async getCustomerFiles(customerId: string, tenantId: string): Promise<any[]> {
    try {
      const result = await query(
        `SELECT id, filename, original_filename, file_type, file_size, 
                document_type, created_at
         FROM documents 
         WHERE customer_id = $1 AND tenant_id = $2
         ORDER BY created_at DESC`,
        [customerId, tenantId]
      );

      return result.rows;
    } catch (error) {
      logger.error('Error getting customer files:', error);
      return [];
    }
  }

  /**
   * Clean up old files (for maintenance)
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await query(
        `SELECT id, tenant_id, storage_path 
         FROM documents 
         WHERE created_at < $1`,
        [cutoffDate]
      );

      let deletedCount = 0;

      for (const doc of result.rows) {
        const fullPath = path.join(this.uploadDir, doc.storage_path);
        
        try {
          if (fs.existsSync(fullPath)) {
            fs.unlinkSync(fullPath);
          }
          
          await query('DELETE FROM documents WHERE id = $1', [doc.id]);
          deletedCount++;
        } catch (error) {
          logger.error(`Error deleting old file ${doc.id}:`, error);
        }
      }

      logger.info(`Cleaned up ${deletedCount} old files`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old files:', error);
      return 0;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(tenantId?: string): Promise<{
    totalFiles: number;
    totalSize: number;
    averageSize: number;
    fileTypes: { [key: string]: number };
  }> {
    try {
      let whereClause = '';
      const params: any[] = [];

      if (tenantId) {
        whereClause = 'WHERE tenant_id = $1';
        params.push(tenantId);
      }

      const statsResult = await query(
        `SELECT 
           COUNT(*) as total_files,
           SUM(file_size) as total_size,
           AVG(file_size) as average_size
         FROM documents ${whereClause}`,
        params
      );

      const typeResult = await query(
        `SELECT 
           document_type,
           COUNT(*) as count
         FROM documents ${whereClause}
         GROUP BY document_type`,
        params
      );

      const stats = statsResult.rows[0];
      const fileTypes: { [key: string]: number } = {};

      typeResult.rows.forEach(row => {
        fileTypes[row.document_type] = parseInt(row.count);
      });

      return {
        totalFiles: parseInt(stats.total_files) || 0,
        totalSize: parseInt(stats.total_size) || 0,
        averageSize: Math.round(parseFloat(stats.average_size)) || 0,
        fileTypes
      };
    } catch (error) {
      logger.error('Error getting storage stats:', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        averageSize: 0,
        fileTypes: {}
      };
    }
  }
}

export const fileUploadService = new FileUploadService();
