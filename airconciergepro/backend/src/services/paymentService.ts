import Stripe from 'stripe';
import Razorpay from 'razorpay';
import { logger } from './logger';
import { query } from './database';

export type PaymentProvider = 'stripe' | 'razorpay';

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret?: string;
  provider: PaymentProvider;
  metadata?: Record<string, any>;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  provider: PaymentProvider;
  metadata?: Record<string, any>;
  customer_info?: {
    name?: string;
    email?: string;
    phone?: string;
  };
}

export class PaymentService {
  private stripe: Stripe;
  private razorpay: Razorpay;

  constructor() {
    // Initialize Stripe
    const stripeKey = process.env.STRIPE_SECRET_KEY;
    if (stripeKey) {
      this.stripe = new Stripe(stripeKey, {
        apiVersion: '2023-08-16',
      });
    } else {
      logger.warn('Stripe secret key not configured. Stripe payments will not work.');
    }

    // Initialize Razorpay
    const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
    const razorpayKeySecret = process.env.RAZORPAY_KEY_SECRET;

    logger.info(`Razorpay Key ID: ${razorpayKeyId ? 'Present' : 'Missing'}`);
    logger.info(`Razorpay Key Secret: ${razorpayKeySecret ? 'Present' : 'Missing'}`);

    if (razorpayKeyId && razorpayKeySecret) {
      this.razorpay = new Razorpay({
        key_id: razorpayKeyId,
        key_secret: razorpayKeySecret,
      });
      logger.info('Razorpay initialized successfully');
    } else {
      logger.warn('Razorpay credentials not configured. Razorpay payments will not work.');
    }
  }

  /**
   * Create payment intent based on provider
   */
  async createPaymentIntent(request: CreatePaymentIntentRequest): Promise<PaymentIntent> {
    try {
      switch (request.provider) {
        case 'stripe':
          if (!this.stripe) {
            throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
          }
          return await this.createStripePaymentIntent(request);
        case 'razorpay':
          if (!this.razorpay) {
            throw new Error('Razorpay is not configured. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET environment variables.');
          }
          return await this.createRazorpayOrder(request);
        default:
          throw new Error(`Unsupported payment provider: ${request.provider}`);
      }
    } catch (error) {
      logger.error('Payment intent creation failed:', error);
      throw error;
    }
  }

  /**
   * Create Stripe Payment Intent
   */
  private async createStripePaymentIntent(request: CreatePaymentIntentRequest): Promise<PaymentIntent> {
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: Math.round(request.amount * 100), // Convert to cents
      currency: request.currency.toLowerCase(),
      metadata: request.metadata || {},
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return {
      id: paymentIntent.id,
      amount: request.amount,
      currency: request.currency,
      status: paymentIntent.status,
      client_secret: paymentIntent.client_secret || undefined,
      provider: 'stripe',
      metadata: paymentIntent.metadata,
    };
  }

  /**
   * Create Razorpay Order
   */
  private async createRazorpayOrder(request: CreatePaymentIntentRequest): Promise<PaymentIntent> {
    const order = await this.razorpay.orders.create({
      amount: Math.round(request.amount * 100), // Amount in paise (smallest currency unit)
      currency: request.currency.toUpperCase(),
      notes: request.metadata || {},
    });

    const orderAmount = Number(order.amount);
    logger.info(`Razorpay order created: ${order.id}, amount: ${orderAmount} paise (₹${orderAmount / 100})`);

    return {
      id: order.id,
      amount: orderAmount / 100, // Convert back to main currency unit for response
      currency: request.currency,
      status: order.status,
      provider: 'razorpay',
      metadata: order.notes,
    };
  }

  /**
   * Verify payment based on provider
   */
  async verifyPayment(paymentId: string, provider: PaymentProvider, additionalData?: any): Promise<boolean> {
    try {
      switch (provider) {
        case 'stripe':
          return await this.verifyStripePayment(paymentId);
        case 'razorpay':
          return await this.verifyRazorpayPayment(paymentId, additionalData);
        default:
          throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      logger.error('Payment verification failed:', error);
      return false;
    }
  }

  /**
   * Verify Stripe Payment
   */
  private async verifyStripePayment(paymentIntentId: string): Promise<boolean> {
    const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
    return paymentIntent.status === 'succeeded';
  }

  /**
   * Verify Razorpay Payment
   */
  private async verifyRazorpayPayment(paymentId: string, verificationData: any): Promise<boolean> {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = verificationData;
    
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(`${razorpay_order_id}|${razorpay_payment_id}`)
      .digest('hex');

    return expectedSignature === razorpay_signature;
  }

  /**
   * Get payment details
   */
  async getPaymentDetails(paymentId: string, provider: PaymentProvider): Promise<any> {
    try {
      switch (provider) {
        case 'stripe':
          return await this.stripe.paymentIntents.retrieve(paymentId);
        case 'razorpay':
          return await this.razorpay.orders.fetch(paymentId);
        default:
          throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      logger.error('Failed to get payment details:', error);
      throw error;
    }
  }

  /**
   * Process refund
   */
  async processRefund(paymentId: string, provider: PaymentProvider, amount?: number): Promise<any> {
    try {
      switch (provider) {
        case 'stripe':
          return await this.stripe.refunds.create({
            payment_intent: paymentId,
            amount: amount ? Math.round(amount * 100) : undefined,
          });
        case 'razorpay':
          // For Razorpay, we need the payment ID, not order ID
          return await this.razorpay.payments.refund(paymentId, {
            amount: amount ? Math.round(amount * 100) : undefined,
          });
        default:
          throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      logger.error('Refund processing failed:', error);
      throw error;
    }
  }

  /**
   * Save payment record to database
   */
  async savePaymentRecord(data: {
    tenant_id: string;
    booking_id: string;
    payment_intent_id: string;
    provider: PaymentProvider;
    amount: number;
    currency: string;
    status: string;
    metadata?: Record<string, any>;
  }): Promise<string> {
    const result = await query(
      `INSERT INTO payments (
        tenant_id, booking_id, payment_intent_id, payment_provider, 
        amount, currency, status, metadata, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW()) 
      RETURNING id`,
      [
        data.tenant_id,
        data.booking_id,
        data.payment_intent_id,
        data.provider,
        data.amount,
        data.currency,
        data.status,
        JSON.stringify(data.metadata || {}),
      ]
    );

    return result.rows[0].id;
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(paymentIntentId: string, status: string, metadata?: Record<string, any>): Promise<void> {
    await query(
      `UPDATE payments 
       SET status = $1, metadata = $2, updated_at = NOW() 
       WHERE payment_intent_id = $3`,
      [status, JSON.stringify(metadata || {}), paymentIntentId]
    );
  }
}

export const paymentService = new PaymentService();
