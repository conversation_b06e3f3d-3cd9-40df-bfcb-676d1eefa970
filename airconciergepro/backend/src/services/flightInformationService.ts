import axios from 'axios';
import { query } from './database';
import { logger } from './logger';

interface FlightData {
  flight_number: string;
  airline_code: string;
  airline_name: string;
  departure_airport: string;
  arrival_airport: string;
  departure_terminal?: string;
  arrival_terminal?: string;
  flight_date: string;
  scheduled_departure?: string;
  scheduled_arrival?: string;
  estimated_departure?: string;
  estimated_arrival?: string;
  actual_departure?: string;
  actual_arrival?: string;
  flight_status: string;
  aircraft_type?: string;
  gate?: string;
  baggage_belt?: string;
  flight_details: any;
}

interface TimeSlotSuggestion {
  recommended_time: string;
  buffer_minutes: number;
  reason: string;
  confidence: 'high' | 'medium' | 'low';
}

class FlightInformationService {
  private aviationStackApiKey: string;
  private aviationStackBaseUrl: string;

  constructor() {
    this.aviationStackApiKey = process.env.AVIATIONSTACK_API_KEY || '';
    this.aviationStackBaseUrl = 'https://api.aviationstack.com/v1';
  }

  /**
   * Fetch flight information from AviationStack API
   */
  async fetchFlightInfo(flightNumber: string, date?: string): Promise<FlightData | null> {
    try {
      if (!this.aviationStackApiKey) {
        logger.warn('AviationStack API key not configured');
        return null;
      }

      const params: any = {
        access_key: this.aviationStackApiKey,
        flight_iata: flightNumber,
        limit: 1
      };

      if (date) {
        params.flight_date = date;
      }

      const response = await axios.get(`${this.aviationStackBaseUrl}/flights`, { params });
      
      if (response.data.data && response.data.data.length > 0) {
        const flight = response.data.data[0];
        
        return {
          flight_number: flight.flight.iata || flightNumber,
          airline_code: flight.airline.iata,
          airline_name: flight.airline.name,
          departure_airport: flight.departure.iata,
          arrival_airport: flight.arrival.iata,
          departure_terminal: flight.departure.terminal,
          arrival_terminal: flight.arrival.terminal,
          flight_date: flight.flight_date,
          scheduled_departure: flight.departure.scheduled,
          scheduled_arrival: flight.arrival.scheduled,
          estimated_departure: flight.departure.estimated,
          estimated_arrival: flight.arrival.estimated,
          actual_departure: flight.departure.actual,
          actual_arrival: flight.arrival.actual,
          flight_status: flight.flight_status,
          aircraft_type: flight.aircraft?.registration,
          gate: flight.departure.gate || flight.arrival.gate,
          baggage_belt: flight.arrival.baggage,
          flight_details: flight
        };
      }

      return null;
    } catch (error) {
      logger.error('Error fetching flight information:', error);
      return null;
    }
  }

  /**
   * Get time slot suggestions based on flight information
   */
  getTimeSlotSuggestions(flightData: FlightData, serviceType: 'arrival' | 'departure'): TimeSlotSuggestion[] {
    const suggestions: TimeSlotSuggestion[] = [];
    
    if (serviceType === 'arrival') {
      // For arrival services, suggest times based on arrival time
      const arrivalTime = flightData.estimated_arrival || flightData.scheduled_arrival;
      
      if (arrivalTime) {
        const arrivalDate = new Date(arrivalTime);
        
        // Suggest 30 minutes after arrival (high confidence)
        const recommended30 = new Date(arrivalDate.getTime() + 30 * 60000);
        suggestions.push({
          recommended_time: recommended30.toISOString(),
          buffer_minutes: 30,
          reason: 'Standard 30-minute buffer for passenger disembarkation and baggage collection',
          confidence: 'high'
        });

        // Suggest 45 minutes after arrival (medium confidence)
        const recommended45 = new Date(arrivalDate.getTime() + 45 * 60000);
        suggestions.push({
          recommended_time: recommended45.toISOString(),
          buffer_minutes: 45,
          reason: 'Extended buffer for international flights or potential delays',
          confidence: 'medium'
        });

        // Suggest 60 minutes after arrival (low confidence, safe option)
        const recommended60 = new Date(arrivalDate.getTime() + 60 * 60000);
        suggestions.push({
          recommended_time: recommended60.toISOString(),
          buffer_minutes: 60,
          reason: 'Conservative buffer for complex arrivals or first-time travelers',
          confidence: 'low'
        });
      }
    } else if (serviceType === 'departure') {
      // For departure services, suggest times based on departure time
      const departureTime = flightData.scheduled_departure;
      
      if (departureTime) {
        const departureDate = new Date(departureTime);
        
        // Suggest 2 hours before departure (high confidence)
        const recommended120 = new Date(departureDate.getTime() - 120 * 60000);
        suggestions.push({
          recommended_time: recommended120.toISOString(),
          buffer_minutes: 120,
          reason: 'Standard 2-hour buffer for international flights check-in and security',
          confidence: 'high'
        });

        // Suggest 90 minutes before departure (medium confidence)
        const recommended90 = new Date(departureDate.getTime() - 90 * 60000);
        suggestions.push({
          recommended_time: recommended90.toISOString(),
          buffer_minutes: 90,
          reason: 'Suitable for domestic flights or experienced travelers',
          confidence: 'medium'
        });

        // Suggest 3 hours before departure (low confidence, very safe)
        const recommended180 = new Date(departureDate.getTime() - 180 * 60000);
        suggestions.push({
          recommended_time: recommended180.toISOString(),
          buffer_minutes: 180,
          reason: 'Extra safe buffer for peak travel times or complex itineraries',
          confidence: 'low'
        });
      }
    }

    return suggestions;
  }

  /**
   * Update booking with flight information
   */
  async updateBookingFlightInfo(bookingId: string, flightData: FlightData): Promise<boolean> {
    try {
      await query(
        `UPDATE bookings SET 
          airline_code = $1,
          airline_name = $2,
          departure_terminal = $3,
          arrival_terminal = $4,
          scheduled_departure = $5,
          scheduled_arrival = $6,
          estimated_departure = $7,
          estimated_arrival = $8,
          actual_departure = $9,
          actual_arrival = $10,
          flight_status = $11,
          aircraft_type = $12,
          gate = $13,
          baggage_belt = $14,
          flight_details = $15,
          updated_at = NOW()
        WHERE id = $16`,
        [
          flightData.airline_code,
          flightData.airline_name,
          flightData.departure_terminal,
          flightData.arrival_terminal,
          flightData.scheduled_departure,
          flightData.scheduled_arrival,
          flightData.estimated_departure,
          flightData.estimated_arrival,
          flightData.actual_departure,
          flightData.actual_arrival,
          flightData.flight_status,
          flightData.aircraft_type,
          flightData.gate,
          flightData.baggage_belt,
          JSON.stringify(flightData.flight_details),
          bookingId
        ]
      );

      return true;
    } catch (error) {
      logger.error('Error updating booking flight info:', error);
      return false;
    }
  }

  /**
   * Process flight information for a booking
   */
  async processFlightInformation(
    flightNumber: string, 
    date: string, 
    serviceType: 'arrival' | 'departure'
  ): Promise<{
    flightData: FlightData | null;
    timeSlotSuggestions: TimeSlotSuggestion[];
    error?: string;
  }> {
    try {
      const flightData = await this.fetchFlightInfo(flightNumber, date);
      
      if (!flightData) {
        return {
          flightData: null,
          timeSlotSuggestions: [],
          error: 'Flight information not found. Please verify the flight number and date.'
        };
      }

      const timeSlotSuggestions = this.getTimeSlotSuggestions(flightData, serviceType);

      return {
        flightData,
        timeSlotSuggestions
      };
    } catch (error) {
      logger.error('Error processing flight information:', error);
      return {
        flightData: null,
        timeSlotSuggestions: [],
        error: 'Unable to fetch flight information. Please try again later.'
      };
    }
  }

  /**
   * Get flight status updates for active bookings
   */
  async updateActiveFlights(): Promise<void> {
    try {
      // Get bookings with flights in the next 24 hours that need updates
      const result = await query(
        `SELECT id, flight_number, flight_date, service_type
         FROM bookings 
         WHERE flight_number IS NOT NULL 
           AND flight_date >= CURRENT_DATE 
           AND flight_date <= CURRENT_DATE + INTERVAL '1 day'
           AND status IN ('confirmed', 'in_progress')
           AND (flight_details->>'last_update' IS NULL 
                OR (flight_details->>'last_update')::timestamp < NOW() - INTERVAL '30 minutes')`
      );

      for (const booking of result.rows) {
        const flightData = await this.fetchFlightInfo(booking.flight_number, booking.flight_date);
        if (flightData) {
          await this.updateBookingFlightInfo(booking.id, flightData);
          logger.info(`Updated flight info for booking ${booking.id}`);
        }
      }
    } catch (error) {
      logger.error('Error updating active flights:', error);
    }
  }
}

export const flightInformationService = new FlightInformationService();
