import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { logger } from './logger';
import { query } from './database';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  tenantId?: string;
  role?: string;
}

interface BookingUpdate {
  bookingId: string;
  status: string;
  agentId?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
  message?: string;
}

interface AgentLocation {
  agentId: string;
  latitude: number;
  longitude: number;
  timestamp: Date;
  status: 'available' | 'busy' | 'offline';
}

interface FlightUpdate {
  flightNumber: string;
  status: string;
  gate?: string;
  terminal?: string;
  delay?: number;
  estimatedTime?: Date;
  actualTime?: Date;
}

export class WebSocketService {
  public io: SocketIOServer;
  private connectedClients: Map<string, AuthenticatedSocket> = new Map();
  private agentLocations: Map<string, AgentLocation> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    
    logger.info('WebSocket service initialized');
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware(): void {
    this.io.use(async (socket: any, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret') as any;
        
        // Get user details from database
        const userResult = await query(
          'SELECT id, tenant_id, role, status FROM users WHERE id = $1 AND status = $2',
          [decoded.userId, 'active']
        );

        if (userResult.rows.length === 0) {
          return next(new Error('Invalid token or user not found'));
        }

        const user = userResult.rows[0];
        socket.userId = user.id;
        socket.tenantId = user.tenant_id;
        socket.role = user.role;

        next();
      } catch (error) {
        logger.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      logger.info(`Client connected: ${socket.userId} (${socket.role})`);
      
      // Store connected client
      this.connectedClients.set(socket.id, socket);

      // Join tenant-specific room
      if (socket.tenantId) {
        socket.join(`tenant:${socket.tenantId}`);
      }

      // Join role-specific room
      if (socket.role) {
        socket.join(`role:${socket.role}`);
      }

      // Handle agent location updates
      socket.on('agent:location', (data: { latitude: number; longitude: number; status: string }) => {
        this.handleAgentLocationUpdate(socket, data);
      });

      // Handle booking status updates
      socket.on('booking:update', (data: BookingUpdate) => {
        this.handleBookingUpdate(socket, data);
      });

      // Handle agent status updates
      socket.on('agent:status', (data: { status: 'available' | 'busy' | 'offline' }) => {
        this.handleAgentStatusUpdate(socket, data);
      });

      // Handle flight tracking subscription
      socket.on('flight:subscribe', (data: { flightNumber: string }) => {
        socket.join(`flight:${data.flightNumber}`);
        logger.info(`Client subscribed to flight updates: ${data.flightNumber}`);
      });

      // Handle flight tracking unsubscription
      socket.on('flight:unsubscribe', (data: { flightNumber: string }) => {
        socket.leave(`flight:${data.flightNumber}`);
        logger.info(`Client unsubscribed from flight updates: ${data.flightNumber}`);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.userId}`);
        this.connectedClients.delete(socket.id);
        
        // Remove agent location if it was an agent
        if (socket.role === 'field_agent' && socket.userId) {
          this.agentLocations.delete(socket.userId);
          this.broadcastAgentLocationUpdate(socket.tenantId!, socket.userId, null);
        }
      });

      // Send initial data
      this.sendInitialData(socket);
    });
  }

  /**
   * Handle agent location updates
   */
  private async handleAgentLocationUpdate(
    socket: AuthenticatedSocket, 
    data: { latitude: number; longitude: number; status: string }
  ): Promise<void> {
    if (socket.role !== 'field_agent' || !socket.userId) {
      return;
    }

    const location: AgentLocation = {
      agentId: socket.userId,
      latitude: data.latitude,
      longitude: data.longitude,
      timestamp: new Date(),
      status: data.status as 'available' | 'busy' | 'offline'
    };

    // Store location
    this.agentLocations.set(socket.userId, location);

    // Update database
    try {
      await query(
        'UPDATE agents SET current_location = $1, status = $2 WHERE user_id = $3',
        [JSON.stringify({ latitude: data.latitude, longitude: data.longitude }), data.status, socket.userId]
      );
    } catch (error) {
      logger.error('Error updating agent location in database:', error);
    }

    // Broadcast to operations managers in the same tenant
    this.broadcastAgentLocationUpdate(socket.tenantId!, socket.userId, location);
  }

  /**
   * Handle booking status updates
   */
  private async handleBookingUpdate(socket: AuthenticatedSocket, data: BookingUpdate): Promise<void> {
    try {
      // Update booking in database
      await query(
        'UPDATE bookings SET status = $1, updated_at = NOW() WHERE id = $2 AND tenant_id = $3',
        [data.status, data.bookingId, socket.tenantId]
      );

      // Create booking activity
      await query(
        `INSERT INTO booking_activities (
          id, booking_id, user_id, activity_type, description, created_at
        ) VALUES (uuid_generate_v4(), $1, $2, $3, $4, NOW())`,
        [data.bookingId, socket.userId, 'status_update', data.message || `Status updated to ${data.status}`]
      );

      // Broadcast update to all relevant users in tenant
      this.io.to(`tenant:${socket.tenantId}`).emit('booking:updated', {
        bookingId: data.bookingId,
        status: data.status,
        agentId: data.agentId,
        location: data.location,
        timestamp: data.timestamp,
        updatedBy: socket.userId
      });

      logger.info(`Booking ${data.bookingId} status updated to ${data.status}`);
    } catch (error) {
      logger.error('Error handling booking update:', error);
    }
  }

  /**
   * Handle agent status updates
   */
  private async handleAgentStatusUpdate(
    socket: AuthenticatedSocket, 
    data: { status: 'available' | 'busy' | 'offline' }
  ): Promise<void> {
    if (socket.role !== 'field_agent' || !socket.userId) {
      return;
    }

    try {
      // Update agent status in database
      await query(
        'UPDATE agents SET status = $1 WHERE user_id = $2',
        [data.status, socket.userId]
      );

      // Update stored location
      const location = this.agentLocations.get(socket.userId);
      if (location) {
        location.status = data.status;
        location.timestamp = new Date();
      }

      // Broadcast to operations managers
      this.io.to(`tenant:${socket.tenantId}`).emit('agent:status_updated', {
        agentId: socket.userId,
        status: data.status,
        timestamp: new Date()
      });

      logger.info(`Agent ${socket.userId} status updated to ${data.status}`);
    } catch (error) {
      logger.error('Error updating agent status:', error);
    }
  }

  /**
   * Broadcast agent location update
   */
  private broadcastAgentLocationUpdate(tenantId: string, agentId: string, location: AgentLocation | null): void {
    this.io.to(`tenant:${tenantId}`).emit('agent:location_updated', {
      agentId,
      location,
      timestamp: new Date()
    });
  }

  /**
   * Send initial data to connected client
   */
  private async sendInitialData(socket: AuthenticatedSocket): Promise<void> {
    try {
      if (socket.role === 'operations_manager' || socket.role === 'company_admin') {
        // Send current agent locations
        const agentLocations = Array.from(this.agentLocations.values())
          .filter(location => {
            // Filter by tenant (you'd need to store tenant info with locations)
            return true; // Simplified for now
          });

        socket.emit('initial:agent_locations', agentLocations);

        // Send active bookings
        const activeBookingsResult = await query(
          `SELECT id, booking_reference, status, assigned_agent_id, customer_id, service_id
           FROM bookings 
           WHERE tenant_id = $1 AND status IN ('confirmed', 'in_progress')
           ORDER BY created_at DESC
           LIMIT 50`,
          [socket.tenantId]
        );

        socket.emit('initial:active_bookings', activeBookingsResult.rows);
      }
    } catch (error) {
      logger.error('Error sending initial data:', error);
    }
  }

  /**
   * Broadcast flight update to subscribers
   */
  public broadcastFlightUpdate(flightUpdate: FlightUpdate): void {
    this.io.to(`flight:${flightUpdate.flightNumber}`).emit('flight:updated', flightUpdate);
    logger.info(`Flight update broadcasted for ${flightUpdate.flightNumber}`);
  }

  /**
   * Broadcast system notification
   */
  public broadcastSystemNotification(tenantId: string, notification: {
    type: 'info' | 'warning' | 'error' | 'success';
    title: string;
    message: string;
    data?: any;
  }): void {
    this.io.to(`tenant:${tenantId}`).emit('system:notification', {
      ...notification,
      timestamp: new Date()
    });
  }

  /**
   * Get connected clients count
   */
  public getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * Get agent locations for a tenant
   */
  public getAgentLocations(tenantId?: string): AgentLocation[] {
    // In a real implementation, you'd filter by tenant
    return Array.from(this.agentLocations.values());
  }
}

export let websocketService: WebSocketService;
