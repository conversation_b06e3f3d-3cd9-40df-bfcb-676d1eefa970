import axios from 'axios';
import { logger } from './logger';
import { query } from './database';

interface AviationStackAirport {
  airport_name: string;
  iata_code: string;
  icao_code: string;
  latitude: string;
  longitude: string;
  geoname_id: string;
  timezone: string;
  gmt: string;
  phone_number: string;
  country_name: string;
  country_iso2: string;
  city_iata_code: string;
}

interface AviationStackFlight {
  flight_date: string;
  flight_status: string;
  departure: {
    airport: string;
    timezone: string;
    iata: string;
    icao: string;
    terminal: string;
    gate: string;
    delay: number;
    scheduled: string;
    estimated: string;
    actual: string;
    estimated_runway: string;
    actual_runway: string;
  };
  arrival: {
    airport: string;
    timezone: string;
    iata: string;
    icao: string;
    terminal: string;
    gate: string;
    baggage: string;
    delay: number;
    scheduled: string;
    estimated: string;
    actual: string;
    estimated_runway: string;
    actual_runway: string;
  };
  airline: {
    name: string;
    iata: string;
    icao: string;
  };
  flight: {
    number: string;
    iata: string;
    icao: string;
    codeshared: any;
  };
  aircraft: {
    registration: string;
    iata: string;
    icao: string;
    icao24: string;
  };
  live: {
    updated: string;
    latitude: number;
    longitude: number;
    altitude: number;
    direction: number;
    speed_horizontal: number;
    speed_vertical: number;
    is_ground: boolean;
  };
}

export class AviationStackService {
  private readonly apiKey: string;
  private readonly baseUrl = 'http://api.aviationstack.com/v1';
  private readonly axiosInstance;

  constructor() {
    this.apiKey = process.env.AVIATIONSTACK_API_KEY || '********************************';
    
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      params: {
        access_key: this.apiKey
      }
    });
  }

  /**
   * Sync airports from AviationStack API to local database
   */
  async syncAirports(limit: number = 100, offset: number = 0): Promise<void> {
    try {
      logger.info(`Syncing airports from AviationStack API (limit: ${limit}, offset: ${offset})`);
      
      const response = await this.axiosInstance.get('/airports', {
        params: {
          limit,
          offset
        }
      });

      const airports: AviationStackAirport[] = response.data.data;
      
      if (!airports || airports.length === 0) {
        logger.info('No more airports to sync');
        return;
      }

      // Insert or update airports in database
      for (const airport of airports) {
        await this.upsertAirport(airport);
      }

      logger.info(`Successfully synced ${airports.length} airports`);
      
      // If we got a full batch, there might be more
      if (airports.length === limit) {
        logger.info('More airports available, continuing sync...');
        await this.syncAirports(limit, offset + limit);
      }
    } catch (error) {
      logger.error('Error syncing airports:', error);
      throw error;
    }
  }

  /**
   * Insert or update airport in database
   */
  private async upsertAirport(airport: AviationStackAirport): Promise<void> {
    try {
      await query(
        `INSERT INTO airports (
          iata_code, icao_code, name, city, country, timezone,
          latitude, longitude, phone_number, country_iso2,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        ON CONFLICT (iata_code) 
        DO UPDATE SET
          icao_code = EXCLUDED.icao_code,
          name = EXCLUDED.name,
          city = EXCLUDED.city,
          country = EXCLUDED.country,
          timezone = EXCLUDED.timezone,
          latitude = EXCLUDED.latitude,
          longitude = EXCLUDED.longitude,
          phone_number = EXCLUDED.phone_number,
          country_iso2 = EXCLUDED.country_iso2,
          updated_at = NOW()`,
        [
          airport.iata_code,
          airport.icao_code,
          airport.airport_name,
          airport.city_iata_code || 'Unknown',
          airport.country_name,
          airport.timezone,
          parseFloat(airport.latitude) || null,
          parseFloat(airport.longitude) || null,
          airport.phone_number,
          airport.country_iso2
        ]
      );
    } catch (error) {
      logger.error(`Error upserting airport ${airport.iata_code}:`, error);
    }
  }

  /**
   * Get real-time flight information
   */
  async getFlightInfo(flightNumber: string, date?: string): Promise<AviationStackFlight | null> {
    try {
      const params: any = {
        flight_iata: flightNumber
      };

      if (date) {
        params.flight_date = date;
      }

      const response = await this.axiosInstance.get('/flights', { params });
      
      const flights: AviationStackFlight[] = response.data.data;
      
      if (!flights || flights.length === 0) {
        return null;
      }

      // Return the most recent flight
      return flights[0];
    } catch (error) {
      logger.error(`Error fetching flight info for ${flightNumber}:`, error);
      return null;
    }
  }

  /**
   * Get flights by airport
   */
  async getFlightsByAirport(
    airportCode: string, 
    type: 'departure' | 'arrival' = 'arrival',
    limit: number = 50
  ): Promise<AviationStackFlight[]> {
    try {
      const params: any = {
        limit
      };

      if (type === 'departure') {
        params.dep_iata = airportCode;
      } else {
        params.arr_iata = airportCode;
      }

      const response = await this.axiosInstance.get('/flights', { params });
      
      return response.data.data || [];
    } catch (error) {
      logger.error(`Error fetching flights for airport ${airportCode}:`, error);
      return [];
    }
  }

  /**
   * Search airports by name or code
   */
  async searchAirports(query: string, limit: number = 20): Promise<any[]> {
    try {
      const result = await this.axiosInstance.get('/airports', {
        params: {
          search: query,
          limit
        }
      });

      return result.data.data || [];
    } catch (error) {
      logger.error(`Error searching airports with query "${query}":`, error);
      return [];
    }
  }

  /**
   * Get airport details by IATA code
   */
  async getAirportByCode(iataCode: string): Promise<AviationStackAirport | null> {
    try {
      const response = await this.axiosInstance.get('/airports', {
        params: {
          iata_code: iataCode
        }
      });

      const airports: AviationStackAirport[] = response.data.data;
      return airports && airports.length > 0 ? airports[0] : null;
    } catch (error) {
      logger.error(`Error fetching airport ${iataCode}:`, error);
      return null;
    }
  }

  /**
   * Cache flight information in database
   */
  async cacheFlightInfo(flight: AviationStackFlight): Promise<void> {
    try {
      await query(
        `INSERT INTO flight_cache (
          flight_number, flight_date, status, airline_name, airline_iata,
          departure_airport, departure_scheduled, departure_estimated, departure_actual,
          departure_terminal, departure_gate, departure_delay,
          arrival_airport, arrival_scheduled, arrival_estimated, arrival_actual,
          arrival_terminal, arrival_gate, arrival_baggage, arrival_delay,
          aircraft_registration, aircraft_type, live_data,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, NOW(), NOW()
        )
        ON CONFLICT (flight_number, flight_date)
        DO UPDATE SET
          status = EXCLUDED.status,
          departure_estimated = EXCLUDED.departure_estimated,
          departure_actual = EXCLUDED.departure_actual,
          departure_gate = EXCLUDED.departure_gate,
          departure_delay = EXCLUDED.departure_delay,
          arrival_estimated = EXCLUDED.arrival_estimated,
          arrival_actual = EXCLUDED.arrival_actual,
          arrival_gate = EXCLUDED.arrival_gate,
          arrival_delay = EXCLUDED.arrival_delay,
          live_data = EXCLUDED.live_data,
          updated_at = NOW()`,
        [
          flight.flight.iata,
          flight.flight_date,
          flight.flight_status,
          flight.airline.name,
          flight.airline.iata,
          flight.departure.iata,
          flight.departure.scheduled,
          flight.departure.estimated,
          flight.departure.actual,
          flight.departure.terminal,
          flight.departure.gate,
          flight.departure.delay,
          flight.arrival.iata,
          flight.arrival.scheduled,
          flight.arrival.estimated,
          flight.arrival.actual,
          flight.arrival.terminal,
          flight.arrival.gate,
          flight.arrival.baggage,
          flight.arrival.delay,
          flight.aircraft?.registration,
          flight.aircraft?.iata,
          JSON.stringify(flight.live)
        ]
      );
    } catch (error) {
      logger.error('Error caching flight info:', error);
    }
  }
}

export const aviationStackService = new AviationStackService();
