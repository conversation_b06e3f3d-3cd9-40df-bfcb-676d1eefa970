"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parsePaginationParams = exports.calculatePagination = exports.sendInternalServerError = exports.sendNotFoundError = exports.sendForbiddenError = exports.sendUnauthorizedError = exports.sendValidationError = exports.sendError = exports.sendSuccess = void 0;
/**
 * Send a successful response with data
 */
const sendSuccess = (res, data, statusCode = 200, pagination) => {
    const response = {
        success: true,
        data,
    };
    if (pagination) {
        response.pagination = pagination;
    }
    res.status(statusCode).json(response);
};
exports.sendSuccess = sendSuccess;
/**
 * Send an error response
 */
const sendError = (res, error, statusCode = 400) => {
    const response = {
        success: false,
        error,
    };
    res.status(statusCode).json(response);
};
exports.sendError = sendError;
/**
 * Send a validation error response
 */
const sendValidationError = (res, error) => {
    (0, exports.sendError)(res, error, 400);
};
exports.sendValidationError = sendValidationError;
/**
 * Send an unauthorized error response
 */
const sendUnauthorizedError = (res, error = 'Unauthorized') => {
    (0, exports.sendError)(res, error, 401);
};
exports.sendUnauthorizedError = sendUnauthorizedError;
/**
 * Send a forbidden error response
 */
const sendForbiddenError = (res, error = 'Forbidden') => {
    (0, exports.sendError)(res, error, 403);
};
exports.sendForbiddenError = sendForbiddenError;
/**
 * Send a not found error response
 */
const sendNotFoundError = (res, error = 'Resource not found') => {
    (0, exports.sendError)(res, error, 404);
};
exports.sendNotFoundError = sendNotFoundError;
/**
 * Send an internal server error response
 */
const sendInternalServerError = (res, error = 'Internal server error') => {
    (0, exports.sendError)(res, error, 500);
};
exports.sendInternalServerError = sendInternalServerError;
/**
 * Calculate pagination metadata
 */
const calculatePagination = (totalItems, page = 1, limit = 10) => {
    const totalPages = Math.ceil(totalItems / limit);
    const currentPage = Math.max(1, Math.min(page, totalPages));
    return {
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage: limit,
    };
};
exports.calculatePagination = calculatePagination;
/**
 * Parse pagination parameters from query
 */
const parsePaginationParams = (query) => {
    const page = Math.max(1, parseInt(query.page) || 1);
    const limit = Math.max(1, Math.min(100, parseInt(query.limit) || 10));
    return { page, limit };
};
exports.parsePaginationParams = parsePaginationParams;
//# sourceMappingURL=response.js.map