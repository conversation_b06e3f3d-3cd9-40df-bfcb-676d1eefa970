import { Response } from 'express';
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
    };
}
export interface PaginationOptions {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
}
/**
 * Send a successful response with data
 */
export declare const sendSuccess: <T>(res: Response, data: T, statusCode?: number, pagination?: PaginationOptions) => void;
/**
 * Send an error response
 */
export declare const sendError: (res: Response, error: string, statusCode?: number) => void;
/**
 * Send a validation error response
 */
export declare const sendValidationError: (res: Response, error: string) => void;
/**
 * Send an unauthorized error response
 */
export declare const sendUnauthorizedError: (res: Response, error?: string) => void;
/**
 * Send a forbidden error response
 */
export declare const sendForbiddenError: (res: Response, error?: string) => void;
/**
 * Send a not found error response
 */
export declare const sendNotFoundError: (res: Response, error?: string) => void;
/**
 * Send an internal server error response
 */
export declare const sendInternalServerError: (res: Response, error?: string) => void;
/**
 * Calculate pagination metadata
 */
export declare const calculatePagination: (totalItems: number, page?: number, limit?: number) => PaginationOptions;
/**
 * Parse pagination parameters from query
 */
export declare const parsePaginationParams: (query: any) => {
    page: number;
    limit: number;
};
//# sourceMappingURL=response.d.ts.map