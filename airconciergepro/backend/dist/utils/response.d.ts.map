{"version": 3, "file": "response.d.ts", "sourceRoot": "", "sources": ["../../src/utils/response.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEnC,MAAM,WAAW,WAAW,CAAC,CAAC,GAAG,GAAG;IAClC,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE;QACX,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,MAAM,CAAC;QACnB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;CACH;AAED,MAAM,WAAW,iBAAiB;IAChC,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,eAAO,MAAM,WAAW,GAAI,CAAC,EAC3B,KAAK,QAAQ,EACb,MAAM,CAAC,EACP,aAAY,MAAY,EACxB,aAAa,iBAAiB,KAC7B,IAWF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,SAAS,GACpB,KAAK,QAAQ,EACb,OAAO,MAAM,EACb,aAAY,MAAY,KACvB,IAOF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,mBAAmB,GAC9B,KAAK,QAAQ,EACb,OAAO,MAAM,KACZ,IAEF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,qBAAqB,GAChC,KAAK,QAAQ,EACb,QAAO,MAAuB,KAC7B,IAEF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,kBAAkB,GAC7B,KAAK,QAAQ,EACb,QAAO,MAAoB,KAC1B,IAEF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,iBAAiB,GAC5B,KAAK,QAAQ,EACb,QAAO,MAA6B,KACnC,IAEF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,uBAAuB,GAClC,KAAK,QAAQ,EACb,QAAO,MAAgC,KACtC,IAEF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,mBAAmB,GAC9B,YAAY,MAAM,EAClB,OAAM,MAAU,EAChB,QAAO,MAAW,KACjB,iBAUF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,qBAAqB,GAAI,OAAO,GAAG,KAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAK/E,CAAC"}