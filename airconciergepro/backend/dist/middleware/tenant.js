"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkTenantLimits = exports.tenantMiddleware = void 0;
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const tenantMiddleware = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required.' });
        }
        // Get tenant details
        const tenantResult = await (0, database_1.query)('SELECT id, name, plan, status, settings FROM tenants WHERE id = $1', [req.user.tenant_id]);
        if (tenantResult.rows.length === 0) {
            return res.status(404).json({ error: 'Tenant not found.' });
        }
        const tenant = tenantResult.rows[0];
        if (tenant.status !== 'active' && tenant.status !== 'trial') {
            return res.status(403).json({ error: 'Tenant account is suspended.' });
        }
        req.tenant = tenant;
        next();
    }
    catch (error) {
        logger_1.logger.error('Tenant middleware error:', error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
exports.tenantMiddleware = tenantMiddleware;
const checkTenantLimits = (resource) => {
    return async (req, res, next) => {
        try {
            if (!req.tenant) {
                return res.status(400).json({ error: 'Tenant information required.' });
            }
            const limits = req.tenant.settings?.limits;
            if (!limits) {
                return next(); // No limits set, allow request
            }
            switch (resource) {
                case 'bookings':
                    if (limits.monthly_bookings) {
                        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
                        const bookingCount = await (0, database_1.query)(`SELECT COUNT(*) as count FROM bookings 
               WHERE tenant_id = $1 AND DATE_TRUNC('month', created_at) = $2`, [req.tenant.id, currentMonth + '-01']);
                        if (parseInt(bookingCount.rows[0].count) >= limits.monthly_bookings) {
                            return res.status(429).json({
                                error: 'Monthly booking limit exceeded. Please upgrade your plan.'
                            });
                        }
                    }
                    break;
                case 'api_calls':
                    // This would be implemented with a more sophisticated rate limiting system
                    break;
            }
            next();
        }
        catch (error) {
            logger_1.logger.error('Tenant limits check error:', error);
            res.status(500).json({ error: 'Internal server error.' });
        }
    };
};
exports.checkTenantLimits = checkTenantLimits;
//# sourceMappingURL=tenant.js.map