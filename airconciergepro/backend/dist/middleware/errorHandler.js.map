{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,+CAA4C;AAO5C,MAAa,QAAS,SAAQ,KAAK;IAIjC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,4BAWC;AAEM,MAAM,YAAY,GAAG,CAAC,GAAa,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAE5B,YAAY;IACZ,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;QAC7B,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAC;IAEH,oBAAoB;IACpB,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,IAAK,GAAW,CAAC,IAAI,EAAE,CAAC;QAC9C,QAAS,GAAW,CAAC,IAAI,EAAE,CAAC;YAC1B,KAAK,OAAO,EAAE,mBAAmB;gBAC/B,KAAK,GAAG,IAAI,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,OAAO,EAAE,wBAAwB;gBACpC,KAAK,GAAG,IAAI,QAAQ,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC;gBAChE,MAAM;YACR,KAAK,OAAO,EAAE,kBAAkB;gBAC9B,KAAK,GAAG,IAAI,QAAQ,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;gBACnD,MAAM;YACR;gBACE,KAAK,GAAG,IAAI,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IAEzD,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,OAAO;QACd,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC;KACpE,CAAC,CAAC;AACL,CAAC,CAAC;AAxCW,QAAA,YAAY,gBAwCvB;AAIK,MAAM,YAAY,GAAG,CAC1B,EAA0E,EAC1E,EAAE,CAAC,CAAC,GAAY,EAAE,GAAoB,EAAE,IAAkB,EAAqB,EAAE;IACjF,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzD,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB"}