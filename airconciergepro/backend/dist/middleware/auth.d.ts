import { Request, Response, NextFunction } from 'express';
export interface AuthRequest extends Request {
    user?: {
        id: string;
        tenant_id: string;
        role: string;
        permissions: string[];
    };
}
export declare const authMiddleware: (req: AuthRequest, res: Response, next: NextFunction) => Promise<any>;
export declare const requirePermission: (permission: string) => (req: AuthRequest, res: Response, next: NextFunction) => any;
export declare const requireRole: (roles: string[]) => (req: AuthRequest, res: Response, next: NextFunction) => any;
export declare const authenticateToken: (req: AuthRequest, res: Response, next: NextFunction) => Promise<any>;
//# sourceMappingURL=auth.d.ts.map