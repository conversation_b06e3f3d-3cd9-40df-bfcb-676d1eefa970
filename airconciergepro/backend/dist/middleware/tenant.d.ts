import { Response, NextFunction } from 'express';
import { AuthRequest } from './auth';
export interface TenantRequest extends AuthRequest {
    tenant?: {
        id: string;
        name: string;
        plan: string;
        status: string;
        settings: any;
    };
}
export declare const tenantMiddleware: (req: TenantRequest, res: Response, next: NextFunction) => Promise<any>;
export declare const checkTenantLimits: (resource: string) => (req: TenantRequest, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
//# sourceMappingURL=tenant.d.ts.map