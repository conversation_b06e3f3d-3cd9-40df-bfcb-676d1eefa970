import { Request, Response, NextFunction } from 'express';
export interface AppError extends <PERSON>rror {
    statusCode: number;
    isOperational: boolean;
}
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode: number);
}
export declare const errorHandler: (err: AppError, req: Request, res: Response, next: NextFunction) => void;
import { Response as ExpressResponse } from 'express';
export declare const asyncHandler: <T = void>(fn: (req: Request, res: ExpressResponse, next: NextFunction) => Promise<T>) => (req: Request, res: ExpressResponse, next: NextFunction) => Promise<T | void>;
//# sourceMappingURL=errorHandler.d.ts.map