"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateToken = exports.requireRole = exports.requirePermission = exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const authMiddleware = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ error: 'Access denied. No token provided.' });
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback_secret');
        // Get user details from database
        const userResult = await (0, database_1.query)('SELECT id, tenant_id, role, permissions, status FROM users WHERE id = $1', [decoded.userId]);
        if (userResult.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid token. User not found.' });
        }
        const user = userResult.rows[0];
        if (user.status !== 'active') {
            return res.status(401).json({ error: 'Account is not active.' });
        }
        req.user = {
            id: user.id,
            tenant_id: user.tenant_id,
            role: user.role,
            permissions: user.permissions || []
        };
        next();
    }
    catch (error) {
        logger_1.logger.error('Auth middleware error:', error);
        res.status(401).json({ error: 'Invalid token.' });
    }
};
exports.authMiddleware = authMiddleware;
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required.' });
        }
        // Check if user has the specific permission, "all" permission, or is super_admin
        if (!req.user.permissions.includes(permission) &&
            !req.user.permissions.includes('all') &&
            req.user.role !== 'super_admin') {
            return res.status(403).json({ error: 'Insufficient permissions.' });
        }
        next();
    };
};
exports.requirePermission = requirePermission;
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required.' });
        }
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient role permissions.' });
        }
        next();
    };
};
exports.requireRole = requireRole;
// Export alias for backward compatibility
exports.authenticateToken = exports.authMiddleware;
//# sourceMappingURL=auth.js.map