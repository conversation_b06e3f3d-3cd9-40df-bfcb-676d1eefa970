"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const joi_1 = __importDefault(require("joi"));
const auth_1 = require("../middleware/auth");
const logger_1 = require("../services/logger");
const availability_1 = require("../services/availability");
const router = express_1.default.Router();
// Validation schemas
const checkAvailabilitySchema = joi_1.default.object({
    serviceId: joi_1.default.string().uuid().required(),
    date: joi_1.default.date().iso().required(),
    timeSlot: joi_1.default.string().optional(),
    passengerCount: joi_1.default.number().integer().min(1).required(),
    duration: joi_1.default.number().integer().min(15).max(480).optional(), // 15 minutes to 8 hours
    airportCode: joi_1.default.string().length(3).optional()
});
const holdTimeSlotSchema = joi_1.default.object({
    serviceId: joi_1.default.string().uuid().required(),
    date: joi_1.default.date().iso().required(),
    timeSlot: joi_1.default.string().required(),
    passengerCount: joi_1.default.number().integer().min(1).required(),
    customerId: joi_1.default.string().uuid().optional()
});
const calendarSchema = joi_1.default.object({
    serviceId: joi_1.default.string().uuid().required(),
    month: joi_1.default.number().integer().min(1).max(12).required(),
    year: joi_1.default.number().integer().min(2024).max(2030).required()
});
/**
 * Check availability for a service
 * POST /api/v1/availability/check
 */
router.post('/check', (0, auth_1.requirePermission)('view_availability'), async (req, res) => {
    try {
        const { error, value } = checkAvailabilitySchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        const availability = await availability_1.availabilityService.checkAvailability({
            tenantId: req.user.tenant_id,
            ...value
        });
        res.json({
            success: true,
            data: availability
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Check availability error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Get availability calendar for a month
 * GET /api/v1/availability/calendar
 */
router.get('/calendar', (0, auth_1.requirePermission)('view_availability'), async (req, res) => {
    try {
        const { error, value } = calendarSchema.validate(req.query);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        const calendar = await availability_1.availabilityService.getAvailabilityCalendar(req.user.tenant_id, value.serviceId, value.month, value.year);
        res.json({
            success: true,
            data: calendar
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Get availability calendar error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Hold a time slot temporarily
 * POST /api/v1/availability/hold
 */
router.post('/hold', (0, auth_1.requirePermission)('create_booking'), async (req, res) => {
    try {
        const { error, value } = holdTimeSlotSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        const hold = await availability_1.availabilityService.holdTimeSlot(req.user.tenant_id, value.serviceId, new Date(value.date), value.timeSlot, value.passengerCount, value.customerId);
        if (hold.success) {
            res.json({
                success: true,
                data: {
                    holdId: hold.holdId,
                    expiresAt: hold.expiresAt
                }
            });
        }
        else {
            res.status(409).json({
                error: 'Time slot is no longer available',
                code: 'SLOT_UNAVAILABLE'
            });
        }
        return;
    }
    catch (error) {
        logger_1.logger.error('Hold time slot error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Get capacity utilization metrics
 * GET /api/v1/availability/metrics
 */
router.get('/metrics', (0, auth_1.requirePermission)('view_analytics'), async (req, res) => {
    try {
        const serviceId = req.query.serviceId;
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        if (!serviceId || !startDate || !endDate) {
            return res.status(400).json({
                error: 'serviceId, startDate, and endDate are required'
            });
        }
        const metrics = await availability_1.availabilityService.getCapacityMetrics(req.user.tenant_id, serviceId, new Date(startDate), new Date(endDate));
        res.json({
            success: true,
            data: metrics
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Get capacity metrics error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Get real-time availability for dashboard
 * GET /api/v1/availability/realtime
 */
router.get('/realtime', (0, auth_1.requirePermission)('view_operations'), async (req, res) => {
    try {
        const realTimeData = await availability_1.availabilityService.getRealTimeAvailability(req.user.tenant_id);
        res.json({
            success: true,
            data: realTimeData
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Get real-time availability error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Get availability status for multiple services
 * POST /api/v1/availability/bulk-check
 */
router.post('/bulk-check', (0, auth_1.requirePermission)('view_availability'), async (req, res) => {
    try {
        const { serviceIds, date, passengerCount } = req.body;
        if (!Array.isArray(serviceIds) || !date || !passengerCount) {
            return res.status(400).json({
                error: 'serviceIds (array), date, and passengerCount are required'
            });
        }
        const results = await Promise.allSettled(serviceIds.map(serviceId => availability_1.availabilityService.checkAvailability({
            tenantId: req.user.tenant_id,
            serviceId,
            date: new Date(date),
            passengerCount
        })));
        const availability = serviceIds.map((serviceId, index) => {
            const result = results[index];
            return {
                serviceId,
                success: result.status === 'fulfilled',
                data: result.status === 'fulfilled' ? result.value : null,
                error: result.status === 'rejected' ? result.reason.message : null
            };
        });
        res.json({
            success: true,
            data: availability
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Bulk availability check error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Get availability summary for operations dashboard
 * GET /api/v1/availability/summary
 */
router.get('/summary', (0, auth_1.requirePermission)('view_operations'), async (req, res) => {
    try {
        const date = req.query.date || new Date().toISOString().split('T')[0];
        // This would aggregate availability across all services for a tenant
        // For now, return real-time data
        const summary = await availability_1.availabilityService.getRealTimeAvailability(req.user.tenant_id);
        res.json({
            success: true,
            data: {
                date,
                summary
            }
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Get availability summary error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
/**
 * Release expired holds manually (admin endpoint)
 * POST /api/v1/availability/release-holds
 */
router.post('/release-holds', (0, auth_1.requirePermission)('manage_system'), async (req, res) => {
    try {
        const releasedCount = await availability_1.availabilityService.releaseExpiredHolds();
        res.json({
            success: true,
            data: {
                releasedHolds: releasedCount,
                message: `Released ${releasedCount} expired holds`
            }
        });
        return;
    }
    catch (error) {
        logger_1.logger.error('Release holds error:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }
});
exports.default = router;
//# sourceMappingURL=availability.js.map