"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const errorHandler_1 = require("../middleware/errorHandler");
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const flightInformation_1 = require("../services/flightInformation");
const response_1 = require("../utils/response");
const router = express_1.default.Router();
/**
 * @route GET /api/v1/realtime/dashboard
 * @desc Get real-time dashboard data
 * @access Private
 */
router.get('/dashboard', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        // Get active bookings with real-time status
        const activeBookingsResult = await (0, database_1.query)(`SELECT b.*, 
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              s.name as service_name, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name,
              a.status as agent_status, a.current_location as agent_location
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       LEFT JOIN services s ON b.service_id = s.id
       LEFT JOIN users u ON b.assigned_agent_id = u.id
       LEFT JOIN agents a ON u.id = a.user_id
       WHERE b.tenant_id = $1 
         AND b.status IN ('confirmed', 'in_progress', 'agent_assigned')
         AND DATE(b.flight_date) = CURRENT_DATE
       ORDER BY b.flight_date ASC`, [tenantId]);
        // Get agent status summary
        const agentStatusResult = await (0, database_1.query)(`SELECT 
         a.status,
         COUNT(*) as count
       FROM agents a
       JOIN users u ON a.user_id = u.id
       WHERE u.tenant_id = $1 AND u.status = 'active'
       GROUP BY a.status`, [tenantId]);
        // Get today's statistics
        const todayStatsResult = await (0, database_1.query)(`SELECT 
         COUNT(*) as total_bookings,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
         COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_bookings,
         COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
         SUM(total_price) as total_revenue
       FROM bookings 
       WHERE tenant_id = $1 AND DATE(created_at) = CURRENT_DATE`, [tenantId]);
        // Get flight updates for today's bookings
        const flightUpdates = [];
        for (const booking of activeBookingsResult.rows) {
            if (booking.flight_number) {
                try {
                    const flightInfo = await flightInformation_1.flightInformationService.getFlightInfo(booking.flight_number, new Date(booking.flight_date));
                    if (flightInfo) {
                        flightUpdates.push({
                            bookingId: booking.id,
                            flightNumber: booking.flight_number,
                            status: flightInfo.status,
                            gate: flightInfo.gate,
                            terminal: flightInfo.terminal,
                            estimatedArrival: flightInfo.estimatedArrival,
                            delay: flightInfo.delayReason ? true : false
                        });
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Error fetching flight info for ${booking.flight_number}:`, error);
                }
            }
        }
        // Get WebSocket service for agent locations
        const wsService = global.websocketService;
        const agentLocations = wsService ? wsService.getAgentLocations(tenantId) : [];
        return (0, response_1.sendSuccess)(res, {
            activeBookings: activeBookingsResult.rows,
            agentStatus: agentStatusResult.rows.reduce((acc, row) => {
                acc[row.status] = parseInt(row.count);
                return acc;
            }, {}),
            todayStats: todayStatsResult.rows[0],
            flightUpdates,
            agentLocations,
            lastUpdated: new Date()
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching dashboard data:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch dashboard data');
    }
}));
/**
 * @route GET /api/v1/realtime/agents
 * @desc Get real-time agent status and locations
 * @access Private
 */
router.get('/agents', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        // Get agents with current status and location
        const agentsResult = await (0, database_1.query)(`SELECT 
         u.id, u.first_name, u.last_name, u.email,
         a.status, a.current_location, a.specializations, a.languages,
         a.rating, a.total_services,
         COUNT(b.id) as active_bookings
       FROM users u
       JOIN agents a ON u.id = a.user_id
       LEFT JOIN bookings b ON u.id = b.assigned_agent_id 
         AND b.status IN ('confirmed', 'in_progress', 'agent_assigned')
       WHERE u.tenant_id = $1 AND u.role = 'field_agent' AND u.status = 'active'
       GROUP BY u.id, u.first_name, u.last_name, u.email, a.status, 
                a.current_location, a.specializations, a.languages, a.rating, a.total_services
       ORDER BY a.status, u.first_name`, [tenantId]);
        // Get real-time locations from WebSocket service
        const wsService = global.websocketService;
        const realtimeLocations = wsService ? wsService.getAgentLocations(tenantId) : [];
        // Merge database data with real-time data
        const agents = agentsResult.rows.map((agent) => {
            const realtimeLocation = realtimeLocations.find((loc) => loc.agentId === agent.id);
            return {
                ...agent,
                realtimeLocation: realtimeLocation || null,
                lastLocationUpdate: realtimeLocation?.timestamp || null
            };
        });
        return (0, response_1.sendSuccess)(res, {
            agents,
            summary: {
                total: agents.length,
                available: agents.filter((a) => a.status === 'available').length,
                busy: agents.filter((a) => a.status === 'busy').length,
                offline: agents.filter((a) => a.status === 'offline').length
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching agent data:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch agent data');
    }
}));
/**
 * @route GET /api/v1/realtime/flights
 * @desc Get real-time flight information for today's bookings
 * @access Private
 */
router.get('/flights', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const date = req.query.date ? new Date(req.query.date) : new Date();
        // Get unique flight numbers for the date
        const flightNumbersResult = await (0, database_1.query)(`SELECT DISTINCT flight_number, flight_date
       FROM bookings 
       WHERE tenant_id = $1 
         AND flight_number IS NOT NULL 
         AND DATE(flight_date) = DATE($2)`, [tenantId, date]);
        const flightUpdates = [];
        for (const row of flightNumbersResult.rows) {
            try {
                const flightInfo = await flightInformation_1.flightInformationService.getFlightInfo(row.flight_number, new Date(row.flight_date));
                if (flightInfo) {
                    // Get bookings for this flight
                    const bookingsResult = await (0, database_1.query)(`SELECT id, booking_reference, customer_id, assigned_agent_id, status
             FROM bookings 
             WHERE tenant_id = $1 AND flight_number = $2 AND DATE(flight_date) = DATE($3)`, [tenantId, row.flight_number, row.flight_date]);
                    flightUpdates.push({
                        flightNumber: row.flight_number,
                        flightDate: row.flight_date,
                        flightInfo,
                        affectedBookings: bookingsResult.rows
                    });
                }
            }
            catch (error) {
                logger_1.logger.error(`Error fetching flight info for ${row.flight_number}:`, error);
            }
        }
        return (0, response_1.sendSuccess)(res, {
            date: date.toISOString().split('T')[0],
            flights: flightUpdates,
            lastUpdated: new Date()
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching flight data:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch flight data');
    }
}));
/**
 * @route POST /api/v1/realtime/notifications
 * @desc Send real-time notification to specific users or groups
 * @access Private
 */
router.post('/notifications', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { type, title, message, targetUsers, targetRoles, data } = req.body;
        if (!type || !title || !message) {
            return (0, response_1.sendValidationError)(res, 'Type, title, and message are required');
        }
        const wsService = global.websocketService;
        if (!wsService) {
            return (0, response_1.sendError)(res, 'Real-time service not available', 503);
        }
        const notification = {
            type,
            title,
            message,
            data: data || {},
            sentBy: req.user.id,
            sentAt: new Date()
        };
        // Send to specific users
        if (targetUsers && Array.isArray(targetUsers)) {
            // Implementation would target specific user sockets
            // For now, broadcast to tenant
            wsService.broadcastSystemNotification(tenantId, notification);
        }
        // Send to specific roles
        if (targetRoles && Array.isArray(targetRoles)) {
            // Implementation would target role-specific rooms
            wsService.broadcastSystemNotification(tenantId, notification);
        }
        // If no specific targets, broadcast to entire tenant
        if (!targetUsers && !targetRoles) {
            wsService.broadcastSystemNotification(tenantId, notification);
        }
        return (0, response_1.sendSuccess)(res, {
            message: 'Notification sent successfully',
            notification
        });
    }
    catch (error) {
        logger_1.logger.error('Error sending notification:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to send notification');
    }
}));
/**
 * @route GET /api/v1/realtime/status
 * @desc Get real-time service status
 * @access Private
 */
router.get('/status', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const wsService = global.websocketService;
        return (0, response_1.sendSuccess)(res, {
            websocketConnected: !!wsService,
            connectedClients: wsService ? wsService.getConnectedClientsCount() : 0,
            services: {
                flightInformation: true,
                aviationStack: true,
                notifications: true
            },
            lastUpdated: new Date()
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching status:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch status');
    }
}));
exports.default = router;
//# sourceMappingURL=realtime.js.map