"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const database_1 = require("../services/database");
const errorHandler_1 = require("../middleware/errorHandler");
const router = express_1.default.Router();
// Get booking analytics
router.get('/bookings', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const timeframe = req.query.timeframe || '30d';
    const groupBy = req.query.groupBy || 'day';
    let dateFilter = '';
    let groupByClause = '';
    switch (timeframe) {
        case '7d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '7 days'";
            break;
        case '30d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
            break;
        case '90d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '90 days'";
            break;
        case '1y':
            dateFilter = "AND created_at >= NOW() - INTERVAL '1 year'";
            break;
    }
    switch (groupBy) {
        case 'hour':
            groupByClause = "DATE_TRUNC('hour', created_at)";
            break;
        case 'day':
            groupByClause = "DATE_TRUNC('day', created_at)";
            break;
        case 'week':
            groupByClause = "DATE_TRUNC('week', created_at)";
            break;
        case 'month':
            groupByClause = "DATE_TRUNC('month', created_at)";
            break;
    }
    const result = await (0, database_1.query)(`SELECT 
       ${groupByClause} as period,
       COUNT(*) as total_bookings,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
       COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
       SUM(total_price) as revenue,
       AVG(total_price) as avg_booking_value
     FROM bookings 
     WHERE tenant_id = $1 ${dateFilter}
     GROUP BY ${groupByClause}
     ORDER BY period DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: result.rows
    });
}));
// Get revenue analytics
router.get('/revenue', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const timeframe = req.query.timeframe || '30d';
    let dateFilter = '';
    switch (timeframe) {
        case '7d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '7 days'";
            break;
        case '30d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
            break;
        case '90d':
            dateFilter = "AND created_at >= NOW() - INTERVAL '90 days'";
            break;
        case '1y':
            dateFilter = "AND created_at >= NOW() - INTERVAL '1 year'";
            break;
    }
    const revenueResult = await (0, database_1.query)(`SELECT 
       SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as total_revenue,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
       AVG(CASE WHEN status = 'completed' THEN total_price END) as avg_booking_value,
       SUM(CASE WHEN status = 'cancelled' THEN total_price ELSE 0 END) as lost_revenue
     FROM bookings 
     WHERE tenant_id = $1 ${dateFilter}`, [req.user.tenant_id]);
    const serviceRevenueResult = await (0, database_1.query)(`SELECT 
       s.name as service_name,
       s.category,
       COUNT(b.id) as bookings_count,
       SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END) as revenue
     FROM services s
     LEFT JOIN bookings b ON s.id = b.service_id AND b.tenant_id = $1 ${dateFilter}
     WHERE s.tenant_id = $1
     GROUP BY s.id, s.name, s.category
     ORDER BY revenue DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: {
            summary: revenueResult.rows[0],
            byService: serviceRevenueResult.rows
        }
    });
}));
// Get customer analytics
router.get('/customers', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const customerStatsResult = await (0, database_1.query)(`SELECT 
       COUNT(*) as total_customers,
       COUNT(CASE WHEN total_bookings > 1 THEN 1 END) as repeat_customers,
       AVG(total_bookings) as avg_bookings_per_customer,
       AVG(total_spent) as avg_spent_per_customer
     FROM customers 
     WHERE tenant_id = $1`, [req.user.tenant_id]);
    const topCustomersResult = await (0, database_1.query)(`SELECT 
       first_name, last_name, email, total_bookings, total_spent
     FROM customers 
     WHERE tenant_id = $1
     ORDER BY total_spent DESC
     LIMIT 10`, [req.user.tenant_id]);
    const newCustomersResult = await (0, database_1.query)(`SELECT 
       DATE_TRUNC('day', created_at) as date,
       COUNT(*) as new_customers
     FROM customers 
     WHERE tenant_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
     GROUP BY DATE_TRUNC('day', created_at)
     ORDER BY date DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: {
            summary: customerStatsResult.rows[0],
            topCustomers: topCustomersResult.rows,
            newCustomers: newCustomersResult.rows
        }
    });
}));
// Get service performance analytics
router.get('/services', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const serviceStatsResult = await (0, database_1.query)(`SELECT 
       s.id,
       s.name,
       s.category,
       s.type,
       COUNT(b.id) as total_bookings,
       COUNT(CASE WHEN b.status = 'completed' THEN 1 END) as completed_bookings,
       COUNT(CASE WHEN b.status = 'cancelled' THEN 1 END) as cancelled_bookings,
       SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END) as revenue,
       AVG(CASE WHEN b.status = 'completed' THEN b.total_price END) as avg_price
     FROM services s
     LEFT JOIN bookings b ON s.id = b.service_id
     WHERE s.tenant_id = $1
     GROUP BY s.id, s.name, s.category, s.type
     ORDER BY revenue DESC`, [req.user.tenant_id]);
    const categoryStatsResult = await (0, database_1.query)(`SELECT 
       s.category,
       COUNT(DISTINCT s.id) as service_count,
       COUNT(b.id) as total_bookings,
       SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END) as revenue
     FROM services s
     LEFT JOIN bookings b ON s.id = b.service_id
     WHERE s.tenant_id = $1
     GROUP BY s.category
     ORDER BY revenue DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: {
            services: serviceStatsResult.rows,
            categories: categoryStatsResult.rows
        }
    });
}));
// Get agent performance analytics
router.get('/agents', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const agentStatsResult = await (0, database_1.query)(`SELECT 
       u.id,
       u.first_name,
       u.last_name,
       a.rating,
       a.total_services,
       COUNT(b.id) as bookings_handled,
       COUNT(CASE WHEN b.status = 'completed' THEN 1 END) as completed_services,
       AVG(CASE WHEN b.status = 'completed' THEN 
         EXTRACT(EPOCH FROM (b.service_end_time - b.service_start_time))/60 
       END) as avg_service_duration_minutes
     FROM users u
     LEFT JOIN agents a ON u.id = a.user_id
     LEFT JOIN bookings b ON u.id = b.assigned_agent_id
     WHERE u.tenant_id = $1 AND u.role = 'field_agent'
     GROUP BY u.id, u.first_name, u.last_name, a.rating, a.total_services
     ORDER BY completed_services DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: agentStatsResult.rows
    });
}));
// Get airport analytics
router.get('/airports', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const airportStatsResult = await (0, database_1.query)(`SELECT 
       arrival_airport as airport,
       COUNT(*) as arrival_bookings,
       SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as arrival_revenue
     FROM bookings 
     WHERE tenant_id = $1 AND service_type IN ('arrival', 'transit')
     GROUP BY arrival_airport
     UNION ALL
     SELECT 
       departure_airport as airport,
       COUNT(*) as departure_bookings,
       SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as departure_revenue
     FROM bookings 
     WHERE tenant_id = $1 AND service_type = 'departure'
     GROUP BY departure_airport
     ORDER BY arrival_revenue + departure_revenue DESC`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: airportStatsResult.rows
    });
}));
// Get real-time dashboard data
router.get('/dashboard', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // Today's key metrics
    const todayStatsResult = await (0, database_1.query)(`SELECT 
       COUNT(*) as today_bookings,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as today_completed,
       COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_services,
       SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as today_revenue
     FROM bookings 
     WHERE tenant_id = $1 AND DATE(created_at) = CURRENT_DATE`, [req.user.tenant_id]);
    // Upcoming services (next 4 hours)
    const upcomingServicesResult = await (0, database_1.query)(`SELECT 
       b.id, b.booking_reference, b.flight_number, b.service_type,
       b.estimated_arrival, b.meeting_point,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       u.first_name as agent_first_name, u.last_name as agent_last_name
     FROM bookings b
     LEFT JOIN customers c ON b.customer_id = c.id
     LEFT JOIN users u ON b.assigned_agent_id = u.id
     WHERE b.tenant_id = $1 
       AND b.status = 'confirmed'
       AND b.estimated_arrival BETWEEN NOW() AND NOW() + INTERVAL '4 hours'
     ORDER BY b.estimated_arrival ASC
     LIMIT 10`, [req.user.tenant_id]);
    // Recent activities
    const recentActivitiesResult = await (0, database_1.query)(`SELECT 
       ba.activity_type, ba.description, ba.created_at,
       b.booking_reference,
       u.first_name, u.last_name
     FROM booking_activities ba
     LEFT JOIN bookings b ON ba.booking_id = b.id
     LEFT JOIN users u ON ba.user_id = u.id
     WHERE b.tenant_id = $1
     ORDER BY ba.created_at DESC
     LIMIT 20`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: {
            todayStats: todayStatsResult.rows[0],
            upcomingServices: upcomingServicesResult.rows,
            recentActivities: recentActivitiesResult.rows
        }
    });
}));
exports.default = router;
//# sourceMappingURL=analytics.js.map