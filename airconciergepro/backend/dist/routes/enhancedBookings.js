"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../services/database");
const tenant_1 = require("../middleware/tenant");
const auth_1 = require("../middleware/auth");
const logger_1 = require("../services/logger");
const dynamicPricing_1 = require("../services/dynamicPricing");
const notification_1 = require("../services/notification");
const flightInformation_1 = require("../services/flightInformation");
const router = express_1.default.Router();
// Enhanced validation schema
const createBookingSchema = joi_1.default.object({
    customerId: joi_1.default.string().uuid().required(),
    serviceId: joi_1.default.string().uuid().required(),
    flightNumber: joi_1.default.string().required(),
    airline: joi_1.default.string().required(),
    departureAirport: joi_1.default.string().length(3).required(),
    arrivalAirport: joi_1.default.string().length(3).required(),
    flightDate: joi_1.default.date().iso().required(),
    estimatedArrival: joi_1.default.date().iso(),
    serviceType: joi_1.default.string().valid('arrival', 'departure', 'transit').required(),
    passengerCount: joi_1.default.number().integer().min(1).required(),
    passengers: joi_1.default.array().items(joi_1.default.object({
        firstName: joi_1.default.string().required(),
        lastName: joi_1.default.string().required(),
        age: joi_1.default.number().integer().min(0).max(120),
        specialRequirements: joi_1.default.array().items(joi_1.default.string()),
        contactNumber: joi_1.default.string()
    })).required(),
    specialRequirements: joi_1.default.array().items(joi_1.default.string()),
    meetingPoint: joi_1.default.string(),
    whitelabelConfigId: joi_1.default.string().uuid(),
    requestedEquipment: joi_1.default.array().items(joi_1.default.string()),
    applyDynamicPricing: joi_1.default.boolean().default(true)
});
const updateBookingSchema = joi_1.default.object({
    status: joi_1.default.string().valid('confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'),
    assignedAgentId: joi_1.default.string().uuid(),
    estimatedArrival: joi_1.default.date().iso(),
    actualArrival: joi_1.default.date().iso(),
    serviceStartTime: joi_1.default.date().iso(),
    serviceEndTime: joi_1.default.date().iso(),
    specialRequirements: joi_1.default.array().items(joi_1.default.string()),
    meetingPoint: joi_1.default.string(),
    assignedEquipment: joi_1.default.array().items(joi_1.default.string())
});
// Generate unique booking reference
const generateBookingReference = () => {
    const prefix = 'ACG';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
};
// Enhanced create booking with dynamic pricing and real-time flight data
router.post('/', (0, tenant_1.checkTenantLimits)('bookings'), (0, auth_1.requirePermission)('create_booking'), async (req, res) => {
    try {
        const { error, value } = createBookingSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        const bookingId = (0, uuid_1.v4)();
        const bookingReference = generateBookingReference();
        // Get customer details including group for pricing
        const customerResult = await (0, database_1.query)('SELECT id, user_group_id, email, phone, first_name, last_name FROM customers WHERE id = $1 AND tenant_id = $2', [value.customerId, req.user.tenant_id]);
        if (customerResult.rows.length === 0) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        const customer = customerResult.rows[0];
        // Get real-time flight information
        const flightInfo = await flightInformation_1.flightInformationService.getFlightInfo(value.flightNumber, new Date(value.flightDate));
        let estimatedArrival = value.estimatedArrival;
        if (flightInfo && flightInfo.estimatedArrival) {
            estimatedArrival = flightInfo.estimatedArrival;
        }
        // Calculate dynamic pricing
        let finalPrice;
        let pricingBreakdown = null;
        if (value.applyDynamicPricing) {
            const pricingContext = {
                serviceId: value.serviceId,
                tenantId: req.user.tenant_id,
                passengerCount: value.passengerCount,
                bookingDate: new Date(),
                serviceDate: new Date(value.flightDate),
                customerGroupId: customer.user_group_id,
                isWhiteLabel: !!value.whitelabelConfigId,
                whitelabelPartnerId: value.whitelabelConfigId
            };
            const pricing = await dynamicPricing_1.dynamicPricingService.calculatePrice(pricingContext);
            finalPrice = pricing.finalPrice;
            pricingBreakdown = pricing;
        }
        else {
            // Get base service price
            const serviceResult = await (0, database_1.query)('SELECT base_price FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3', [value.serviceId, req.user.tenant_id, 'active']);
            if (serviceResult.rows.length === 0) {
                return res.status(404).json({ error: 'Service not found or inactive' });
            }
            finalPrice = serviceResult.rows[0].base_price * value.passengerCount;
        }
        // Get service currency
        const serviceResult = await (0, database_1.query)('SELECT currency FROM services WHERE id = $1', [value.serviceId]);
        const currency = serviceResult.rows[0]?.currency || 'USD';
        // Create booking with enhanced data
        const bookingResult = await (0, database_1.query)(`INSERT INTO bookings (
          id, tenant_id, customer_id, service_id, booking_reference, status,
          flight_number, airline, departure_airport, arrival_airport, flight_date,
          estimated_arrival, actual_arrival, service_type, passenger_count, passengers,
          special_requirements, meeting_point, base_price, total_price,
          currency, payment_status, white_label_config_id, assigned_equipment,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, NOW(), NOW()
        ) RETURNING *`, [
            bookingId,
            req.user.tenant_id,
            value.customerId,
            value.serviceId,
            bookingReference,
            'confirmed',
            value.flightNumber,
            value.airline,
            value.departureAirport,
            value.arrivalAirport,
            value.flightDate,
            estimatedArrival,
            flightInfo?.actualArrival || null,
            value.serviceType,
            value.passengerCount,
            JSON.stringify(value.passengers),
            JSON.stringify(value.specialRequirements || []),
            value.meetingPoint || 'Main Terminal',
            pricingBreakdown?.basePrice || finalPrice,
            finalPrice,
            currency,
            'pending',
            value.whitelabelConfigId || null,
            JSON.stringify(value.requestedEquipment || [])
        ]);
        // Log booking activity
        await (0, database_1.query)(`INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`, [
            (0, uuid_1.v4)(),
            bookingId,
            req.user.id,
            'created',
            `Booking created for ${value.flightNumber} - ${value.serviceType}`,
            JSON.stringify({
                flightInfo: flightInfo ? {
                    status: flightInfo.status,
                    gate: flightInfo.gate,
                    terminal: flightInfo.terminal
                } : null,
                pricing: pricingBreakdown
            })
        ]);
        // Send booking confirmation notification
        await notification_1.notificationService.sendBookingConfirmation(req.user.tenant_id, bookingId, customer.email, {
            bookingReference,
            flightNumber: value.flightNumber,
            flightDate: value.flightDate,
            serviceName: 'Meet & Greet Service',
            meetingPoint: value.meetingPoint || 'Main Terminal'
        });
        // Emit real-time update
        const io = req.app.get('io');
        io.to(`tenant_${req.user.tenant_id}`).emit('booking_created', {
            booking: bookingResult.rows[0],
            flightInfo,
            pricing: pricingBreakdown
        });
        return res.status(201).json({
            success: true,
            data: {
                booking: bookingResult.rows[0],
                flightInfo,
                pricing: pricingBreakdown
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Enhanced booking creation error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});
// Get all bookings with enhanced filtering
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;
        // Build filter conditions
        const filters = ['b.tenant_id = $1'];
        const params = [req.user.tenant_id];
        let paramIndex = 2;
        if (req.query.status) {
            filters.push(`b.status = $${paramIndex}`);
            params.push(req.query.status);
            paramIndex++;
        }
        if (req.query.serviceType) {
            filters.push(`b.service_type = $${paramIndex}`);
            params.push(req.query.serviceType);
            paramIndex++;
        }
        if (req.query.flightDate) {
            filters.push(`DATE(b.flight_date) = $${paramIndex}`);
            params.push(req.query.flightDate);
            paramIndex++;
        }
        if (req.query.airport) {
            filters.push(`(b.departure_airport = $${paramIndex} OR b.arrival_airport = $${paramIndex})`);
            params.push(req.query.airport);
            paramIndex++;
        }
        if (req.query.assignedAgentId) {
            filters.push(`b.assigned_agent_id = $${paramIndex}`);
            params.push(req.query.assignedAgentId);
            paramIndex++;
        }
        if (req.query.userGroup) {
            filters.push(`c.user_group_id = $${paramIndex}`);
            params.push(req.query.userGroup);
            paramIndex++;
        }
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';
        // Get total count
        const countResult = await (0, database_1.query)(`SELECT COUNT(*) as total 
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       ${whereClause}`, params);
        // Get bookings with enhanced data
        const bookingsResult = await (0, database_1.query)(`SELECT b.*, 
              c.first_name as customer_first_name, c.last_name as customer_last_name, 
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name,
              ug.name as customer_group_name, ug.pricing_tier,
              wl.partner_name as white_label_partner
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id
       LEFT JOIN services s ON b.service_id = s.id
       LEFT JOIN users u ON b.assigned_agent_id = u.id
       LEFT JOIN user_groups ug ON c.user_group_id = ug.id
       LEFT JOIN white_label_configs wl ON b.white_label_config_id = wl.id
       ${whereClause}
       ORDER BY b.created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`, [...params, limit, offset]);
        const total = parseInt(countResult.rows[0].total);
        const totalPages = Math.ceil(total / limit);
        return res.json({
            success: true,
            data: {
                bookings: bookingsResult.rows,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limit
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get bookings error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});
// Enhanced assign agent with equipment
router.post('/:id/assign', (0, auth_1.requirePermission)('assign_booking'), async (req, res) => {
    try {
        const { agentId, equipmentIds } = req.body;
        if (!agentId) {
            return res.status(400).json({ error: 'Agent ID is required' });
        }
        // Check if agent exists and is available
        const agentResult = await (0, database_1.query)('SELECT * FROM agents WHERE user_id = $1 AND tenant_id = $2', [agentId, req.user.tenant_id]);
        if (agentResult.rows.length === 0) {
            return res.status(404).json({ error: 'Agent not found' });
        }
        // Reserve equipment if specified
        if (equipmentIds && equipmentIds.length > 0) {
            await (0, database_1.query)(`UPDATE equipment 
           SET status = 'in_use', current_booking_id = $1, updated_at = NOW()
           WHERE id = ANY($2) AND tenant_id = $3 AND status = 'available'`, [req.params.id, equipmentIds, req.user.tenant_id]);
        }
        // Update booking
        const result = await (0, database_1.query)(`UPDATE bookings 
         SET assigned_agent_id = $1, assignment_time = NOW(), 
             assigned_equipment = $2, updated_at = NOW()
         WHERE id = $3 AND tenant_id = $4
         RETURNING *`, [agentId, JSON.stringify(equipmentIds || []), req.params.id, req.user.tenant_id]);
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Booking not found' });
        }
        // Get agent details for notification
        const userResult = await (0, database_1.query)('SELECT email, first_name, last_name FROM users WHERE id = $1', [agentId]);
        if (userResult.rows.length > 0) {
            const booking = result.rows[0];
            await notification_1.notificationService.sendAgentAssignment(req.user.tenant_id, req.params.id, userResult.rows[0].email, {
                bookingReference: booking.booking_reference,
                customerName: 'Customer', // You might want to get this from customer table
                flightNumber: booking.flight_number,
                flightDate: booking.flight_date,
                meetingPoint: booking.meeting_point
            });
        }
        // Log activity
        await (0, database_1.query)(`INSERT INTO booking_activities (id, booking_id, user_id, activity_type, description, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`, [
            (0, uuid_1.v4)(),
            req.params.id,
            req.user.id,
            'assigned',
            `Booking assigned to agent ${agentId}`,
            JSON.stringify({ equipmentIds })
        ]);
        // Emit real-time update
        const io = req.app.get('io');
        io.to(`tenant_${req.user.tenant_id}`).emit('booking_assigned', {
            booking: result.rows[0],
            agentId,
            equipmentIds
        });
        return res.json({
            success: true,
            data: result.rows[0]
        });
    }
    catch (error) {
        logger_1.logger.error('Assign booking error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});
// Get price quote
router.post('/quote', async (req, res) => {
    try {
        const { serviceId, passengerCount, flightDate, customerId } = req.body;
        // Get customer group if provided
        let customerGroupId;
        if (customerId) {
            const customerResult = await (0, database_1.query)('SELECT user_group_id FROM customers WHERE id = $1', [customerId]);
            customerGroupId = customerResult.rows[0]?.user_group_id;
        }
        const pricingContext = {
            serviceId,
            tenantId: req.user.tenant_id,
            passengerCount: passengerCount || 1,
            bookingDate: new Date(),
            serviceDate: new Date(flightDate),
            customerGroupId
        };
        const quote = await dynamicPricing_1.dynamicPricingService.getQuote(pricingContext);
        return res.json({
            success: true,
            data: quote
        });
    }
    catch (error) {
        logger_1.logger.error('Quote generation error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});
// Update flight information for booking
router.post('/:id/update-flight', (0, auth_1.requirePermission)('update_booking'), async (req, res) => {
    try {
        const updated = await flightInformation_1.flightInformationService.updateBookingWithFlightInfo(req.params.id);
        if (updated) {
            return res.json({
                success: true,
                message: 'Flight information updated'
            });
        }
        else {
            return res.status(404).json({ error: 'Booking not found or flight information unavailable' });
        }
    }
    catch (error) {
        logger_1.logger.error('Update flight info error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;
//# sourceMappingURL=enhancedBookings.js.map