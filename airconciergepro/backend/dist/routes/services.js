"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../services/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validation schemas
const createServiceSchema = joi_1.default.object({
    name: joi_1.default.string().required(),
    description: joi_1.default.string().required(),
    category: joi_1.default.string().valid('meet_greet', 'fast_track', 'lounge_access', 'vip_terminal', 'transfer', 'porter').required(),
    type: joi_1.default.string().valid('arrival', 'departure', 'transit').required(),
    basePrice: joi_1.default.number().positive().required(),
    currency: joi_1.default.string().length(3).required(),
    durationMinutes: joi_1.default.number().integer().positive().required(),
    maxPassengers: joi_1.default.number().integer().positive().required(),
    availableAirports: joi_1.default.array().items(joi_1.default.string().length(3)).required(),
    requirements: joi_1.default.array().items(joi_1.default.string()),
    inclusions: joi_1.default.array().items(joi_1.default.string())
});
const updateServiceSchema = joi_1.default.object({
    name: joi_1.default.string(),
    description: joi_1.default.string(),
    category: joi_1.default.string().valid('meet_greet', 'fast_track', 'lounge_access', 'vip_terminal', 'transfer', 'porter'),
    type: joi_1.default.string().valid('arrival', 'departure', 'transit'),
    basePrice: joi_1.default.number().positive(),
    currency: joi_1.default.string().length(3),
    durationMinutes: joi_1.default.number().integer().positive(),
    maxPassengers: joi_1.default.number().integer().positive(),
    availableAirports: joi_1.default.array().items(joi_1.default.string().length(3)),
    requirements: joi_1.default.array().items(joi_1.default.string()),
    inclusions: joi_1.default.array().items(joi_1.default.string()),
    status: joi_1.default.string().valid('active', 'inactive')
});
// Create new service
router.post('/', (0, auth_1.requirePermission)('create_service'), (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const { error, value } = createServiceSchema.validate(req.body);
    if (error) {
        return res.status(400).json({ error: error.details[0].message });
    }
    const serviceId = (0, uuid_1.v4)();
    const result = await (0, database_1.query)(`INSERT INTO services (
        id, tenant_id, name, description, category, type, base_price, currency,
        duration_minutes, max_passengers, available_airports, requirements,
        inclusions, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()) RETURNING *`, [
        serviceId,
        req.user.tenant_id,
        value.name,
        value.description,
        value.category,
        value.type,
        value.basePrice,
        value.currency,
        value.durationMinutes,
        value.maxPassengers,
        JSON.stringify(value.availableAirports),
        JSON.stringify(value.requirements || []),
        JSON.stringify(value.inclusions || []),
        'active'
    ]);
    res.status(201).json({
        success: true,
        data: result.rows[0]
    });
}));
// Get all services with filtering
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    // Build filter conditions
    const filters = ['tenant_id = $1'];
    const params = [req.user.tenant_id];
    let paramIndex = 2;
    if (req.query.category) {
        filters.push(`category = $${paramIndex}`);
        params.push(req.query.category);
        paramIndex++;
    }
    if (req.query.type) {
        filters.push(`type = $${paramIndex}`);
        params.push(req.query.type);
        paramIndex++;
    }
    if (req.query.status) {
        filters.push(`status = $${paramIndex}`);
        params.push(req.query.status);
        paramIndex++;
    }
    if (req.query.airport) {
        filters.push(`available_airports::jsonb ? $${paramIndex}`);
        params.push(req.query.airport);
        paramIndex++;
    }
    const whereClause = `WHERE ${filters.join(' AND ')}`;
    // Get total count
    const countResult = await (0, database_1.query)(`SELECT COUNT(*) as total FROM services ${whereClause}`, params);
    // Get services
    const servicesResult = await (0, database_1.query)(`SELECT * FROM services ${whereClause}
     ORDER BY created_at DESC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`, [...params, limit, offset]);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);
    res.json({
        success: true,
        data: {
            services: servicesResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
            }
        }
    });
}));
// Get single service
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const serviceResult = await (0, database_1.query)('SELECT * FROM services WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (serviceResult.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    // Get recent bookings for this service
    const bookingsResult = await (0, database_1.query)(`SELECT COUNT(*) as total_bookings,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
            AVG(CASE WHEN status = 'completed' THEN total_price END) as avg_price
     FROM bookings 
     WHERE service_id = $1`, [req.params.id]);
    res.json({
        success: true,
        data: {
            service: serviceResult.rows[0],
            statistics: bookingsResult.rows[0]
        }
    });
}));
// Update service
router.put('/:id', (0, auth_1.requirePermission)('update_service'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = updateServiceSchema.validate(req.body);
    if (error) {
        return res.status(400).json({ error: error.details[0].message });
    }
    // Check if service exists
    const existingService = await (0, database_1.query)('SELECT id FROM services WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (existingService.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;
    Object.entries(value).forEach(([key, val]) => {
        if (val !== undefined) {
            const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
            if (['available_airports', 'requirements', 'inclusions'].includes(dbKey)) {
                updates.push(`${dbKey} = $${paramIndex}`);
                params.push(JSON.stringify(val));
            }
            else {
                updates.push(`${dbKey} = $${paramIndex}`);
                params.push(val);
            }
            paramIndex++;
        }
    });
    if (updates.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
    }
    updates.push('updated_at = NOW()');
    params.push(req.params.id, req.user.tenant_id);
    const updateQuery = `
      UPDATE services 
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
      RETURNING *
    `;
    const result = await (0, database_1.query)(updateQuery, params);
    res.json({
        success: true,
        data: result.rows[0]
    });
}));
// Delete service
router.delete('/:id', (0, auth_1.requirePermission)('delete_service'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // Check if service has any bookings
    const bookingsCheck = await (0, database_1.query)('SELECT COUNT(*) as count FROM bookings WHERE service_id = $1', [req.params.id]);
    if (parseInt(bookingsCheck.rows[0].count) > 0) {
        return res.status(400).json({
            error: 'Cannot delete service with existing bookings. Set status to inactive instead.'
        });
    }
    const result = await (0, database_1.query)('DELETE FROM services WHERE id = $1 AND tenant_id = $2 RETURNING id', [req.params.id, req.user.tenant_id]);
    if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    res.json({
        success: true,
        message: 'Service deleted successfully'
    });
}));
// Get available services for booking (public endpoint for booking widget)
router.get('/available/:airport/:type', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { airport, type } = req.params;
    const passengerCount = parseInt(req.query.passengers) || 1;
    const result = await (0, database_1.query)(`SELECT id, name, description, category, base_price, currency, duration_minutes, 
            max_passengers, inclusions, requirements
     FROM services 
     WHERE tenant_id = $1 
       AND status = 'active' 
       AND type = $2 
       AND available_airports::jsonb ? $3
       AND max_passengers >= $4
     ORDER BY base_price ASC`, [req.user.tenant_id, type, airport, passengerCount]);
    res.json({
        success: true,
        data: result.rows
    });
}));
// Get service categories and statistics
router.get('/stats/categories', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const result = await (0, database_1.query)(`SELECT 
       category,
       COUNT(*) as total_services,
       COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services,
       AVG(base_price) as avg_price,
       MIN(base_price) as min_price,
       MAX(base_price) as max_price
     FROM services 
     WHERE tenant_id = $1
     GROUP BY category
     ORDER BY category`, [req.user.tenant_id]);
    res.json({
        success: true,
        data: result.rows
    });
}));
// Duplicate service
router.post('/:id/duplicate', (0, auth_1.requirePermission)('create_service'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // Get original service
    const originalService = await (0, database_1.query)('SELECT * FROM services WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (originalService.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    const service = originalService.rows[0];
    const newServiceId = (0, uuid_1.v4)();
    // Create duplicate with modified name
    const result = await (0, database_1.query)(`INSERT INTO services (
        id, tenant_id, name, description, category, type, base_price, currency,
        duration_minutes, max_passengers, available_airports, requirements,
        inclusions, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()) RETURNING *`, [
        newServiceId,
        req.user.tenant_id,
        `${service.name} (Copy)`,
        service.description,
        service.category,
        service.type,
        service.base_price,
        service.currency,
        service.duration_minutes,
        service.max_passengers,
        service.available_airports,
        service.requirements,
        service.inclusions,
        'inactive' // Start as inactive for review
    ]);
    res.status(201).json({
        success: true,
        data: result.rows[0]
    });
}));
exports.default = router;
//# sourceMappingURL=services.js.map