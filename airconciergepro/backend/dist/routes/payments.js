"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const stripe_1 = __importDefault(require("stripe"));
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const tenant_1 = require("../middleware/tenant");
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const paymentService_1 = require("../services/paymentService");
const router = express_1.default.Router();
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY || '', {
    apiVersion: '2023-08-16',
});
/**
 * Get all payments for the tenant
 */
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const paymentsResult = await (0, database_1.query)(`SELECT
       p.id, p.booking_id, p.amount, p.currency, p.payment_method,
       p.payment_provider, p.status, p.created_at,
       b.booking_reference, b.flight_number, b.airline,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       s.name as service_name
     FROM payments p
     JOIN bookings b ON p.booking_id = b.id
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE p.tenant_id = $1
     ORDER BY p.created_at DESC
     LIMIT $2 OFFSET $3`, [req.user.tenant_id, limit, offset]);
    const countResult = await (0, database_1.query)('SELECT COUNT(*) as total FROM payments WHERE tenant_id = $1', [req.user.tenant_id]);
    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / limit);
    res.json({
        success: true,
        data: {
            payments: paymentsResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems,
                itemsPerPage: limit
            }
        }
    });
}));
/**
 * Create a payment intent for a booking (supports multiple providers)
 */
router.post('/create-payment-intent', auth_1.authMiddleware, tenant_1.tenantMiddleware, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { bookingId, provider = 'stripe', customer_info } = req.body;
    if (!bookingId) {
        return res.status(400).json({ error: 'Booking ID is required' });
    }
    if (!['stripe', 'razorpay'].includes(provider)) {
        return res.status(400).json({ error: 'Invalid payment provider' });
    }
    const bookingResult = await (0, database_1.query)('SELECT total_price, currency, customer_id FROM bookings WHERE id = $1 AND tenant_id = $2', [bookingId, req.user.tenant_id]);
    if (bookingResult.rows.length === 0) {
        return res.status(404).json({ error: 'Booking not found' });
    }
    const booking = bookingResult.rows[0];
    const paymentIntent = await paymentService_1.paymentService.createPaymentIntent({
        amount: booking.total_price,
        currency: booking.currency,
        provider: provider,
        metadata: {
            booking_id: bookingId,
            tenant_id: req.user.tenant_id,
        },
        customer_info,
    });
    // Save payment record
    await paymentService_1.paymentService.savePaymentRecord({
        tenant_id: req.user.tenant_id,
        booking_id: bookingId,
        payment_intent_id: paymentIntent.id,
        provider: provider,
        amount: booking.total_price,
        currency: booking.currency,
        status: paymentIntent.status,
        metadata: paymentIntent.metadata,
    });
    res.json({
        success: true,
        data: {
            payment_intent: paymentIntent,
            provider,
        },
    });
    return;
}));
/**
 * Verify payment (supports multiple providers)
 */
router.post('/verify-payment', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { payment_id, provider, verification_data } = req.body;
    if (!payment_id || !provider) {
        return res.status(400).json({ error: 'Payment ID and provider are required' });
    }
    const isValid = await paymentService_1.paymentService.verifyPayment(payment_id, provider, verification_data);
    if (isValid) {
        // Update payment status in database
        await paymentService_1.paymentService.updatePaymentStatus(payment_id, 'succeeded');
        return res.json({
            success: true,
            message: 'Payment verified successfully',
        });
    }
    else {
        return res.status(400).json({
            success: false,
            error: 'Payment verification failed',
        });
    }
}));
/**
 * Stripe Webhook Handler
 */
router.post('/webhook/stripe', express_1.default.raw({ type: 'application/json' }), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    let event;
    try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    }
    catch (err) {
        logger_1.logger.error(`Stripe webhook error: ${err.message}`);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }
    // Handle the event
    switch (event.type) {
        case 'payment_intent.succeeded':
            const paymentIntent = event.data.object;
            await paymentService_1.paymentService.updatePaymentStatus(paymentIntent.id, 'succeeded', paymentIntent.metadata);
            logger_1.logger.info(`Payment succeeded: ${paymentIntent.id}`);
            break;
        case 'payment_intent.payment_failed':
            const failedPayment = event.data.object;
            await paymentService_1.paymentService.updatePaymentStatus(failedPayment.id, 'failed', failedPayment.metadata);
            logger_1.logger.info(`Payment failed: ${failedPayment.id}`);
            break;
        default:
            logger_1.logger.info(`Unhandled Stripe event type: ${event.type}`);
    }
    res.json({ received: true });
    return;
}));
/**
 * Razorpay Webhook Handler
 */
router.post('/webhook/razorpay', express_1.default.raw({ type: 'application/json' }), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    const crypto = require('crypto');
    const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(JSON.stringify(req.body))
        .digest('hex');
    const actualSignature = req.headers['x-razorpay-signature'];
    if (expectedSignature !== actualSignature) {
        logger_1.logger.error('Razorpay webhook signature verification failed');
        return res.status(400).send('Webhook signature verification failed');
    }
    const event = req.body;
    // Handle the event
    switch (event.event) {
        case 'payment.captured':
            await paymentService_1.paymentService.updatePaymentStatus(event.payload.payment.entity.order_id, 'succeeded');
            logger_1.logger.info(`Razorpay payment captured: ${event.payload.payment.entity.id}`);
            break;
        case 'payment.failed':
            await paymentService_1.paymentService.updatePaymentStatus(event.payload.payment.entity.order_id, 'failed');
            logger_1.logger.info(`Razorpay payment failed: ${event.payload.payment.entity.id}`);
            break;
        default:
            logger_1.logger.info(`Unhandled Razorpay event type: ${event.event}`);
    }
    res.json({ received: true });
    return;
}));
exports.default = router;
//# sourceMappingURL=payments.js.map