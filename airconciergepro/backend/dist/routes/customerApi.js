"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("../middleware/errorHandler");
const airportManagement_1 = require("../services/airportManagement");
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const paymentService_1 = require("../services/paymentService");
const response_1 = require("../utils/response");
const router = express_1.default.Router();
const paymentService = new paymentService_1.PaymentService();
/**
 * Public Customer API Routes
 * These endpoints don't require authentication and are used by the customer portal
 */
/**
 * @route GET /api/customer/v1/airports
 * @desc Get all airports for customer portal (public)
 * @access Public
 */
router.get('/airports', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 100;
        const result = await airportManagement_1.airportManagementService.getAirports(page, limit);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching airports for customer portal:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch airports'
        });
    }
}));
/**
 * @route GET /api/customer/v1/airports/search
 * @desc Search airports by name, city, or IATA code (public)
 * @access Public
 */
router.get('/airports/search', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const query_param = req.query.q;
        if (!query_param || query_param.length < 2) {
            res.status(400).json({
                success: false,
                error: 'Search query must be at least 2 characters'
            });
            return;
        }
        const result = await (0, database_1.query)(`SELECT id, iata_code as code, name, city, country, timezone
       FROM airports
       WHERE LOWER(name) LIKE LOWER($1)
          OR LOWER(city) LIKE LOWER($1)
          OR LOWER(iata_code) LIKE LOWER($1)
          OR LOWER(country) LIKE LOWER($1)
       ORDER BY
         CASE
           WHEN LOWER(iata_code) = LOWER($2) THEN 1
           WHEN LOWER(iata_code) LIKE LOWER($1) THEN 2
           WHEN LOWER(name) LIKE LOWER($1) THEN 3
           WHEN LOWER(city) LIKE LOWER($1) THEN 4
           ELSE 5
         END,
         name ASC
       LIMIT 20`, [`%${query_param}%`, query_param]);
        res.json({
            success: true,
            data: result.rows
        });
    }
    catch (error) {
        logger_1.logger.error('Error searching airports:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to search airports'
        });
    }
}));
/**
 * @route GET /api/customer/v1/airports/:code
 * @desc Get airport details by IATA code or ID (public)
 * @access Public
 */
router.get('/airports/:code', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { code } = req.params;
        if (!code) {
            res.status(400).json({
                success: false,
                error: 'Airport code or ID required'
            });
            return;
        }
        let airport;
        if (code.length === 36 && code.includes('-')) {
            // It's a UUID (airport ID)
            const airportResult = await (0, database_1.query)('SELECT * FROM airports WHERE id = $1', [code]);
            if (airportResult.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    error: 'Airport not found'
                });
                return;
            }
            const row = airportResult.rows[0];
            airport = {
                id: row.id,
                iataCode: row.iata_code,
                icaoCode: row.icao_code,
                name: row.name,
                city: row.city,
                country: row.country,
                timezone: row.timezone
            };
        }
        else {
            // It's an IATA code
            airport = await airportManagement_1.airportManagementService.getAirport(code);
        }
        if (!airport) {
            res.status(404).json({
                success: false,
                error: 'Airport not found'
            });
            return;
        }
        res.json({
            success: true,
            data: airport
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching airport details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch airport details'
        });
    }
}));
/**
 * @route GET /api/customer/v1/airports/:code/services
 * @desc Get available services for an airport (public)
 * @access Public
 */
router.get('/airports/:code/services', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { code } = req.params;
        const serviceType = req.query.service_type;
        const passengerCount = parseInt(req.query.passengers) || 1;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        // Check if code is UUID (airport ID) or IATA code
        let airport;
        let airportCode;
        if (code.length === 36 && code.includes('-')) {
            // It's a UUID (airport ID)
            const airportResult = await (0, database_1.query)('SELECT * FROM airports WHERE id = $1', [code]);
            if (airportResult.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    error: 'Airport not found'
                });
                return;
            }
            airport = airportResult.rows[0];
            airportCode = airport.iata_code;
        }
        else {
            // It's an IATA code
            airport = await airportManagement_1.airportManagementService.getAirport(code);
            if (!airport) {
                res.status(404).json({
                    success: false,
                    error: 'Airport not found'
                });
                return;
            }
            airportCode = code;
        }
        // Build query conditions - filter by tenant for SaaS isolation
        let whereConditions = ['s.status = $1', 's.tenant_id = $2', 's.available_airports::jsonb ? $3'];
        let queryParams = ['active', tenantId, airportCode];
        let paramIndex = 4;
        if (serviceType) {
            whereConditions.push(`s.type = $${paramIndex}`);
            queryParams.push(serviceType);
            paramIndex++;
        }
        if (passengerCount > 1) {
            whereConditions.push(`s.max_passengers >= $${paramIndex}`);
            queryParams.push(passengerCount);
            paramIndex++;
        }
        const result = await (0, database_1.query)(`SELECT s.id, s.name, s.description, s.category, s.type, s.base_price, s.currency,
              s.duration_minutes, s.max_passengers, s.inclusions, s.requirements
       FROM services s
       WHERE ${whereConditions.join(' AND ')}
       ORDER BY s.category, s.base_price ASC`, queryParams);
        res.json({
            success: true,
            data: result.rows
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching airport services:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch services'
        });
    }
}));
/**
 * @route GET /api/customer/v1/popular-airports
 * @desc Get popular airports for quick selection (public)
 * @access Public
 */
router.get('/popular-airports', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        // Get airports with most bookings or predefined popular ones
        const result = await (0, database_1.query)(`SELECT a.id, a.iata_code as code, a.name, a.city, a.country, a.timezone,
              COUNT(b.id) as booking_count
       FROM airports a
       LEFT JOIN bookings b ON a.iata_code = b.departure_airport OR a.iata_code = b.arrival_airport
       GROUP BY a.id, a.iata_code, a.name, a.city, a.country, a.timezone
       ORDER BY booking_count DESC, a.name ASC
       LIMIT 12`);
        res.json({
            success: true,
            data: result.rows
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching popular airports:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch popular airports'
        });
    }
}));
/**
 * @route GET /api/customer/v1/services/:id
 * @desc Get service details by ID (public)
 * @access Public
 */
router.get('/services/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { id } = req.params;
        const result = await (0, database_1.query)(`SELECT s.id, s.name, s.description, s.category, s.type, s.base_price, s.currency,
              s.duration_minutes, s.max_passengers, s.inclusions, s.requirements
       FROM services s
       WHERE s.id = $1 AND s.status = 'active'`, [id]);
        if (result.rows.length === 0) {
            res.status(404).json({
                success: false,
                error: 'Service not found'
            });
            return;
        }
        res.json({
            success: true,
            data: result.rows[0]
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching service details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch service details'
        });
    }
}));
/**
 * @route POST /api/customer/v1/availability/check
 * @desc Check availability for services (public)
 * @access Public
 */
router.post('/availability/check', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { airport_id, service_ids, datetime } = req.body;
        if (!airport_id || !service_ids || !datetime) {
            res.status(400).json({
                success: false,
                error: 'Missing required fields: airport_id, service_ids, datetime'
            });
            return;
        }
        // For now, return a simple availability response
        // In a real implementation, this would check actual availability
        res.json({
            success: true,
            data: {
                available: true,
                alternatives: []
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error checking availability:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to check availability'
        });
    }
}));
/**
 * @route POST /api/customer/v1/pricing/calculate
 * @desc Calculate pricing for services (public)
 * @access Public
 */
router.post('/pricing/calculate', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { service_ids, passenger_count, datetime } = req.body;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        if (!service_ids || !passenger_count) {
            res.status(400).json({
                success: false,
                error: 'Missing required fields: service_ids, passenger_count'
            });
            return;
        }
        // Get service prices with tenant filtering
        const result = await (0, database_1.query)(`SELECT id, name, base_price, currency
       FROM services
       WHERE id = ANY($1) AND status = 'active' AND tenant_id = $2`, [service_ids, tenantId]);
        let total = 0;
        const breakdown = result.rows.map(service => {
            const serviceTotal = service.base_price * passenger_count;
            total += serviceTotal;
            return {
                service_id: service.id,
                service_name: service.name,
                base_price: service.base_price,
                passenger_count,
                total: serviceTotal,
                currency: service.currency
            };
        });
        res.json({
            success: true,
            data: {
                total,
                currency: result.rows[0]?.currency || 'USD',
                breakdown
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error calculating pricing:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to calculate pricing'
        });
    }
}));
// Validation schema for booking creation
const createBookingSchema = joi_1.default.object({
    customerId: joi_1.default.string().uuid().required(),
    serviceId: joi_1.default.string().uuid().required(),
    flightNumber: joi_1.default.string().required(),
    airline: joi_1.default.string().required(),
    departureAirport: joi_1.default.string().length(3).required(),
    arrivalAirport: joi_1.default.string().length(3).required(),
    flightDate: joi_1.default.date().iso().required(),
    estimatedArrival: joi_1.default.date().iso(),
    serviceType: joi_1.default.string().valid('arrival', 'departure', 'transit').required(),
    passengerCount: joi_1.default.number().integer().min(1).required(),
    passengers: joi_1.default.array().items(joi_1.default.object({
        firstName: joi_1.default.string().required(),
        lastName: joi_1.default.string().required(),
        age: joi_1.default.number().integer().min(0).max(120),
        specialRequirements: joi_1.default.array().items(joi_1.default.string()),
        contactNumber: joi_1.default.string()
    })).required(),
    specialRequirements: joi_1.default.array().items(joi_1.default.string()),
    meetingPoint: joi_1.default.string().required()
});
// Generate unique booking reference
const generateBookingReference = () => {
    const prefix = 'ACG';
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
};
/**
 * @route POST /api/customer/v1/bookings
 * @desc Create a new booking (public)
 * @access Public
 */
router.post('/bookings', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        const { error, value } = createBookingSchema.validate(req.body);
        if (error) {
            return (0, response_1.sendValidationError)(res, error.details[0].message);
        }
        // Verify service exists and is active
        const serviceResult = await (0, database_1.query)('SELECT * FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3', [value.serviceId, tenantId, 'active']);
        if (serviceResult.rows.length === 0) {
            return (0, response_1.sendNotFoundError)(res, 'Service not found or inactive');
        }
        const service = serviceResult.rows[0];
        const bookingId = (0, uuid_1.v4)();
        const bookingReference = generateBookingReference();
        // Create booking in database
        const bookingResult = await (0, database_1.query)(`INSERT INTO bookings (
        id, tenant_id, customer_id, service_id, booking_reference, status,
        flight_number, airline, departure_airport, arrival_airport, flight_date,
        estimated_arrival, service_type, passenger_count, passengers,
        special_requirements, meeting_point, base_price, total_price,
        currency, payment_status, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, NOW(), NOW()
      ) RETURNING *`, [
            bookingId,
            tenantId,
            value.customerId,
            value.serviceId,
            bookingReference,
            'confirmed',
            value.flightNumber,
            value.airline,
            value.departureAirport,
            value.arrivalAirport,
            value.flightDate,
            value.estimatedArrival,
            value.serviceType,
            value.passengerCount,
            JSON.stringify(value.passengers),
            JSON.stringify(value.specialRequirements || []),
            value.meetingPoint,
            service.base_price,
            service.base_price * value.passengerCount,
            service.currency,
            'pending'
        ]);
        return (0, response_1.sendSuccess)(res, bookingResult.rows[0], 201);
    }
    catch (error) {
        logger_1.logger.error('Error creating booking:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to create booking');
    }
}));
/**
 * @route GET /api/customer/v1/bookings
 * @desc Get customer bookings by customer ID or email (public)
 * @access Public
 */
router.get('/bookings', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const tenantId = req.headers['x-tenant-id'];
        const { customerId, email } = req.query;
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        if (!customerId && !email) {
            return (0, response_1.sendValidationError)(res, 'Customer ID or email is required');
        }
        let whereClause = 'b.tenant_id = $1';
        let params = [tenantId];
        if (customerId) {
            whereClause += ' AND b.customer_id = $2';
            params.push(customerId);
        }
        else if (email) {
            whereClause += ' AND c.email = $2';
            params.push(email);
        }
        const bookingsResult = await (0, database_1.query)(`SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name, c.email as customer_email,
              s.name as service_name, s.category as service_category
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       WHERE ${whereClause}
       ORDER BY b.created_at DESC`, params);
        return (0, response_1.sendSuccess)(res, bookingsResult.rows);
    }
    catch (error) {
        logger_1.logger.error('Error fetching bookings:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch bookings');
    }
}));
/**
 * @route GET /api/customer/v1/bookings/:id
 * @desc Get booking details by ID (public)
 * @access Public
 */
router.get('/bookings/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        const bookingResult = await (0, database_1.query)(`SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.description as service_description, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name, u.email as agent_email
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       LEFT JOIN users u ON b.assigned_agent_id = u.id AND u.tenant_id = b.tenant_id
       WHERE b.id = $1 AND b.tenant_id = $2`, [id, tenantId]);
        if (bookingResult.rows.length === 0) {
            return (0, response_1.sendNotFoundError)(res, 'Booking not found');
        }
        // Get booking activities
        const activitiesResult = await (0, database_1.query)(`SELECT ba.*, u.first_name, u.last_name, u.role
       FROM booking_activities ba
       LEFT JOIN users u ON ba.user_id = u.id
       WHERE ba.booking_id = $1
       ORDER BY ba.created_at DESC`, [id]);
        return (0, response_1.sendSuccess)(res, {
            booking: bookingResult.rows[0],
            activities: activitiesResult.rows
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching booking:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch booking');
    }
}));
/**
 * @route POST /api/customer/v1/payments/create-payment-intent
 * @desc Create payment intent for customer booking (public)
 * @access Public
 */
router.post('/payments/create-payment-intent', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { amount, currency, provider, booking_data } = req.body;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        if (!amount || !currency || !provider) {
            res.status(400).json({
                success: false,
                error: 'Amount, currency, and provider are required'
            });
            return;
        }
        // Create payment intent using the payment service
        const paymentIntent = await paymentService.createPaymentIntent({
            amount: parseFloat(amount),
            currency: currency.toLowerCase(),
            provider: provider,
            metadata: {
                tenant_id: tenantId,
                booking_data: JSON.stringify(booking_data)
            }
        });
        res.json({
            success: true,
            data: paymentIntent
        });
    }
    catch (error) {
        logger_1.logger.error('Error creating payment intent:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create payment intent'
        });
    }
}));
/**
 * @route POST /api/customer/v1/payments/verify-payment
 * @desc Verify payment for customer booking (public)
 * @access Public
 */
router.post('/payments/verify-payment', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { payment_id, provider, verification_data } = req.body;
        if (!payment_id || !provider) {
            res.status(400).json({
                success: false,
                error: 'Payment ID and provider are required'
            });
            return;
        }
        const isValid = await paymentService.verifyPayment(payment_id, provider, verification_data);
        if (isValid) {
            res.json({
                success: true,
                message: 'Payment verified successfully'
            });
        }
        else {
            res.status(400).json({
                success: false,
                error: 'Payment verification failed'
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error verifying payment:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to verify payment'
        });
    }
}));
/**
 * @route GET /api/customer/v1/bookings/reference/:reference
 * @desc Get booking details by reference (public)
 * @access Public
 */
router.get('/bookings/reference/:reference', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { reference } = req.params;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        if (!reference) {
            return (0, response_1.sendValidationError)(res, 'Booking reference is required');
        }
        const bookingResult = await (0, database_1.query)(`SELECT b.*,
              c.first_name as customer_first_name, c.last_name as customer_last_name,
              c.email as customer_email, c.phone as customer_phone,
              s.name as service_name, s.description as service_description, s.category as service_category,
              u.first_name as agent_first_name, u.last_name as agent_last_name, u.email as agent_email
       FROM bookings b
       LEFT JOIN customers c ON b.customer_id = c.id AND c.tenant_id = b.tenant_id
       LEFT JOIN services s ON b.service_id = s.id AND s.tenant_id = b.tenant_id
       LEFT JOIN users u ON b.assigned_agent_id = u.id AND u.tenant_id = b.tenant_id
       WHERE b.booking_reference = $1 AND b.tenant_id = $2`, [reference, tenantId]);
        if (bookingResult.rows.length === 0) {
            return (0, response_1.sendNotFoundError)(res, 'Booking not found');
        }
        // Get booking activities
        const activitiesResult = await (0, database_1.query)(`SELECT ba.*, u.first_name, u.last_name, u.role
       FROM booking_activities ba
       LEFT JOIN users u ON ba.user_id = u.id
       WHERE ba.booking_id = $1
       ORDER BY ba.created_at DESC`, [bookingResult.rows[0].id]);
        return (0, response_1.sendSuccess)(res, {
            booking: bookingResult.rows[0],
            activities: activitiesResult.rows
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching booking by reference:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch booking');
    }
}));
exports.default = router;
//# sourceMappingURL=customerApi.js.map