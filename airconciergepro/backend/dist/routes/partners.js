"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const crypto_1 = __importDefault(require("crypto"));
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../services/database");
const errorHandler_1 = require("../middleware/errorHandler");
const router = express_1.default.Router();
// API Key validation middleware
const validateApiKey = async (req, res, next) => {
    const apiKey = req.header('X-API-Key');
    if (!apiKey) {
        return res.status(401).json({ error: 'API key required' });
    }
    const keyHash = crypto_1.default.createHash('sha256').update(apiKey).digest('hex');
    const apiKeyResult = await (0, database_1.query)(`SELECT ak.*, t.status as tenant_status 
     FROM api_keys ak
     JOIN tenants t ON ak.tenant_id = t.id
     WHERE ak.key_hash = $1 AND ak.status = 'active'`, [keyHash]);
    if (apiKeyResult.rows.length === 0) {
        return res.status(401).json({ error: 'Invalid API key' });
    }
    const apiKeyData = apiKeyResult.rows[0];
    if (apiKeyData.tenant_status !== 'active') {
        return res.status(403).json({ error: 'Tenant account is suspended' });
    }
    if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
        return res.status(401).json({ error: 'API key has expired' });
    }
    // Update usage count and last used
    await (0, database_1.query)('UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = $1', [apiKeyData.id]);
    req.apiKey = apiKeyData;
    next();
};
// Validation schemas
const partnerBookingSchema = joi_1.default.object({
    partnerBookingId: joi_1.default.string().required(),
    customerId: joi_1.default.string().uuid(),
    customerDetails: joi_1.default.object({
        email: joi_1.default.string().email().required(),
        firstName: joi_1.default.string().required(),
        lastName: joi_1.default.string().required(),
        phone: joi_1.default.string().required()
    }).when('customerId', { is: joi_1.default.exist(), then: joi_1.default.forbidden(), otherwise: joi_1.default.required() }),
    serviceId: joi_1.default.string().uuid().required(),
    flightNumber: joi_1.default.string().required(),
    airline: joi_1.default.string().required(),
    departureAirport: joi_1.default.string().length(3).required(),
    arrivalAirport: joi_1.default.string().length(3).required(),
    flightDate: joi_1.default.date().iso().required(),
    estimatedArrival: joi_1.default.date().iso(),
    serviceType: joi_1.default.string().valid('arrival', 'departure', 'transit').required(),
    passengerCount: joi_1.default.number().integer().min(1).required(),
    passengers: joi_1.default.array().items(joi_1.default.object({
        firstName: joi_1.default.string().required(),
        lastName: joi_1.default.string().required(),
        age: joi_1.default.number().integer().min(0).max(120),
        specialRequirements: joi_1.default.array().items(joi_1.default.string())
    })).required(),
    specialRequirements: joi_1.default.array().items(joi_1.default.string()),
    meetingPoint: joi_1.default.string(),
    partnerCommission: joi_1.default.number().min(0).max(100)
});
// Partner authentication
router.post('/auth', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { tenantId, apiSecret } = req.body;
    if (!tenantId || !apiSecret) {
        return res.status(400).json({ error: 'Tenant ID and API secret required' });
    }
    // In a real implementation, this would validate against stored API credentials
    const secretHash = crypto_1.default.createHash('sha256').update(apiSecret).digest('hex');
    const tenantResult = await (0, database_1.query)('SELECT id, name, status FROM tenants WHERE id = $1 AND status = $2', [tenantId, 'active']);
    if (tenantResult.rows.length === 0) {
        return res.status(401).json({ error: 'Invalid credentials' });
    }
    // Generate temporary API key (24 hours)
    const apiKey = crypto_1.default.randomBytes(32).toString('hex');
    const keyHash = crypto_1.default.createHash('sha256').update(apiKey).digest('hex');
    await (0, database_1.query)(`INSERT INTO api_keys (id, tenant_id, name, key_hash, permissions, expires_at, status, created_at, updated_at)
     VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())`, [
        (0, uuid_1.v4)(),
        tenantId,
        'Partner API Key',
        keyHash,
        JSON.stringify(['partner_booking', 'partner_query']),
        new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        'active'
    ]);
    return res.json({
        success: true,
        data: {
            apiKey,
            expiresIn: '24h',
            tenant: tenantResult.rows[0]
        }
    });
}));
// Get available inventory/services
router.get('/inventory', validateApiKey, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { airport, type, date, passengers } = req.query;
    const filters = ['tenant_id = $1', 'status = $2'];
    const params = [req.apiKey.tenant_id, 'active'];
    let paramIndex = 3;
    if (airport) {
        filters.push(`available_airports::jsonb ? $${paramIndex}`);
        params.push(airport);
        paramIndex++;
    }
    if (type) {
        filters.push(`type = $${paramIndex}`);
        params.push(type);
        paramIndex++;
    }
    if (passengers) {
        filters.push(`max_passengers >= $${paramIndex}`);
        params.push(parseInt(passengers));
        paramIndex++;
    }
    const whereClause = `WHERE ${filters.join(' AND ')}`;
    const result = await (0, database_1.query)(`SELECT id, name, description, category, type, base_price, currency, 
            duration_minutes, max_passengers, available_airports, inclusions
     FROM services ${whereClause}
     ORDER BY base_price ASC`, params);
    res.json({
        success: true,
        data: result.rows
    });
}));
// Get service pricing quote
router.post('/quote', validateApiKey, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { serviceId, passengerCount, date } = req.body;
    const serviceResult = await (0, database_1.query)('SELECT * FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3', [serviceId, req.apiKey.tenant_id, 'active']);
    if (serviceResult.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    const service = serviceResult.rows[0];
    const basePrice = service.base_price * (passengerCount || 1);
    // Apply dynamic pricing based on date/demand (simplified)
    let priceMultiplier = 1;
    if (date) {
        const bookingDate = new Date(date);
        const dayOfWeek = bookingDate.getDay();
        // Weekend surcharge
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            priceMultiplier = 1.15;
        }
    }
    const totalPrice = Math.round(basePrice * priceMultiplier * 100) / 100;
    return res.json({
        success: true,
        data: {
            serviceId,
            serviceName: service.name,
            basePrice: service.base_price,
            passengerCount: passengerCount || 1,
            subtotal: basePrice,
            priceMultiplier,
            totalPrice,
            currency: service.currency,
            validUntil: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
        }
    });
}));
// Create booking via partner API
router.post('/book', validateApiKey, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = partnerBookingSchema.validate(req.body);
    if (error) {
        return res.status(400).json({ error: error.details[0].message });
    }
    let customerId = value.customerId;
    // Create customer if not provided
    if (!customerId && value.customerDetails) {
        const existingCustomer = await (0, database_1.query)('SELECT id FROM customers WHERE email = $1 AND tenant_id = $2', [value.customerDetails.email, req.apiKey.tenant_id]);
        if (existingCustomer.rows.length > 0) {
            customerId = existingCustomer.rows[0].id;
        }
        else {
            customerId = (0, uuid_1.v4)();
            await (0, database_1.query)(`INSERT INTO customers (
          id, tenant_id, email, first_name, last_name, phone,
          preferences, loyalty_points, total_bookings, total_spent, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())`, [
                customerId,
                req.apiKey.tenant_id,
                value.customerDetails.email,
                value.customerDetails.firstName,
                value.customerDetails.lastName,
                value.customerDetails.phone,
                JSON.stringify({}),
                0, 0, 0
            ]);
        }
    }
    // Get service details
    const serviceResult = await (0, database_1.query)('SELECT * FROM services WHERE id = $1 AND tenant_id = $2 AND status = $3', [value.serviceId, req.apiKey.tenant_id, 'active']);
    if (serviceResult.rows.length === 0) {
        return res.status(404).json({ error: 'Service not found' });
    }
    const service = serviceResult.rows[0];
    const bookingId = (0, uuid_1.v4)();
    const bookingReference = `PAR${Date.now().toString().slice(-6)}${Math.random().toString(36).substring(2, 5).toUpperCase()}`;
    // Calculate pricing
    const basePrice = service.base_price * value.passengerCount;
    const totalPrice = basePrice; // Simplified pricing
    // Create booking
    const bookingResult = await (0, database_1.query)(`INSERT INTO bookings (
      id, tenant_id, customer_id, service_id, booking_reference, status,
      flight_number, airline, departure_airport, arrival_airport, flight_date,
      estimated_arrival, service_type, passenger_count, passengers,
      special_requirements, meeting_point, base_price, total_price,
      currency, payment_status, created_at, updated_at
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, NOW(), NOW()
    ) RETURNING *`, [
        bookingId,
        req.apiKey.tenant_id,
        customerId,
        value.serviceId,
        bookingReference,
        'confirmed',
        value.flightNumber,
        value.airline,
        value.departureAirport,
        value.arrivalAirport,
        value.flightDate,
        value.estimatedArrival,
        value.serviceType,
        value.passengerCount,
        JSON.stringify(value.passengers),
        JSON.stringify(value.specialRequirements || []),
        value.meetingPoint || 'Main Terminal',
        basePrice,
        totalPrice,
        service.currency,
        'pending'
    ]);
    // Store partner booking reference
    await (0, database_1.query)(`INSERT INTO partner_bookings (id, booking_id, partner_booking_id, api_key_id, commission_rate, created_at)
     VALUES ($1, $2, $3, $4, $5, NOW())`, [
        (0, uuid_1.v4)(),
        bookingId,
        value.partnerBookingId,
        req.apiKey.id,
        value.partnerCommission || 0
    ]);
    return res.status(201).json({
        success: true,
        data: {
            bookingId,
            bookingReference,
            partnerBookingId: value.partnerBookingId,
            status: 'confirmed',
            totalPrice,
            currency: service.currency
        }
    });
}));
// Get partner bookings
router.get('/bookings', validateApiKey, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const result = await (0, database_1.query)(`SELECT 
       b.id, b.booking_reference, b.status, b.flight_number, b.airline,
       b.departure_airport, b.arrival_airport, b.flight_date, b.service_type,
       b.total_price, b.currency, b.created_at,
       pb.partner_booking_id, pb.commission_rate,
       s.name as service_name,
       c.first_name as customer_first_name, c.last_name as customer_last_name
     FROM bookings b
     JOIN partner_bookings pb ON b.id = pb.booking_id
     JOIN services s ON b.service_id = s.id
     JOIN customers c ON b.customer_id = c.id
     WHERE pb.api_key_id = $1
     ORDER BY b.created_at DESC
     LIMIT $2 OFFSET $3`, [req.apiKey.id, limit, offset]);
    res.json({
        success: true,
        data: result.rows
    });
}));
// Get single booking details
router.get('/bookings/:id', validateApiKey, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const result = await (0, database_1.query)(`SELECT 
       b.*, pb.partner_booking_id, pb.commission_rate,
       s.name as service_name, s.description as service_description,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       c.email as customer_email, c.phone as customer_phone
     FROM bookings b
     JOIN partner_bookings pb ON b.id = pb.booking_id
     JOIN services s ON b.service_id = s.id
     JOIN customers c ON b.customer_id = c.id
     WHERE b.id = $1 AND pb.api_key_id = $2`, [req.params.id, req.apiKey.id]);
    if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Booking not found' });
    }
    return res.json({
        success: true,
        data: result.rows[0]
    });
}));
// Admin endpoints (no API key validation, uses auth middleware from server)
// Get all partners (API keys) for admin dashboard
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const result = await (0, database_1.query)(`SELECT
       ak.id, ak.name, ak.permissions, ak.usage_limit, ak.usage_count,
       ak.last_used, ak.expires_at, ak.status, ak.created_at,
       COUNT(pb.id) as total_bookings,
       COALESCE(SUM(b.total_price), 0) as total_revenue
     FROM api_keys ak
     LEFT JOIN partner_bookings pb ON ak.id = pb.api_key_id
     LEFT JOIN bookings b ON pb.booking_id = b.id
     WHERE ak.tenant_id = $1
     GROUP BY ak.id, ak.name, ak.permissions, ak.usage_limit, ak.usage_count,
              ak.last_used, ak.expires_at, ak.status, ak.created_at
     ORDER BY ak.created_at DESC
     LIMIT $2 OFFSET $3`, [req.user.tenant_id, limit, offset]);
    const countResult = await (0, database_1.query)('SELECT COUNT(*) FROM api_keys WHERE tenant_id = $1', [req.user.tenant_id]);
    res.json({
        success: true,
        data: result.rows,
        pagination: {
            page,
            limit,
            total: parseInt(countResult.rows[0].count),
            pages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
        }
    });
}));
// Get single partner details
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const partnerResult = await (0, database_1.query)(`SELECT
       ak.id, ak.name, ak.permissions, ak.usage_limit, ak.usage_count,
       ak.last_used, ak.expires_at, ak.status, ak.created_at,
       COUNT(pb.id) as total_bookings,
       COALESCE(SUM(b.total_price), 0) as total_revenue
     FROM api_keys ak
     LEFT JOIN partner_bookings pb ON ak.id = pb.api_key_id
     LEFT JOIN bookings b ON pb.booking_id = b.id
     WHERE ak.id = $1 AND ak.tenant_id = $2
     GROUP BY ak.id, ak.name, ak.permissions, ak.usage_limit, ak.usage_count,
              ak.last_used, ak.expires_at, ak.status, ak.created_at`, [req.params.id, req.user.tenant_id]);
    if (partnerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Partner not found' });
    }
    // Get recent bookings for this partner
    const recentBookingsResult = await (0, database_1.query)(`SELECT
       b.id, b.booking_reference, b.status, b.flight_number, b.airline,
       b.flight_date, b.total_price, b.created_at,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       s.name as service_name,
       pb.partner_booking_id, pb.commission_rate
     FROM partner_bookings pb
     JOIN bookings b ON pb.booking_id = b.id
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE pb.api_key_id = $1
     ORDER BY b.created_at DESC
     LIMIT 10`, [req.params.id]);
    return res.json({
        success: true,
        data: {
            partner: partnerResult.rows[0],
            recentBookings: recentBookingsResult.rows
        }
    });
}));
exports.default = router;
//# sourceMappingURL=partners.js.map