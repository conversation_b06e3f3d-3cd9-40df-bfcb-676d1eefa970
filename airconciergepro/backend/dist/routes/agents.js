"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../services/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validation schemas
const createAgentSchema = joi_1.default.object({
    userId: joi_1.default.string().uuid().required(),
    employeeId: joi_1.default.string().optional(),
    specializations: joi_1.default.array().items(joi_1.default.string()).default([]),
    languages: joi_1.default.array().items(joi_1.default.string()).default([]),
    airports: joi_1.default.array().items(joi_1.default.string().length(3)).default([])
});
const updateAgentSchema = joi_1.default.object({
    employeeId: joi_1.default.string().optional(),
    status: joi_1.default.string().valid('available', 'busy', 'offline').optional(),
    specializations: joi_1.default.array().items(joi_1.default.string()).optional(),
    languages: joi_1.default.array().items(joi_1.default.string()).optional(),
    airports: joi_1.default.array().items(joi_1.default.string().length(3)).optional(),
    currentLocation: joi_1.default.object().optional()
});
/**
 * Get all agents for the tenant
 */
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const status = req.query.status;
    let whereClause = 'WHERE a.tenant_id = $1';
    const params = [req.user.tenant_id];
    if (status) {
        whereClause += ' AND a.status = $2';
        params.push(status);
    }
    const agentsResult = await (0, database_1.query)(`SELECT 
       a.id, a.employee_id, a.status, a.current_location, a.specializations,
       a.languages, a.airports, a.rating, a.total_services, a.created_at,
       u.first_name, u.last_name, u.email,
       COUNT(b.id) as active_bookings
     FROM agents a
     JOIN users u ON a.user_id = u.id
     LEFT JOIN bookings b ON a.user_id = b.assigned_agent_id AND b.status IN ('confirmed', 'in_progress')
     ${whereClause}
     GROUP BY a.id, u.id
     ORDER BY a.created_at DESC
     LIMIT $${params.length + 1} OFFSET $${params.length + 2}`, [...params, limit, offset]);
    const countResult = await (0, database_1.query)(`SELECT COUNT(*) as total FROM agents a WHERE a.tenant_id = $1`, [req.user.tenant_id]);
    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / limit);
    res.json({
        success: true,
        data: {
            agents: agentsResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems,
                itemsPerPage: limit
            }
        }
    });
}));
/**
 * Get single agent details
 */
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const agentResult = await (0, database_1.query)(`SELECT
       a.*, u.first_name, u.last_name, u.email
     FROM agents a
     JOIN users u ON a.user_id = u.id
     WHERE a.id = $1 AND a.tenant_id = $2`, [req.params.id, req.user.tenant_id]);
    if (agentResult.rows.length === 0) {
        return res.status(404).json({ error: 'Agent not found' });
    }
    // Get recent bookings for this agent
    const bookingsResult = await (0, database_1.query)(`SELECT 
       b.id, b.booking_reference, b.status, b.flight_number, b.airline,
       b.flight_date, b.total_price, b.created_at,
       c.first_name as customer_first_name, c.last_name as customer_last_name,
       s.name as service_name
     FROM bookings b
     JOIN customers c ON b.customer_id = c.id
     JOIN services s ON b.service_id = s.id
     WHERE b.assigned_agent_id = $1
     ORDER BY b.created_at DESC
     LIMIT 10`, [agentResult.rows[0].user_id]);
    return res.json({
        success: true,
        data: {
            agent: agentResult.rows[0],
            recentBookings: bookingsResult.rows
        }
    });
}));
/**
 * Create new agent
 */
router.post('/', (0, auth_1.requirePermission)('create_agent'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createAgentSchema.validate(req.body);
    if (error) {
        return res.status(400).json({ error: error.details[0].message });
    }
    // Check if user exists and is in the same tenant
    const userResult = await (0, database_1.query)('SELECT id, role FROM users WHERE id = $1 AND tenant_id = $2', [value.userId, req.user.tenant_id]);
    if (userResult.rows.length === 0) {
        return res.status(400).json({ error: 'User not found or not in the same tenant' });
    }
    // Check if agent already exists for this user
    const existingAgent = await (0, database_1.query)('SELECT id FROM agents WHERE user_id = $1 AND tenant_id = $2', [value.userId, req.user.tenant_id]);
    if (existingAgent.rows.length > 0) {
        return res.status(400).json({ error: 'Agent already exists for this user' });
    }
    const agentId = (0, uuid_1.v4)();
    const result = await (0, database_1.query)(`INSERT INTO agents (
        id, tenant_id, user_id, employee_id, status, specializations,
        languages, airports, rating, total_services, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()) RETURNING *`, [
        agentId,
        req.user.tenant_id,
        value.userId,
        value.employeeId || null,
        'offline',
        JSON.stringify(value.specializations),
        JSON.stringify(value.languages),
        JSON.stringify(value.airports),
        5.0, // Default rating
        0 // Initial service count
    ]);
    return res.status(201).json({
        success: true,
        data: result.rows[0]
    });
}));
/**
 * Update agent
 */
router.put('/:id', (0, auth_1.requirePermission)('update_agent'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = updateAgentSchema.validate(req.body);
    if (error) {
        return res.status(400).json({ error: error.details[0].message });
    }
    const updateFields = [];
    const params = [];
    let paramIndex = 1;
    if (value.employeeId !== undefined) {
        updateFields.push(`employee_id = $${paramIndex++}`);
        params.push(value.employeeId);
    }
    if (value.status) {
        updateFields.push(`status = $${paramIndex++}`);
        params.push(value.status);
    }
    if (value.specializations) {
        updateFields.push(`specializations = $${paramIndex++}`);
        params.push(JSON.stringify(value.specializations));
    }
    if (value.languages) {
        updateFields.push(`languages = $${paramIndex++}`);
        params.push(JSON.stringify(value.languages));
    }
    if (value.airports) {
        updateFields.push(`airports = $${paramIndex++}`);
        params.push(JSON.stringify(value.airports));
    }
    if (value.currentLocation) {
        updateFields.push(`current_location = $${paramIndex++}`);
        params.push(JSON.stringify(value.currentLocation));
    }
    if (updateFields.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
    }
    updateFields.push(`updated_at = NOW()`);
    params.push(req.params.id, req.user.tenant_id);
    const result = await (0, database_1.query)(`UPDATE agents SET ${updateFields.join(', ')} 
       WHERE id = $${paramIndex++} AND tenant_id = $${paramIndex++} 
       RETURNING *`, params);
    if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Agent not found' });
    }
    return res.json({
        success: true,
        data: result.rows[0]
    });
}));
/**
 * Delete agent
 */
router.delete('/:id', (0, auth_1.requirePermission)('delete_agent'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // Check if agent has any active bookings
    const activeBookingsCheck = await (0, database_1.query)(`SELECT COUNT(*) as count FROM bookings b
       JOIN agents a ON b.assigned_agent_id = a.user_id
       WHERE a.id = $1 AND b.status IN ('confirmed', 'in_progress')`, [req.params.id]);
    if (parseInt(activeBookingsCheck.rows[0].count) > 0) {
        return res.status(400).json({
            error: 'Cannot delete agent with active bookings'
        });
    }
    const result = await (0, database_1.query)('DELETE FROM agents WHERE id = $1 AND tenant_id = $2 RETURNING id', [req.params.id, req.user.tenant_id]);
    if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Agent not found' });
    }
    return res.json({
        success: true,
        message: 'Agent deleted successfully'
    });
}));
exports.default = router;
//# sourceMappingURL=agents.js.map