"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../services/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const response_1 = require("../utils/response");
const router = express_1.default.Router();
// Validation schemas
const createCustomerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    phone: joi_1.default.string().required(),
    dateOfBirth: joi_1.default.date().iso(),
    preferences: joi_1.default.object({
        language: joi_1.default.string(),
        specialRequirements: joi_1.default.array().items(joi_1.default.string()),
        communicationPreferences: joi_1.default.array().items(joi_1.default.string())
    })
});
const updateCustomerSchema = joi_1.default.object({
    email: joi_1.default.string().email(),
    firstName: joi_1.default.string(),
    lastName: joi_1.default.string(),
    phone: joi_1.default.string(),
    dateOfBirth: joi_1.default.date().iso(),
    preferences: joi_1.default.object({
        language: joi_1.default.string(),
        specialRequirements: joi_1.default.array().items(joi_1.default.string()),
        communicationPreferences: joi_1.default.array().items(joi_1.default.string())
    })
});
// Create new customer
router.post('/', (0, auth_1.requirePermission)('create_customer'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createCustomerSchema.validate(req.body);
    if (error) {
        return (0, response_1.sendValidationError)(res, error.details[0].message);
    }
    // Check if customer with email already exists
    const existingCustomer = await (0, database_1.query)('SELECT id FROM customers WHERE email = $1 AND tenant_id = $2', [value.email, req.user.tenant_id]);
    if (existingCustomer.rows.length > 0) {
        return (0, response_1.sendValidationError)(res, 'Customer with this email already exists');
    }
    const customerId = (0, uuid_1.v4)();
    const result = await (0, database_1.query)(`INSERT INTO customers (
        id, tenant_id, email, first_name, last_name, phone, date_of_birth,
        preferences, loyalty_points, total_bookings, total_spent, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW()) RETURNING *`, [
        customerId,
        req.user.tenant_id,
        value.email,
        value.firstName,
        value.lastName,
        value.phone,
        value.dateOfBirth || null,
        JSON.stringify(value.preferences || {}),
        0, // Initial loyalty points
        0, // Initial booking count
        0 // Initial total spent
    ]);
    return (0, response_1.sendSuccess)(res, result.rows[0], 201);
}));
// Get all customers with filtering and pagination
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search;
    // Build filter conditions
    let filters = ['tenant_id = $1'];
    let params = [req.user.tenant_id];
    let paramIndex = 2;
    if (search) {
        filters.push(`(
      LOWER(first_name) LIKE LOWER($${paramIndex}) OR 
      LOWER(last_name) LIKE LOWER($${paramIndex}) OR 
      LOWER(email) LIKE LOWER($${paramIndex})
    )`);
        params.push(`%${search}%`);
        paramIndex++;
    }
    const whereClause = `WHERE ${filters.join(' AND ')}`;
    // Get total count
    const countResult = await (0, database_1.query)(`SELECT COUNT(*) as total FROM customers ${whereClause}`, params);
    // Get customers
    const customersResult = await (0, database_1.query)(`SELECT * FROM customers ${whereClause}
     ORDER BY created_at DESC
     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`, [...params, limit, offset]);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);
    res.json({
        success: true,
        data: {
            customers: customersResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
            }
        }
    });
}));
// Get single customer
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const customerResult = await (0, database_1.query)('SELECT * FROM customers WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (customerResult.rows.length === 0) {
        res.status(404).json({ error: 'Customer not found' });
        return;
    }
    res.json({
        success: true,
        data: customerResult.rows[0]
    });
}));
// Update customer
router.put('/:id', (0, auth_1.requirePermission)('update_customer'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = updateCustomerSchema.validate(req.body);
    if (error) {
        return (0, response_1.sendValidationError)(res, error.details[0].message);
    }
    // Check if customer exists
    const existingCustomer = await (0, database_1.query)('SELECT id FROM customers WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (existingCustomer.rows.length === 0) {
        return (0, response_1.sendNotFoundError)(res, 'Customer not found');
    }
    // Check email uniqueness if email is being updated
    if (value.email) {
        const emailCheck = await (0, database_1.query)('SELECT id FROM customers WHERE email = $1 AND tenant_id = $2 AND id != $3', [value.email, req.user.tenant_id, req.params.id]);
        if (emailCheck.rows.length > 0) {
            return (0, response_1.sendValidationError)(res, 'Email already exists for another customer');
        }
    }
    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;
    Object.entries(value).forEach(([key, val]) => {
        if (val !== undefined) {
            const dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
            if (dbKey === 'preferences') {
                updates.push(`${dbKey} = $${paramIndex}`);
                params.push(JSON.stringify(val));
            }
            else {
                updates.push(`${dbKey} = $${paramIndex}`);
                params.push(val);
            }
            paramIndex++;
        }
    });
    if (updates.length === 0) {
        return (0, response_1.sendValidationError)(res, 'No valid fields to update');
    }
    updates.push('updated_at = NOW()');
    params.push(req.params.id, req.user.tenant_id);
    const updateQuery = `
      UPDATE customers
      SET ${updates.join(', ')}
      WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
      RETURNING *
    `;
    const result = await (0, database_1.query)(updateQuery, params);
    return (0, response_1.sendSuccess)(res, result.rows[0]);
}));
// Get customer booking history
router.get('/:id/bookings', (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    // Check if customer exists
    const customerCheck = await (0, database_1.query)('SELECT id FROM customers WHERE id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (customerCheck.rows.length === 0) {
        res.status(404).json({ error: 'Customer not found' });
        return;
    }
    // Get booking count
    const countResult = await (0, database_1.query)('SELECT COUNT(*) as total FROM bookings WHERE customer_id = $1', [req.params.id]);
    // Get bookings with service details
    const bookingsResult = await (0, database_1.query)(`SELECT b.*, s.name as service_name, s.category as service_category
     FROM bookings b
     LEFT JOIN services s ON b.service_id = s.id
     WHERE b.customer_id = $1
     ORDER BY b.created_at DESC
     LIMIT $2 OFFSET $3`, [req.params.id, limit, offset]);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);
    res.json({
        success: true,
        data: {
            bookings: bookingsResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
            }
        }
    });
}));
// Delete customer
router.delete('/:id', (0, auth_1.requirePermission)('delete_customer'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // Check if customer has any bookings
    const bookingsCheck = await (0, database_1.query)('SELECT COUNT(*) as count FROM bookings WHERE customer_id = $1 AND tenant_id = $2', [req.params.id, req.user.tenant_id]);
    if (parseInt(bookingsCheck.rows[0].count) > 0) {
        return (0, response_1.sendValidationError)(res, 'Cannot delete customer with existing bookings');
    }
    const result = await (0, database_1.query)('DELETE FROM customers WHERE id = $1 AND tenant_id = $2 RETURNING id', [req.params.id, req.user.tenant_id]);
    if (result.rows.length === 0) {
        return (0, response_1.sendNotFoundError)(res, 'Customer not found');
    }
    return (0, response_1.sendSuccess)(res, { message: 'Customer deleted successfully' });
}));
// Search customers
router.get('/search/:query', (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const searchQuery = req.params.query;
    const limit = parseInt(req.query.limit) || 10;
    const result = await (0, database_1.query)(`SELECT id, first_name, last_name, email, phone
     FROM customers 
     WHERE tenant_id = $1 AND (
       LOWER(first_name) LIKE LOWER($2) OR 
       LOWER(last_name) LIKE LOWER($2) OR 
       LOWER(email) LIKE LOWER($2) OR
       phone LIKE $2
     )
     ORDER BY first_name, last_name
     LIMIT $3`, [req.user.tenant_id, `%${searchQuery}%`, limit]);
    res.json({
        success: true,
        data: result.rows
    });
}));
exports.default = router;
//# sourceMappingURL=customers.js.map