"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const airportManagement_1 = require("../services/airportManagement");
const flightInformation_1 = require("../services/flightInformation");
const aviationStackService_1 = require("../services/aviationStackService");
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const response_1 = require("../utils/response");
const router = express_1.default.Router();
/**
 * @route GET /api/v1/airports
 * @desc Get all airports with pagination
 * @access Private
 */
router.get('/', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const result = await airportManagement_1.airportManagementService.getAirports(page, limit);
    res.json({
        success: true,
        data: result
    });
}));
/**
 * @route GET /api/v1/airports/search
 * @desc Search airports by name, code, or city
 * @access Private
 */
router.get('/search', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = req.query.q;
    if (!query || query.length < 2) {
        res.status(400).json({
            success: false,
            error: 'Search query must be at least 2 characters long'
        });
        return;
    }
    const airports = await airportManagement_1.airportManagementService.searchAirports(query);
    res.json({
        success: true,
        data: airports
    });
}));
/**
 * @route GET /api/v1/airports/:code
 * @desc Get airport details by IATA code
 * @access Private
 */
router.get('/:code', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    if (!code || code.length !== 3) {
        res.status(400).json({
            success: false,
            error: 'Valid IATA code required (3 characters)'
        });
        return;
    }
    const airport = await airportManagement_1.airportManagementService.getAirport(code);
    if (!airport) {
        res.status(404).json({
            success: false,
            error: 'Airport not found'
        });
        return;
    }
    res.json({
        success: true,
        data: airport
    });
}));
/**
 * @route GET /api/v1/airports/:code/terminals
 * @desc Get airport terminals
 * @access Private
 */
router.get('/:code/terminals', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const airport = await airportManagement_1.airportManagementService.getAirport(code);
    if (!airport) {
        res.status(404).json({
            success: false,
            error: 'Airport not found'
        });
        return;
    }
    const terminals = await airportManagement_1.airportManagementService.getAirportTerminals(airport.id);
    res.json({
        success: true,
        data: terminals
    });
}));
/**
 * @route GET /api/v1/airports/:code/meeting-points
 * @desc Get airport meeting points
 * @access Private
 */
router.get('/:code/meeting-points', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const terminal = req.query.terminal;
    const airport = await airportManagement_1.airportManagementService.getAirport(code);
    if (!airport) {
        res.status(404).json({
            success: false,
            error: 'Airport not found'
        });
        return;
    }
    const meetingPoints = await airportManagement_1.airportManagementService.getAirportMeetingPoints(airport.id, terminal);
    res.json({
        success: true,
        data: meetingPoints
    });
}));
/**
 * @route POST /api/v1/airports/:code/meeting-points
 * @desc Create or update meeting point
 * @access Private (Admin only)
 */
router.post('/:code/meeting-points', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const { terminal, name, description, coordinates, instructions, accessibilityFeatures, capacity, equipmentAvailable } = req.body;
    // Check if user has admin permissions
    if (req.user.role !== 'super_admin' && req.user.role !== 'company_admin') {
        res.status(403).json({
            success: false,
            error: 'Admin permissions required'
        });
        return;
    }
    const airport = await airportManagement_1.airportManagementService.getAirport(code);
    if (!airport) {
        res.status(404).json({
            success: false,
            error: 'Airport not found'
        });
        return;
    }
    if (!name || !instructions) {
        res.status(400).json({
            success: false,
            error: 'Name and instructions are required'
        });
        return;
    }
    const meetingPointId = await airportManagement_1.airportManagementService.upsertMeetingPoint({
        airportId: airport.id,
        terminal,
        name,
        description,
        coordinates,
        instructions,
        accessibilityFeatures: accessibilityFeatures || [],
        capacity: capacity || 1,
        equipmentAvailable: equipmentAvailable || [],
        status: 'active'
    });
    if (!meetingPointId) {
        res.status(500).json({
            success: false,
            error: 'Failed to create meeting point'
        });
        return;
    }
    res.status(201).json({
        success: true,
        data: { id: meetingPointId }
    });
}));
/**
 * @route GET /api/v1/airports/:code/operations
 * @desc Get airport operational data
 * @access Private
 */
router.get('/:code/operations', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const date = req.query.date ? new Date(req.query.date) : undefined;
    const airport = await airportManagement_1.airportManagementService.getAirport(code);
    if (!airport) {
        res.status(404).json({
            success: false,
            error: 'Airport not found'
        });
        return;
    }
    const operations = await airportManagement_1.airportManagementService.getAirportOperations(airport.id, date);
    res.json({
        success: true,
        data: operations
    });
}));
/**
 * @route GET /api/v1/airports/:code/delays
 * @desc Get airport delay information
 * @access Private
 */
router.get('/:code/delays', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const delayInfo = await flightInformation_1.flightInformationService.getAirportDelays(code);
    res.json({
        success: true,
        data: delayInfo
    });
}));
/**
 * @route GET /api/v1/airports/:code/flights
 * @desc Get flights for a specific airport
 * @access Private
 */
router.get('/:code/flights', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code } = req.params;
    const date = req.query.date;
    // This would typically integrate with flight information APIs
    // For now, return a placeholder response
    res.json({
        success: true,
        data: {
            airport: code,
            date: date || new Date().toISOString().split('T')[0],
            flights: [],
            message: 'Flight data integration in progress'
        }
    });
}));
/**
 * @route GET /api/v1/airports/stats/summary
 * @desc Get airport statistics summary
 * @access Private
 */
router.get('/stats/summary', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const flightStatusSummary = await flightInformation_1.flightInformationService.getFlightStatusSummary();
        res.json({
            success: true,
            data: {
                flightStatus: flightStatusSummary,
                lastUpdated: new Date().toISOString()
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting airport stats summary:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get airport statistics'
        });
    }
}));
/**
 * @route POST /api/v1/airports/sync
 * @desc Sync airports from AviationStack API
 * @access Private (Admin only)
 */
router.post('/sync', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        // Check if user has admin permissions
        if (!req.user || !['super_admin', 'company_admin'].includes(req.user.role)) {
            return (0, response_1.sendError)(res, 'Insufficient permissions', 403);
        }
        const { limit = 100, offset = 0 } = req.body;
        logger_1.logger.info(`Starting airport sync with limit: ${limit}, offset: ${offset}`);
        await aviationStackService_1.aviationStackService.syncAirports(limit, offset);
        // Get updated count
        const countResult = await (0, database_1.query)('SELECT COUNT(*) as total FROM airports', []);
        const total = parseInt(countResult.rows[0].total);
        return (0, response_1.sendSuccess)(res, {
            message: 'Airport sync completed successfully',
            totalAirports: total
        });
    }
    catch (error) {
        logger_1.logger.error('Error syncing airports:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to sync airports');
    }
}));
/**
 * @route GET /api/v1/airports/:code/flights
 * @desc Get real-time flights for an airport
 * @access Private
 */
router.get('/:code/flights', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { code } = req.params;
        const type = req.query.type || 'arrival';
        const limit = parseInt(req.query.limit) || 20;
        if (!code || code.length !== 3) {
            return (0, response_1.sendValidationError)(res, 'Valid IATA code (3 characters) is required');
        }
        // Get flights from AviationStack
        const flights = await aviationStackService_1.aviationStackService.getFlightsByAirport(code.toUpperCase(), type, limit);
        return (0, response_1.sendSuccess)(res, {
            airport: code.toUpperCase(),
            type,
            flights: flights.map(flight => ({
                flightNumber: flight.flight.iata,
                airline: flight.airline.name,
                status: flight.flight_status,
                scheduled: type === 'arrival' ? flight.arrival.scheduled : flight.departure.scheduled,
                estimated: type === 'arrival' ? flight.arrival.estimated : flight.departure.estimated,
                actual: type === 'arrival' ? flight.arrival.actual : flight.departure.actual,
                terminal: type === 'arrival' ? flight.arrival.terminal : flight.departure.terminal,
                gate: type === 'arrival' ? flight.arrival.gate : flight.departure.gate,
                delay: type === 'arrival' ? flight.arrival.delay : flight.departure.delay,
                origin: flight.departure.iata,
                destination: flight.arrival.iata
            }))
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching airport flights:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to fetch flights');
    }
}));
/**
 * @route GET /api/v1/airports/search/external
 * @desc Search airports using AviationStack API
 * @access Private
 */
router.get('/search/external', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const searchQuery = req.query.q;
        const limit = parseInt(req.query.limit) || 20;
        if (!searchQuery || searchQuery.length < 2) {
            return (0, response_1.sendValidationError)(res, 'Search query must be at least 2 characters');
        }
        const airports = await aviationStackService_1.aviationStackService.searchAirports(searchQuery, limit);
        return (0, response_1.sendSuccess)(res, {
            query: searchQuery,
            airports: airports.map(airport => ({
                iataCode: airport.iata_code,
                icaoCode: airport.icao_code,
                name: airport.airport_name,
                city: airport.city_iata_code,
                country: airport.country_name,
                timezone: airport.timezone
            }))
        });
    }
    catch (error) {
        logger_1.logger.error('Error searching airports:', error);
        return (0, response_1.sendInternalServerError)(res, 'Failed to search airports');
    }
}));
exports.default = router;
//# sourceMappingURL=airports.js.map