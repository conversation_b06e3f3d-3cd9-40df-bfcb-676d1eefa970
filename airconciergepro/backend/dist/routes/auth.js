"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jwt = __importStar(require("jsonwebtoken"));
const uuid_1 = require("uuid");
const database_1 = require("../services/database");
const logger_1 = require("../services/logger");
const response_1 = require("../utils/response");
const joi_1 = __importDefault(require("joi"));
const router = express_1.default.Router();
// Validation schemas
const registerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    password: joi_1.default.string().min(8).required(),
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    tenantName: joi_1.default.string().required(),
    subdomain: joi_1.default.string().alphanum().min(3).max(20).required()
});
const loginSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    password: joi_1.default.string().required()
});
// Generate JWT token
const generateToken = (userId) => {
    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const expiresIn = process.env.JWT_EXPIRE || '7d';
    return jwt.sign({ userId }, secret, { expiresIn });
};
// Register new tenant and admin user
router.post('/register', async (req, res) => {
    try {
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return (0, response_1.sendValidationError)(res, error.details[0].message);
        }
        const { email, password, firstName, lastName, tenantName, subdomain } = value;
        // Check if email already exists
        const existingUser = await (0, database_1.query)('SELECT id FROM users WHERE email = $1', [email]);
        if (existingUser.rows.length > 0) {
            return (0, response_1.sendValidationError)(res, 'Email already registered');
        }
        // Check if subdomain already exists
        const existingTenant = await (0, database_1.query)('SELECT id FROM tenants WHERE subdomain = $1', [subdomain]);
        if (existingTenant.rows.length > 0) {
            return (0, response_1.sendValidationError)(res, 'Subdomain already taken');
        }
        // Hash password
        const salt = await bcryptjs_1.default.genSalt(12);
        const passwordHash = await bcryptjs_1.default.hash(password, salt);
        // Create tenant
        const tenantId = (0, uuid_1.v4)();
        const tenantResult = await (0, database_1.query)(`INSERT INTO tenants (id, name, subdomain, plan, status, settings, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW()) RETURNING *`, [
            tenantId,
            tenantName,
            subdomain,
            'trial',
            'active',
            JSON.stringify({
                features: ['basic_booking', 'customer_management'],
                limits: {
                    monthly_bookings: 100,
                    api_calls: 1000,
                    locations: 1
                }
            })
        ]);
        // Create admin user
        const userId = (0, uuid_1.v4)();
        const userResult = await (0, database_1.query)(`INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, status, permissions, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()) RETURNING id, email, first_name, last_name, role`, [
            userId,
            tenantId,
            email,
            passwordHash,
            firstName,
            lastName,
            'company_admin',
            'active',
            JSON.stringify(['all'])
        ]);
        const token = generateToken(userId);
        return (0, response_1.sendSuccess)(res, {
            token,
            user: userResult.rows[0],
            tenant: tenantResult.rows[0]
        }, 201);
    }
    catch (error) {
        logger_1.logger.error('Registration error:', error);
        return (0, response_1.sendInternalServerError)(res, 'Registration failed');
    }
});
// Login
router.post('/login', async (req, res) => {
    try {
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return (0, response_1.sendValidationError)(res, error.details[0].message);
        }
        const { email, password } = value;
        // Get user with tenant info
        const userResult = await (0, database_1.query)(`SELECT u.*, t.name as tenant_name, t.subdomain, t.plan, t.status as tenant_status
       FROM users u
       JOIN tenants t ON u.tenant_id = t.id
       WHERE u.email = $1`, [email]);
        if (userResult.rows.length === 0) {
            return (0, response_1.sendUnauthorizedError)(res, 'Invalid email or password');
        }
        const user = userResult.rows[0];
        // Check password
        logger_1.logger.info(`Password received: ${password}`);
        logger_1.logger.info(`Password hash from database: ${user.password_hash}`);
        // Verify hash generation
        const testHash = await bcryptjs_1.default.hash(password, 12);
        logger_1.logger.info(`Generated test hash: ${testHash}`);
        const isValidPassword = await bcryptjs_1.default.compare(password, user.password_hash);
        logger_1.logger.info(`bcrypt.compare result: ${isValidPassword}`);
        logger_1.logger.info(`Hash matches: ${testHash === user.password_hash}`);
        if (!isValidPassword) {
            return (0, response_1.sendUnauthorizedError)(res, 'Invalid email or password');
        }
        // Check user status
        if (user.status !== 'active') {
            return (0, response_1.sendUnauthorizedError)(res, 'Account is not active');
        }
        // Check tenant status
        if (user.tenant_status === 'suspended') {
            return (0, response_1.sendUnauthorizedError)(res, 'Account is suspended');
        }
        // Update last login
        await (0, database_1.query)('UPDATE users SET last_login = NOW() WHERE id = $1', [user.id]);
        const token = generateToken(user.id);
        return (0, response_1.sendSuccess)(res, {
            token,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                permissions: user.permissions
            },
            tenant: {
                id: user.tenant_id,
                name: user.tenant_name,
                subdomain: user.subdomain,
                plan: user.plan
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Login error:', error);
        return (0, response_1.sendInternalServerError)(res, 'Login failed');
    }
});
// Get current user
router.get('/me', async (req, res) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');
        const userResult = await (0, database_1.query)(`SELECT u.id, u.email, u.first_name, u.last_name, u.role, u.permissions,
              t.id as tenant_id, t.name as tenant_name, t.subdomain, t.plan
       FROM users u
       JOIN tenants t ON u.tenant_id = t.id
       WHERE u.id = $1 AND u.status = 'active'`, [decoded.userId]);
        if (userResult.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid token' });
        }
        const user = userResult.rows[0];
        res.json({
            success: true,
            data: {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    role: user.role,
                    permissions: user.permissions
                },
                tenant: {
                    id: user.tenant_id,
                    name: user.tenant_name,
                    subdomain: user.subdomain,
                    plan: user.plan
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get user error:', error);
        return res.status(401).json({ error: 'Invalid token' });
    }
});
// Customer authentication endpoints for customer portal
// Customer login
router.post('/customer/login', async (req, res) => {
    try {
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return (0, response_1.sendValidationError)(res, error.details[0].message);
        }
        const { email, password } = value;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        // Get customer with tenant info
        const customerResult = await (0, database_1.query)(`SELECT c.*, t.name as tenant_name, t.subdomain, t.plan, t.status as tenant_status
       FROM customers c
       JOIN tenants t ON c.tenant_id = t.id
       WHERE c.email = $1 AND c.tenant_id = $2`, [email, tenantId]);
        if (customerResult.rows.length === 0) {
            return (0, response_1.sendUnauthorizedError)(res, 'Invalid email or password');
        }
        const customer = customerResult.rows[0];
        // For customers, we'll use a simple password check or create a customer-specific auth system
        // For now, we'll allow login with any password for customers (this should be enhanced)
        // Check tenant status
        if (customer.tenant_status === 'suspended') {
            return (0, response_1.sendUnauthorizedError)(res, 'Service is temporarily unavailable');
        }
        const token = generateToken(customer.id);
        return (0, response_1.sendSuccess)(res, {
            token,
            customer: {
                id: customer.id,
                email: customer.email,
                firstName: customer.first_name,
                lastName: customer.last_name,
                phone: customer.phone
            },
            tenant: {
                id: customer.tenant_id,
                name: customer.tenant_name,
                subdomain: customer.subdomain,
                plan: customer.plan
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Customer login error:', error);
        return (0, response_1.sendInternalServerError)(res, 'Login failed');
    }
});
// Customer registration
router.post('/customer/register', async (req, res) => {
    try {
        const customerSchema = joi_1.default.object({
            email: joi_1.default.string().email().required(),
            firstName: joi_1.default.string().required(),
            lastName: joi_1.default.string().required(),
            phone: joi_1.default.string().required(),
            password: joi_1.default.string().min(6).required()
        });
        const { error, value } = customerSchema.validate(req.body);
        if (error) {
            return (0, response_1.sendValidationError)(res, error.details[0].message);
        }
        const { email, firstName, lastName, phone, password } = value;
        const tenantId = req.headers['x-tenant-id'];
        if (!tenantId) {
            return (0, response_1.sendValidationError)(res, 'Tenant ID is required');
        }
        // Check if customer already exists
        const existingCustomer = await (0, database_1.query)('SELECT id FROM customers WHERE email = $1 AND tenant_id = $2', [email, tenantId]);
        if (existingCustomer.rows.length > 0) {
            return (0, response_1.sendValidationError)(res, 'Customer with this email already exists');
        }
        // Create customer (password is optional for customers in this system)
        const customerId = (0, uuid_1.v4)();
        const result = await (0, database_1.query)(`INSERT INTO customers (
        id, tenant_id, email, first_name, last_name, phone,
        preferences, loyalty_points, total_bookings, total_spent, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()) RETURNING *`, [
            customerId,
            tenantId,
            email,
            firstName,
            lastName,
            phone,
            JSON.stringify({}),
            0,
            0,
            0
        ]);
        const token = generateToken(customerId);
        return (0, response_1.sendSuccess)(res, {
            token,
            customer: result.rows[0],
            message: 'Customer account created successfully'
        }, 201);
    }
    catch (error) {
        logger_1.logger.error('Customer registration error:', error);
        return (0, response_1.sendInternalServerError)(res, 'Registration failed');
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map