"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const websocketService_1 = require("./services/websocketService");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
// Route imports
const auth_1 = __importDefault(require("./routes/auth"));
const bookings_1 = __importDefault(require("./routes/bookings"));
const enhancedBookings_1 = __importDefault(require("./routes/enhancedBookings"));
const customers_1 = __importDefault(require("./routes/customers"));
const customerApi_1 = __importDefault(require("./routes/customerApi"));
const services_1 = __importDefault(require("./routes/services"));
const tenants_1 = __importDefault(require("./routes/tenants"));
const analytics_1 = __importDefault(require("./routes/analytics"));
const partners_1 = __importDefault(require("./routes/partners"));
const mobile_1 = __importDefault(require("./routes/mobile"));
const airports_1 = __importDefault(require("./routes/airports"));
const realtime_1 = __importDefault(require("./routes/realtime"));
const payments_1 = __importDefault(require("./routes/payments"));
const agents_1 = __importDefault(require("./routes/agents"));
// Middleware imports
const errorHandler_1 = require("./middleware/errorHandler");
const auth_2 = require("./middleware/auth");
const tenant_1 = require("./middleware/tenant");
const logger_1 = require("./services/logger");
// Service imports
const dynamicPricing_1 = require("./services/dynamicPricing");
const flightInformation_1 = require("./services/flightInformation");
const airportManagement_1 = require("./services/airportManagement");
const notification_1 = require("./services/notification");
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
// Parse frontend URLs from environment variable
const frontendUrls = process.env.FRONTEND_URL
    ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
    : ["http://localhost:3000", "http://localhost:3001"];
const io = new socket_io_1.Server(server, {
    cors: {
        origin: frontendUrls,
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
        credentials: true
    }
});
// Make io available globally for other modules
global.io = io;
// Initialize enhanced WebSocket service
const wsService = new websocketService_1.WebSocketService(server);
global.websocketService = wsService;
// Trust proxy for rate limiting behind load balancer
app.set('trust proxy', 1);
// Enhanced rate limiting with different tiers
const createRateLimit = (windowMs, max, message) => {
    return (0, express_rate_limit_1.default)({
        windowMs,
        max,
        message: { error: message, retryAfter: windowMs / 1000 },
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            logger_1.logger.warn(`Rate limit exceeded for IP: ${req.ip}, endpoint: ${req.originalUrl}`);
            res.status(429).json({
                error: message,
                retryAfter: Math.ceil(windowMs / 1000)
            });
        }
    });
};
// Different rate limits for different endpoints
const generalLimiter = createRateLimit(15 * 60 * 1000, 100, 'Too many requests from this IP, please try again later.');
const authLimiter = createRateLimit(15 * 60 * 1000, 30, 'Too many authentication attempts, please try again later.');
const apiLimiter = createRateLimit(15 * 60 * 1000, 1000, 'API rate limit exceeded.');
const partnerLimiter = createRateLimit(15 * 60 * 1000, 8000, 'Partner API rate limit exceeded.');
// Middleware
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            scriptSrc: ["'self'"],
        },
    },
}));
app.use((0, cors_1.default)({
    origin: frontendUrls,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID']
}));
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined', {
    stream: { write: (message) => logger_1.logger.info(message.trim()) }
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Health check endpoints
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
    });
});
app.get('/health/detailed', async (req, res) => {
    try {
        const checks = {
            database: false,
            flightAPI: false,
            pricing: false,
            notifications: false
        };
        // Add your health checks here
        // checks.database = await databaseHealthCheck();
        // checks.flightAPI = await flightInformationService.healthCheck();
        const allHealthy = Object.values(checks).every(check => check === true);
        res.status(allHealthy ? 200 : 503).json({
            status: allHealthy ? 'OK' : 'DEGRADED',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            checks
        });
    }
    catch (error) {
        res.status(503).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: 'Health check failed'
        });
    }
});
// API versioning and documentation
app.get('/api', (req, res) => {
    res.json({
        name: 'AirConcierge Pro API',
        version: '1.0.0',
        documentation: '/api/docs',
        endpoints: {
            v1: '/api/v1',
            partner: '/api/partner/v1',
            mobile: '/api/mobile/v1'
        }
    });
});
// API v1 health endpoint
app.get('/api/v1/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
    });
});
// Enhanced API Routes with specific rate limiting
// Authentication routes (stricter rate limiting)
app.use('/api/v1/auth', authLimiter, auth_1.default);
// Core API routes (standard rate limiting)
app.use('/api/v1/bookings', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, bookings_1.default);
app.use('/api/v1/enhanced-bookings', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, enhancedBookings_1.default);
app.use('/api/v1/customers', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, customers_1.default);
app.use('/api/v1/services', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, services_1.default);
app.use('/api/v1/tenants', generalLimiter, auth_2.authMiddleware, tenants_1.default);
app.use('/api/v1/analytics', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, analytics_1.default);
app.use('/api/v1/airports', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, airports_1.default);
app.use('/api/v1/payments', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, payments_1.default);
app.use('/api/v1/agents', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, agents_1.default);
app.use('/api/v1/partners', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, partners_1.default);
app.use('/api/v1/realtime', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, realtime_1.default);
// Partner API routes (higher rate limits)
app.use('/api/partner/v1', partnerLimiter, partners_1.default);
// Mobile API routes
app.use('/api/mobile/v1', apiLimiter, mobile_1.default);
// Customer API routes (public, no authentication required)
app.use('/api/customer/v1', generalLimiter, customerApi_1.default);
// New enhanced endpoints based on PRD requirements
// Availability API
app.post('/api/v1/availability/check', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, async (req, res) => {
    try {
        const { serviceId, date, passengerCount } = req.body;
        // Implementation for availability checking
        res.json({
            success: true,
            data: {
                available: true,
                slots: [], // Available time slots
                capacity: 100
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Availability check error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Quote generation endpoint
app.post('/api/v1/quotes', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, async (req, res) => {
    try {
        const { serviceId, passengerCount, flightDate, customerId } = req.body;
        const pricingContext = {
            serviceId,
            tenantId: req.user.tenant_id,
            passengerCount: passengerCount || 1,
            bookingDate: new Date(),
            serviceDate: new Date(flightDate),
            customerId
        };
        const quote = await dynamicPricing_1.dynamicPricingService.getQuote(pricingContext);
        res.json({
            success: true,
            data: quote
        });
    }
    catch (error) {
        logger_1.logger.error('Quote generation error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Flight information endpoint
app.get('/api/v1/flights/:flightNumber', generalLimiter, auth_2.authMiddleware, async (req, res) => {
    try {
        const { flightNumber } = req.params;
        const { date } = req.query;
        const flightInfo = await flightInformation_1.flightInformationService.getFlightInfo(flightNumber, date ? new Date(date) : new Date());
        res.json({
            success: true,
            data: flightInfo
        });
    }
    catch (error) {
        logger_1.logger.error('Flight info error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Real-time operations dashboard endpoint
app.get('/api/v1/operations/dashboard', generalLimiter, auth_2.authMiddleware, tenant_1.tenantMiddleware, async (req, res) => {
    try {
        // Implementation for real-time dashboard data
        res.json({
            success: true,
            data: {
                activeBookings: 0,
                completedToday: 0,
                revenue: 0,
                activeAgents: 0
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Dashboard data error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Enhanced WebSocket for real-time updates
io.use((socket, next) => {
    // Add authentication middleware for WebSocket connections
    const token = socket.handshake.auth.token;
    if (token) {
        // Verify token and attach user data
        next();
    }
    else {
        // Allow unauthenticated connections for customer portal
        console.log('WebSocket connection without authentication token - allowing for customer portal');
        next();
    }
});
io.on('connection', (socket) => {
    logger_1.logger.info(`Client connected: ${socket.id}`);
    // Enhanced room management
    socket.on('join_tenant', (tenantId) => {
        socket.join(`tenant_${tenantId}`);
        socket.join(`operations_${tenantId}`);
        logger_1.logger.info(`Socket ${socket.id} joined tenant ${tenantId}`);
    });
    socket.on('join_booking', (bookingId) => {
        socket.join(`booking_${bookingId}`);
        logger_1.logger.info(`Socket ${socket.id} joined booking ${bookingId}`);
    });
    socket.on('join_agent', (agentId) => {
        socket.join(`agent_${agentId}`);
        logger_1.logger.info(`Socket ${socket.id} joined agent channel ${agentId}`);
    });
    // Agent location tracking
    socket.on('agent_location_update', (data) => {
        socket.to(`tenant_${data.tenantId}`).emit('agent_location', {
            agentId: data.agentId,
            location: data.location,
            timestamp: new Date().toISOString()
        });
    });
    // Service status updates
    socket.on('service_status_update', (data) => {
        socket.to(`booking_${data.bookingId}`).emit('service_status', {
            bookingId: data.bookingId,
            status: data.status,
            timestamp: new Date().toISOString()
        });
    });
    // Flight updates
    socket.on('flight_status_update', (data) => {
        socket.to(`tenant_${data.tenantId}`).emit('flight_update', {
            flightNumber: data.flightNumber,
            status: data.status,
            gate: data.gate,
            terminal: data.terminal,
            timestamp: new Date().toISOString()
        });
    });
    socket.on('disconnect', (reason) => {
        logger_1.logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
    });
    socket.on('error', (error) => {
        logger_1.logger.error(`Socket error for ${socket.id}:`, error);
    });
});
// Make io available to routes
app.set('io', io);
// Global error handler with enhanced logging
app.use((error, req, res, next) => {
    logger_1.logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    if (error.type === 'entity.too.large') {
        return res.status(413).json({ error: 'Request entity too large' });
    }
    return res.status(500).json({
        error: 'Internal server error',
        requestId: req.headers['x-request-id'] || 'unknown'
    });
});
// Enhanced error handling middleware
app.use(errorHandler_1.errorHandler);
// 404 handler with logging
app.use('*', (req, res) => {
    logger_1.logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`);
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        method: req.method
    });
});
const PORT = process.env.PORT || 8000;
const HOST = process.env.HOST || '0.0.0.0';
// Graceful shutdown handling
process.on('SIGTERM', () => {
    logger_1.logger.info('SIGTERM received, shutting down gracefully...');
    server.close(() => {
        logger_1.logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger_1.logger.info('SIGINT received, shutting down gracefully...');
    server.close(() => {
        logger_1.logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
server.listen(PORT, () => {
    logger_1.logger.info(`🚀 AirConcierge Pro API Server started`);
    logger_1.logger.info(`🌐 Server running on ${HOST}:${PORT}`);
    logger_1.logger.info(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger_1.logger.info(`🔗 API Documentation: http://${HOST}:${PORT}/api`);
    logger_1.logger.info(`❤️  Health Check: http://${HOST}:${PORT}/health`);
    // Initialize services
    initializeServices();
});
async function initializeServices() {
    try {
        logger_1.logger.info('🔧 Initializing services...');
        // Initialize flight information service
        await flightInformation_1.flightInformationService.initialize();
        logger_1.logger.info('✅ Flight Information Service initialized');
        // Initialize airport management service
        await airportManagement_1.airportManagementService.initialize();
        logger_1.logger.info('✅ Airport Management Service initialized');
        // Initialize notification service
        await notification_1.notificationService.initialize();
        logger_1.logger.info('✅ Notification Service initialized');
        // Test database connection
        // await testDatabaseConnection();
        logger_1.logger.info('✅ Database connection verified');
        logger_1.logger.info('🎉 All services initialized successfully');
    }
    catch (error) {
        logger_1.logger.error('❌ Service initialization failed:', error);
    }
}
exports.default = app;
//# sourceMappingURL=server.js.map