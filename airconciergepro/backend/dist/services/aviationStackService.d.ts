interface AviationStackAirport {
    airport_name: string;
    iata_code: string;
    icao_code: string;
    latitude: string;
    longitude: string;
    geoname_id: string;
    timezone: string;
    gmt: string;
    phone_number: string;
    country_name: string;
    country_iso2: string;
    city_iata_code: string;
}
interface AviationStackFlight {
    flight_date: string;
    flight_status: string;
    departure: {
        airport: string;
        timezone: string;
        iata: string;
        icao: string;
        terminal: string;
        gate: string;
        delay: number;
        scheduled: string;
        estimated: string;
        actual: string;
        estimated_runway: string;
        actual_runway: string;
    };
    arrival: {
        airport: string;
        timezone: string;
        iata: string;
        icao: string;
        terminal: string;
        gate: string;
        baggage: string;
        delay: number;
        scheduled: string;
        estimated: string;
        actual: string;
        estimated_runway: string;
        actual_runway: string;
    };
    airline: {
        name: string;
        iata: string;
        icao: string;
    };
    flight: {
        number: string;
        iata: string;
        icao: string;
        codeshared: any;
    };
    aircraft: {
        registration: string;
        iata: string;
        icao: string;
        icao24: string;
    };
    live: {
        updated: string;
        latitude: number;
        longitude: number;
        altitude: number;
        direction: number;
        speed_horizontal: number;
        speed_vertical: number;
        is_ground: boolean;
    };
}
export declare class AviationStackService {
    private readonly apiKey;
    private readonly baseUrl;
    private readonly axiosInstance;
    constructor();
    /**
     * Sync airports from AviationStack API to local database
     */
    syncAirports(limit?: number, offset?: number): Promise<void>;
    /**
     * Insert or update airport in database
     */
    private upsertAirport;
    /**
     * Get real-time flight information
     */
    getFlightInfo(flightNumber: string, date?: string): Promise<AviationStackFlight | null>;
    /**
     * Get flights by airport
     */
    getFlightsByAirport(airportCode: string, type?: 'departure' | 'arrival', limit?: number): Promise<AviationStackFlight[]>;
    /**
     * Search airports by name or code
     */
    searchAirports(query: string, limit?: number): Promise<any[]>;
    /**
     * Get airport details by IATA code
     */
    getAirportByCode(iataCode: string): Promise<AviationStackAirport | null>;
    /**
     * Cache flight information in database
     */
    cacheFlightInfo(flight: AviationStackFlight): Promise<void>;
}
export declare const aviationStackService: AviationStackService;
export {};
//# sourceMappingURL=aviationStackService.d.ts.map