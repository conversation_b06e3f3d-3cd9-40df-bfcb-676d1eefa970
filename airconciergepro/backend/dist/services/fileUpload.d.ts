import multer from 'multer';
interface UploadResult {
    success: boolean;
    fileId?: string;
    filePath?: string;
    error?: string;
}
export declare class FileUploadService {
    private uploadDir;
    private maxFileSize;
    private allowedTypes;
    constructor();
    /**
     * Ensure upload directory exists
     */
    private ensureUploadDir;
    /**
     * Configure multer storage
     */
    getMulterConfig(): multer.Multer;
    /**
     * Save file metadata to database
     */
    saveFileMetadata(tenantId: string, file: Express.Multer.File, documentType?: string, bookingId?: string, customerId?: string, userId?: string): Promise<UploadResult>;
    /**
     * Get file metadata
     */
    getFileMetadata(fileId: string, tenantId: string): Promise<any>;
    /**
     * Get file path for download
     */
    getFilePath(fileId: string, tenantId: string): Promise<string | null>;
    /**
     * Delete file
     */
    deleteFile(fileId: string, tenantId: string): Promise<boolean>;
    /**
     * Get files for booking
     */
    getBookingFiles(bookingId: string, tenantId: string): Promise<any[]>;
    /**
     * Get files for customer
     */
    getCustomerFiles(customerId: string, tenantId: string): Promise<any[]>;
    /**
     * Clean up old files (for maintenance)
     */
    cleanupOldFiles(daysOld?: number): Promise<number>;
    /**
     * Get storage statistics
     */
    getStorageStats(tenantId?: string): Promise<{
        totalFiles: number;
        totalSize: number;
        averageSize: number;
        fileTypes: {
            [key: string]: number;
        };
    }>;
}
export declare const fileUploadService: FileUploadService;
export {};
//# sourceMappingURL=fileUpload.d.ts.map