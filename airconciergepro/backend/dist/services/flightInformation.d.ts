interface FlightInfo {
    flightNumber: string;
    airline: string;
    departureAirport: string;
    arrivalAirport: string;
    scheduledDeparture?: Date;
    scheduledArrival?: Date;
    estimatedDeparture?: Date;
    estimatedArrival?: Date;
    actualDeparture?: Date;
    actualArrival?: Date;
    status: string;
    gate?: string;
    terminal?: string;
    aircraftType?: string;
    delayReason?: string;
    lastUpdated: Date;
}
export declare class FlightInformationService {
    private apiKey;
    private apiUrl;
    constructor();
    /**
     * Initialize the flight information service
     */
    initialize(): Promise<void>;
    /**
     * Get real-time flight information
     */
    getFlightInfo(flightNumber: string, date?: Date): Promise<FlightInfo | null>;
    /**
     * Fetch flight info from external API using AviationStack service
     */
    private fetchFromExternalAPI;
    /**
     * Parse external API response
     */
    private parseExternalFlightData;
    /**
     * Get cached flight info from database
     */
    private getCachedFlightInfo;
    /**
     * Check if cached data is still valid (15 minutes)
     */
    private isCacheValid;
    /**
     * Cache flight info in database
     */
    private cacheFlightInfo;
    /**
     * Format cached flight info to FlightInfo interface
     */
    private formatFlightInfo;
    /**
     * Format AviationStack flight data to our internal format
     */
    private formatAviationStackFlight;
    /**
     * Map AviationStack status to our internal status
     */
    private mapAviationStackStatus;
    /**
     * Get mock flight data for development/demo
     */
    private getMockFlightData;
    /**
     * Update booking with latest flight information
     */
    updateBookingWithFlightInfo(bookingId: string): Promise<boolean>;
    /**
     * Monitor flights for updates
     */
    monitorFlights(): Promise<void>;
    /**
     * Get flight delays for a specific airport
     */
    getAirportDelays(airportCode: string): Promise<{
        totalFlights: number;
        delayedFlights: number;
        averageDelay: number;
        delayPercentage: number;
    }>;
    /**
     * Get flight status summary for dashboard
     */
    getFlightStatusSummary(): Promise<{
        onTime: number;
        delayed: number;
        cancelled: number;
        boarding: number;
    }>;
}
export declare const flightInformationService: FlightInformationService;
export {};
//# sourceMappingURL=flightInformation.d.ts.map