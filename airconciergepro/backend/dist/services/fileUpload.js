"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileUploadService = exports.FileUploadService = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const uuid_1 = require("uuid");
const database_1 = require("./database");
const logger_1 = require("./logger");
class FileUploadService {
    constructor() {
        this.uploadDir = process.env.UPLOAD_DIR || './uploads';
        this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB default
        this.allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,pdf,doc,docx').split(',');
        this.ensureUploadDir();
    }
    /**
     * Ensure upload directory exists
     */
    ensureUploadDir() {
        if (!fs_1.default.existsSync(this.uploadDir)) {
            fs_1.default.mkdirSync(this.uploadDir, { recursive: true });
            logger_1.logger.info(`Created upload directory: ${this.uploadDir}`);
        }
    }
    /**
     * Configure multer storage
     */
    getMulterConfig() {
        const storage = multer_1.default.diskStorage({
            destination: (req, file, cb) => {
                // Create tenant-specific directory
                const tenantId = req.user?.tenant_id || 'default';
                const tenantDir = path_1.default.join(this.uploadDir, tenantId);
                if (!fs_1.default.existsSync(tenantDir)) {
                    fs_1.default.mkdirSync(tenantDir, { recursive: true });
                }
                cb(null, tenantDir);
            },
            filename: (req, file, cb) => {
                // Generate unique filename
                const uniqueName = `${(0, uuid_1.v4)()}-${Date.now()}${path_1.default.extname(file.originalname)}`;
                cb(null, uniqueName);
            }
        });
        const fileFilter = (req, file, cb) => {
            const fileExt = path_1.default.extname(file.originalname).toLowerCase().slice(1);
            if (this.allowedTypes.includes(fileExt)) {
                cb(null, true);
            }
            else {
                cb(new Error(`File type ${fileExt} not allowed. Allowed types: ${this.allowedTypes.join(', ')}`));
            }
        };
        return (0, multer_1.default)({
            storage,
            fileFilter,
            limits: {
                fileSize: this.maxFileSize
            }
        });
    }
    /**
     * Save file metadata to database
     */
    async saveFileMetadata(tenantId, file, documentType, bookingId, customerId, userId) {
        try {
            const fileId = (0, uuid_1.v4)();
            const relativePath = path_1.default.relative(this.uploadDir, file.path);
            await (0, database_1.query)(`INSERT INTO documents (
          id, tenant_id, booking_id, customer_id, user_id, filename, 
          original_filename, file_type, file_size, storage_path, 
          document_type, metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())`, [
                fileId,
                tenantId,
                bookingId || null,
                customerId || null,
                userId || null,
                file.filename,
                file.originalname,
                file.mimetype,
                file.size,
                relativePath,
                documentType || 'other',
                JSON.stringify({
                    uploadDate: new Date().toISOString(),
                    originalSize: file.size,
                    encoding: file.encoding
                })
            ]);
            return {
                success: true,
                fileId,
                filePath: relativePath
            };
        }
        catch (error) {
            logger_1.logger.error('Error saving file metadata:', error);
            // Clean up uploaded file if database save fails
            try {
                fs_1.default.unlinkSync(file.path);
            }
            catch (unlinkError) {
                logger_1.logger.error('Error cleaning up file:', unlinkError);
            }
            return {
                success: false,
                error: 'Failed to save file metadata'
            };
        }
    }
    /**
     * Get file metadata
     */
    async getFileMetadata(fileId, tenantId) {
        try {
            const result = await (0, database_1.query)('SELECT * FROM documents WHERE id = $1 AND tenant_id = $2', [fileId, tenantId]);
            return result.rows[0] || null;
        }
        catch (error) {
            logger_1.logger.error('Error getting file metadata:', error);
            return null;
        }
    }
    /**
     * Get file path for download
     */
    async getFilePath(fileId, tenantId) {
        try {
            const metadata = await this.getFileMetadata(fileId, tenantId);
            if (!metadata) {
                return null;
            }
            const fullPath = path_1.default.join(this.uploadDir, metadata.storage_path);
            // Check if file exists
            if (fs_1.default.existsSync(fullPath)) {
                return fullPath;
            }
            logger_1.logger.warn(`File not found: ${fullPath}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error getting file path:', error);
            return null;
        }
    }
    /**
     * Delete file
     */
    async deleteFile(fileId, tenantId) {
        try {
            const metadata = await this.getFileMetadata(fileId, tenantId);
            if (!metadata) {
                return false;
            }
            const fullPath = path_1.default.join(this.uploadDir, metadata.storage_path);
            // Delete physical file
            if (fs_1.default.existsSync(fullPath)) {
                fs_1.default.unlinkSync(fullPath);
            }
            // Delete metadata
            await (0, database_1.query)('DELETE FROM documents WHERE id = $1 AND tenant_id = $2', [fileId, tenantId]);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Error deleting file:', error);
            return false;
        }
    }
    /**
     * Get files for booking
     */
    async getBookingFiles(bookingId, tenantId) {
        try {
            const result = await (0, database_1.query)(`SELECT id, filename, original_filename, file_type, file_size, 
                document_type, created_at
         FROM documents 
         WHERE booking_id = $1 AND tenant_id = $2
         ORDER BY created_at DESC`, [bookingId, tenantId]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error getting booking files:', error);
            return [];
        }
    }
    /**
     * Get files for customer
     */
    async getCustomerFiles(customerId, tenantId) {
        try {
            const result = await (0, database_1.query)(`SELECT id, filename, original_filename, file_type, file_size, 
                document_type, created_at
         FROM documents 
         WHERE customer_id = $1 AND tenant_id = $2
         ORDER BY created_at DESC`, [customerId, tenantId]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error getting customer files:', error);
            return [];
        }
    }
    /**
     * Clean up old files (for maintenance)
     */
    async cleanupOldFiles(daysOld = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            const result = await (0, database_1.query)(`SELECT id, tenant_id, storage_path 
         FROM documents 
         WHERE created_at < $1`, [cutoffDate]);
            let deletedCount = 0;
            for (const doc of result.rows) {
                const fullPath = path_1.default.join(this.uploadDir, doc.storage_path);
                try {
                    if (fs_1.default.existsSync(fullPath)) {
                        fs_1.default.unlinkSync(fullPath);
                    }
                    await (0, database_1.query)('DELETE FROM documents WHERE id = $1', [doc.id]);
                    deletedCount++;
                }
                catch (error) {
                    logger_1.logger.error(`Error deleting old file ${doc.id}:`, error);
                }
            }
            logger_1.logger.info(`Cleaned up ${deletedCount} old files`);
            return deletedCount;
        }
        catch (error) {
            logger_1.logger.error('Error cleaning up old files:', error);
            return 0;
        }
    }
    /**
     * Get storage statistics
     */
    async getStorageStats(tenantId) {
        try {
            let whereClause = '';
            const params = [];
            if (tenantId) {
                whereClause = 'WHERE tenant_id = $1';
                params.push(tenantId);
            }
            const statsResult = await (0, database_1.query)(`SELECT 
           COUNT(*) as total_files,
           SUM(file_size) as total_size,
           AVG(file_size) as average_size
         FROM documents ${whereClause}`, params);
            const typeResult = await (0, database_1.query)(`SELECT 
           document_type,
           COUNT(*) as count
         FROM documents ${whereClause}
         GROUP BY document_type`, params);
            const stats = statsResult.rows[0];
            const fileTypes = {};
            typeResult.rows.forEach(row => {
                fileTypes[row.document_type] = parseInt(row.count);
            });
            return {
                totalFiles: parseInt(stats.total_files) || 0,
                totalSize: parseInt(stats.total_size) || 0,
                averageSize: Math.round(parseFloat(stats.average_size)) || 0,
                fileTypes
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting storage stats:', error);
            return {
                totalFiles: 0,
                totalSize: 0,
                averageSize: 0,
                fileTypes: {}
            };
        }
    }
}
exports.FileUploadService = FileUploadService;
exports.fileUploadService = new FileUploadService();
//# sourceMappingURL=fileUpload.js.map