"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aviationStackService = exports.AviationStackService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("./logger");
const database_1 = require("./database");
class AviationStackService {
    constructor() {
        this.baseUrl = 'http://api.aviationstack.com/v1';
        this.apiKey = process.env.AVIATIONSTACK_API_KEY || '********************************';
        this.axiosInstance = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 10000,
            params: {
                access_key: this.apiKey
            }
        });
    }
    /**
     * Sync airports from AviationStack API to local database
     */
    async syncAirports(limit = 100, offset = 0) {
        try {
            logger_1.logger.info(`Syncing airports from AviationStack API (limit: ${limit}, offset: ${offset})`);
            const response = await this.axiosInstance.get('/airports', {
                params: {
                    limit,
                    offset
                }
            });
            const airports = response.data.data;
            if (!airports || airports.length === 0) {
                logger_1.logger.info('No more airports to sync');
                return;
            }
            // Insert or update airports in database
            for (const airport of airports) {
                await this.upsertAirport(airport);
            }
            logger_1.logger.info(`Successfully synced ${airports.length} airports`);
            // If we got a full batch, there might be more
            if (airports.length === limit) {
                logger_1.logger.info('More airports available, continuing sync...');
                await this.syncAirports(limit, offset + limit);
            }
        }
        catch (error) {
            logger_1.logger.error('Error syncing airports:', error);
            throw error;
        }
    }
    /**
     * Insert or update airport in database
     */
    async upsertAirport(airport) {
        try {
            await (0, database_1.query)(`INSERT INTO airports (
          iata_code, icao_code, name, city, country, timezone,
          latitude, longitude, phone_number, country_iso2,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        ON CONFLICT (iata_code) 
        DO UPDATE SET
          icao_code = EXCLUDED.icao_code,
          name = EXCLUDED.name,
          city = EXCLUDED.city,
          country = EXCLUDED.country,
          timezone = EXCLUDED.timezone,
          latitude = EXCLUDED.latitude,
          longitude = EXCLUDED.longitude,
          phone_number = EXCLUDED.phone_number,
          country_iso2 = EXCLUDED.country_iso2,
          updated_at = NOW()`, [
                airport.iata_code,
                airport.icao_code,
                airport.airport_name,
                airport.city_iata_code || 'Unknown',
                airport.country_name,
                airport.timezone,
                parseFloat(airport.latitude) || null,
                parseFloat(airport.longitude) || null,
                airport.phone_number,
                airport.country_iso2
            ]);
        }
        catch (error) {
            logger_1.logger.error(`Error upserting airport ${airport.iata_code}:`, error);
        }
    }
    /**
     * Get real-time flight information
     */
    async getFlightInfo(flightNumber, date) {
        try {
            const params = {
                flight_iata: flightNumber
            };
            if (date) {
                params.flight_date = date;
            }
            const response = await this.axiosInstance.get('/flights', { params });
            const flights = response.data.data;
            if (!flights || flights.length === 0) {
                return null;
            }
            // Return the most recent flight
            return flights[0];
        }
        catch (error) {
            logger_1.logger.error(`Error fetching flight info for ${flightNumber}:`, error);
            return null;
        }
    }
    /**
     * Get flights by airport
     */
    async getFlightsByAirport(airportCode, type = 'arrival', limit = 50) {
        try {
            const params = {
                limit
            };
            if (type === 'departure') {
                params.dep_iata = airportCode;
            }
            else {
                params.arr_iata = airportCode;
            }
            const response = await this.axiosInstance.get('/flights', { params });
            return response.data.data || [];
        }
        catch (error) {
            logger_1.logger.error(`Error fetching flights for airport ${airportCode}:`, error);
            return [];
        }
    }
    /**
     * Search airports by name or code
     */
    async searchAirports(query, limit = 20) {
        try {
            const result = await this.axiosInstance.get('/airports', {
                params: {
                    search: query,
                    limit
                }
            });
            return result.data.data || [];
        }
        catch (error) {
            logger_1.logger.error(`Error searching airports with query "${query}":`, error);
            return [];
        }
    }
    /**
     * Get airport details by IATA code
     */
    async getAirportByCode(iataCode) {
        try {
            const response = await this.axiosInstance.get('/airports', {
                params: {
                    iata_code: iataCode
                }
            });
            const airports = response.data.data;
            return airports && airports.length > 0 ? airports[0] : null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching airport ${iataCode}:`, error);
            return null;
        }
    }
    /**
     * Cache flight information in database
     */
    async cacheFlightInfo(flight) {
        try {
            await (0, database_1.query)(`INSERT INTO flight_cache (
          flight_number, flight_date, status, airline_name, airline_iata,
          departure_airport, departure_scheduled, departure_estimated, departure_actual,
          departure_terminal, departure_gate, departure_delay,
          arrival_airport, arrival_scheduled, arrival_estimated, arrival_actual,
          arrival_terminal, arrival_gate, arrival_baggage, arrival_delay,
          aircraft_registration, aircraft_type, live_data,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, NOW(), NOW()
        )
        ON CONFLICT (flight_number, flight_date)
        DO UPDATE SET
          status = EXCLUDED.status,
          departure_estimated = EXCLUDED.departure_estimated,
          departure_actual = EXCLUDED.departure_actual,
          departure_gate = EXCLUDED.departure_gate,
          departure_delay = EXCLUDED.departure_delay,
          arrival_estimated = EXCLUDED.arrival_estimated,
          arrival_actual = EXCLUDED.arrival_actual,
          arrival_gate = EXCLUDED.arrival_gate,
          arrival_delay = EXCLUDED.arrival_delay,
          live_data = EXCLUDED.live_data,
          updated_at = NOW()`, [
                flight.flight.iata,
                flight.flight_date,
                flight.flight_status,
                flight.airline.name,
                flight.airline.iata,
                flight.departure.iata,
                flight.departure.scheduled,
                flight.departure.estimated,
                flight.departure.actual,
                flight.departure.terminal,
                flight.departure.gate,
                flight.departure.delay,
                flight.arrival.iata,
                flight.arrival.scheduled,
                flight.arrival.estimated,
                flight.arrival.actual,
                flight.arrival.terminal,
                flight.arrival.gate,
                flight.arrival.baggage,
                flight.arrival.delay,
                flight.aircraft?.registration,
                flight.aircraft?.iata,
                JSON.stringify(flight.live)
            ]);
        }
        catch (error) {
            logger_1.logger.error('Error caching flight info:', error);
        }
    }
}
exports.AviationStackService = AviationStackService;
exports.aviationStackService = new AviationStackService();
//# sourceMappingURL=aviationStackService.js.map