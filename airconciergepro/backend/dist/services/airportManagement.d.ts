interface Airport {
    id: string;
    iataCode: string;
    icaoCode: string;
    name: string;
    city: string;
    country: string;
    timezone: string;
    terminals?: Terminal[];
    meetingPoints?: MeetingPoint[];
}
interface Terminal {
    id: string;
    airportId: string;
    terminalCode: string;
    terminalName: string;
    gates: string[];
    facilities: string[];
    operatingHours: {
        open: string;
        close: string;
    };
    contactInfo: {
        phone?: string;
        email?: string;
    };
}
interface MeetingPoint {
    id: string;
    airportId: string;
    terminal?: string;
    name: string;
    description: string;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
    instructions: string;
    accessibilityFeatures: string[];
    capacity: number;
    equipmentAvailable: string[];
    status: 'active' | 'inactive' | 'maintenance';
}
interface AirportOperations {
    id: string;
    airportId: string;
    date: Date;
    totalArrivals: number;
    totalDepartures: number;
    delayedFlights: number;
    cancelledFlights: number;
    averageDelayMinutes: number;
    weatherConditions?: string;
    operationalStatus: 'normal' | 'disrupted' | 'closed';
    notes?: string;
}
export declare class AirportManagementService {
    /**
     * Initialize the airport management service
     */
    initialize(): Promise<void>;
    /**
     * Load initial airport data from SQL file
     */
    private loadInitialAirportData;
    /**
     * Get airport by IATA code
     */
    getAirport(iataCode: string): Promise<Airport | null>;
    /**
     * Get all airports with pagination
     */
    getAirports(page?: number, limit?: number): Promise<{
        airports: Airport[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    /**
     * Get airport terminals
     */
    getAirportTerminals(airportId: string): Promise<Terminal[]>;
    /**
     * Get airport meeting points
     */
    getAirportMeetingPoints(airportId: string, terminal?: string): Promise<MeetingPoint[]>;
    /**
     * Create or update meeting point
     */
    upsertMeetingPoint(meetingPoint: Omit<MeetingPoint, 'id'>): Promise<string | null>;
    /**
     * Get airport operational data
     */
    getAirportOperations(airportId: string, date?: Date): Promise<AirportOperations | null>;
    /**
     * Search airports by name or code
     */
    searchAirports(query_term: string): Promise<Airport[]>;
}
export declare const airportManagementService: AirportManagementService;
export {};
//# sourceMappingURL=airportManagement.d.ts.map