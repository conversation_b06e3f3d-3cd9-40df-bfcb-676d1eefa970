"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = exports.NotificationService = void 0;
const database_1 = require("./database");
const logger_1 = require("./logger");
const nodemailer_1 = __importDefault(require("nodemailer"));
const uuid_1 = require("uuid");
class NotificationService {
    constructor() {
        this.emailTransporter = null;
        this.smsClient = null;
        this.initializeEmailTransporter();
        this.initializeSMSClient();
    }
    /**
     * Initialize the notification service
     */
    async initialize() {
        try {
            logger_1.logger.info('Initializing Notification Service...');
            // Re-initialize transporters to ensure they're ready
            this.initializeEmailTransporter();
            this.initializeSMSClient();
            // Test email configuration if available
            if (this.emailTransporter && process.env.SMTP_USER) {
                try {
                    await this.emailTransporter.verify();
                    logger_1.logger.info('Email transporter verified successfully');
                }
                catch (error) {
                    logger_1.logger.warn('Email transporter verification failed:', error);
                }
            }
            logger_1.logger.info('Notification Service initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Notification Service:', error);
            throw error;
        }
    }
    initializeEmailTransporter() {
        try {
            const emailConfig = {
                host: process.env.SMTP_HOST || 'smtp.gmail.com',
                port: parseInt(process.env.SMTP_PORT || '587'),
                secure: false,
                auth: {
                    user: process.env.SMTP_USER || '',
                    pass: process.env.SMTP_PASS || ''
                }
            };
            this.emailTransporter = nodemailer_1.default.createTransport(emailConfig);
            logger_1.logger.info('Email transporter initialized');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize email transporter:', error);
        }
    }
    initializeSMSClient() {
        try {
            if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
                // In a real implementation, you would initialize Twilio client here
                logger_1.logger.info('SMS client would be initialized with Twilio');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize SMS client:', error);
        }
    }
    /**
     * Send notification
     */
    async sendNotification(data) {
        try {
            // Store notification in database
            const notificationId = await this.storeNotification(data);
            let result;
            switch (data.type) {
                case 'email':
                    result = await this.sendEmail(data);
                    break;
                case 'sms':
                    result = await this.sendSMS(data);
                    break;
                case 'push':
                    result = await this.sendPushNotification(data);
                    break;
                case 'webhook':
                    result = await this.sendWebhook(data);
                    break;
                default:
                    throw new Error(`Unsupported notification type: ${data.type}`);
            }
            // Update notification status
            await this.updateNotificationStatus(notificationId, result.success ? 'sent' : 'failed', result.messageId);
            return result;
        }
        catch (error) {
            logger_1.logger.error('Notification send error:', error);
            return { success: false, error: error.message };
        }
    }
    /**
     * Send email notification
     */
    async sendEmail(data) {
        if (!this.emailTransporter) {
            return { success: false, error: 'Email transporter not configured' };
        }
        try {
            const mailOptions = {
                from: process.env.SMTP_FROM || '<EMAIL>',
                to: data.recipient,
                subject: data.subject || 'AirConcierge Pro Notification',
                html: this.generateEmailTemplate(data)
            };
            const info = await this.emailTransporter.sendMail(mailOptions);
            return { success: true, messageId: info.messageId };
        }
        catch (error) {
            logger_1.logger.error('Email send error:', error);
            return { success: false, error: error.message };
        }
    }
    /**
     * Send SMS notification
     */
    async sendSMS(data) {
        if (!this.smsClient) {
            logger_1.logger.warn('SMS client not configured, simulating SMS send');
            // Simulate SMS sending for demo
            return { success: true, messageId: `sms_${Date.now()}` };
        }
        try {
            // In a real implementation, you would use Twilio or another SMS service
            // const message = await this.smsClient.messages.create({
            //   body: data.message,
            //   from: process.env.TWILIO_PHONE_NUMBER,
            //   to: data.recipient
            // });
            return { success: true, messageId: `sms_simulated_${Date.now()}` };
        }
        catch (error) {
            logger_1.logger.error('SMS send error:', error);
            return { success: false, error: error.message };
        }
    }
    /**
     * Send push notification
     */
    async sendPushNotification(data) {
        try {
            // In a real implementation, you would use Firebase Cloud Messaging or similar
            logger_1.logger.info('Push notification would be sent via FCM');
            return { success: true, messageId: `push_simulated_${Date.now()}` };
        }
        catch (error) {
            logger_1.logger.error('Push notification error:', error);
            return { success: false, error: error.message };
        }
    }
    /**
     * Send webhook notification
     */
    async sendWebhook(data) {
        try {
            // In a real implementation, you would make HTTP POST to webhook URL
            logger_1.logger.info('Webhook would be sent to:', data.recipient);
            return { success: true, messageId: `webhook_simulated_${Date.now()}` };
        }
        catch (error) {
            logger_1.logger.error('Webhook send error:', error);
            return { success: false, error: error.message };
        }
    }
    /**
     * Store notification in database
     */
    async storeNotification(data) {
        const notificationId = (0, uuid_1.v4)();
        await (0, database_1.query)(`INSERT INTO notifications (
        id, tenant_id, booking_id, user_id, customer_id, type, channel, 
        recipient, subject, message, status, metadata, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())`, [
            notificationId,
            data.tenantId,
            data.bookingId || null,
            data.userId || null,
            data.customerId || null,
            data.type,
            data.channel,
            data.recipient,
            data.subject || null,
            data.message,
            'pending',
            JSON.stringify(data.metadata || {})
        ]);
        return notificationId;
    }
    /**
     * Update notification status
     */
    async updateNotificationStatus(notificationId, status, messageId) {
        const updateFields = ['status = $2', 'updated_at = NOW()'];
        const params = [notificationId, status];
        if (status === 'sent' && messageId) {
            updateFields.push('sent_at = NOW()');
            updateFields.push(`metadata = COALESCE(metadata, '{}') || '{"message_id": "${messageId}"}'`);
        }
        await (0, database_1.query)(`UPDATE notifications SET ${updateFields.join(', ')} WHERE id = $1`, params);
    }
    /**
     * Generate email template
     */
    generateEmailTemplate(data) {
        // Basic HTML email template
        return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>AirConcierge Pro</h1>
            </div>
            <div class="content">
              ${data.message}
            </div>
            <div class="footer">
              <p>This is an automated message from AirConcierge Pro. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
    }
    /**
     * Send booking confirmation notification
     */
    async sendBookingConfirmation(tenantId, bookingId, customerEmail, bookingDetails) {
        const message = `
      <h2>Booking Confirmation</h2>
      <p>Your meet & greet service has been confirmed.</p>
      <div style="background-color: white; padding: 15px; border-radius: 5px;">
        <p><strong>Booking Reference:</strong> ${bookingDetails.bookingReference}</p>
        <p><strong>Flight:</strong> ${bookingDetails.flightNumber}</p>
        <p><strong>Date:</strong> ${new Date(bookingDetails.flightDate).toLocaleDateString()}</p>
        <p><strong>Service:</strong> ${bookingDetails.serviceName}</p>
        <p><strong>Meeting Point:</strong> ${bookingDetails.meetingPoint}</p>
      </div>
      <p>Our team will contact you 24 hours before your flight with final details.</p>
    `;
        await this.sendNotification({
            tenantId,
            bookingId,
            type: 'email',
            channel: 'smtp',
            recipient: customerEmail,
            subject: `Booking Confirmed - ${bookingDetails.bookingReference}`,
            message,
            metadata: { bookingReference: bookingDetails.bookingReference }
        });
    }
    /**
     * Send booking status update
     */
    async sendBookingStatusUpdate(tenantId, bookingId, customerEmail, customerPhone, status, message) {
        // Send email
        await this.sendNotification({
            tenantId,
            bookingId,
            type: 'email',
            channel: 'smtp',
            recipient: customerEmail,
            subject: `Booking Update - Status: ${status}`,
            message: `
        <h2>Booking Status Update</h2>
        <p>Your booking status has been updated to: <strong>${status}</strong></p>
        <p>${message}</p>
      `,
            metadata: { status }
        });
        // Send SMS if phone number provided
        if (customerPhone) {
            await this.sendNotification({
                tenantId,
                bookingId,
                type: 'sms',
                channel: 'twilio',
                recipient: customerPhone,
                message: `AirConcierge Pro: Your booking status is now ${status}. ${message}`,
                metadata: { status }
            });
        }
    }
    /**
     * Send agent assignment notification
     */
    async sendAgentAssignment(tenantId, bookingId, agentEmail, bookingDetails) {
        const message = `
      <h2>New Assignment</h2>
      <p>You have been assigned to a new booking.</p>
      <div style="background-color: white; padding: 15px; border-radius: 5px;">
        <p><strong>Booking Reference:</strong> ${bookingDetails.bookingReference}</p>
        <p><strong>Customer:</strong> ${bookingDetails.customerName}</p>
        <p><strong>Flight:</strong> ${bookingDetails.flightNumber}</p>
        <p><strong>Date:</strong> ${new Date(bookingDetails.flightDate).toLocaleDateString()}</p>
        <p><strong>Meeting Point:</strong> ${bookingDetails.meetingPoint}</p>
      </div>
      <p>Please review the booking details in your mobile app.</p>
    `;
        await this.sendNotification({
            tenantId,
            bookingId,
            type: 'email',
            channel: 'smtp',
            recipient: agentEmail,
            subject: `New Assignment - ${bookingDetails.bookingReference}`,
            message,
            metadata: { bookingReference: bookingDetails.bookingReference }
        });
    }
    /**
     * Send bulk notifications
     */
    async sendBulkNotifications(notifications) {
        const results = [];
        let successful = 0;
        let failed = 0;
        for (const notification of notifications) {
            const result = await this.sendNotification(notification);
            results.push(result);
            if (result.success) {
                successful++;
            }
            else {
                failed++;
            }
            // Add small delay to avoid overwhelming email servers
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return { successful, failed, results };
    }
}
exports.NotificationService = NotificationService;
exports.notificationService = new NotificationService();
//# sourceMappingURL=notification.js.map