import { Server as HTTPServer } from 'http';
interface AgentLocation {
    agentId: string;
    latitude: number;
    longitude: number;
    timestamp: Date;
    status: 'available' | 'busy' | 'offline';
}
interface FlightUpdate {
    flightNumber: string;
    status: string;
    gate?: string;
    terminal?: string;
    delay?: number;
    estimatedTime?: Date;
    actualTime?: Date;
}
export declare class WebSocketService {
    private io;
    private connectedClients;
    private agentLocations;
    constructor(server: HTTPServer);
    /**
     * Setup authentication middleware
     */
    private setupMiddleware;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Handle agent location updates
     */
    private handleAgentLocationUpdate;
    /**
     * Handle booking status updates
     */
    private handleBookingUpdate;
    /**
     * Handle agent status updates
     */
    private handleAgentStatusUpdate;
    /**
     * Broadcast agent location update
     */
    private broadcastAgentLocationUpdate;
    /**
     * Send initial data to connected client
     */
    private sendInitialData;
    /**
     * Broadcast flight update to subscribers
     */
    broadcastFlightUpdate(flightUpdate: FlightUpdate): void;
    /**
     * Broadcast system notification
     */
    broadcastSystemNotification(tenantId: string, notification: {
        type: 'info' | 'warning' | 'error' | 'success';
        title: string;
        message: string;
        data?: any;
    }): void;
    /**
     * Get connected clients count
     */
    getConnectedClientsCount(): number;
    /**
     * Get agent locations for a tenant
     */
    getAgentLocations(tenantId?: string): AgentLocation[];
}
export declare let websocketService: WebSocketService;
export {};
//# sourceMappingURL=websocketService.d.ts.map