"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = exports.WebSocketService = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("./logger");
const database_1 = require("./database");
class WebSocketService {
    constructor(server) {
        this.connectedClients = new Map();
        this.agentLocations = new Map();
        this.io = new socket_io_1.Server(server, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });
        this.setupMiddleware();
        this.setupEventHandlers();
        logger_1.logger.info('WebSocket service initialized');
    }
    /**
     * Setup authentication middleware
     */
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
                if (!token) {
                    return next(new Error('Authentication token required'));
                }
                const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback_secret');
                // Get user details from database
                const userResult = await (0, database_1.query)('SELECT id, tenant_id, role, status FROM users WHERE id = $1 AND status = $2', [decoded.userId, 'active']);
                if (userResult.rows.length === 0) {
                    return next(new Error('Invalid token or user not found'));
                }
                const user = userResult.rows[0];
                socket.userId = user.id;
                socket.tenantId = user.tenant_id;
                socket.role = user.role;
                next();
            }
            catch (error) {
                logger_1.logger.error('WebSocket authentication error:', error);
                next(new Error('Authentication failed'));
            }
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info(`Client connected: ${socket.userId} (${socket.role})`);
            // Store connected client
            this.connectedClients.set(socket.id, socket);
            // Join tenant-specific room
            if (socket.tenantId) {
                socket.join(`tenant:${socket.tenantId}`);
            }
            // Join role-specific room
            if (socket.role) {
                socket.join(`role:${socket.role}`);
            }
            // Handle agent location updates
            socket.on('agent:location', (data) => {
                this.handleAgentLocationUpdate(socket, data);
            });
            // Handle booking status updates
            socket.on('booking:update', (data) => {
                this.handleBookingUpdate(socket, data);
            });
            // Handle agent status updates
            socket.on('agent:status', (data) => {
                this.handleAgentStatusUpdate(socket, data);
            });
            // Handle flight tracking subscription
            socket.on('flight:subscribe', (data) => {
                socket.join(`flight:${data.flightNumber}`);
                logger_1.logger.info(`Client subscribed to flight updates: ${data.flightNumber}`);
            });
            // Handle flight tracking unsubscription
            socket.on('flight:unsubscribe', (data) => {
                socket.leave(`flight:${data.flightNumber}`);
                logger_1.logger.info(`Client unsubscribed from flight updates: ${data.flightNumber}`);
            });
            // Handle disconnect
            socket.on('disconnect', () => {
                logger_1.logger.info(`Client disconnected: ${socket.userId}`);
                this.connectedClients.delete(socket.id);
                // Remove agent location if it was an agent
                if (socket.role === 'field_agent' && socket.userId) {
                    this.agentLocations.delete(socket.userId);
                    this.broadcastAgentLocationUpdate(socket.tenantId, socket.userId, null);
                }
            });
            // Send initial data
            this.sendInitialData(socket);
        });
    }
    /**
     * Handle agent location updates
     */
    async handleAgentLocationUpdate(socket, data) {
        if (socket.role !== 'field_agent' || !socket.userId) {
            return;
        }
        const location = {
            agentId: socket.userId,
            latitude: data.latitude,
            longitude: data.longitude,
            timestamp: new Date(),
            status: data.status
        };
        // Store location
        this.agentLocations.set(socket.userId, location);
        // Update database
        try {
            await (0, database_1.query)('UPDATE agents SET current_location = $1, status = $2 WHERE user_id = $3', [JSON.stringify({ latitude: data.latitude, longitude: data.longitude }), data.status, socket.userId]);
        }
        catch (error) {
            logger_1.logger.error('Error updating agent location in database:', error);
        }
        // Broadcast to operations managers in the same tenant
        this.broadcastAgentLocationUpdate(socket.tenantId, socket.userId, location);
    }
    /**
     * Handle booking status updates
     */
    async handleBookingUpdate(socket, data) {
        try {
            // Update booking in database
            await (0, database_1.query)('UPDATE bookings SET status = $1, updated_at = NOW() WHERE id = $2 AND tenant_id = $3', [data.status, data.bookingId, socket.tenantId]);
            // Create booking activity
            await (0, database_1.query)(`INSERT INTO booking_activities (
          id, booking_id, user_id, activity_type, description, created_at
        ) VALUES (uuid_generate_v4(), $1, $2, $3, $4, NOW())`, [data.bookingId, socket.userId, 'status_update', data.message || `Status updated to ${data.status}`]);
            // Broadcast update to all relevant users in tenant
            this.io.to(`tenant:${socket.tenantId}`).emit('booking:updated', {
                bookingId: data.bookingId,
                status: data.status,
                agentId: data.agentId,
                location: data.location,
                timestamp: data.timestamp,
                updatedBy: socket.userId
            });
            logger_1.logger.info(`Booking ${data.bookingId} status updated to ${data.status}`);
        }
        catch (error) {
            logger_1.logger.error('Error handling booking update:', error);
        }
    }
    /**
     * Handle agent status updates
     */
    async handleAgentStatusUpdate(socket, data) {
        if (socket.role !== 'field_agent' || !socket.userId) {
            return;
        }
        try {
            // Update agent status in database
            await (0, database_1.query)('UPDATE agents SET status = $1 WHERE user_id = $2', [data.status, socket.userId]);
            // Update stored location
            const location = this.agentLocations.get(socket.userId);
            if (location) {
                location.status = data.status;
                location.timestamp = new Date();
            }
            // Broadcast to operations managers
            this.io.to(`tenant:${socket.tenantId}`).emit('agent:status_updated', {
                agentId: socket.userId,
                status: data.status,
                timestamp: new Date()
            });
            logger_1.logger.info(`Agent ${socket.userId} status updated to ${data.status}`);
        }
        catch (error) {
            logger_1.logger.error('Error updating agent status:', error);
        }
    }
    /**
     * Broadcast agent location update
     */
    broadcastAgentLocationUpdate(tenantId, agentId, location) {
        this.io.to(`tenant:${tenantId}`).emit('agent:location_updated', {
            agentId,
            location,
            timestamp: new Date()
        });
    }
    /**
     * Send initial data to connected client
     */
    async sendInitialData(socket) {
        try {
            if (socket.role === 'operations_manager' || socket.role === 'company_admin') {
                // Send current agent locations
                const agentLocations = Array.from(this.agentLocations.values())
                    .filter(location => {
                    // Filter by tenant (you'd need to store tenant info with locations)
                    return true; // Simplified for now
                });
                socket.emit('initial:agent_locations', agentLocations);
                // Send active bookings
                const activeBookingsResult = await (0, database_1.query)(`SELECT id, booking_reference, status, assigned_agent_id, customer_id, service_id
           FROM bookings 
           WHERE tenant_id = $1 AND status IN ('confirmed', 'in_progress')
           ORDER BY created_at DESC
           LIMIT 50`, [socket.tenantId]);
                socket.emit('initial:active_bookings', activeBookingsResult.rows);
            }
        }
        catch (error) {
            logger_1.logger.error('Error sending initial data:', error);
        }
    }
    /**
     * Broadcast flight update to subscribers
     */
    broadcastFlightUpdate(flightUpdate) {
        this.io.to(`flight:${flightUpdate.flightNumber}`).emit('flight:updated', flightUpdate);
        logger_1.logger.info(`Flight update broadcasted for ${flightUpdate.flightNumber}`);
    }
    /**
     * Broadcast system notification
     */
    broadcastSystemNotification(tenantId, notification) {
        this.io.to(`tenant:${tenantId}`).emit('system:notification', {
            ...notification,
            timestamp: new Date()
        });
    }
    /**
     * Get connected clients count
     */
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    /**
     * Get agent locations for a tenant
     */
    getAgentLocations(tenantId) {
        // In a real implementation, you'd filter by tenant
        return Array.from(this.agentLocations.values());
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=websocketService.js.map