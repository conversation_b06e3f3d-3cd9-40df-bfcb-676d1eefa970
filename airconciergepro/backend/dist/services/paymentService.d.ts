export type PaymentProvider = 'stripe' | 'razorpay';
export interface PaymentIntent {
    id: string;
    amount: number;
    currency: string;
    status: string;
    client_secret?: string;
    provider: PaymentProvider;
    metadata?: Record<string, any>;
}
export interface CreatePaymentIntentRequest {
    amount: number;
    currency: string;
    provider: PaymentProvider;
    metadata?: Record<string, any>;
    customer_info?: {
        name?: string;
        email?: string;
        phone?: string;
    };
}
export declare class PaymentService {
    private stripe;
    private razorpay;
    constructor();
    /**
     * Create payment intent based on provider
     */
    createPaymentIntent(request: CreatePaymentIntentRequest): Promise<PaymentIntent>;
    /**
     * Create Stripe Payment Intent
     */
    private createStripePaymentIntent;
    /**
     * Create Razorpay Order
     */
    private createRazorpayOrder;
    /**
     * Verify payment based on provider
     */
    verifyPayment(paymentId: string, provider: PaymentProvider, additionalData?: any): Promise<boolean>;
    /**
     * Verify Stripe Payment
     */
    private verifyStripePayment;
    /**
     * Verify Razorpay Payment
     */
    private verifyRazorpayPayment;
    /**
     * Get payment details
     */
    getPaymentDetails(paymentId: string, provider: PaymentProvider): Promise<any>;
    /**
     * Process refund
     */
    processRefund(paymentId: string, provider: PaymentProvider, amount?: number): Promise<any>;
    /**
     * Save payment record to database
     */
    savePaymentRecord(data: {
        tenant_id: string;
        booking_id: string;
        payment_intent_id: string;
        provider: PaymentProvider;
        amount: number;
        currency: string;
        status: string;
        metadata?: Record<string, any>;
    }): Promise<string>;
    /**
     * Update payment status
     */
    updatePaymentStatus(paymentIntentId: string, status: string, metadata?: Record<string, any>): Promise<void>;
}
export declare const paymentService: PaymentService;
//# sourceMappingURL=paymentService.d.ts.map