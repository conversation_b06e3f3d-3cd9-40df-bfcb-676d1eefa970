interface PricingContext {
    serviceId: string;
    tenantId: string;
    passengerCount: number;
    bookingDate: Date;
    serviceDate: Date;
    customerGroupId?: string;
    isWhiteLabel?: boolean;
    whitelabelPartnerId?: string;
}
export declare class DynamicPricingService {
    /**
     * Calculate dynamic price for a service based on various factors
     */
    calculatePrice(context: PricingContext): Promise<{
        basePrice: number;
        adjustments: Array<{
            ruleName: string;
            type: string;
            amount: number;
            percentage?: number;
        }>;
        finalPrice: number;
        currency: string;
    }>;
    /**
     * Apply a specific pricing rule
     */
    private applyPricingRule;
    /**
     * Apply time-based pricing (peak hours, weekends, etc.)
     */
    private applyTimeBased;
    /**
     * Apply demand-based pricing
     */
    private applyDemandBased;
    /**
     * Apply group discount
     */
    private applyGroupDiscount;
    /**
     * Apply corporate rate
     */
    private applyCorporateRate;
    /**
     * Apply surge pricing
     */
    private applySurgePricing;
    /**
     * Get customer group discount percentage
     */
    private getCustomerGroupDiscount;
    /**
     * Get price quote without creating booking
     */
    getQuote(context: PricingContext): Promise<{
        basePrice: number;
        adjustments: Array<{
            ruleName: string;
            type: string;
            amount: number;
            percentage?: number;
        }>;
        finalPrice: number;
        currency: string;
        validUntil: Date;
    }>;
    /**
     * Validate if a quote is still valid
     */
    validateQuote(context: PricingContext, quotedPrice: number, quoteTime: Date): Promise<{
        valid: boolean;
        newPrice?: number;
        reason?: string;
    }>;
}
export declare const dynamicPricingService: DynamicPricingService;
export {};
//# sourceMappingURL=dynamicPricing.d.ts.map