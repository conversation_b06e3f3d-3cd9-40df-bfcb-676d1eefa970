"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dynamicPricingService = exports.DynamicPricingService = void 0;
const database_1 = require("./database");
const logger_1 = require("./logger");
class DynamicPricingService {
    /**
     * Calculate dynamic price for a service based on various factors
     */
    async calculatePrice(context) {
        try {
            // Get base service price
            const serviceResult = await (0, database_1.query)('SELECT base_price, currency FROM services WHERE id = $1 AND tenant_id = $2', [context.serviceId, context.tenantId]);
            if (serviceResult.rows.length === 0) {
                throw new Error('Service not found');
            }
            const service = serviceResult.rows[0];
            let basePrice = service.base_price * context.passengerCount;
            const currency = service.currency;
            // Get applicable pricing rules
            const rulesResult = await (0, database_1.query)(`SELECT * FROM pricing_rules 
         WHERE tenant_id = $1 
           AND (service_id = $2 OR service_id IS NULL)
           AND status = 'active'
           AND (valid_from IS NULL OR valid_from <= $3)
           AND (valid_to IS NULL OR valid_to >= $3)
         ORDER BY priority ASC`, [context.tenantId, context.serviceId, context.serviceDate]);
            const rules = rulesResult.rows;
            const adjustments = [];
            let finalPrice = basePrice;
            // Apply pricing rules in priority order
            for (const rule of rules) {
                const adjustment = await this.applyPricingRule(rule, context, finalPrice);
                if (adjustment) {
                    adjustments.push({
                        ruleName: rule.rule_name,
                        type: rule.rule_type,
                        amount: adjustment.amount,
                        percentage: adjustment.percentage
                    });
                    finalPrice += adjustment.amount;
                }
            }
            // Apply customer group discounts
            if (context.customerGroupId) {
                const groupDiscount = await this.getCustomerGroupDiscount(context.customerGroupId);
                if (groupDiscount > 0) {
                    const discountAmount = -(finalPrice * groupDiscount / 100);
                    adjustments.push({
                        ruleName: 'Customer Group Discount',
                        type: 'group_discount',
                        amount: discountAmount,
                        percentage: groupDiscount
                    });
                    finalPrice += discountAmount;
                }
            }
            // Ensure price doesn't go below minimum threshold (10% of base price)
            const minimumPrice = basePrice * 0.1;
            if (finalPrice < minimumPrice) {
                finalPrice = minimumPrice;
            }
            return {
                basePrice,
                adjustments,
                finalPrice: Math.round(finalPrice * 100) / 100, // Round to 2 decimal places
                currency
            };
        }
        catch (error) {
            logger_1.logger.error('Dynamic pricing calculation error:', error);
            throw error;
        }
    }
    /**
     * Apply a specific pricing rule
     */
    async applyPricingRule(rule, context, currentPrice) {
        try {
            switch (rule.rule_type) {
                case 'time_based':
                    return this.applyTimeBased(rule, context, currentPrice);
                case 'demand_based':
                    return await this.applyDemandBased(rule, context, currentPrice);
                case 'group_discount':
                    return this.applyGroupDiscount(rule, context, currentPrice);
                case 'corporate_rate':
                    return this.applyCorporateRate(rule, context, currentPrice);
                case 'surge_pricing':
                    return await this.applySurgePricing(rule, context, currentPrice);
                default:
                    logger_1.logger.warn(`Unknown pricing rule type: ${rule.rule_type}`);
                    return null;
            }
        }
        catch (error) {
            logger_1.logger.error(`Error applying pricing rule ${rule.rule_name}:`, error);
            return null;
        }
    }
    /**
     * Apply time-based pricing (peak hours, weekends, etc.)
     */
    applyTimeBased(rule, context, currentPrice) {
        const conditions = rule.conditions;
        const serviceTime = context.serviceDate;
        // Check if current time matches the rule conditions
        const timeString = serviceTime.toTimeString().slice(0, 5); // HH:MM format
        // Get weekday in short format and convert to lowercase
        const dayOfWeek = serviceTime.toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase();
        // Check day conditions
        if (conditions.days && !conditions.days.includes(dayOfWeek)) {
            return null;
        }
        // Check time conditions
        if (conditions.hours) {
            let timeMatches = false;
            for (const timeRange of conditions.hours) {
                const [startTime, endTime] = timeRange.split('-');
                if (timeString >= startTime && timeString <= endTime) {
                    timeMatches = true;
                    break;
                }
            }
            if (!timeMatches) {
                return null;
            }
        }
        // Apply adjustment
        if (rule.adjustment_type === 'percentage') {
            const amount = currentPrice * (rule.adjustment_value / 100);
            return { amount, percentage: rule.adjustment_value };
        }
        else {
            return { amount: rule.adjustment_value };
        }
    }
    /**
     * Apply demand-based pricing
     */
    async applyDemandBased(rule, context, currentPrice) {
        const conditions = rule.conditions;
        const bookingThreshold = conditions.booking_threshold || 10;
        const timeWindowHours = conditions.time_window_hours || 24;
        // Check demand in the specified time window
        const windowStart = new Date(context.serviceDate.getTime() - (timeWindowHours * 60 * 60 * 1000 / 2));
        const windowEnd = new Date(context.serviceDate.getTime() + (timeWindowHours * 60 * 60 * 1000 / 2));
        const demandResult = await (0, database_1.query)(`SELECT COUNT(*) as booking_count FROM bookings 
       WHERE tenant_id = $1 
         AND service_id = $2 
         AND flight_date BETWEEN $3 AND $4
         AND status NOT IN ('cancelled', 'no_show')`, [context.tenantId, context.serviceId, windowStart, windowEnd]);
        const currentDemand = parseInt(demandResult.rows[0].booking_count);
        if (currentDemand >= bookingThreshold) {
            if (rule.adjustment_type === 'percentage') {
                const amount = currentPrice * (rule.adjustment_value / 100);
                return { amount, percentage: rule.adjustment_value };
            }
            else {
                return { amount: rule.adjustment_value };
            }
        }
        return null;
    }
    /**
     * Apply group discount
     */
    applyGroupDiscount(rule, context, currentPrice) {
        const conditions = rule.conditions;
        const minPassengers = conditions.min_passengers || 1;
        const maxPassengers = conditions.max_passengers || 999;
        if (context.passengerCount >= minPassengers && context.passengerCount <= maxPassengers) {
            if (rule.adjustment_type === 'percentage') {
                const amount = currentPrice * (rule.adjustment_value / 100);
                return { amount, percentage: rule.adjustment_value };
            }
            else {
                return { amount: rule.adjustment_value };
            }
        }
        return null;
    }
    /**
     * Apply corporate rate
     */
    applyCorporateRate(rule, context, currentPrice) {
        // This would check if the customer belongs to a corporate account
        // For now, we'll implement a simple version
        if (rule.adjustment_type === 'percentage') {
            const amount = currentPrice * (rule.adjustment_value / 100);
            return { amount, percentage: rule.adjustment_value };
        }
        else {
            return { amount: rule.adjustment_value };
        }
    }
    /**
     * Apply surge pricing
     */
    async applySurgePricing(rule, context, currentPrice) {
        // Similar to demand-based but with different thresholds
        return await this.applyDemandBased(rule, context, currentPrice);
    }
    /**
     * Get customer group discount percentage
     */
    async getCustomerGroupDiscount(userGroupId) {
        try {
            const result = await (0, database_1.query)('SELECT discount_percentage FROM user_groups WHERE id = $1', [userGroupId]);
            if (result.rows.length > 0) {
                return result.rows[0].discount_percentage || 0;
            }
            return 0;
        }
        catch (error) {
            logger_1.logger.error('Error getting customer group discount:', error);
            return 0;
        }
    }
    /**
     * Get price quote without creating booking
     */
    async getQuote(context) {
        const pricing = await this.calculatePrice(context);
        // Quote valid for 30 minutes
        const validUntil = new Date(Date.now() + 30 * 60 * 1000);
        return {
            ...pricing,
            validUntil
        };
    }
    /**
     * Validate if a quote is still valid
     */
    async validateQuote(context, quotedPrice, quoteTime) {
        const currentPricing = await this.calculatePrice(context);
        const priceDifference = Math.abs(currentPricing.finalPrice - quotedPrice);
        const maxVariation = quotedPrice * 0.05; // 5% tolerance
        // Check if quote is expired (30 minutes)
        const quoteAge = Date.now() - quoteTime.getTime();
        if (quoteAge > 30 * 60 * 1000) {
            return {
                valid: false,
                newPrice: currentPricing.finalPrice,
                reason: 'Quote expired'
            };
        }
        // Check if price has changed significantly
        if (priceDifference > maxVariation) {
            return {
                valid: false,
                newPrice: currentPricing.finalPrice,
                reason: 'Price changed due to market conditions'
            };
        }
        return { valid: true };
    }
}
exports.DynamicPricingService = DynamicPricingService;
exports.dynamicPricingService = new DynamicPricingService();
//# sourceMappingURL=dynamicPricing.js.map