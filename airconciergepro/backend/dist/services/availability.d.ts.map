{"version": 3, "file": "availability.d.ts", "sourceRoot": "", "sources": ["../../src/services/availability.ts"], "names": [], "mappings": "AAGA,UAAU,mBAAmB;IAC3B,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,IAAI,CAAC;IACX,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,UAAU,QAAQ;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,UAAU,oBAAoB;IAC5B,IAAI,EAAE,IAAI,CAAC;IACX,SAAS,EAAE,OAAO,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,SAAS,EAAE,QAAQ,EAAE,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;CACzB;AASD,qBAAa,mBAAmB;IAC9B;;OAEG;IACG,iBAAiB,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IA6EpF;;OAEG;YACW,iBAAiB;IAmE/B;;OAEG;IACG,YAAY,CAChB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,IAAI,EACV,QAAQ,EAAE,MAAM,EAChB,cAAc,EAAE,MAAM,EACtB,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAC;QAAC,SAAS,CAAC,EAAE,IAAI,CAAA;KAAE,CAAC;IAmCnE;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC;IAmB5C;;OAEG;IACG,uBAAuB,CAC3B,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,GACX,OAAO,CAAC;QACT,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,KAAK,CAAC;YACV,IAAI,EAAE,IAAI,CAAC;YACX,SAAS,EAAE,OAAO,CAAC;YACnB,QAAQ,EAAE,MAAM,CAAC;YACjB,QAAQ,EAAE,MAAM,CAAC;YACjB,sBAAsB,EAAE,MAAM,CAAC;SAChC,CAAC,CAAC;KACJ,CAAC;IA+EF;;OAEG;IACG,kBAAkB,CACtB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,IAAI,EACf,OAAO,EAAE,IAAI,GACZ,OAAO,CAAC;QACT,aAAa,EAAE,MAAM,CAAC;QACtB,aAAa,EAAE,MAAM,CAAC;QACtB,qBAAqB,EAAE,MAAM,CAAC;QAC9B,kBAAkB,EAAE;YAAE,IAAI,EAAE,IAAI,CAAC;YAAC,UAAU,EAAE,MAAM,CAAA;SAAE,CAAC;QACvD,iBAAiB,EAAE;YAAE,IAAI,EAAE,IAAI,CAAC;YAAC,UAAU,EAAE,MAAM,CAAA;SAAE,CAAC;QACtD,oBAAoB,EAAE,MAAM,CAAC;QAC7B,UAAU,EAAE,KAAK,CAAC;YAAE,IAAI,EAAE,IAAI,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAC;YAAC,WAAW,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;KAC5F,CAAC;IAoFF;;OAEG;IACG,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC;QACvD,KAAK,EAAE;YACL,aAAa,EAAE,MAAM,CAAC;YACtB,eAAe,EAAE,MAAM,CAAC;YACxB,iBAAiB,EAAE,MAAM,CAAC;YAC1B,qBAAqB,EAAE,MAAM,CAAC;SAC/B,CAAC;QACF,QAAQ,EAAE;YACR,aAAa,EAAE,MAAM,CAAC;YACtB,eAAe,EAAE,MAAM,CAAC;YACxB,iBAAiB,EAAE,MAAM,CAAC;YAC1B,qBAAqB,EAAE,MAAM,CAAC;SAC/B,CAAC;QACF,aAAa,EAAE,KAAK,CAAC;YACnB,IAAI,EAAE,MAAM,CAAC;YACb,SAAS,EAAE,OAAO,CAAC;YACnB,QAAQ,EAAE,MAAM,CAAC;YACjB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC,CAAC;KACJ,CAAC;IAiHF;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CAUlC;AAED,eAAO,MAAM,mBAAmB,qBAA4B,CAAC"}