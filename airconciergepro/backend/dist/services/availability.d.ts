interface AvailabilityRequest {
    tenantId: string;
    serviceId: string;
    date: Date;
    timeSlot?: string;
    passengerCount: number;
    duration?: number;
    airportCode?: string;
}
interface TimeSlot {
    startTime: string;
    endTime: string;
    available: boolean;
    capacity: number;
    bookings: number;
    price?: number;
}
interface AvailabilityResponse {
    date: Date;
    available: boolean;
    totalCapacity: number;
    availableCapacity: number;
    timeSlots: TimeSlot[];
    restrictions?: string[];
}
export declare class AvailabilityService {
    /**
     * Check availability for a specific service and date
     */
    checkAvailability(request: AvailabilityRequest): Promise<AvailabilityResponse>;
    /**
     * Generate time slots for a given date
     */
    private generateTimeSlots;
    /**
     * Hold a time slot temporarily
     */
    holdTimeSlot(tenantId: string, serviceId: string, date: Date, timeSlot: string, passengerCount: number, customerId?: string): Promise<{
        success: boolean;
        holdId?: string;
        expiresAt?: Date;
    }>;
    /**
     * Release expired holds
     */
    releaseExpiredHolds(): Promise<number>;
    /**
     * Get availability calendar for a month
     */
    getAvailabilityCalendar(tenantId: string, serviceId: string, month: number, year: number): Promise<{
        month: number;
        year: number;
        days: Array<{
            date: Date;
            available: boolean;
            capacity: number;
            bookings: number;
            availabilityPercentage: number;
        }>;
    }>;
    /**
     * Get capacity utilization metrics
     */
    getCapacityMetrics(tenantId: string, serviceId: string, startDate: Date, endDate: Date): Promise<{
        totalCapacity: number;
        totalBookings: number;
        utilizationPercentage: number;
        peakUtilizationDay: {
            date: Date;
            percentage: number;
        };
        lowUtilizationDay: {
            date: Date;
            percentage: number;
        };
        averageDailyBookings: number;
        trendsData: Array<{
            date: Date;
            bookings: number;
            capacity: number;
            utilization: number;
        }>;
    }>;
    /**
     * Get real-time availability for operations dashboard
     */
    getRealTimeAvailability(tenantId: string): Promise<{
        today: {
            totalCapacity: number;
            currentBookings: number;
            availableCapacity: number;
            utilizationPercentage: number;
        };
        tomorrow: {
            totalCapacity: number;
            currentBookings: number;
            availableCapacity: number;
            utilizationPercentage: number;
        };
        upcomingSlots: Array<{
            time: string;
            available: boolean;
            capacity: number;
            bookings: number;
        }>;
    }>;
    /**
     * Initialize availability monitoring
     */
    initialize(): Promise<void>;
}
export declare const availabilityService: AvailabilityService;
export {};
//# sourceMappingURL=availability.d.ts.map