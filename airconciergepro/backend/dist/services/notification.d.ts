interface NotificationData {
    tenantId: string;
    bookingId?: string;
    userId?: string;
    customerId?: string;
    type: 'email' | 'sms' | 'push' | 'webhook';
    channel: string;
    recipient: string;
    subject?: string;
    message: string;
    metadata?: any;
}
export declare class NotificationService {
    private emailTransporter;
    private smsClient;
    constructor();
    /**
     * Initialize the notification service
     */
    initialize(): Promise<void>;
    private initializeEmailTransporter;
    private initializeSMSClient;
    /**
     * Send notification
     */
    sendNotification(data: NotificationData): Promise<{
        success: boolean;
        messageId?: string;
        error?: string;
    }>;
    /**
     * Send email notification
     */
    private sendEmail;
    /**
     * Send SMS notification
     */
    private sendSMS;
    /**
     * Send push notification
     */
    private sendPushNotification;
    /**
     * Send webhook notification
     */
    private sendWebhook;
    /**
     * Store notification in database
     */
    private storeNotification;
    /**
     * Update notification status
     */
    private updateNotificationStatus;
    /**
     * Generate email template
     */
    private generateEmailTemplate;
    /**
     * Send booking confirmation notification
     */
    sendBookingConfirmation(tenantId: string, bookingId: string, customerEmail: string, bookingDetails: any): Promise<void>;
    /**
     * Send booking status update
     */
    sendBookingStatusUpdate(tenantId: string, bookingId: string, customerEmail: string, customerPhone: string, status: string, message: string): Promise<void>;
    /**
     * Send agent assignment notification
     */
    sendAgentAssignment(tenantId: string, bookingId: string, agentEmail: string, bookingDetails: any): Promise<void>;
    /**
     * Send bulk notifications
     */
    sendBulkNotifications(notifications: NotificationData[]): Promise<{
        successful: number;
        failed: number;
        results: Array<{
            success: boolean;
            error?: string;
        }>;
    }>;
}
export declare const notificationService: NotificationService;
export {};
//# sourceMappingURL=notification.d.ts.map