"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentService = exports.PaymentService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const razorpay_1 = __importDefault(require("razorpay"));
const logger_1 = require("./logger");
const database_1 = require("./database");
class PaymentService {
    constructor() {
        // Initialize Stripe
        const stripeKey = process.env.STRIPE_SECRET_KEY;
        if (stripeKey) {
            this.stripe = new stripe_1.default(stripeKey, {
                apiVersion: '2023-08-16',
            });
        }
        else {
            logger_1.logger.warn('Stripe secret key not configured. Stripe payments will not work.');
        }
        // Initialize Razorpay
        const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
        const razorpayKeySecret = process.env.RAZORPAY_KEY_SECRET;
        if (razorpayKeyId && razorpayKeySecret) {
            this.razorpay = new razorpay_1.default({
                key_id: razorpayKeyId,
                key_secret: razorpayKeySecret,
            });
        }
        else {
            logger_1.logger.warn('Razorpay credentials not configured. Razorpay payments will not work.');
        }
    }
    /**
     * Create payment intent based on provider
     */
    async createPaymentIntent(request) {
        try {
            switch (request.provider) {
                case 'stripe':
                    if (!this.stripe) {
                        throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
                    }
                    return await this.createStripePaymentIntent(request);
                case 'razorpay':
                    if (!this.razorpay) {
                        throw new Error('Razorpay is not configured. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET environment variables.');
                    }
                    return await this.createRazorpayOrder(request);
                default:
                    throw new Error(`Unsupported payment provider: ${request.provider}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Payment intent creation failed:', error);
            throw error;
        }
    }
    /**
     * Create Stripe Payment Intent
     */
    async createStripePaymentIntent(request) {
        const paymentIntent = await this.stripe.paymentIntents.create({
            amount: Math.round(request.amount * 100), // Convert to cents
            currency: request.currency.toLowerCase(),
            metadata: request.metadata || {},
            automatic_payment_methods: {
                enabled: true,
            },
        });
        return {
            id: paymentIntent.id,
            amount: request.amount,
            currency: request.currency,
            status: paymentIntent.status,
            client_secret: paymentIntent.client_secret || undefined,
            provider: 'stripe',
            metadata: paymentIntent.metadata,
        };
    }
    /**
     * Create Razorpay Order
     */
    async createRazorpayOrder(request) {
        const order = await this.razorpay.orders.create({
            amount: Math.round(request.amount * 100), // Amount in paise (smallest currency unit)
            currency: request.currency.toUpperCase(),
            notes: request.metadata || {},
        });
        return {
            id: order.id,
            amount: request.amount,
            currency: request.currency,
            status: order.status,
            provider: 'razorpay',
            metadata: order.notes,
        };
    }
    /**
     * Verify payment based on provider
     */
    async verifyPayment(paymentId, provider, additionalData) {
        try {
            switch (provider) {
                case 'stripe':
                    return await this.verifyStripePayment(paymentId);
                case 'razorpay':
                    return await this.verifyRazorpayPayment(paymentId, additionalData);
                default:
                    throw new Error(`Unsupported payment provider: ${provider}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Payment verification failed:', error);
            return false;
        }
    }
    /**
     * Verify Stripe Payment
     */
    async verifyStripePayment(paymentIntentId) {
        const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
        return paymentIntent.status === 'succeeded';
    }
    /**
     * Verify Razorpay Payment
     */
    async verifyRazorpayPayment(paymentId, verificationData) {
        const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = verificationData;
        const crypto = require('crypto');
        const expectedSignature = crypto
            .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
            .update(`${razorpay_order_id}|${razorpay_payment_id}`)
            .digest('hex');
        return expectedSignature === razorpay_signature;
    }
    /**
     * Get payment details
     */
    async getPaymentDetails(paymentId, provider) {
        try {
            switch (provider) {
                case 'stripe':
                    return await this.stripe.paymentIntents.retrieve(paymentId);
                case 'razorpay':
                    return await this.razorpay.orders.fetch(paymentId);
                default:
                    throw new Error(`Unsupported payment provider: ${provider}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to get payment details:', error);
            throw error;
        }
    }
    /**
     * Process refund
     */
    async processRefund(paymentId, provider, amount) {
        try {
            switch (provider) {
                case 'stripe':
                    return await this.stripe.refunds.create({
                        payment_intent: paymentId,
                        amount: amount ? Math.round(amount * 100) : undefined,
                    });
                case 'razorpay':
                    // For Razorpay, we need the payment ID, not order ID
                    return await this.razorpay.payments.refund(paymentId, {
                        amount: amount ? Math.round(amount * 100) : undefined,
                    });
                default:
                    throw new Error(`Unsupported payment provider: ${provider}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Refund processing failed:', error);
            throw error;
        }
    }
    /**
     * Save payment record to database
     */
    async savePaymentRecord(data) {
        const result = await (0, database_1.query)(`INSERT INTO payments (
        tenant_id, booking_id, payment_intent_id, payment_provider, 
        amount, currency, status, metadata, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW()) 
      RETURNING id`, [
            data.tenant_id,
            data.booking_id,
            data.payment_intent_id,
            data.provider,
            data.amount,
            data.currency,
            data.status,
            JSON.stringify(data.metadata || {}),
        ]);
        return result.rows[0].id;
    }
    /**
     * Update payment status
     */
    async updatePaymentStatus(paymentIntentId, status, metadata) {
        await (0, database_1.query)(`UPDATE payments 
       SET status = $1, metadata = $2, updated_at = NOW() 
       WHERE payment_intent_id = $3`, [status, JSON.stringify(metadata || {}), paymentIntentId]);
    }
}
exports.PaymentService = PaymentService;
exports.paymentService = new PaymentService();
//# sourceMappingURL=paymentService.js.map