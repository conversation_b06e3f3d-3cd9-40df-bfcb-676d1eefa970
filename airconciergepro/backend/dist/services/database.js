"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheDel = exports.cacheSet = exports.cacheGet = exports.query = exports.redisClient = exports.pool = void 0;
const pg_1 = require("pg");
const redis_1 = require("redis");
const logger_1 = require("./logger");
// PostgreSQL connection
exports.pool = new pg_1.Pool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'airconciergepro',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});
// Redis connection
exports.redisClient = (0, redis_1.createClient)({
    socket: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
    },
    password: process.env.REDIS_PASSWORD,
});
exports.redisClient.on('error', (err) => {
    logger_1.logger.error('Redis Client Error:', err);
});
exports.redisClient.on('connect', () => {
    logger_1.logger.info('Connected to Redis');
});
// Initialize Redis connection
exports.redisClient.connect().catch((err) => {
    logger_1.logger.error('Failed to connect to Redis:', err);
});
// Database query helper
const query = async (text, params) => {
    const start = Date.now();
    try {
        const res = await exports.pool.query(text, params);
        const duration = Date.now() - start;
        logger_1.logger.info(`Executed query in ${duration}ms`);
        return res;
    }
    catch (error) {
        logger_1.logger.error('Database query error:', error);
        throw error;
    }
};
exports.query = query;
// Cache helper functions
const cacheGet = async (key) => {
    try {
        return await exports.redisClient.get(key);
    }
    catch (error) {
        logger_1.logger.error('Cache get error:', error);
        return null;
    }
};
exports.cacheGet = cacheGet;
const cacheSet = async (key, value, expireInSeconds) => {
    try {
        if (expireInSeconds) {
            await exports.redisClient.setEx(key, expireInSeconds, value);
        }
        else {
            await exports.redisClient.set(key, value);
        }
    }
    catch (error) {
        logger_1.logger.error('Cache set error:', error);
    }
};
exports.cacheSet = cacheSet;
const cacheDel = async (key) => {
    try {
        await exports.redisClient.del(key);
    }
    catch (error) {
        logger_1.logger.error('Cache delete error:', error);
    }
};
exports.cacheDel = cacheDel;
//# sourceMappingURL=database.js.map