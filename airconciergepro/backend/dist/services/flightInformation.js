"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.flightInformationService = exports.FlightInformationService = void 0;
const database_1 = require("./database");
const logger_1 = require("./logger");
const aviationStackService_1 = require("./aviationStackService");
class FlightInformationService {
    constructor() {
        this.apiKey = process.env.FLIGHT_API_KEY || '';
        this.apiUrl = process.env.FLIGHT_API_URL || 'http://api.aviationstack.com/v1';
    }
    /**
     * Initialize the flight information service
     */
    async initialize() {
        try {
            logger_1.logger.info('Initializing Flight Information Service...');
            // Test API connection if API key is provided
            if (this.apiKey) {
                logger_1.logger.info('Flight API key configured, service ready');
            }
            else {
                logger_1.logger.warn('Flight API key not configured, using mock data');
            }
            logger_1.logger.info('Flight Information Service initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Flight Information Service:', error);
            throw error;
        }
    }
    /**
     * Get real-time flight information
     */
    async getFlightInfo(flightNumber, date) {
        try {
            // First check our local cache
            const cachedFlight = await this.getCachedFlightInfo(flightNumber, date);
            if (cachedFlight && this.isCacheValid(cachedFlight.last_updated)) {
                return this.formatFlightInfo(cachedFlight);
            }
            // If not in cache or expired, fetch from external API
            const externalData = await this.fetchFromExternalAPI(flightNumber, date);
            if (externalData) {
                await this.cacheFlightInfo(externalData);
                return externalData;
            }
            // Return cached data even if expired as fallback
            if (cachedFlight) {
                return this.formatFlightInfo(cachedFlight);
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error getting flight info:', error);
            return null;
        }
    }
    /**
     * Fetch flight info from external API using AviationStack service
     */
    async fetchFromExternalAPI(flightNumber, date) {
        try {
            // Use AviationStack service for real flight data
            const dateStr = date ? date.toISOString().split('T')[0] : undefined;
            const aviationStackFlight = await aviationStackService_1.aviationStackService.getFlightInfo(flightNumber, dateStr);
            if (aviationStackFlight) {
                // Cache the raw AviationStack data
                await aviationStackService_1.aviationStackService.cacheFlightInfo(aviationStackFlight);
                // Convert to our internal format
                return this.formatAviationStackFlight(aviationStackFlight);
            }
            // Fallback to mock data if no real data available
            logger_1.logger.warn(`No real flight data found for ${flightNumber}, using mock data`);
            return this.getMockFlightData(flightNumber, date);
        }
        catch (error) {
            logger_1.logger.error('External flight API error:', error);
            return this.getMockFlightData(flightNumber, date);
        }
    }
    /**
     * Parse external API response
     */
    parseExternalFlightData(data) {
        return {
            flightNumber: data.flight.iata || data.flight.icao,
            airline: data.airline.name,
            departureAirport: data.departure.iata,
            arrivalAirport: data.arrival.iata,
            scheduledDeparture: new Date(data.departure.scheduled),
            scheduledArrival: new Date(data.arrival.scheduled),
            estimatedDeparture: data.departure.estimated ? new Date(data.departure.estimated) : undefined,
            estimatedArrival: data.arrival.estimated ? new Date(data.arrival.estimated) : undefined,
            actualDeparture: data.departure.actual ? new Date(data.departure.actual) : undefined,
            actualArrival: data.arrival.actual ? new Date(data.arrival.actual) : undefined,
            status: data.flight_status || 'scheduled',
            gate: data.departure.gate || data.arrival.gate,
            terminal: data.departure.terminal || data.arrival.terminal,
            aircraftType: data.aircraft?.registration,
            delayReason: data.departure.delay_reason,
            lastUpdated: new Date()
        };
    }
    /**
     * Get cached flight info from database
     */
    async getCachedFlightInfo(flightNumber, date) {
        try {
            let dateFilter = '';
            const params = [flightNumber];
            if (date) {
                dateFilter = 'AND DATE(scheduled_departure) = $2';
                params.push(date.toISOString().split('T')[0]);
            }
            const result = await (0, database_1.query)(`SELECT * FROM flight_information 
         WHERE flight_number = $1 ${dateFilter}
         ORDER BY last_updated DESC 
         LIMIT 1`, params);
            return result.rows[0] || null;
        }
        catch (error) {
            logger_1.logger.error('Error getting cached flight info:', error);
            return null;
        }
    }
    /**
     * Check if cached data is still valid (15 minutes)
     */
    isCacheValid(lastUpdated) {
        const cacheAge = Date.now() - new Date(lastUpdated).getTime();
        return cacheAge < 15 * 60 * 1000; // 15 minutes
    }
    /**
     * Cache flight info in database
     */
    async cacheFlightInfo(flightInfo) {
        try {
            await (0, database_1.query)(`INSERT INTO flight_information (
          flight_number, airline, departure_airport, arrival_airport,
          scheduled_departure, scheduled_arrival, estimated_departure, 
          estimated_arrival, actual_departure, actual_arrival,
          status, gate, terminal, aircraft_type, delay_reason, last_updated
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW())
        ON CONFLICT (flight_number, scheduled_departure) 
        DO UPDATE SET
          estimated_departure = EXCLUDED.estimated_departure,
          estimated_arrival = EXCLUDED.estimated_arrival,
          actual_departure = EXCLUDED.actual_departure,
          actual_arrival = EXCLUDED.actual_arrival,
          status = EXCLUDED.status,
          gate = EXCLUDED.gate,
          terminal = EXCLUDED.terminal,
          delay_reason = EXCLUDED.delay_reason,
          last_updated = NOW()`, [
                flightInfo.flightNumber,
                flightInfo.airline,
                flightInfo.departureAirport,
                flightInfo.arrivalAirport,
                flightInfo.scheduledDeparture,
                flightInfo.scheduledArrival,
                flightInfo.estimatedDeparture || null,
                flightInfo.estimatedArrival || null,
                flightInfo.actualDeparture || null,
                flightInfo.actualArrival || null,
                flightInfo.status,
                flightInfo.gate || null,
                flightInfo.terminal || null,
                flightInfo.aircraftType || null,
                flightInfo.delayReason || null
            ]);
        }
        catch (error) {
            logger_1.logger.error('Error caching flight info:', error);
        }
    }
    /**
     * Format cached flight info to FlightInfo interface
     */
    formatFlightInfo(cached) {
        return {
            flightNumber: cached.flight_number,
            airline: cached.airline,
            departureAirport: cached.departure_airport,
            arrivalAirport: cached.arrival_airport,
            scheduledDeparture: new Date(cached.scheduled_departure),
            scheduledArrival: new Date(cached.scheduled_arrival),
            estimatedDeparture: cached.estimated_departure ? new Date(cached.estimated_departure) : undefined,
            estimatedArrival: cached.estimated_arrival ? new Date(cached.estimated_arrival) : undefined,
            actualDeparture: cached.actual_departure ? new Date(cached.actual_departure) : undefined,
            actualArrival: cached.actual_arrival ? new Date(cached.actual_arrival) : undefined,
            status: cached.status,
            gate: cached.gate,
            terminal: cached.terminal,
            aircraftType: cached.aircraft_type,
            delayReason: cached.delay_reason,
            lastUpdated: new Date(cached.updated_at)
        };
    }
    /**
     * Format AviationStack flight data to our internal format
     */
    formatAviationStackFlight(aviationFlight) {
        return {
            flightNumber: aviationFlight.flight.iata,
            airline: aviationFlight.airline.name,
            departureAirport: aviationFlight.departure.iata,
            arrivalAirport: aviationFlight.arrival.iata,
            scheduledDeparture: aviationFlight.departure.scheduled ? new Date(aviationFlight.departure.scheduled) : undefined,
            estimatedDeparture: aviationFlight.departure.estimated ? new Date(aviationFlight.departure.estimated) : undefined,
            actualDeparture: aviationFlight.departure.actual ? new Date(aviationFlight.departure.actual) : undefined,
            scheduledArrival: aviationFlight.arrival.scheduled ? new Date(aviationFlight.arrival.scheduled) : undefined,
            estimatedArrival: aviationFlight.arrival.estimated ? new Date(aviationFlight.arrival.estimated) : undefined,
            actualArrival: aviationFlight.arrival.actual ? new Date(aviationFlight.arrival.actual) : undefined,
            status: this.mapAviationStackStatus(aviationFlight.flight_status),
            gate: aviationFlight.arrival.gate || aviationFlight.departure.gate,
            terminal: aviationFlight.arrival.terminal || aviationFlight.departure.terminal,
            aircraftType: aviationFlight.aircraft?.iata,
            delayReason: aviationFlight.departure.delay > 0 || aviationFlight.arrival.delay > 0 ? 'Flight delayed' : undefined,
            lastUpdated: new Date()
        };
    }
    /**
     * Map AviationStack status to our internal status
     */
    mapAviationStackStatus(status) {
        switch (status?.toLowerCase()) {
            case 'scheduled':
                return 'scheduled';
            case 'active':
            case 'en-route':
                return 'departed';
            case 'landed':
                return 'arrived';
            case 'cancelled':
                return 'cancelled';
            case 'incident':
            case 'diverted':
                return 'delayed';
            default:
                return 'scheduled';
        }
    }
    /**
     * Get mock flight data for development/demo
     */
    getMockFlightData(flightNumber, date) {
        const mockFlights = {
            'BA175': {
                airline: 'British Airways',
                departureAirport: 'LHR',
                arrivalAirport: 'JFK',
                status: 'delayed',
                gate: 'A12',
                terminal: 'T4',
                delayReason: 'Air traffic control delay'
            },
            'AA101': {
                airline: 'American Airlines',
                departureAirport: 'JFK',
                arrivalAirport: 'LAX',
                status: 'on_time',
                gate: 'B24',
                terminal: 'T8'
            },
            'EK205': {
                airline: 'Emirates',
                departureAirport: 'DXB',
                arrivalAirport: 'JFK',
                status: 'boarding',
                gate: 'C15',
                terminal: 'T3'
            }
        };
        const mockData = mockFlights[flightNumber];
        if (!mockData) {
            return null;
        }
        const baseDate = date || new Date();
        const departureTime = new Date(baseDate);
        departureTime.setHours(10, 0, 0, 0);
        const arrivalTime = new Date(departureTime);
        arrivalTime.setHours(departureTime.getHours() + 8); // 8-hour flight
        const estimatedArrival = new Date(arrivalTime);
        if (mockData.status === 'delayed') {
            estimatedArrival.setMinutes(arrivalTime.getMinutes() + 30); // 30 min delay
        }
        return {
            flightNumber,
            airline: mockData.airline || 'Unknown Airline',
            departureAirport: mockData.departureAirport || 'XXX',
            arrivalAirport: mockData.arrivalAirport || 'XXX',
            scheduledDeparture: departureTime,
            scheduledArrival: arrivalTime,
            estimatedArrival,
            status: mockData.status || 'scheduled',
            gate: mockData.gate,
            terminal: mockData.terminal,
            delayReason: mockData.delayReason,
            lastUpdated: new Date()
        };
    }
    /**
     * Update booking with latest flight information
     */
    async updateBookingWithFlightInfo(bookingId) {
        try {
            // Get booking details
            const bookingResult = await (0, database_1.query)('SELECT flight_number, flight_date FROM bookings WHERE id = $1', [bookingId]);
            if (bookingResult.rows.length === 0) {
                return false;
            }
            const booking = bookingResult.rows[0];
            const flightInfo = await this.getFlightInfo(booking.flight_number, new Date(booking.flight_date));
            if (flightInfo) {
                await (0, database_1.query)(`UPDATE bookings 
           SET estimated_arrival = $1, 
               actual_arrival = $2,
               updated_at = NOW()
           WHERE id = $3`, [
                    flightInfo.estimatedArrival || flightInfo.scheduledArrival,
                    flightInfo.actualArrival || null,
                    bookingId
                ]);
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error('Error updating booking with flight info:', error);
            return false;
        }
    }
    /**
     * Monitor flights for updates
     */
    async monitorFlights() {
        try {
            // Get all bookings for today and tomorrow that don't have actual arrival
            const bookingsResult = await (0, database_1.query)(`SELECT DISTINCT b.flight_number, b.flight_date
         FROM bookings b
         WHERE b.flight_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '1 day'
           AND b.status IN ('confirmed', 'in_progress')
           AND b.actual_arrival IS NULL`, []);
            logger_1.logger.info(`Monitoring ${bookingsResult.rows.length} flights for updates`);
            for (const booking of bookingsResult.rows) {
                await this.getFlightInfo(booking.flight_number, new Date(booking.flight_date));
                // Small delay to avoid overwhelming external APIs
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        catch (error) {
            logger_1.logger.error('Error monitoring flights:', error);
        }
    }
    /**
     * Get flight delays for a specific airport
     */
    async getAirportDelays(airportCode) {
        try {
            const result = await (0, database_1.query)(`SELECT 
           COUNT(*) as total_flights,
           COUNT(CASE WHEN status = 'delayed' THEN 1 END) as delayed_flights,
           AVG(CASE 
             WHEN estimated_arrival IS NOT NULL AND scheduled_arrival IS NOT NULL 
             THEN EXTRACT(EPOCH FROM (estimated_arrival - scheduled_arrival))/60 
             ELSE 0 
           END) as avg_delay_minutes
         FROM flight_information
         WHERE (departure_airport = $1 OR arrival_airport = $1)
           AND DATE(scheduled_departure) = CURRENT_DATE`, [airportCode]);
            const data = result.rows[0];
            const totalFlights = parseInt(data.total_flights) || 0;
            const delayedFlights = parseInt(data.delayed_flights) || 0;
            const averageDelay = parseFloat(data.avg_delay_minutes) || 0;
            const delayPercentage = totalFlights > 0 ? (delayedFlights / totalFlights) * 100 : 0;
            return {
                totalFlights,
                delayedFlights,
                averageDelay: Math.round(averageDelay),
                delayPercentage: Math.round(delayPercentage * 100) / 100
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting airport delays:', error);
            return {
                totalFlights: 0,
                delayedFlights: 0,
                averageDelay: 0,
                delayPercentage: 0
            };
        }
    }
    /**
     * Get flight status summary for dashboard
     */
    async getFlightStatusSummary() {
        try {
            const result = await (0, database_1.query)(`SELECT 
           COUNT(CASE WHEN status = 'on_time' THEN 1 END) as on_time,
           COUNT(CASE WHEN status = 'delayed' THEN 1 END) as delayed,
           COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
           COUNT(CASE WHEN status = 'boarding' THEN 1 END) as boarding
         FROM flight_information
         WHERE DATE(scheduled_departure) = CURRENT_DATE`, []);
            const data = result.rows[0];
            return {
                onTime: parseInt(data.on_time) || 0,
                delayed: parseInt(data.delayed) || 0,
                cancelled: parseInt(data.cancelled) || 0,
                boarding: parseInt(data.boarding) || 0
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting flight status summary:', error);
            return { onTime: 0, delayed: 0, cancelled: 0, boarding: 0 };
        }
    }
}
exports.FlightInformationService = FlightInformationService;
exports.flightInformationService = new FlightInformationService();
//# sourceMappingURL=flightInformation.js.map