{"version": 3, "file": "aviationStackService.js", "sourceRoot": "", "sources": ["../../src/services/aviationStackService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,qCAAkC;AAClC,yCAAmC;AA8EnC,MAAa,oBAAoB;IAK/B;QAHiB,YAAO,GAAG,iCAAiC,CAAC;QAI3D,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,kCAAkC,CAAC;QAEtF,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE;gBACN,UAAU,EAAE,IAAI,CAAC,MAAM;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,GAAG,EAAE,SAAiB,CAAC;QACxD,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,mDAAmD,KAAK,aAAa,MAAM,GAAG,CAAC,CAAC;YAE5F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;gBACzD,MAAM,EAAE;oBACN,KAAK;oBACL,MAAM;iBACP;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAA2B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAE5D,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,wCAAwC;YACxC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;YAE/D,8CAA8C;YAC9C,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC9B,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBAC3D,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,OAA6B;QACvD,IAAI,CAAC;YACH,MAAM,IAAA,gBAAK,EACT;;;;;;;;;;;;;;;;6BAgBqB,EACrB;gBACE,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,YAAY;gBACpB,OAAO,CAAC,cAAc,IAAI,SAAS;gBACnC,OAAO,CAAC,YAAY;gBACpB,OAAO,CAAC,QAAQ;gBAChB,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI;gBACpC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI;gBACrC,OAAO,CAAC,YAAY;gBACpB,OAAO,CAAC,YAAY;aACrB,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,YAAoB,EAAE,IAAa;QACrD,IAAI,CAAC;YACH,MAAM,MAAM,GAAQ;gBAClB,WAAW,EAAE,YAAY;aAC1B,CAAC;YAEF,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;YAC5B,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAEtE,MAAM,OAAO,GAA0B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAE1D,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,gCAAgC;YAChC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,WAAmB,EACnB,OAAgC,SAAS,EACzC,QAAgB,EAAE;QAElB,IAAI,CAAC;YACH,MAAM,MAAM,GAAQ;gBAClB,KAAK;aACN,CAAC;YAEF,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;gBACzB,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC;YAChC,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAEtE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,QAAgB,EAAE;QACpD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;gBACvD,MAAM,EAAE;oBACN,MAAM,EAAE,KAAK;oBACb,KAAK;iBACN;aACF,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;gBACzD,MAAM,EAAE;oBACN,SAAS,EAAE,QAAQ;iBACpB;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAA2B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5D,OAAO,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAA2B;QAC/C,IAAI,CAAC;YACH,MAAM,IAAA,gBAAK,EACT;;;;;;;;;;;;;;;;;;;;;;;6BAuBqB,EACrB;gBACE,MAAM,CAAC,MAAM,CAAC,IAAI;gBAClB,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,aAAa;gBACpB,MAAM,CAAC,OAAO,CAAC,IAAI;gBACnB,MAAM,CAAC,OAAO,CAAC,IAAI;gBACnB,MAAM,CAAC,SAAS,CAAC,IAAI;gBACrB,MAAM,CAAC,SAAS,CAAC,SAAS;gBAC1B,MAAM,CAAC,SAAS,CAAC,SAAS;gBAC1B,MAAM,CAAC,SAAS,CAAC,MAAM;gBACvB,MAAM,CAAC,SAAS,CAAC,QAAQ;gBACzB,MAAM,CAAC,SAAS,CAAC,IAAI;gBACrB,MAAM,CAAC,SAAS,CAAC,KAAK;gBACtB,MAAM,CAAC,OAAO,CAAC,IAAI;gBACnB,MAAM,CAAC,OAAO,CAAC,SAAS;gBACxB,MAAM,CAAC,OAAO,CAAC,SAAS;gBACxB,MAAM,CAAC,OAAO,CAAC,MAAM;gBACrB,MAAM,CAAC,OAAO,CAAC,QAAQ;gBACvB,MAAM,CAAC,OAAO,CAAC,IAAI;gBACnB,MAAM,CAAC,OAAO,CAAC,OAAO;gBACtB,MAAM,CAAC,OAAO,CAAC,KAAK;gBACpB,MAAM,CAAC,QAAQ,EAAE,YAAY;gBAC7B,MAAM,CAAC,QAAQ,EAAE,IAAI;gBACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;aAC5B,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF;AA5PD,oDA4PC;AAEY,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC"}