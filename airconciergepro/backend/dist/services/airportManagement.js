"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.airportManagementService = exports.AirportManagementService = void 0;
const database_1 = require("./database");
const logger_1 = require("./logger");
class AirportManagementService {
    /**
     * Initialize the airport management service
     */
    async initialize() {
        try {
            logger_1.logger.info('Initializing Airport Management Service...');
            // Check if airports table has data
            const airportCount = await (0, database_1.query)('SELECT COUNT(*) as count FROM airports');
            const count = parseInt(airportCount.rows[0].count);
            if (count === 0) {
                logger_1.logger.info('No airports found, loading initial airport data...');
                await this.loadInitialAirportData();
            }
            else {
                logger_1.logger.info(`Airport Management Service initialized with ${count} airports`);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Airport Management Service:', error);
            throw error;
        }
    }
    /**
     * Load initial airport data from SQL file
     */
    async loadInitialAirportData() {
        try {
            // This would typically load from the airports_data.sql file
            // For now, we'll add a few key airports programmatically
            const majorAirports = [
                { iata: 'JFK', icao: 'KJFK', name: 'John F. Kennedy International Airport', city: 'New York', country: 'United States', timezone: 'America/New_York' },
                { iata: 'LHR', icao: 'EGLL', name: 'Heathrow Airport', city: 'London', country: 'United Kingdom', timezone: 'Europe/London' },
                { iata: 'DXB', icao: 'OMDB', name: 'Dubai International Airport', city: 'Dubai', country: 'United Arab Emirates', timezone: 'Asia/Dubai' },
                { iata: 'LAX', icao: 'KLAX', name: 'Los Angeles International Airport', city: 'Los Angeles', country: 'United States', timezone: 'America/Los_Angeles' },
                { iata: 'CDG', icao: 'LFPG', name: 'Charles de Gaulle Airport', city: 'Paris', country: 'France', timezone: 'Europe/Paris' }
            ];
            for (const airport of majorAirports) {
                await (0, database_1.query)(`INSERT INTO airports (iata_code, icao_code, name, city, country, timezone) 
           VALUES ($1, $2, $3, $4, $5, $6) ON CONFLICT (iata_code) DO NOTHING`, [airport.iata, airport.icao, airport.name, airport.city, airport.country, airport.timezone]);
            }
            logger_1.logger.info(`Loaded ${majorAirports.length} major airports`);
        }
        catch (error) {
            logger_1.logger.error('Error loading initial airport data:', error);
            throw error;
        }
    }
    /**
     * Get airport by IATA code
     */
    async getAirport(iataCode) {
        try {
            const result = await (0, database_1.query)('SELECT * FROM airports WHERE iata_code = $1', [iataCode.toUpperCase()]);
            if (result.rows.length === 0) {
                return null;
            }
            const airport = result.rows[0];
            // Get terminals and meeting points
            const terminals = await this.getAirportTerminals(airport.id);
            const meetingPoints = await this.getAirportMeetingPoints(airport.id);
            return {
                id: airport.id,
                iataCode: airport.iata_code,
                icaoCode: airport.icao_code,
                name: airport.name,
                city: airport.city,
                country: airport.country,
                timezone: airport.timezone,
                terminals,
                meetingPoints
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting airport:', error);
            return null;
        }
    }
    /**
     * Get all airports with pagination
     */
    async getAirports(page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            const countResult = await (0, database_1.query)('SELECT COUNT(*) as count FROM airports');
            const total = parseInt(countResult.rows[0].count);
            const result = await (0, database_1.query)('SELECT * FROM airports ORDER BY name LIMIT $1 OFFSET $2', [limit, offset]);
            const airports = result.rows.map(row => ({
                id: row.id,
                iataCode: row.iata_code,
                icaoCode: row.icao_code,
                name: row.name,
                city: row.city,
                country: row.country,
                timezone: row.timezone
            }));
            return {
                airports,
                total,
                page,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting airports:', error);
            return { airports: [], total: 0, page: 1, totalPages: 0 };
        }
    }
    /**
     * Get airport terminals
     */
    async getAirportTerminals(airportId) {
        try {
            const result = await (0, database_1.query)('SELECT * FROM airport_terminals WHERE airport_id = $1 ORDER BY terminal_code', [airportId]);
            return result.rows.map(row => ({
                id: row.id,
                airportId: row.airport_id,
                terminalCode: row.terminal_code,
                terminalName: row.terminal_name,
                gates: row.gates || [],
                facilities: row.facilities || [],
                operatingHours: row.operating_hours || { open: '00:00', close: '23:59' },
                contactInfo: row.contact_info || {}
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting airport terminals:', error);
            return [];
        }
    }
    /**
     * Get airport meeting points
     */
    async getAirportMeetingPoints(airportId, terminal) {
        try {
            let query_text = 'SELECT * FROM meeting_points WHERE airport_id = $1';
            const params = [airportId];
            if (terminal) {
                query_text += ' AND terminal = $2';
                params.push(terminal);
            }
            query_text += ' ORDER BY name';
            const result = await (0, database_1.query)(query_text, params);
            return result.rows.map(row => ({
                id: row.id,
                airportId: row.airport_id,
                terminal: row.terminal,
                name: row.name,
                description: row.description,
                coordinates: row.coordinates,
                instructions: row.instructions,
                accessibilityFeatures: row.accessibility_features || [],
                capacity: row.capacity,
                equipmentAvailable: row.equipment_available || [],
                status: row.status
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting airport meeting points:', error);
            return [];
        }
    }
    /**
     * Create or update meeting point
     */
    async upsertMeetingPoint(meetingPoint) {
        try {
            const result = await (0, database_1.query)(`INSERT INTO meeting_points (
          airport_id, terminal, name, description, coordinates, 
          instructions, accessibility_features, capacity, equipment_available, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        ON CONFLICT (airport_id, terminal, name) 
        DO UPDATE SET
          description = EXCLUDED.description,
          coordinates = EXCLUDED.coordinates,
          instructions = EXCLUDED.instructions,
          accessibility_features = EXCLUDED.accessibility_features,
          capacity = EXCLUDED.capacity,
          equipment_available = EXCLUDED.equipment_available,
          status = EXCLUDED.status,
          updated_at = NOW()
        RETURNING id`, [
                meetingPoint.airportId,
                meetingPoint.terminal,
                meetingPoint.name,
                meetingPoint.description,
                JSON.stringify(meetingPoint.coordinates),
                meetingPoint.instructions,
                meetingPoint.accessibilityFeatures,
                meetingPoint.capacity,
                meetingPoint.equipmentAvailable,
                meetingPoint.status
            ]);
            return result.rows[0]?.id || null;
        }
        catch (error) {
            logger_1.logger.error('Error upserting meeting point:', error);
            return null;
        }
    }
    /**
     * Get airport operational data
     */
    async getAirportOperations(airportId, date) {
        try {
            const targetDate = date || new Date();
            const dateStr = targetDate.toISOString().split('T')[0];
            const result = await (0, database_1.query)('SELECT * FROM airport_operations WHERE airport_id = $1 AND date = $2', [airportId, dateStr]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            return {
                id: row.id,
                airportId: row.airport_id,
                date: new Date(row.date),
                totalArrivals: row.total_arrivals,
                totalDepartures: row.total_departures,
                delayedFlights: row.delayed_flights,
                cancelledFlights: row.cancelled_flights,
                averageDelayMinutes: row.average_delay_minutes,
                weatherConditions: row.weather_conditions,
                operationalStatus: row.operational_status,
                notes: row.notes
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting airport operations:', error);
            return null;
        }
    }
    /**
     * Search airports by name or code
     */
    async searchAirports(query_term) {
        try {
            const result = await (0, database_1.query)(`SELECT * FROM airports 
         WHERE UPPER(name) LIKE UPPER($1) 
            OR UPPER(iata_code) LIKE UPPER($1) 
            OR UPPER(icao_code) LIKE UPPER($1)
            OR UPPER(city) LIKE UPPER($1)
         ORDER BY 
           CASE 
             WHEN UPPER(iata_code) = UPPER($2) THEN 1
             WHEN UPPER(icao_code) = UPPER($2) THEN 2
             WHEN UPPER(name) LIKE UPPER($1) THEN 3
             ELSE 4
           END
         LIMIT 20`, [`%${query_term}%`, query_term]);
            return result.rows.map(row => ({
                id: row.id,
                iataCode: row.iata_code,
                icaoCode: row.icao_code,
                name: row.name,
                city: row.city,
                country: row.country,
                timezone: row.timezone
            }));
        }
        catch (error) {
            logger_1.logger.error('Error searching airports:', error);
            return [];
        }
    }
}
exports.AirportManagementService = AirportManagementService;
exports.airportManagementService = new AirportManagementService();
//# sourceMappingURL=airportManagement.js.map