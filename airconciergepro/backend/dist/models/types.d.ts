export interface Tenant {
    id: string;
    name: string;
    subdomain: string;
    plan: 'starter' | 'professional' | 'enterprise' | 'enterprise_plus';
    status: 'active' | 'suspended' | 'trial';
    settings: {
        branding?: {
            logo?: string;
            primaryColor?: string;
            secondaryColor?: string;
        };
        features?: string[];
        limits?: {
            monthly_bookings?: number;
            api_calls?: number;
            locations?: number;
        };
    };
    created_at: Date;
    updated_at: Date;
}
export interface User {
    id: string;
    tenant_id: string;
    email: string;
    password_hash: string;
    first_name: string;
    last_name: string;
    role: 'super_admin' | 'company_admin' | 'operations_manager' | 'field_agent' | 'customer_service';
    status: 'active' | 'inactive' | 'pending';
    permissions: string[];
    last_login?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface Customer {
    id: string;
    tenant_id: string;
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
    date_of_birth?: Date;
    preferences: {
        language?: string;
        special_requirements?: string[];
        communication_preferences?: string[];
    };
    loyalty_points: number;
    total_bookings: number;
    total_spent: number;
    created_at: Date;
    updated_at: Date;
}
export interface Airport {
    id: string;
    iata_code: string;
    icao_code: string;
    name: string;
    city: string;
    country: string;
    timezone: string;
    terminals: Terminal[];
    created_at: Date;
    updated_at: Date;
}
export interface Terminal {
    id: string;
    airport_id: string;
    name: string;
    terminal_code: string;
    meeting_points: MeetingPoint[];
    facilities: string[];
}
export interface MeetingPoint {
    id: string;
    terminal_id: string;
    name: string;
    description: string;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
    instructions: string;
}
export interface Service {
    id: string;
    tenant_id: string;
    name: string;
    description: string;
    category: 'meet_greet' | 'fast_track' | 'lounge_access' | 'vip_terminal' | 'transfer' | 'porter';
    type: 'arrival' | 'departure' | 'transit';
    base_price: number;
    currency: string;
    duration_minutes: number;
    max_passengers: number;
    available_airports: string[];
    requirements: string[];
    inclusions: string[];
    status: 'active' | 'inactive';
    created_at: Date;
    updated_at: Date;
}
export interface Booking {
    id: string;
    tenant_id: string;
    customer_id: string;
    service_id: string;
    booking_reference: string;
    status: 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
    flight_number: string;
    airline: string;
    departure_airport: string;
    arrival_airport: string;
    flight_date: Date;
    estimated_arrival?: Date;
    actual_arrival?: Date;
    service_type: 'arrival' | 'departure' | 'transit';
    passenger_count: number;
    passengers: Passenger[];
    special_requirements?: string[];
    meeting_point: string;
    assigned_agent_id?: string;
    assignment_time?: Date;
    base_price: number;
    additional_charges: number;
    total_price: number;
    currency: string;
    payment_status: 'pending' | 'paid' | 'refunded' | 'failed';
    payment_method?: string;
    payment_reference?: string;
    service_start_time?: Date;
    service_end_time?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface Passenger {
    first_name: string;
    last_name: string;
    age?: number;
    special_requirements?: string[];
    contact_number?: string;
}
export interface BookingActivity {
    id: string;
    booking_id: string;
    user_id: string;
    activity_type: 'created' | 'updated' | 'assigned' | 'started' | 'completed' | 'cancelled';
    description: string;
    metadata?: any;
    created_at: Date;
}
export interface Agent {
    id: string;
    tenant_id: string;
    user_id: string;
    employee_id: string;
    status: 'available' | 'busy' | 'offline';
    current_location?: {
        latitude: number;
        longitude: number;
        accuracy: number;
        timestamp: Date;
    };
    specializations: string[];
    languages: string[];
    airports: string[];
    rating: number;
    total_services: number;
    created_at: Date;
    updated_at: Date;
}
export interface Payment {
    id: string;
    tenant_id: string;
    booking_id: string;
    amount: number;
    currency: string;
    payment_method: 'credit_card' | 'debit_card' | 'paypal' | 'stripe' | 'bank_transfer';
    payment_provider: string;
    provider_transaction_id: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    metadata?: any;
    created_at: Date;
    updated_at: Date;
}
export interface ApiKey {
    id: string;
    tenant_id: string;
    name: string;
    key_hash: string;
    permissions: string[];
    usage_limit?: number;
    usage_count: number;
    last_used?: Date;
    expires_at?: Date;
    status: 'active' | 'inactive' | 'revoked';
    created_at: Date;
    updated_at: Date;
}
//# sourceMappingURL=types.d.ts.map