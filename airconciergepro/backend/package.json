{"name": "airconciergepro-backend", "version": "1.0.0", "description": "AirConcierge Pro Backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "db:migrate": "knex migrate:latest", "db:seed": "knex seed:run", "sync-airports": "ts-node scripts/syncAirports.ts sync", "sync-airports:all": "ts-node scripts/syncAirports.ts all", "test-flights": "ts-node scripts/syncAirports.ts test-flights"}, "keywords": ["meet-greet", "airport", "saas", "concierge"], "author": "AirConcierge Pro", "license": "ISC", "dependencies": {"aws-sdk": "^2.1433.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "knex": "^2.5.1", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "pg": "^8.11.3", "razorpay": "^2.9.6", "redis": "^4.6.7", "socket.io": "^4.7.2", "stripe": "^13.6.0", "twilio": "^4.15.0", "typescript": "^5.1.6", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.8.1", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.10.2", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}