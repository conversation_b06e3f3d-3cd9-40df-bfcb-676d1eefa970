services:
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: airconciergepro
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  backend:
    build: ./backend
    restart: always
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      DB_HOST: db
      DB_PORT: 5432
      DB_NAME: airconciergepro
      DB_USER: postgres
      DB_PASSWORD: password
      JWT_SECRET: 48656A9F-F24A-4B31-AD51-75D3D69A4224
      FRONTEND_URL: "http://localhost:3000,http://localhost:3001"
      REDIS_HOST: redis
      REDIS_PORT: 6379

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"

  frontend:
    build: ./frontend
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1

  customer-portal:
    build:
      context: ./customer-portal
      args:
        NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_publishable_key_here
        NEXT_PUBLIC_RAZORPAY_KEY_ID: rzp_test_your_razorpay_key_here
        NEXT_PUBLIC_DEFAULT_CURRENCY: INR
    restart: always
    ports:
      - "3001:3001"
    depends_on:
      - backend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_publishable_key_here
      NEXT_PUBLIC_RAZORPAY_KEY_ID: rzp_test_your_razorpay_key_here
      NEXT_PUBLIC_DEFAULT_CURRENCY: INR
      NEXT_PUBLIC_WS_URL: ws://localhost:8000/ws

volumes:
  db_data:
