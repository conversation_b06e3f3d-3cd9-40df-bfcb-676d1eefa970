# AirConcierge Pro Docker Setup

This document provides instructions for running the complete AirConcierge Pro application stack using Docker Compose.

## Services Overview

The Docker Compose setup includes the following services:

- **Database (PostgreSQL)**: Port 5432
- **Redis**: Port 6379  
- **Backend API**: Port 8000
- **Admin Frontend**: Port 3000
- **Customer Portal**: Port 3001

## Prerequisites

- Docker and Docker Compose installed
- Node.js 22+ (for local development)
- Git

## Quick Start

1. **Clone and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd airconciergepro
   ```

2. **Set up environment variables:**
   ```bash
   # Copy environment templates
   cp backend/.env.example backend/.env
   cp customer-portal/.env.example customer-portal/.env
   
   # Edit the .env files with your configuration
   ```

3. **Build and start all services:**
   ```bash
   docker-compose up --build
   ```

4. **Access the applications:**
   - Admin Frontend: http://localhost:3000
   - Customer Portal: http://localhost:3001
   - Backend API: http://localhost:8000
   - Database: localhost:5432

## Environment Configuration

### Backend (.env)
```env
DB_HOST=db
DB_PORT=5432
DB_NAME=airconciergepro
DB_USER=postgres
DB_PASSWORD=password
JWT_SECRET=your-jwt-secret
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
```

### Customer Portal (.env)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:8000
```

## Development Commands

```bash
# Start all services
docker-compose up

# Start in background
docker-compose up -d

# Rebuild and start
docker-compose up --build

# Stop all services
docker-compose down

# View logs
docker-compose logs [service-name]

# Execute commands in running container
docker-compose exec backend npm run migrate
docker-compose exec backend npm run seed
```

## Database Setup

The database will be automatically created when you start the services. To run migrations and seed data:

```bash
docker-compose exec backend npm run migrate
docker-compose exec backend npm run seed
```

## Troubleshooting

### Port Conflicts
If you have port conflicts, modify the ports in docker-compose.yml:
```yaml
ports:
  - "3002:3000"  # Change external port
```

### Build Issues
```bash
# Clean build
docker-compose down
docker system prune -f
docker-compose up --build
```

### Database Connection Issues
```bash
# Check database logs
docker-compose logs db

# Reset database
docker-compose down -v
docker-compose up
```

## Production Deployment

For production deployment:

1. Update environment variables with production values
2. Use production-ready database credentials
3. Configure proper SSL certificates
4. Set up reverse proxy (nginx/Apache)
5. Enable Docker restart policies

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│  Admin Frontend │    │ Customer Portal │
│    (Port 3000)  │    │   (Port 3001)   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
            ┌────────▼────────┐
            │   Backend API   │
            │   (Port 8000)   │
            └────────┬────────┘
                     │
        ┌────────────┼────────────┐
        │            │            │
   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐
   │   DB   │   │ Redis  │   │  ...   │
   │ :5432  │   │ :6379  │   │        │
   └────────┘   └────────┘   └────────┘
```
