import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;
  private authToken: string | null = null;
  private isOnline: boolean = true;
  private requestQueue: Array<() => Promise<any>> = [];

  constructor() {
    this.baseURL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000/api/v1';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.setupNetworkMonitoring();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          await this.handleAuthError();
        }
        
        if (!this.isOnline) {
          // Queue request for when online
          this.queueRequest(() => this.api.request(error.config));
        }
        
        return Promise.reject(error);
      }
    );
  }

  private setupNetworkMonitoring() {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (wasOffline && this.isOnline) {
        // Process queued requests
        this.processRequestQueue();
      }
    });
  }

  private async handleAuthError() {
    // Clear stored auth data
    await AsyncStorage.multiRemove(['authToken', 'user']);
    this.authToken = null;
    
    // Redirect to login (handled by AuthContext)
    // This would trigger a re-render and show login screen
  }

  private queueRequest(requestFn: () => Promise<any>) {
    this.requestQueue.push(requestFn);
  }

  private async processRequestQueue() {
    const queue = [...this.requestQueue];
    this.requestQueue = [];
    
    for (const requestFn of queue) {
      try {
        await requestFn();
      } catch (error) {
        console.error('Queued request failed:', error);
      }
    }
  }

  public setAuthToken(token: string | null) {
    this.authToken = token;
  }

  public async get<T = any>(url: string, params?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.api.get(url, { params });
  }

  public async post<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.api.post(url, data);
  }

  public async put<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.api.put(url, data);
  }

  public async patch<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.api.patch(url, data);
  }

  public async delete<T = any>(url: string): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.api.delete(url);
  }

  // Mobile-specific endpoints
  public async loginAgent(email: string, password: string) {
    return this.post('/mobile/auth/login', { email, password });
  }

  public async updateLocation(latitude: number, longitude: number, accuracy?: number) {
    return this.post('/mobile/location/update', {
      latitude,
      longitude,
      accuracy,
      timestamp: new Date().toISOString()
    });
  }

  public async updateStatus(status: string) {
    return this.post('/mobile/status/update', { status });
  }

  public async getMyBookings() {
    return this.get('/mobile/bookings');
  }

  public async getNearbyBookings(latitude: number, longitude: number, radius: number = 50) {
    return this.get('/mobile/bookings/nearby', {
      latitude,
      longitude,
      radius
    });
  }

  public async getBookingDetails(bookingId: string) {
    return this.get(`/mobile/bookings/${bookingId}`);
  }

  public async checkinToBooking(bookingId: string, latitude: number, longitude: number, notes?: string) {
    return this.post(`/mobile/bookings/${bookingId}/checkin`, {
      latitude,
      longitude,
      notes
    });
  }

  public async completeBooking(bookingId: string, notes?: string, rating?: number, signature?: string) {
    return this.post(`/mobile/bookings/${bookingId}/complete`, {
      notes,
      rating,
      customerSignature: signature
    });
  }

  public async getAgentStats(period: 'today' | 'week' | 'month' = 'today') {
    return this.get('/mobile/agent/stats', { period });
  }

  public async uploadPhoto(bookingId: string, photoUri: string, type: 'before' | 'after' | 'signature') {
    const formData = new FormData();
    formData.append('photo', {
      uri: photoUri,
      type: 'image/jpeg',
      name: `${type}_${bookingId}_${Date.now()}.jpg`,
    } as any);
    formData.append('type', type);

    return this.api.post(`/mobile/bookings/${bookingId}/photos`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  public async getFlightInfo(flightNumber: string, date?: string) {
    return this.get('/mobile/flights/info', {
      flightNumber,
      date
    });
  }

  public async reportIssue(bookingId: string, issue: string, severity: 'low' | 'medium' | 'high') {
    return this.post('/mobile/issues/report', {
      bookingId,
      issue,
      severity,
      timestamp: new Date().toISOString()
    });
  }

  public async getNotifications(page: number = 1, limit: number = 20) {
    return this.get('/mobile/notifications', { page, limit });
  }

  public async markNotificationRead(notificationId: string) {
    return this.patch(`/mobile/notifications/${notificationId}/read`);
  }

  // Offline support methods
  public async syncOfflineData() {
    try {
      const offlineData = await AsyncStorage.getItem('offlineData');
      if (offlineData) {
        const data = JSON.parse(offlineData);
        
        // Sync location updates
        if (data.locationUpdates) {
          for (const update of data.locationUpdates) {
            try {
              await this.updateLocation(update.latitude, update.longitude, update.accuracy);
            } catch (error) {
              console.error('Failed to sync location update:', error);
            }
          }
        }

        // Sync booking updates
        if (data.bookingUpdates) {
          for (const update of data.bookingUpdates) {
            try {
              if (update.type === 'checkin') {
                await this.checkinToBooking(update.bookingId, update.latitude, update.longitude, update.notes);
              } else if (update.type === 'complete') {
                await this.completeBooking(update.bookingId, update.notes, update.rating);
              }
            } catch (error) {
              console.error('Failed to sync booking update:', error);
            }
          }
        }

        // Clear synced data
        await AsyncStorage.removeItem('offlineData');
      }
    } catch (error) {
      console.error('Error syncing offline data:', error);
    }
  }

  public async storeOfflineData(type: string, data: any) {
    try {
      const existingData = await AsyncStorage.getItem('offlineData');
      const offlineData = existingData ? JSON.parse(existingData) : {};
      
      if (!offlineData[type]) {
        offlineData[type] = [];
      }
      
      offlineData[type].push({
        ...data,
        timestamp: new Date().toISOString()
      });

      await AsyncStorage.setItem('offlineData', JSON.stringify(offlineData));
    } catch (error) {
      console.error('Error storing offline data:', error);
    }
  }

  public isNetworkAvailable(): boolean {
    return this.isOnline;
  }
}

export const apiService = new ApiService();
export type { ApiResponse };
