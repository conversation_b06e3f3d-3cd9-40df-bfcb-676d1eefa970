import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Geolocation from 'react-native-geolocation-service';
import { Platform, Alert, AppState } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { apiService } from '../services/apiService';
import { useAuth } from './AuthContext';

interface Location {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

interface LocationContextType {
  currentLocation: Location | null;
  isTracking: boolean;
  locationPermission: boolean;
  startTracking: () => Promise<void>;
  stopTracking: () => void;
  updateLocation: () => Promise<void>;
  getDistance: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export const useLocation = () => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

interface LocationProviderProps {
  children: ReactNode;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({ children }) => {
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [locationPermission, setLocationPermission] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    checkLocationPermission();
    
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active' && user && locationPermission) {
        startTracking();
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Keep tracking in background for field agents
        if (user?.role === 'field_agent') {
          // Continue background tracking
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
      if (watchId !== null) {
        Geolocation.clearWatch(watchId);
      }
    };
  }, [user, locationPermission]);

  const checkLocationPermission = async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE 
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

      const result = await check(permission);
      
      if (result === RESULTS.GRANTED) {
        setLocationPermission(true);
        if (user?.role === 'field_agent') {
          startTracking();
        }
      } else if (result === RESULTS.DENIED) {
        requestLocationPermission();
      } else {
        setLocationPermission(false);
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE 
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

      const result = await request(permission);
      
      if (result === RESULTS.GRANTED) {
        setLocationPermission(true);
        if (user?.role === 'field_agent') {
          startTracking();
        }
      } else {
        setLocationPermission(false);
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to provide field agent services. Please enable location permissions in settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const startTracking = async (): Promise<void> => {
    if (!locationPermission || isTracking) {
      return;
    }

    try {
      setIsTracking(true);

      // Get initial location
      await updateLocation();

      // Start watching location changes
      const id = Geolocation.watchPosition(
        (position) => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          };
          
          setCurrentLocation(location);
          sendLocationUpdate(location);
        },
        (error) => {
          console.error('Location tracking error:', error);
          setIsTracking(false);
        },
        {
          enableHighAccuracy: true,
          distanceFilter: 10, // Update every 10 meters
          interval: 30000, // Update every 30 seconds
          fastestInterval: 15000, // Fastest update every 15 seconds
          forceRequestLocation: true,
          showLocationDialog: true,
        }
      );

      setWatchId(id);
    } catch (error) {
      console.error('Error starting location tracking:', error);
      setIsTracking(false);
    }
  };

  const stopTracking = (): void => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
      setWatchId(null);
    }
    setIsTracking(false);
  };

  const updateLocation = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          };
          
          setCurrentLocation(location);
          sendLocationUpdate(location);
          resolve();
        },
        (error) => {
          console.error('Error getting current location:', error);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  };

  const sendLocationUpdate = async (location: Location): Promise<void> => {
    if (!user) return;

    try {
      await apiService.post('/mobile/location/update', {
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        timestamp: new Date(location.timestamp).toISOString()
      });
    } catch (error) {
      console.error('Error sending location update:', error);
    }
  };

  // Calculate distance between two points using Haversine formula
  const getDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return distance;
  };

  const value: LocationContextType = {
    currentLocation,
    isTracking,
    locationPermission,
    startTracking,
    stopTracking,
    updateLocation,
    getDistance
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};
