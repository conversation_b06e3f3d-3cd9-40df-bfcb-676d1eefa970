import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import io, { Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { Alert } from 'react-native';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  data?: any;
}

interface BookingUpdate {
  bookingId: string;
  status: string;
  agentId?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
  updatedBy: string;
}

interface WebSocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  notifications: Notification[];
  bookingUpdates: BookingUpdate[];
  connect: () => void;
  disconnect: () => void;
  sendBookingUpdate: (update: BookingUpdate) => void;
  subscribeToFlight: (flightNumber: string) => void;
  unsubscribeFromFlight: (flightNumber: string) => void;
  clearNotifications: () => void;
  markNotificationRead: (id: string) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [bookingUpdates, setBookingUpdates] = useState<BookingUpdate[]>([]);
  const { user, token } = useAuth();

  useEffect(() => {
    if (user && token) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [user, token]);

  const connect = () => {
    if (!user || !token || socket?.connected) {
      return;
    }

    const newSocket = io(process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('WebSocket connected');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setIsConnected(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setIsConnected(false);
    });

    // Listen for system notifications
    newSocket.on('system:notification', (notification: any) => {
      const newNotification: Notification = {
        id: Date.now().toString(),
        type: notification.type,
        title: notification.title,
        message: notification.message,
        timestamp: new Date(notification.timestamp),
        data: notification.data
      };

      setNotifications(prev => [newNotification, ...prev.slice(0, 49)]); // Keep last 50

      // Show alert for important notifications
      if (notification.type === 'error' || notification.type === 'warning') {
        Alert.alert(notification.title, notification.message);
      }
    });

    // Listen for booking updates
    newSocket.on('booking:updated', (update: any) => {
      const bookingUpdate: BookingUpdate = {
        bookingId: update.bookingId,
        status: update.status,
        agentId: update.agentId,
        location: update.location,
        timestamp: new Date(update.timestamp),
        updatedBy: update.updatedBy
      };

      setBookingUpdates(prev => [bookingUpdate, ...prev.slice(0, 99)]); // Keep last 100

      // Show notification for booking updates
      if (update.agentId === user.id) {
        Alert.alert(
          'Booking Update',
          `Booking status updated to: ${update.status}`,
          [{ text: 'OK' }]
        );
      }
    });

    // Listen for flight updates
    newSocket.on('flight:updated', (flightUpdate: any) => {
      const notification: Notification = {
        id: Date.now().toString(),
        type: 'info',
        title: 'Flight Update',
        message: `Flight ${flightUpdate.flightNumber} status: ${flightUpdate.status}`,
        timestamp: new Date(),
        data: flightUpdate
      };

      setNotifications(prev => [notification, ...prev.slice(0, 49)]);
    });

    // Listen for agent status updates
    newSocket.on('agent:status_updated', (statusUpdate: any) => {
      if (statusUpdate.agentId !== user.id) {
        const notification: Notification = {
          id: Date.now().toString(),
          type: 'info',
          title: 'Agent Status Update',
          message: `Agent status updated to: ${statusUpdate.status}`,
          timestamp: new Date(statusUpdate.timestamp),
          data: statusUpdate
        };

        setNotifications(prev => [notification, ...prev.slice(0, 49)]);
      }
    });

    setSocket(newSocket);
  };

  const disconnect = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
    }
  };

  const sendBookingUpdate = (update: BookingUpdate) => {
    if (socket && isConnected) {
      socket.emit('booking:update', update);
    }
  };

  const subscribeToFlight = (flightNumber: string) => {
    if (socket && isConnected) {
      socket.emit('flight:subscribe', { flightNumber });
    }
  };

  const unsubscribeFromFlight = (flightNumber: string) => {
    if (socket && isConnected) {
      socket.emit('flight:unsubscribe', { flightNumber });
    }
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const markNotificationRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const value: WebSocketContextType = {
    socket,
    isConnected,
    notifications,
    bookingUpdates,
    connect,
    disconnect,
    sendBookingUpdate,
    subscribeToFlight,
    unsubscribeFromFlight,
    clearNotifications,
    markNotificationRead
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};
