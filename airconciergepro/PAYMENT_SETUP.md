# Payment Gateway Setup Guide

This guide explains how to set up both Stripe and Razorpay payment gateways in AirConcierge Pro.

## Overview

AirConcierge Pro now supports multiple payment providers:
- **Stripe**: Global payment processing with credit/debit cards
- **Razorpay**: Indian payment gateway with UPI, cards, net banking, and wallets

## 1. Database Migration

First, run the database migration to add payment provider support:

```bash
# Connect to your PostgreSQL database and run:
# psql -d your_database_name -f backend/database/migrations/add_payment_intent_id.sql
psql -h localhost -U postgres -d airconciergepro -f backend/database/migrations/add_payment_intent_id.sql

```

## 2. Stripe Setup

### 2.1 Get Stripe API Keys

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Create an account or log in
3. Navigate to **Developers > API Keys**
4. Copy your **Publishable key** and **Secret key**

### 2.2 Configure Stripe Environment Variables

**Backend (.env):**
```bash
STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

**Customer Portal (.env.local):**
```bash
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_stripe_publishable_key
```

### 2.3 Stripe Test Cards

Use these test card numbers for development:

| Card Number | Brand | Result |
|-------------|-------|--------|
| **************** | Visa | Success |
| **************** | Visa | Declined |
| **************** | Visa | Insufficient funds |
| **************** | Visa | 3D Secure authentication |

## 3. Razorpay Setup

### 3.1 Get Razorpay API Keys

1. Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Create an account (requires Indian business details)
3. Navigate to **Settings > API Keys**
4. Generate and copy your **Key ID** and **Key Secret**

### 3.2 Configure Razorpay Environment Variables

**Backend (.env):**
```bash
RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
RAZORPAY_KEY_SECRET=your_actual_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
```

**Customer Portal (.env.local):**
```bash
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
```

### 3.3 Razorpay Test Details

For testing Razorpay payments:

**Test Card Numbers:**
- **Success**: ****************
- **Failure**: ****************

**Test UPI ID:**
- success@razorpay
- failure@razorpay

**Test Net Banking:**
- Use any test bank from the dropdown
- Use "success" as password for successful transactions

## 4. Webhook Configuration

### 4.1 Stripe Webhooks

1. Go to **Stripe Dashboard > Developers > Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://your-domain.com/api/payments/webhook/stripe`
4. Select events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Copy the webhook secret to your environment variables

### 4.2 Razorpay Webhooks

1. Go to **Razorpay Dashboard > Settings > Webhooks**
2. Click **Add New Webhook**
3. Set webhook URL: `https://your-domain.com/api/payments/webhook/razorpay`
4. Select events:
   - `payment.captured`
   - `payment.failed`
5. Set a webhook secret and add it to your environment variables

## 5. Testing the Integration

### 5.1 Test Stripe Payments

1. Start your backend and customer portal
2. Go to the booking flow
3. Select "Credit/Debit Card" payment method
4. Use test card: `****************`
5. Use any future expiry date and any 3-digit CVC

### 5.2 Test Razorpay Payments

1. Select "Razorpay" payment method
2. Choose your preferred payment option (UPI, Card, Net Banking)
3. Use the test credentials mentioned above

## 6. Production Deployment

### 6.1 Switch to Live Keys

**Stripe:**
- Replace `sk_test_` with `sk_live_` for secret key
- Replace `pk_test_` with `pk_live_` for publishable key

**Razorpay:**
- Replace `rzp_test_` with `rzp_live_` for key ID
- Update key secret with live secret

### 6.2 Update Webhook URLs

Update webhook URLs to point to your production domain.

## 7. Multi-Tenant Configuration

For different tenants to use different payment providers:

1. Add payment provider preferences to the tenant configuration
2. Update the payment selection logic to show relevant providers
3. Configure tenant-specific API keys if needed

## 8. Troubleshooting

### Common Issues

**Stripe Issues:**
- **Invalid API Key**: Check if you're using the correct test/live keys
- **Webhook Signature Verification Failed**: Ensure webhook secret matches

**Razorpay Issues:**
- **Key ID Invalid**: Verify the key ID format (should start with `rzp_`)
- **Payment Verification Failed**: Check signature calculation in webhook handler

**General Issues:**
- **CORS Errors**: Ensure your domain is whitelisted in payment provider settings
- **SSL Required**: Both providers require HTTPS in production

### Debug Mode

Enable debug logging by setting:
```bash
LOG_LEVEL=debug
```

This will log detailed payment processing information.

## 9. Security Best Practices

1. **Never expose secret keys** in frontend code
2. **Always verify payments** on the server side
3. **Use HTTPS** in production
4. **Implement rate limiting** on payment endpoints
5. **Log payment attempts** for audit trails
6. **Validate webhook signatures** to prevent fraud

## 10. Support

For payment-related issues:
- **Stripe**: [Stripe Support](https://support.stripe.com/)
- **Razorpay**: [Razorpay Support](https://razorpay.com/support/)

For integration issues, check the application logs and ensure all environment variables are correctly set.
