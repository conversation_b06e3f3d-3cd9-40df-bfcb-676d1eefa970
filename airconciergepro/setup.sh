#!/bin/bash

# AirConcierge Pro Setup Script
# This script helps you set up the development environment

set -e

echo "🚀 Setting up AirConcierge Pro Development Environment"
echo "=================================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is required but not installed. Please install PostgreSQL 14+ from https://postgresql.org/"
    exit 1
fi

if ! command -v redis-server &> /dev/null; then
    echo "⚠️  Redis is recommended but not found. Please install Redis 6+ from https://redis.io/"
fi

echo "✅ Prerequisites check completed"

# Install dependencies
echo "📦 Installing dependencies..."

echo "Installing backend dependencies..."
cd backend
npm install
cd ..

echo "Installing frontend dependencies..."
cd frontend
npm install
cd ..

echo "✅ Dependencies installed successfully"

# Setup environment files
echo "⚙️  Setting up environment files..."

if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "📝 Created backend/.env from example. Please update with your settings."
fi

if [ ! -f frontend/.env.local ]; then
    cp frontend/.env.example frontend/.env.local
    echo "📝 Created frontend/.env.local from example. Please update with your settings."
fi

echo "✅ Environment files created"

# Database setup
echo "🗄️  Setting up database..."

# Check if database exists
if psql -lqt | cut -d \| -f 1 | grep -qw airconciergepro; then
    echo "Database 'airconciergepro' already exists"
else
    echo "Creating database 'airconciergepro'..."
    createdb airconciergepro
    echo "✅ Database created"

    # Run schema
    echo "Setting up database schema..."
    psql -d airconciergepro -f backend/database/schema.sql > /dev/null 2>&1
    echo "✅ Database schema applied"

    # Load sample data
    echo "Loading sample data..."
    psql -d airconciergepro -f backend/database/sample_data.sql > /dev/null 2>&1
    echo "✅ Sample data loaded"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📚 Next steps:"
echo "1. Update backend/.env with your database credentials"
echo "2. Update frontend/.env.local with your preferences"
echo "3. Start the development servers:"
echo ""
echo "   Terminal 1 - Backend API:"
echo "   cd backend && npm run dev"
echo ""
echo "   Terminal 2 - Frontend Dashboard:"
echo "   cd frontend && npm run dev"
echo ""
echo "   Terminal 3 - Redis (if not running as service):"
echo "   redis-server"
echo ""
echo "🌐 Access URLs:"
echo "   Dashboard: http://localhost:3000"
echo "   API: http://localhost:5000"
echo ""
echo "🔑 Demo Login:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📖 For more information, see README.md"
