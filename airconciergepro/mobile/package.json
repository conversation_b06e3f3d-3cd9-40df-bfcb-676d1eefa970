{"name": "airconciergepro-mobile", "version": "1.0.0", "description": "AirConcierge Pro Mobile App for Field Agents", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "^18.2.0", "react-native": "^0.72.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "react-native-screens": "^3.22.0", "react-native-safe-area-context": "^4.7.0", "react-native-gesture-handler": "^2.12.0", "axios": "^1.5.0", "react-native-vector-icons": "^10.0.0", "@react-native-async-storage/async-storage": "^1.19.0", "react-native-geolocation-service": "^5.3.0", "react-native-permissions": "^3.8.0", "react-native-maps": "^1.7.0", "react-native-qrcode-scanner": "^1.5.0", "react-native-camera": "^4.2.0", "react-native-push-notification": "^8.1.0", "react-native-background-job": "^0.2.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/runtime": "^7.22.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.2.0", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.5.0", "eslint": "^8.47.0", "jest": "^29.5.0", "metro-react-native-babel-preset": "^0.76.0", "prettier": "^3.0.0", "react-test-renderer": "^18.2.0", "typescript": "^5.1.0"}, "engines": {"node": ">=18.0.0"}}