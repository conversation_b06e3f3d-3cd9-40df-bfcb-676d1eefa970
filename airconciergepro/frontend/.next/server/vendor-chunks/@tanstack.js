"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-table/build/lib/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFiltering),\n/* harmony export */   Headers: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.Headers),\n/* harmony export */   RowExpanding: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowExpanding),\n/* harmony export */   RowPagination: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPagination),\n/* harmony export */   RowPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPinning),\n/* harmony export */   RowSelection: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSelection),\n/* harmony export */   RowSorting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__._getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.buildHeaderGroups),\n/* harmony export */   createCell: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createCell),\n/* harmony export */   createColumn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumn),\n/* harmony export */   createColumnHelper: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumnHelper),\n/* harmony export */   createRow: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createRow),\n/* harmony export */   createTable: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable),\n/* harmony export */   defaultColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.expandRows),\n/* harmony export */   filterFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.filterFns),\n/* harmony export */   flattenBy: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.flattenBy),\n/* harmony export */   flexRender: () => (/* binding */ flexRender),\n/* harmony export */   functionalUpdate: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getSortedRowModel),\n/* harmony export */   isFunction: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isFunction),\n/* harmony export */   isNumberArray: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isNumberArray),\n/* harmony export */   isRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.makeStateUpdater),\n/* harmony export */   memo: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.memo),\n/* harmony export */   noop: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.noop),\n/* harmony export */   orderColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.sortingFns),\n/* harmony export */   useReactTable: () => (/* binding */ useReactTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/table-core */ \"(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\");\n/**\n   * react-table\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n\n\n\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nfunction flexRender(Comp, props) {\n  return !Comp ? null : isReactComponent(Comp) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Comp, props) : Comp;\n}\nfunction isReactComponent(component) {\n  return isClassComponent(component) || typeof component === 'function' || isExoticComponent(component);\n}\nfunction isClassComponent(component) {\n  return typeof component === 'function' && (() => {\n    const proto = Object.getPrototypeOf(component);\n    return proto.prototype && proto.prototype.isReactComponent;\n  })();\n}\nfunction isExoticComponent(component) {\n  return typeof component === 'object' && typeof component.$$typeof === 'symbol' && ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description);\n}\nfunction useReactTable(options) {\n  // Compose in the generic options to the user options\n  const resolvedOptions = {\n    state: {},\n    // Dummy state\n    onStateChange: () => {},\n    // noop\n    renderFallbackValue: null,\n    ...options\n  };\n\n  // Create a new table and store it in state\n  const [tableRef] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => ({\n    current: (0,_tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable)(resolvedOptions)\n  }));\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => tableRef.current.initialState);\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater);\n      options.onStateChange == null || options.onStateChange(updater);\n    }\n  }));\n  return tableRef.current;\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/table-core/build/lib/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* binding */ ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* binding */ ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* binding */ ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* binding */ ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* binding */ ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* binding */ ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* binding */ ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* binding */ GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* binding */ GlobalFiltering),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   RowExpanding: () => (/* binding */ RowExpanding),\n/* harmony export */   RowPagination: () => (/* binding */ RowPagination),\n/* harmony export */   RowPinning: () => (/* binding */ RowPinning),\n/* harmony export */   RowSelection: () => (/* binding */ RowSelection),\n/* harmony export */   RowSorting: () => (/* binding */ RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* binding */ _getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* binding */ aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* binding */ buildHeaderGroups),\n/* harmony export */   createCell: () => (/* binding */ createCell),\n/* harmony export */   createColumn: () => (/* binding */ createColumn),\n/* harmony export */   createColumnHelper: () => (/* binding */ createColumnHelper),\n/* harmony export */   createRow: () => (/* binding */ createRow),\n/* harmony export */   createTable: () => (/* binding */ createTable),\n/* harmony export */   defaultColumnSizing: () => (/* binding */ defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* binding */ expandRows),\n/* harmony export */   filterFns: () => (/* binding */ filterFns),\n/* harmony export */   flattenBy: () => (/* binding */ flattenBy),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* binding */ getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* binding */ getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* binding */ getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* binding */ getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* binding */ getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* binding */ getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* binding */ getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* binding */ getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* binding */ getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* binding */ getSortedRowModel),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNumberArray: () => (/* binding */ isNumberArray),\n/* harmony export */   isRowSelected: () => (/* binding */ isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* binding */ isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* binding */ makeStateUpdater),\n/* harmony export */   memo: () => (/* binding */ memo),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   orderColumns: () => (/* binding */ orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* binding */ passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* binding */ reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* binding */ selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* binding */ shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* binding */ sortingFns)\n/* harmony export */ });\n/**\n   * table-core\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nfunction createColumnHelper() {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function' ? {\n        ...column,\n        accessorFn: accessor\n      } : {\n        ...column,\n        accessorKey: accessor\n      };\n    },\n    display: column => column,\n    group: column => column\n  };\n}\n\n// Is this type a tuple?\n\n// If this type is a tuple, what indices are allowed?\n\n///\n\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction noop() {\n  //\n}\nfunction makeStateUpdater(key, instance) {\n  return updater => {\n    instance.setState(old => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, old[key])\n      };\n    });\n  };\n}\nfunction isFunction(d) {\n  return d instanceof Function;\n}\nfunction isNumberArray(d) {\n  return Array.isArray(d) && d.every(val => typeof val === 'number');\n}\nfunction flattenBy(arr, getChildren) {\n  const flat = [];\n  const recurse = subArr => {\n    subArr.forEach(item => {\n      flat.push(item);\n      const children = getChildren(item);\n      if (children != null && children.length) {\n        recurse(children);\n      }\n    });\n  };\n  recurse(arr);\n  return flat;\n}\nfunction memo(getDeps, fn, opts) {\n  let deps = [];\n  let result;\n  return depArgs => {\n    let depTime;\n    if (opts.key && opts.debug) depTime = Date.now();\n    const newDeps = getDeps(depArgs);\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && opts.debug) resultTime = Date.now();\n    result = fn(...newDeps);\n    opts == null || opts.onChange == null || opts.onChange(result);\n    if (opts.key && opts.debug) {\n      if (opts != null && opts.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n        const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n        const resultFpsPercentage = resultEndTime / 16;\n        const pad = (str, num) => {\n          str = String(str);\n          while (str.length < num) {\n            str = ' ' + str;\n          }\n          return str;\n        };\n        console.info(`%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`, `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120))}deg 100% 31%);`, opts == null ? void 0 : opts.key);\n      }\n    }\n    return result;\n  };\n}\nfunction getMemoOptions(tableOptions, debugLevel, key, onChange) {\n  return {\n    debug: () => {\n      var _tableOptions$debugAl;\n      return (_tableOptions$debugAl = tableOptions == null ? void 0 : tableOptions.debugAll) != null ? _tableOptions$debugAl : tableOptions[debugLevel];\n    },\n    key:  true && key,\n    onChange\n  };\n}\n\nfunction createCell(table, row, column, columnId) {\n  const getRenderValue = () => {\n    var _cell$getValue;\n    return (_cell$getValue = cell.getValue()) != null ? _cell$getValue : table.options.renderFallbackValue;\n  };\n  const cell = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(() => [table, column, row, cell], (table, column, row, cell) => ({\n      table,\n      column,\n      row,\n      cell: cell,\n      getValue: cell.getValue,\n      renderValue: cell.renderValue\n    }), getMemoOptions(table.options, 'debugCells', 'cell.getContext'))\n  };\n  table._features.forEach(feature => {\n    feature.createCell == null || feature.createCell(cell, column, row, table);\n  }, {});\n  return cell;\n}\n\nfunction createColumn(table, columnDef, depth, parent) {\n  var _ref, _resolvedColumnDef$id;\n  const defaultColumn = table._getDefaultColumnDef();\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef\n  };\n  const accessorKey = resolvedColumnDef.accessorKey;\n  let id = (_ref = (_resolvedColumnDef$id = resolvedColumnDef.id) != null ? _resolvedColumnDef$id : accessorKey ? typeof String.prototype.replaceAll === 'function' ? accessorKey.replaceAll('.', '_') : accessorKey.replace(/\\./g, '_') : undefined) != null ? _ref : typeof resolvedColumnDef.header === 'string' ? resolvedColumnDef.header : undefined;\n  let accessorFn;\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn;\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = originalRow => {\n        let result = originalRow;\n        for (const key of accessorKey.split('.')) {\n          var _result;\n          result = (_result = result) == null ? void 0 : _result[key];\n          if ( true && result === undefined) {\n            console.warn(`\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`);\n          }\n        }\n        return result;\n      };\n    } else {\n      accessorFn = originalRow => originalRow[resolvedColumnDef.accessorKey];\n    }\n  }\n  if (!id) {\n    if (true) {\n      throw new Error(resolvedColumnDef.accessorFn ? `Columns require an id when using an accessorFn` : `Columns require an id when using a non-string header`);\n    }\n    throw new Error();\n  }\n  let column = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent,\n    depth,\n    columnDef: resolvedColumnDef,\n    columns: [],\n    getFlatColumns: memo(() => [true], () => {\n      var _column$columns;\n      return [column, ...((_column$columns = column.columns) == null ? void 0 : _column$columns.flatMap(d => d.getFlatColumns()))];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')),\n    getLeafColumns: memo(() => [table._getOrderColumnsFn()], orderColumns => {\n      var _column$columns2;\n      if ((_column$columns2 = column.columns) != null && _column$columns2.length) {\n        let leafColumns = column.columns.flatMap(column => column.getLeafColumns());\n        return orderColumns(leafColumns);\n      }\n      return [column];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns'))\n  };\n  for (const feature of table._features) {\n    feature.createColumn == null || feature.createColumn(column, table);\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column;\n}\n\nconst debug = 'debugHeaders';\n//\n\nfunction createHeader(table, column, options) {\n  var _options$id;\n  const id = (_options$id = options.id) != null ? _options$id : column.id;\n  let header = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null,\n    getLeafHeaders: () => {\n      const leafHeaders = [];\n      const recurseHeader = h => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader);\n        }\n        leafHeaders.push(h);\n      };\n      recurseHeader(header);\n      return leafHeaders;\n    },\n    getContext: () => ({\n      table,\n      header: header,\n      column\n    })\n  };\n  table._features.forEach(feature => {\n    feature.createHeader == null || feature.createHeader(header, table);\n  });\n  return header;\n}\nconst Headers = {\n  createTable: table => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      var _left$map$filter, _right$map$filter;\n      const leftColumns = (_left$map$filter = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter : [];\n      const rightColumns = (_right$map$filter = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter : [];\n      const centerColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      const headerGroups = buildHeaderGroups(allColumns, [...leftColumns, ...centerColumns, ...rightColumns], table);\n      return headerGroups;\n    }, getMemoOptions(table.options, debug, 'getHeaderGroups'));\n    table.getCenterHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      leafColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      return buildHeaderGroups(allColumns, leafColumns, table, 'center');\n    }, getMemoOptions(table.options, debug, 'getCenterHeaderGroups'));\n    table.getLeftHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left], (allColumns, leafColumns, left) => {\n      var _left$map$filter2;\n      const orderedLeafColumns = (_left$map$filter2 = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left');\n    }, getMemoOptions(table.options, debug, 'getLeftHeaderGroups'));\n    table.getRightHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.right], (allColumns, leafColumns, right) => {\n      var _right$map$filter2;\n      const orderedLeafColumns = (_right$map$filter2 = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right');\n    }, getMemoOptions(table.options, debug, 'getRightHeaderGroups'));\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getFooterGroups'));\n    table.getLeftFooterGroups = memo(() => [table.getLeftHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getLeftFooterGroups'));\n    table.getCenterFooterGroups = memo(() => [table.getCenterHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getCenterFooterGroups'));\n    table.getRightFooterGroups = memo(() => [table.getRightHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getRightFooterGroups'));\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return headerGroups.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getFlatHeaders'));\n    table.getLeftFlatHeaders = memo(() => [table.getLeftHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeftFlatHeaders'));\n    table.getCenterFlatHeaders = memo(() => [table.getCenterHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getCenterFlatHeaders'));\n    table.getRightFlatHeaders = memo(() => [table.getRightHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getRightFlatHeaders'));\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(() => [table.getCenterFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders;\n        return !((_header$subHeaders = header.subHeaders) != null && _header$subHeaders.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getCenterLeafHeaders'));\n    table.getLeftLeafHeaders = memo(() => [table.getLeftFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders2;\n        return !((_header$subHeaders2 = header.subHeaders) != null && _header$subHeaders2.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getLeftLeafHeaders'));\n    table.getRightLeafHeaders = memo(() => [table.getRightFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders3;\n        return !((_header$subHeaders3 = header.subHeaders) != null && _header$subHeaders3.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getRightLeafHeaders'));\n    table.getLeafHeaders = memo(() => [table.getLeftHeaderGroups(), table.getCenterHeaderGroups(), table.getRightHeaderGroups()], (left, center, right) => {\n      var _left$0$headers, _left$, _center$0$headers, _center$, _right$0$headers, _right$;\n      return [...((_left$0$headers = (_left$ = left[0]) == null ? void 0 : _left$.headers) != null ? _left$0$headers : []), ...((_center$0$headers = (_center$ = center[0]) == null ? void 0 : _center$.headers) != null ? _center$0$headers : []), ...((_right$0$headers = (_right$ = right[0]) == null ? void 0 : _right$.headers) != null ? _right$0$headers : [])].map(header => {\n        return header.getLeafHeaders();\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeafHeaders'));\n  }\n};\nfunction buildHeaderGroups(allColumns, columnsToGroup, table, headerFamily) {\n  var _headerGroups$0$heade, _headerGroups$;\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0;\n  const findMaxDepth = function (columns, depth) {\n    if (depth === void 0) {\n      depth = 1;\n    }\n    maxDepth = Math.max(maxDepth, depth);\n    columns.filter(column => column.getIsVisible()).forEach(column => {\n      var _column$columns;\n      if ((_column$columns = column.columns) != null && _column$columns.length) {\n        findMaxDepth(column.columns, depth + 1);\n      }\n    }, 0);\n  };\n  findMaxDepth(allColumns);\n  let headerGroups = [];\n  const createHeaderGroup = (headersToGroup, depth) => {\n    // The header group we are creating\n    const headerGroup = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: []\n    };\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders = [];\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0];\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth;\n      let column;\n      let isPlaceholder = false;\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent;\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column;\n        isPlaceholder = true;\n      }\n      if (latestPendingParentHeader && (latestPendingParentHeader == null ? void 0 : latestPendingParentHeader.column) === column) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup);\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup == null ? void 0 : headerToGroup.id].filter(Boolean).join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder ? `${pendingParentHeaders.filter(d => d.column === column).length}` : undefined,\n          depth,\n          index: pendingParentHeaders.length\n        });\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup);\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header);\n      }\n      headerGroup.headers.push(headerToGroup);\n      headerToGroup.headerGroup = headerGroup;\n    });\n    headerGroups.push(headerGroup);\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1);\n    }\n  };\n  const bottomHeaders = columnsToGroup.map((column, index) => createHeader(table, column, {\n    depth: maxDepth,\n    index\n  }));\n  createHeaderGroup(bottomHeaders, maxDepth - 1);\n  headerGroups.reverse();\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = headers => {\n    const filteredHeaders = headers.filter(header => header.column.getIsVisible());\n    return filteredHeaders.map(header => {\n      let colSpan = 0;\n      let rowSpan = 0;\n      let childRowSpans = [0];\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = [];\n        recurseHeadersForSpans(header.subHeaders).forEach(_ref => {\n          let {\n            colSpan: childColSpan,\n            rowSpan: childRowSpan\n          } = _ref;\n          colSpan += childColSpan;\n          childRowSpans.push(childRowSpan);\n        });\n      } else {\n        colSpan = 1;\n      }\n      const minChildRowSpan = Math.min(...childRowSpans);\n      rowSpan = rowSpan + minChildRowSpan;\n      header.colSpan = colSpan;\n      header.rowSpan = rowSpan;\n      return {\n        colSpan,\n        rowSpan\n      };\n    });\n  };\n  recurseHeadersForSpans((_headerGroups$0$heade = (_headerGroups$ = headerGroups[0]) == null ? void 0 : _headerGroups$.headers) != null ? _headerGroups$0$heade : []);\n  return headerGroups;\n}\n\nconst createRow = (table, id, original, rowIndex, depth, subRows, parentId) => {\n  let row = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      row._valuesCache[columnId] = column.accessorFn(row.original, rowIndex);\n      return row._valuesCache[columnId];\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)];\n        return row._uniqueValuesCache[columnId];\n      }\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(row.original, rowIndex);\n      return row._uniqueValuesCache[columnId];\n    },\n    renderValue: columnId => {\n      var _row$getValue;\n      return (_row$getValue = row.getValue(columnId)) != null ? _row$getValue : table.options.renderFallbackValue;\n    },\n    subRows: subRows != null ? subRows : [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows = [];\n      let currentRow = row;\n      while (true) {\n        const parentRow = currentRow.getParentRow();\n        if (!parentRow) break;\n        parentRows.push(parentRow);\n        currentRow = parentRow;\n      }\n      return parentRows.reverse();\n    },\n    getAllCells: memo(() => [table.getAllLeafColumns()], leafColumns => {\n      return leafColumns.map(column => {\n        return createCell(table, row, column, column.id);\n      });\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCells')),\n    _getAllCellsByColumnId: memo(() => [row.getAllCells()], allCells => {\n      return allCells.reduce((acc, cell) => {\n        acc[cell.column.id] = cell;\n        return acc;\n      }, {});\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId'))\n  };\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i];\n    feature == null || feature.createRow == null || feature.createRow(row, table);\n  }\n  return row;\n};\n\n//\n\nconst ColumnFaceting = {\n  createColumn: (column, table) => {\n    column._getFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, column.id);\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return column._getFacetedRowModel();\n    };\n    column._getFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, column.id);\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map();\n      }\n      return column._getFacetedUniqueValues();\n    };\n    column._getFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, column.id);\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined;\n      }\n      return column._getFacetedMinMaxValues();\n    };\n  }\n};\n\nconst includesString = (row, columnId, filterValue) => {\n  var _filterValue$toString, _row$getValue;\n  const search = filterValue == null || (_filterValue$toString = filterValue.toString()) == null ? void 0 : _filterValue$toString.toLowerCase();\n  return Boolean((_row$getValue = row.getValue(columnId)) == null || (_row$getValue = _row$getValue.toString()) == null || (_row$getValue = _row$getValue.toLowerCase()) == null ? void 0 : _row$getValue.includes(search));\n};\nincludesString.autoRemove = val => testFalsey(val);\nconst includesStringSensitive = (row, columnId, filterValue) => {\n  var _row$getValue2;\n  return Boolean((_row$getValue2 = row.getValue(columnId)) == null || (_row$getValue2 = _row$getValue2.toString()) == null ? void 0 : _row$getValue2.includes(filterValue));\n};\nincludesStringSensitive.autoRemove = val => testFalsey(val);\nconst equalsString = (row, columnId, filterValue) => {\n  var _row$getValue3;\n  return ((_row$getValue3 = row.getValue(columnId)) == null || (_row$getValue3 = _row$getValue3.toString()) == null ? void 0 : _row$getValue3.toLowerCase()) === (filterValue == null ? void 0 : filterValue.toLowerCase());\n};\nequalsString.autoRemove = val => testFalsey(val);\nconst arrIncludes = (row, columnId, filterValue) => {\n  var _row$getValue4;\n  return (_row$getValue4 = row.getValue(columnId)) == null ? void 0 : _row$getValue4.includes(filterValue);\n};\narrIncludes.autoRemove = val => testFalsey(val);\nconst arrIncludesAll = (row, columnId, filterValue) => {\n  return !filterValue.some(val => {\n    var _row$getValue5;\n    return !((_row$getValue5 = row.getValue(columnId)) != null && _row$getValue5.includes(val));\n  });\n};\narrIncludesAll.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst arrIncludesSome = (row, columnId, filterValue) => {\n  return filterValue.some(val => {\n    var _row$getValue6;\n    return (_row$getValue6 = row.getValue(columnId)) == null ? void 0 : _row$getValue6.includes(val);\n  });\n};\narrIncludesSome.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst equals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) === filterValue;\n};\nequals.autoRemove = val => testFalsey(val);\nconst weakEquals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) == filterValue;\n};\nweakEquals.autoRemove = val => testFalsey(val);\nconst inNumberRange = (row, columnId, filterValue) => {\n  let [min, max] = filterValue;\n  const rowValue = row.getValue(columnId);\n  return rowValue >= min && rowValue <= max;\n};\ninNumberRange.resolveFilterValue = val => {\n  let [unsafeMin, unsafeMax] = val;\n  let parsedMin = typeof unsafeMin !== 'number' ? parseFloat(unsafeMin) : unsafeMin;\n  let parsedMax = typeof unsafeMax !== 'number' ? parseFloat(unsafeMax) : unsafeMax;\n  let min = unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin;\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax;\n  if (min > max) {\n    const temp = min;\n    min = max;\n    max = temp;\n  }\n  return [min, max];\n};\ninNumberRange.autoRemove = val => testFalsey(val) || testFalsey(val[0]) && testFalsey(val[1]);\n\n// Export\n\nconst filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange\n};\n// Utils\n\nfunction testFalsey(val) {\n  return val === undefined || val === null || val === '';\n}\n\n//\n\nconst ColumnFiltering = {\n  getDefaultColumnDef: () => {\n    return {\n      filterFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      columnFilters: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return filterFns.includesString;\n      }\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange;\n      }\n      if (typeof value === 'boolean') {\n        return filterFns.equals;\n      }\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals;\n      }\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes;\n      }\n      return filterFns.weakEquals;\n    };\n    column.getFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      return isFunction(column.columnDef.filterFn) ? column.columnDef.filterFn : column.columnDef.filterFn === 'auto' ? column.getAutoFilterFn() : // @ts-ignore\n      (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[column.columnDef.filterFn]) != null ? _table$options$filter : filterFns[column.columnDef.filterFn];\n    };\n    column.getCanFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2;\n      return ((_column$columnDef$ena = column.columnDef.enableColumnFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnFilters) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && !!column.accessorFn;\n    };\n    column.getIsFiltered = () => column.getFilterIndex() > -1;\n    column.getFilterValue = () => {\n      var _table$getState$colum;\n      return (_table$getState$colum = table.getState().columnFilters) == null || (_table$getState$colum = _table$getState$colum.find(d => d.id === column.id)) == null ? void 0 : _table$getState$colum.value;\n    };\n    column.getFilterIndex = () => {\n      var _table$getState$colum2, _table$getState$colum3;\n      return (_table$getState$colum2 = (_table$getState$colum3 = table.getState().columnFilters) == null ? void 0 : _table$getState$colum3.findIndex(d => d.id === column.id)) != null ? _table$getState$colum2 : -1;\n    };\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn();\n        const previousFilter = old == null ? void 0 : old.find(d => d.id === column.id);\n        const newFilter = functionalUpdate(value, previousFilter ? previousFilter.value : undefined);\n\n        //\n        if (shouldAutoRemoveFilter(filterFn, newFilter, column)) {\n          var _old$filter;\n          return (_old$filter = old == null ? void 0 : old.filter(d => d.id !== column.id)) != null ? _old$filter : [];\n        }\n        const newFilterObj = {\n          id: column.id,\n          value: newFilter\n        };\n        if (previousFilter) {\n          var _old$map;\n          return (_old$map = old == null ? void 0 : old.map(d => {\n            if (d.id === column.id) {\n              return newFilterObj;\n            }\n            return d;\n          })) != null ? _old$map : [];\n        }\n        if (old != null && old.length) {\n          return [...old, newFilterObj];\n        }\n        return [newFilterObj];\n      });\n    };\n  },\n  createRow: (row, _table) => {\n    row.columnFilters = {};\n    row.columnFiltersMeta = {};\n  },\n  createTable: table => {\n    table.setColumnFilters = updater => {\n      const leafColumns = table.getAllLeafColumns();\n      const updateFn = old => {\n        var _functionalUpdate;\n        return (_functionalUpdate = functionalUpdate(updater, old)) == null ? void 0 : _functionalUpdate.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id);\n          if (column) {\n            const filterFn = column.getFilterFn();\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false;\n            }\n          }\n          return true;\n        });\n      };\n      table.options.onColumnFiltersChange == null || table.options.onColumnFiltersChange(updateFn);\n    };\n    table.resetColumnFilters = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      table.setColumnFilters(defaultState ? [] : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnFilters) != null ? _table$initialState$c : []);\n    };\n    table.getPreFilteredRowModel = () => table.getCoreRowModel();\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table);\n      }\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getFilteredRowModel();\n    };\n  }\n};\nfunction shouldAutoRemoveFilter(filterFn, value, column) {\n  return (filterFn && filterFn.autoRemove ? filterFn.autoRemove(value, column) : false) || typeof value === 'undefined' || typeof value === 'string' && !value;\n}\n\nconst sum = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId);\n    return sum + (typeof nextValue === 'number' ? nextValue : 0);\n  }, 0);\n};\nconst min = (columnId, _leafRows, childRows) => {\n  let min;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (min > value || min === undefined && value >= value)) {\n      min = value;\n    }\n  });\n  return min;\n};\nconst max = (columnId, _leafRows, childRows) => {\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (max < value || max === undefined && value >= value)) {\n      max = value;\n    }\n  });\n  return max;\n};\nconst extent = (columnId, _leafRows, childRows) => {\n  let min;\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value;\n      } else {\n        if (min > value) min = value;\n        if (max < value) max = value;\n      }\n    }\n  });\n  return [min, max];\n};\nconst mean = (columnId, leafRows) => {\n  let count = 0;\n  let sum = 0;\n  leafRows.forEach(row => {\n    let value = row.getValue(columnId);\n    if (value != null && (value = +value) >= value) {\n      ++count, sum += value;\n    }\n  });\n  if (count) return sum / count;\n  return;\n};\nconst median = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return;\n  }\n  const values = leafRows.map(row => row.getValue(columnId));\n  if (!isNumberArray(values)) {\n    return;\n  }\n  if (values.length === 1) {\n    return values[0];\n  }\n  const mid = Math.floor(values.length / 2);\n  const nums = values.sort((a, b) => a - b);\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1] + nums[mid]) / 2;\n};\nconst unique = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values());\n};\nconst uniqueCount = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size;\n};\nconst count = (_columnId, leafRows) => {\n  return leafRows.length;\n};\nconst aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count\n};\n\n//\n\nconst ColumnGrouping = {\n  getDefaultColumnDef: () => {\n    return {\n      aggregatedCell: props => {\n        var _toString, _props$getValue;\n        return (_toString = (_props$getValue = props.getValue()) == null || _props$getValue.toString == null ? void 0 : _props$getValue.toString()) != null ? _toString : null;\n      },\n      aggregationFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      grouping: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder'\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old != null && old.includes(column.id)) {\n          return old.filter(d => d !== column.id);\n        }\n        return [...(old != null ? old : []), column.id];\n      });\n    };\n    column.getCanGroup = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableGrouping) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGrouping) != null ? _table$options$enable : true) && (!!column.accessorFn || !!column.columnDef.getGroupingValue);\n    };\n    column.getIsGrouped = () => {\n      var _table$getState$group;\n      return (_table$getState$group = table.getState().grouping) == null ? void 0 : _table$getState$group.includes(column.id);\n    };\n    column.getGroupedIndex = () => {\n      var _table$getState$group2;\n      return (_table$getState$group2 = table.getState().grouping) == null ? void 0 : _table$getState$group2.indexOf(column.id);\n    };\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup();\n      return () => {\n        if (!canGroup) return;\n        column.toggleGrouping();\n      };\n    };\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'number') {\n        return aggregationFns.sum;\n      }\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent;\n      }\n    };\n    column.getAggregationFn = () => {\n      var _table$options$aggreg, _table$options$aggreg2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.aggregationFn) ? column.columnDef.aggregationFn : column.columnDef.aggregationFn === 'auto' ? column.getAutoAggregationFn() : (_table$options$aggreg = (_table$options$aggreg2 = table.options.aggregationFns) == null ? void 0 : _table$options$aggreg2[column.columnDef.aggregationFn]) != null ? _table$options$aggreg : aggregationFns[column.columnDef.aggregationFn];\n    };\n  },\n  createTable: table => {\n    table.setGrouping = updater => table.options.onGroupingChange == null ? void 0 : table.options.onGroupingChange(updater);\n    table.resetGrouping = defaultState => {\n      var _table$initialState$g, _table$initialState;\n      table.setGrouping(defaultState ? [] : (_table$initialState$g = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.grouping) != null ? _table$initialState$g : []);\n    };\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel();\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table);\n      }\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel();\n      }\n      return table._getGroupedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.getIsGrouped = () => !!row.groupingColumnId;\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.columnDef.getGroupingValue)) {\n        return row.getValue(columnId);\n      }\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(row.original);\n      return row._groupingValuesCache[columnId];\n    };\n    row._groupingValuesCache = {};\n  },\n  createCell: (cell, column, row, table) => {\n    cell.getIsGrouped = () => column.getIsGrouped() && column.id === row.groupingColumnId;\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped();\n    cell.getIsAggregated = () => {\n      var _row$subRows;\n      return !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n  }\n};\nfunction orderColumns(leafColumns, grouping, groupedColumnMode) {\n  if (!(grouping != null && grouping.length) || !groupedColumnMode) {\n    return leafColumns;\n  }\n  const nonGroupingColumns = leafColumns.filter(col => !grouping.includes(col.id));\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns;\n  }\n  const groupingColumns = grouping.map(g => leafColumns.find(col => col.id === g)).filter(Boolean);\n  return [...groupingColumns, ...nonGroupingColumns];\n}\n\n//\n\nconst ColumnOrdering = {\n  getInitialState: state => {\n    return {\n      columnOrder: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getIndex = memo(position => [_getVisibleLeafColumns(table, position)], columns => columns.findIndex(d => d.id === column.id), getMemoOptions(table.options, 'debugColumns', 'getIndex'));\n    column.getIsFirstColumn = position => {\n      var _columns$;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns$ = columns[0]) == null ? void 0 : _columns$.id) === column.id;\n    };\n    column.getIsLastColumn = position => {\n      var _columns;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns = columns[columns.length - 1]) == null ? void 0 : _columns.id) === column.id;\n    };\n  },\n  createTable: table => {\n    table.setColumnOrder = updater => table.options.onColumnOrderChange == null ? void 0 : table.options.onColumnOrderChange(updater);\n    table.resetColumnOrder = defaultState => {\n      var _table$initialState$c;\n      table.setColumnOrder(defaultState ? [] : (_table$initialState$c = table.initialState.columnOrder) != null ? _table$initialState$c : []);\n    };\n    table._getOrderColumnsFn = memo(() => [table.getState().columnOrder, table.getState().grouping, table.options.groupedColumnMode], (columnOrder, grouping, groupedColumnMode) => columns => {\n      // Sort grouped columns to the start of the column list\n      // before the headers are built\n      let orderedColumns = [];\n\n      // If there is no order, return the normal columns\n      if (!(columnOrder != null && columnOrder.length)) {\n        orderedColumns = columns;\n      } else {\n        const columnOrderCopy = [...columnOrder];\n\n        // If there is an order, make a copy of the columns\n        const columnsCopy = [...columns];\n\n        // And make a new ordered array of the columns\n\n        // Loop over the columns and place them in order into the new array\n        while (columnsCopy.length && columnOrderCopy.length) {\n          const targetColumnId = columnOrderCopy.shift();\n          const foundIndex = columnsCopy.findIndex(d => d.id === targetColumnId);\n          if (foundIndex > -1) {\n            orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]);\n          }\n        }\n\n        // If there are any columns left, add them to the end\n        orderedColumns = [...orderedColumns, ...columnsCopy];\n      }\n      return orderColumns(orderedColumns, grouping, groupedColumnMode);\n    }, getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn'));\n  }\n};\n\n//\n\nconst getDefaultColumnPinningState = () => ({\n  left: [],\n  right: []\n});\nconst ColumnPinning = {\n  getInitialState: state => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.pin = position => {\n      const columnIds = column.getLeafColumns().map(d => d.id).filter(Boolean);\n      table.setColumnPinning(old => {\n        var _old$left3, _old$right3;\n        if (position === 'right') {\n          var _old$left, _old$right;\n          return {\n            left: ((_old$left = old == null ? void 0 : old.left) != null ? _old$left : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n            right: [...((_old$right = old == null ? void 0 : old.right) != null ? _old$right : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds]\n          };\n        }\n        if (position === 'left') {\n          var _old$left2, _old$right2;\n          return {\n            left: [...((_old$left2 = old == null ? void 0 : old.left) != null ? _old$left2 : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds],\n            right: ((_old$right2 = old == null ? void 0 : old.right) != null ? _old$right2 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n          };\n        }\n        return {\n          left: ((_old$left3 = old == null ? void 0 : old.left) != null ? _old$left3 : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n          right: ((_old$right3 = old == null ? void 0 : old.right) != null ? _old$right3 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n        };\n      });\n    };\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns();\n      return leafColumns.some(d => {\n        var _d$columnDef$enablePi, _ref, _table$options$enable;\n        return ((_d$columnDef$enablePi = d.columnDef.enablePinning) != null ? _d$columnDef$enablePi : true) && ((_ref = (_table$options$enable = table.options.enableColumnPinning) != null ? _table$options$enable : table.options.enablePinning) != null ? _ref : true);\n      });\n    };\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id);\n      const {\n        left,\n        right\n      } = table.getState().columnPinning;\n      const isLeft = leafColumnIds.some(d => left == null ? void 0 : left.includes(d));\n      const isRight = leafColumnIds.some(d => right == null ? void 0 : right.includes(d));\n      return isLeft ? 'left' : isRight ? 'right' : false;\n    };\n    column.getPinnedIndex = () => {\n      var _table$getState$colum, _table$getState$colum2;\n      const position = column.getIsPinned();\n      return position ? (_table$getState$colum = (_table$getState$colum2 = table.getState().columnPinning) == null || (_table$getState$colum2 = _table$getState$colum2[position]) == null ? void 0 : _table$getState$colum2.indexOf(column.id)) != null ? _table$getState$colum : -1 : 0;\n    };\n  },\n  createRow: (row, table) => {\n    row.getCenterVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allCells, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allCells.filter(d => !leftAndRight.includes(d.column.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells'));\n    row.getLeftVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left], (allCells, left) => {\n      const cells = (left != null ? left : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'left'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells'));\n    row.getRightVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.right], (allCells, right) => {\n      const cells = (right != null ? right : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'right'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells'));\n  },\n  createTable: table => {\n    table.setColumnPinning = updater => table.options.onColumnPinningChange == null ? void 0 : table.options.onColumnPinningChange(updater);\n    table.resetColumnPinning = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      return table.setColumnPinning(defaultState ? getDefaultColumnPinningState() : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnPinning) != null ? _table$initialState$c : getDefaultColumnPinningState());\n    };\n    table.getIsSomeColumnsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().columnPinning;\n      if (!position) {\n        var _pinningState$left, _pinningState$right;\n        return Boolean(((_pinningState$left = pinningState.left) == null ? void 0 : _pinningState$left.length) || ((_pinningState$right = pinningState.right) == null ? void 0 : _pinningState$right.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table.getLeftLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left], (allColumns, left) => {\n      return (left != null ? left : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns'));\n    table.getRightLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.right], (allColumns, right) => {\n      return (right != null ? right : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns'));\n    table.getCenterLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allColumns.filter(d => !leftAndRight.includes(d.id));\n    }, getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns'));\n  }\n};\n\nfunction safelyAccessDocument(_document) {\n  return _document || (typeof document !== 'undefined' ? document : null);\n}\n\n//\n\n//\n\nconst defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER\n};\nconst getDefaultColumnSizingInfoState = () => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: []\n});\nconst ColumnSizing = {\n  getDefaultColumnDef: () => {\n    return defaultColumnSizing;\n  },\n  getInitialState: state => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getSize = () => {\n      var _column$columnDef$min, _ref, _column$columnDef$max;\n      const columnSize = table.getState().columnSizing[column.id];\n      return Math.min(Math.max((_column$columnDef$min = column.columnDef.minSize) != null ? _column$columnDef$min : defaultColumnSizing.minSize, (_ref = columnSize != null ? columnSize : column.columnDef.size) != null ? _ref : defaultColumnSizing.size), (_column$columnDef$max = column.columnDef.maxSize) != null ? _column$columnDef$max : defaultColumnSizing.maxSize);\n    };\n    column.getStart = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(0, column.getIndex(position)).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getStart'));\n    column.getAfter = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(column.getIndex(position) + 1).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getAfter'));\n    column.resetSize = () => {\n      table.setColumnSizing(_ref2 => {\n        let {\n          [column.id]: _,\n          ...rest\n        } = _ref2;\n        return rest;\n      });\n    };\n    column.getCanResize = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableResizing) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnResizing) != null ? _table$options$enable : true);\n    };\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id;\n    };\n  },\n  createHeader: (header, table) => {\n    header.getSize = () => {\n      let sum = 0;\n      const recurse = header => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse);\n        } else {\n          var _header$column$getSiz;\n          sum += (_header$column$getSiz = header.column.getSize()) != null ? _header$column$getSiz : 0;\n        }\n      };\n      recurse(header);\n      return sum;\n    };\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1];\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize();\n      }\n      return 0;\n    };\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id);\n      const canResize = column == null ? void 0 : column.getCanResize();\n      return e => {\n        if (!column || !canResize) {\n          return;\n        }\n        e.persist == null || e.persist();\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return;\n          }\n        }\n        const startSize = header.getSize();\n        const columnSizingStart = header ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()]) : [[column.id, column.getSize()]];\n        const clientX = isTouchStartEvent(e) ? Math.round(e.touches[0].clientX) : e.clientX;\n        const newColumnSizing = {};\n        const updateOffset = (eventType, clientXPos) => {\n          if (typeof clientXPos !== 'number') {\n            return;\n          }\n          table.setColumnSizingInfo(old => {\n            var _old$startOffset, _old$startSize;\n            const deltaDirection = table.options.columnResizeDirection === 'rtl' ? -1 : 1;\n            const deltaOffset = (clientXPos - ((_old$startOffset = old == null ? void 0 : old.startOffset) != null ? _old$startOffset : 0)) * deltaDirection;\n            const deltaPercentage = Math.max(deltaOffset / ((_old$startSize = old == null ? void 0 : old.startSize) != null ? _old$startSize : 0), -0.999999);\n            old.columnSizingStart.forEach(_ref3 => {\n              let [columnId, headerSize] = _ref3;\n              newColumnSizing[columnId] = Math.round(Math.max(headerSize + headerSize * deltaPercentage, 0) * 100) / 100;\n            });\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage\n            };\n          });\n          if (table.options.columnResizeMode === 'onChange' || eventType === 'end') {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing\n            }));\n          }\n        };\n        const onMove = clientXPos => updateOffset('move', clientXPos);\n        const onEnd = clientXPos => {\n          updateOffset('end', clientXPos);\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: []\n          }));\n        };\n        const contextDocument = safelyAccessDocument(_contextDocument);\n        const mouseEvents = {\n          moveHandler: e => onMove(e.clientX),\n          upHandler: e => {\n            contextDocument == null || contextDocument.removeEventListener('mousemove', mouseEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('mouseup', mouseEvents.upHandler);\n            onEnd(e.clientX);\n          }\n        };\n        const touchEvents = {\n          moveHandler: e => {\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onMove(e.touches[0].clientX);\n            return false;\n          },\n          upHandler: e => {\n            var _e$touches$;\n            contextDocument == null || contextDocument.removeEventListener('touchmove', touchEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('touchend', touchEvents.upHandler);\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onEnd((_e$touches$ = e.touches[0]) == null ? void 0 : _e$touches$.clientX);\n          }\n        };\n        const passiveIfSupported = passiveEventSupported() ? {\n          passive: false\n        } : false;\n        if (isTouchStartEvent(e)) {\n          contextDocument == null || contextDocument.addEventListener('touchmove', touchEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('touchend', touchEvents.upHandler, passiveIfSupported);\n        } else {\n          contextDocument == null || contextDocument.addEventListener('mousemove', mouseEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('mouseup', mouseEvents.upHandler, passiveIfSupported);\n        }\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id\n        }));\n      };\n    };\n  },\n  createTable: table => {\n    table.setColumnSizing = updater => table.options.onColumnSizingChange == null ? void 0 : table.options.onColumnSizingChange(updater);\n    table.setColumnSizingInfo = updater => table.options.onColumnSizingInfoChange == null ? void 0 : table.options.onColumnSizingInfoChange(updater);\n    table.resetColumnSizing = defaultState => {\n      var _table$initialState$c;\n      table.setColumnSizing(defaultState ? {} : (_table$initialState$c = table.initialState.columnSizing) != null ? _table$initialState$c : {});\n    };\n    table.resetHeaderSizeInfo = defaultState => {\n      var _table$initialState$c2;\n      table.setColumnSizingInfo(defaultState ? getDefaultColumnSizingInfoState() : (_table$initialState$c2 = table.initialState.columnSizingInfo) != null ? _table$initialState$c2 : getDefaultColumnSizingInfoState());\n    };\n    table.getTotalSize = () => {\n      var _table$getHeaderGroup, _table$getHeaderGroup2;\n      return (_table$getHeaderGroup = (_table$getHeaderGroup2 = table.getHeaderGroups()[0]) == null ? void 0 : _table$getHeaderGroup2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getHeaderGroup : 0;\n    };\n    table.getLeftTotalSize = () => {\n      var _table$getLeftHeaderG, _table$getLeftHeaderG2;\n      return (_table$getLeftHeaderG = (_table$getLeftHeaderG2 = table.getLeftHeaderGroups()[0]) == null ? void 0 : _table$getLeftHeaderG2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getLeftHeaderG : 0;\n    };\n    table.getCenterTotalSize = () => {\n      var _table$getCenterHeade, _table$getCenterHeade2;\n      return (_table$getCenterHeade = (_table$getCenterHeade2 = table.getCenterHeaderGroups()[0]) == null ? void 0 : _table$getCenterHeade2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getCenterHeade : 0;\n    };\n    table.getRightTotalSize = () => {\n      var _table$getRightHeader, _table$getRightHeader2;\n      return (_table$getRightHeader = (_table$getRightHeader2 = table.getRightHeaderGroups()[0]) == null ? void 0 : _table$getRightHeader2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getRightHeader : 0;\n    };\n  }\n};\nlet passiveSupported = null;\nfunction passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported;\n  let supported = false;\n  try {\n    const options = {\n      get passive() {\n        supported = true;\n        return false;\n      }\n    };\n    const noop = () => {};\n    window.addEventListener('test', noop, options);\n    window.removeEventListener('test', noop);\n  } catch (err) {\n    supported = false;\n  }\n  passiveSupported = supported;\n  return passiveSupported;\n}\nfunction isTouchStartEvent(e) {\n  return e.type === 'touchstart';\n}\n\n//\n\nconst ColumnVisibility = {\n  getInitialState: state => {\n    return {\n      columnVisibility: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value != null ? value : !column.getIsVisible()\n        }));\n      }\n    };\n    column.getIsVisible = () => {\n      var _ref, _table$getState$colum;\n      const childColumns = column.columns;\n      return (_ref = childColumns.length ? childColumns.some(c => c.getIsVisible()) : (_table$getState$colum = table.getState().columnVisibility) == null ? void 0 : _table$getState$colum[column.id]) != null ? _ref : true;\n    };\n    column.getCanHide = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableHiding) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableHiding) != null ? _table$options$enable : true);\n    };\n    column.getToggleVisibilityHandler = () => {\n      return e => {\n        column.toggleVisibility == null || column.toggleVisibility(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row._getAllVisibleCells = memo(() => [row.getAllCells(), table.getState().columnVisibility], cells => {\n      return cells.filter(cell => cell.column.getIsVisible());\n    }, getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells'));\n    row.getVisibleCells = memo(() => [row.getLeftVisibleCells(), row.getCenterVisibleCells(), row.getRightVisibleCells()], (left, center, right) => [...left, ...center, ...right], getMemoOptions(table.options, 'debugRows', 'getVisibleCells'));\n  },\n  createTable: table => {\n    const makeVisibleColumnsMethod = (key, getColumns) => {\n      return memo(() => [getColumns(), getColumns().filter(d => d.getIsVisible()).map(d => d.id).join('_')], columns => {\n        return columns.filter(d => d.getIsVisible == null ? void 0 : d.getIsVisible());\n      }, getMemoOptions(table.options, 'debugColumns', key));\n    };\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod('getVisibleFlatColumns', () => table.getAllFlatColumns());\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod('getVisibleLeafColumns', () => table.getAllLeafColumns());\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod('getLeftVisibleLeafColumns', () => table.getLeftLeafColumns());\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod('getRightVisibleLeafColumns', () => table.getRightLeafColumns());\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod('getCenterVisibleLeafColumns', () => table.getCenterLeafColumns());\n    table.setColumnVisibility = updater => table.options.onColumnVisibilityChange == null ? void 0 : table.options.onColumnVisibilityChange(updater);\n    table.resetColumnVisibility = defaultState => {\n      var _table$initialState$c;\n      table.setColumnVisibility(defaultState ? {} : (_table$initialState$c = table.initialState.columnVisibility) != null ? _table$initialState$c : {});\n    };\n    table.toggleAllColumnsVisible = value => {\n      var _value;\n      value = (_value = value) != null ? _value : !table.getIsAllColumnsVisible();\n      table.setColumnVisibility(table.getAllLeafColumns().reduce((obj, column) => ({\n        ...obj,\n        [column.id]: !value ? !(column.getCanHide != null && column.getCanHide()) : value\n      }), {}));\n    };\n    table.getIsAllColumnsVisible = () => !table.getAllLeafColumns().some(column => !(column.getIsVisible != null && column.getIsVisible()));\n    table.getIsSomeColumnsVisible = () => table.getAllLeafColumns().some(column => column.getIsVisible == null ? void 0 : column.getIsVisible());\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return e => {\n        var _target;\n        table.toggleAllColumnsVisible((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nfunction _getVisibleLeafColumns(table, position) {\n  return !position ? table.getVisibleLeafColumns() : position === 'center' ? table.getCenterVisibleLeafColumns() : position === 'left' ? table.getLeftVisibleLeafColumns() : table.getRightVisibleLeafColumns();\n}\n\n//\n\nconst GlobalFaceting = {\n  createTable: table => {\n    table._getGlobalFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, '__global__');\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getGlobalFacetedRowModel();\n    };\n    table._getGlobalFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, '__global__');\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map();\n      }\n      return table._getGlobalFacetedUniqueValues();\n    };\n    table._getGlobalFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, '__global__');\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return;\n      }\n      return table._getGlobalFacetedMinMaxValues();\n    };\n  }\n};\n\n//\n\nconst GlobalFiltering = {\n  getInitialState: state => {\n    return {\n      globalFilter: undefined,\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        var _table$getCoreRowMode;\n        const value = (_table$getCoreRowMode = table.getCoreRowModel().flatRows[0]) == null || (_table$getCoreRowMode = _table$getCoreRowMode._getAllCellsByColumnId()[column.id]) == null ? void 0 : _table$getCoreRowMode.getValue();\n        return typeof value === 'string' || typeof value === 'number';\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getCanGlobalFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2, _table$options$getCol;\n      return ((_column$columnDef$ena = column.columnDef.enableGlobalFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGlobalFilter) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && ((_table$options$getCol = table.options.getColumnCanGlobalFilter == null ? void 0 : table.options.getColumnCanGlobalFilter(column)) != null ? _table$options$getCol : true) && !!column.accessorFn;\n    };\n  },\n  createTable: table => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString;\n    };\n    table.getGlobalFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      const {\n        globalFilterFn: globalFilterFn\n      } = table.options;\n      return isFunction(globalFilterFn) ? globalFilterFn : globalFilterFn === 'auto' ? table.getGlobalAutoFilterFn() : (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[globalFilterFn]) != null ? _table$options$filter : filterFns[globalFilterFn];\n    };\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange == null || table.options.onGlobalFilterChange(updater);\n    };\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(defaultState ? undefined : table.initialState.globalFilter);\n    };\n  }\n};\n\n//\n\nconst RowExpanding = {\n  getInitialState: state => {\n    return {\n      expanded: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetExpanded = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetExpanded) != null ? _ref : !table.options.manualExpanding) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetExpanded();\n          queued = false;\n        });\n      }\n    };\n    table.setExpanded = updater => table.options.onExpandedChange == null ? void 0 : table.options.onExpandedChange(updater);\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded != null ? expanded : !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true);\n      } else {\n        table.setExpanded({});\n      }\n    };\n    table.resetExpanded = defaultState => {\n      var _table$initialState$e, _table$initialState;\n      table.setExpanded(defaultState ? {} : (_table$initialState$e = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.expanded) != null ? _table$initialState$e : {});\n    };\n    table.getCanSomeRowsExpand = () => {\n      return table.getPrePaginationRowModel().flatRows.some(row => row.getCanExpand());\n    };\n    table.getToggleAllRowsExpandedHandler = () => {\n      return e => {\n        e.persist == null || e.persist();\n        table.toggleAllRowsExpanded();\n      };\n    };\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n      return expanded === true || Object.values(expanded).some(Boolean);\n    };\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true;\n      }\n      if (!Object.keys(expanded).length) {\n        return false;\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false;\n      }\n\n      // They must all be expanded :shrug:\n      return true;\n    };\n    table.getExpandedDepth = () => {\n      let maxDepth = 0;\n      const rowIds = table.getState().expanded === true ? Object.keys(table.getRowModel().rowsById) : Object.keys(table.getState().expanded);\n      rowIds.forEach(id => {\n        const splitId = id.split('.');\n        maxDepth = Math.max(maxDepth, splitId.length);\n      });\n      return maxDepth;\n    };\n    table.getPreExpandedRowModel = () => table.getSortedRowModel();\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table);\n      }\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel();\n      }\n      return table._getExpandedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        var _expanded;\n        const exists = old === true ? true : !!(old != null && old[row.id]);\n        let oldExpanded = {};\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true;\n          });\n        } else {\n          oldExpanded = old;\n        }\n        expanded = (_expanded = expanded) != null ? _expanded : !exists;\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true\n          };\n        }\n        if (exists && !expanded) {\n          const {\n            [row.id]: _,\n            ...rest\n          } = oldExpanded;\n          return rest;\n        }\n        return old;\n      });\n    };\n    row.getIsExpanded = () => {\n      var _table$options$getIsR;\n      const expanded = table.getState().expanded;\n      return !!((_table$options$getIsR = table.options.getIsRowExpanded == null ? void 0 : table.options.getIsRowExpanded(row)) != null ? _table$options$getIsR : expanded === true || (expanded == null ? void 0 : expanded[row.id]));\n    };\n    row.getCanExpand = () => {\n      var _table$options$getRow, _table$options$enable, _row$subRows;\n      return (_table$options$getRow = table.options.getRowCanExpand == null ? void 0 : table.options.getRowCanExpand(row)) != null ? _table$options$getRow : ((_table$options$enable = table.options.enableExpanding) != null ? _table$options$enable : true) && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true;\n      let currentRow = row;\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true);\n        isFullyExpanded = currentRow.getIsExpanded();\n      }\n      return isFullyExpanded;\n    };\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand();\n      return () => {\n        if (!canExpand) return;\n        row.toggleExpanded();\n      };\n    };\n  }\n};\n\n//\n\nconst defaultPageIndex = 0;\nconst defaultPageSize = 10;\nconst getDefaultPaginationState = () => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize\n});\nconst RowPagination = {\n  getInitialState: state => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...(state == null ? void 0 : state.pagination)\n      }\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table)\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetPageIndex = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetPageIndex) != null ? _ref : !table.options.manualPagination) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetPageIndex();\n          queued = false;\n        });\n      }\n    };\n    table.setPagination = updater => {\n      const safeUpdater = old => {\n        let newState = functionalUpdate(updater, old);\n        return newState;\n      };\n      return table.options.onPaginationChange == null ? void 0 : table.options.onPaginationChange(safeUpdater);\n    };\n    table.resetPagination = defaultState => {\n      var _table$initialState$p;\n      table.setPagination(defaultState ? getDefaultPaginationState() : (_table$initialState$p = table.initialState.pagination) != null ? _table$initialState$p : getDefaultPaginationState());\n    };\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex);\n        const maxPageIndex = typeof table.options.pageCount === 'undefined' || table.options.pageCount === -1 ? Number.MAX_SAFE_INTEGER : table.options.pageCount - 1;\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex));\n        return {\n          ...old,\n          pageIndex\n        };\n      });\n    };\n    table.resetPageIndex = defaultState => {\n      var _table$initialState$p2, _table$initialState;\n      table.setPageIndex(defaultState ? defaultPageIndex : (_table$initialState$p2 = (_table$initialState = table.initialState) == null || (_table$initialState = _table$initialState.pagination) == null ? void 0 : _table$initialState.pageIndex) != null ? _table$initialState$p2 : defaultPageIndex);\n    };\n    table.resetPageSize = defaultState => {\n      var _table$initialState$p3, _table$initialState2;\n      table.setPageSize(defaultState ? defaultPageSize : (_table$initialState$p3 = (_table$initialState2 = table.initialState) == null || (_table$initialState2 = _table$initialState2.pagination) == null ? void 0 : _table$initialState2.pageSize) != null ? _table$initialState$p3 : defaultPageSize);\n    };\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize));\n        const topRowIndex = old.pageSize * old.pageIndex;\n        const pageIndex = Math.floor(topRowIndex / pageSize);\n        return {\n          ...old,\n          pageIndex,\n          pageSize\n        };\n      });\n    };\n    //deprecated\n    table.setPageCount = updater => table.setPagination(old => {\n      var _table$options$pageCo;\n      let newPageCount = functionalUpdate(updater, (_table$options$pageCo = table.options.pageCount) != null ? _table$options$pageCo : -1);\n      if (typeof newPageCount === 'number') {\n        newPageCount = Math.max(-1, newPageCount);\n      }\n      return {\n        ...old,\n        pageCount: newPageCount\n      };\n    });\n    table.getPageOptions = memo(() => [table.getPageCount()], pageCount => {\n      let pageOptions = [];\n      if (pageCount && pageCount > 0) {\n        pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i);\n      }\n      return pageOptions;\n    }, getMemoOptions(table.options, 'debugTable', 'getPageOptions'));\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0;\n    table.getCanNextPage = () => {\n      const {\n        pageIndex\n      } = table.getState().pagination;\n      const pageCount = table.getPageCount();\n      if (pageCount === -1) {\n        return true;\n      }\n      if (pageCount === 0) {\n        return false;\n      }\n      return pageIndex < pageCount - 1;\n    };\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1);\n    };\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1;\n      });\n    };\n    table.firstPage = () => {\n      return table.setPageIndex(0);\n    };\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1);\n    };\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel();\n    table.getPaginationRowModel = () => {\n      if (!table._getPaginationRowModel && table.options.getPaginationRowModel) {\n        table._getPaginationRowModel = table.options.getPaginationRowModel(table);\n      }\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel();\n      }\n      return table._getPaginationRowModel();\n    };\n    table.getPageCount = () => {\n      var _table$options$pageCo2;\n      return (_table$options$pageCo2 = table.options.pageCount) != null ? _table$options$pageCo2 : Math.ceil(table.getRowCount() / table.getState().pagination.pageSize);\n    };\n    table.getRowCount = () => {\n      var _table$options$rowCou;\n      return (_table$options$rowCou = table.options.rowCount) != null ? _table$options$rowCou : table.getPrePaginationRowModel().rows.length;\n    };\n  }\n};\n\n//\n\nconst getDefaultRowPinningState = () => ({\n  top: [],\n  bottom: []\n});\nconst RowPinning = {\n  getInitialState: state => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table)\n    };\n  },\n  createRow: (row, table) => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows ? row.getLeafRows().map(_ref => {\n        let {\n          id\n        } = _ref;\n        return id;\n      }) : [];\n      const parentRowIds = includeParentRows ? row.getParentRows().map(_ref2 => {\n        let {\n          id\n        } = _ref2;\n        return id;\n      }) : [];\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds]);\n      table.setRowPinning(old => {\n        var _old$top3, _old$bottom3;\n        if (position === 'bottom') {\n          var _old$top, _old$bottom;\n          return {\n            top: ((_old$top = old == null ? void 0 : old.top) != null ? _old$top : []).filter(d => !(rowIds != null && rowIds.has(d))),\n            bottom: [...((_old$bottom = old == null ? void 0 : old.bottom) != null ? _old$bottom : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)]\n          };\n        }\n        if (position === 'top') {\n          var _old$top2, _old$bottom2;\n          return {\n            top: [...((_old$top2 = old == null ? void 0 : old.top) != null ? _old$top2 : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)],\n            bottom: ((_old$bottom2 = old == null ? void 0 : old.bottom) != null ? _old$bottom2 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n          };\n        }\n        return {\n          top: ((_old$top3 = old == null ? void 0 : old.top) != null ? _old$top3 : []).filter(d => !(rowIds != null && rowIds.has(d))),\n          bottom: ((_old$bottom3 = old == null ? void 0 : old.bottom) != null ? _old$bottom3 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n        };\n      });\n    };\n    row.getCanPin = () => {\n      var _ref3;\n      const {\n        enableRowPinning,\n        enablePinning\n      } = table.options;\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row);\n      }\n      return (_ref3 = enableRowPinning != null ? enableRowPinning : enablePinning) != null ? _ref3 : true;\n    };\n    row.getIsPinned = () => {\n      const rowIds = [row.id];\n      const {\n        top,\n        bottom\n      } = table.getState().rowPinning;\n      const isTop = rowIds.some(d => top == null ? void 0 : top.includes(d));\n      const isBottom = rowIds.some(d => bottom == null ? void 0 : bottom.includes(d));\n      return isTop ? 'top' : isBottom ? 'bottom' : false;\n    };\n    row.getPinnedIndex = () => {\n      var _ref4, _visiblePinnedRowIds$;\n      const position = row.getIsPinned();\n      if (!position) return -1;\n      const visiblePinnedRowIds = (_ref4 = position === 'top' ? table.getTopRows() : table.getBottomRows()) == null ? void 0 : _ref4.map(_ref5 => {\n        let {\n          id\n        } = _ref5;\n        return id;\n      });\n      return (_visiblePinnedRowIds$ = visiblePinnedRowIds == null ? void 0 : visiblePinnedRowIds.indexOf(row.id)) != null ? _visiblePinnedRowIds$ : -1;\n    };\n  },\n  createTable: table => {\n    table.setRowPinning = updater => table.options.onRowPinningChange == null ? void 0 : table.options.onRowPinningChange(updater);\n    table.resetRowPinning = defaultState => {\n      var _table$initialState$r, _table$initialState;\n      return table.setRowPinning(defaultState ? getDefaultRowPinningState() : (_table$initialState$r = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.rowPinning) != null ? _table$initialState$r : getDefaultRowPinningState());\n    };\n    table.getIsSomeRowsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().rowPinning;\n      if (!position) {\n        var _pinningState$top, _pinningState$bottom;\n        return Boolean(((_pinningState$top = pinningState.top) == null ? void 0 : _pinningState$top.length) || ((_pinningState$bottom = pinningState.bottom) == null ? void 0 : _pinningState$bottom.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      var _table$options$keepPi;\n      const rows = ((_table$options$keepPi = table.options.keepPinnedRows) != null ? _table$options$keepPi : true) ?\n      //get all rows that are pinned even if they would not be otherwise visible\n      //account for expanded parent rows, but not pagination or filtering\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => {\n        const row = table.getRow(rowId, true);\n        return row.getIsAllParentsExpanded() ? row : null;\n      }) :\n      //else get only visible rows that are pinned\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => visibleRows.find(row => row.id === rowId));\n      return rows.filter(Boolean).map(d => ({\n        ...d,\n        position\n      }));\n    };\n    table.getTopRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top], (allRows, topPinnedRowIds) => table._getPinnedRows(allRows, topPinnedRowIds, 'top'), getMemoOptions(table.options, 'debugRows', 'getTopRows'));\n    table.getBottomRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.bottom], (allRows, bottomPinnedRowIds) => table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'), getMemoOptions(table.options, 'debugRows', 'getBottomRows'));\n    table.getCenterRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top, table.getState().rowPinning.bottom], (allRows, top, bottom) => {\n      const topAndBottom = new Set([...(top != null ? top : []), ...(bottom != null ? bottom : [])]);\n      return allRows.filter(d => !topAndBottom.has(d.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterRows'));\n  }\n};\n\n//\n\nconst RowSelection = {\n  getInitialState: state => {\n    return {\n      rowSelection: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    };\n  },\n  createTable: table => {\n    table.setRowSelection = updater => table.options.onRowSelectionChange == null ? void 0 : table.options.onRowSelectionChange(updater);\n    table.resetRowSelection = defaultState => {\n      var _table$initialState$r;\n      return table.setRowSelection(defaultState ? {} : (_table$initialState$r = table.initialState.rowSelection) != null ? _table$initialState$r : {});\n    };\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected();\n        const rowSelection = {\n          ...old\n        };\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows;\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return;\n            }\n            rowSelection[row.id] = true;\n          });\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id];\n          });\n        }\n        return rowSelection;\n      });\n    };\n    table.toggleAllPageRowsSelected = value => table.setRowSelection(old => {\n      const resolvedValue = typeof value !== 'undefined' ? value : !table.getIsAllPageRowsSelected();\n      const rowSelection = {\n        ...old\n      };\n      table.getRowModel().rows.forEach(row => {\n        mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table);\n      });\n      return rowSelection;\n    });\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel();\n    table.getSelectedRowModel = memo(() => [table.getState().rowSelection, table.getCoreRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel'));\n    table.getFilteredSelectedRowModel = memo(() => [table.getState().rowSelection, table.getFilteredRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel'));\n    table.getGroupedSelectedRowModel = memo(() => [table.getState().rowSelection, table.getSortedRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel'));\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows;\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllRowsSelected = Boolean(preGroupedFlatRows.length && Object.keys(rowSelection).length);\n      if (isAllRowsSelected) {\n        if (preGroupedFlatRows.some(row => row.getCanSelect() && !rowSelection[row.id])) {\n          isAllRowsSelected = false;\n        }\n      }\n      return isAllRowsSelected;\n    };\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows.filter(row => row.getCanSelect());\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllPageRowsSelected = !!paginationFlatRows.length;\n      if (isAllPageRowsSelected && paginationFlatRows.some(row => !rowSelection[row.id])) {\n        isAllPageRowsSelected = false;\n      }\n      return isAllPageRowsSelected;\n    };\n    table.getIsSomeRowsSelected = () => {\n      var _table$getState$rowSe;\n      const totalSelected = Object.keys((_table$getState$rowSe = table.getState().rowSelection) != null ? _table$getState$rowSe : {}).length;\n      return totalSelected > 0 && totalSelected < table.getFilteredRowModel().flatRows.length;\n    };\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows;\n      return table.getIsAllPageRowsSelected() ? false : paginationFlatRows.filter(row => row.getCanSelect()).some(d => d.getIsSelected() || d.getIsSomeSelected());\n    };\n    table.getToggleAllRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllRowsSelected(e.target.checked);\n      };\n    };\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllPageRowsSelected(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected();\n      table.setRowSelection(old => {\n        var _opts$selectChildren;\n        value = typeof value !== 'undefined' ? value : !isSelected;\n        if (row.getCanSelect() && isSelected === value) {\n          return old;\n        }\n        const selectedRowIds = {\n          ...old\n        };\n        mutateRowIsSelected(selectedRowIds, row.id, value, (_opts$selectChildren = opts == null ? void 0 : opts.selectChildren) != null ? _opts$selectChildren : true, table);\n        return selectedRowIds;\n      });\n    };\n    row.getIsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isRowSelected(row, rowSelection);\n    };\n    row.getIsSomeSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'some';\n    };\n    row.getIsAllSubRowsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'all';\n    };\n    row.getCanSelect = () => {\n      var _table$options$enable;\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row);\n      }\n      return (_table$options$enable = table.options.enableRowSelection) != null ? _table$options$enable : true;\n    };\n    row.getCanSelectSubRows = () => {\n      var _table$options$enable2;\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row);\n      }\n      return (_table$options$enable2 = table.options.enableSubRowSelection) != null ? _table$options$enable2 : true;\n    };\n    row.getCanMultiSelect = () => {\n      var _table$options$enable3;\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row);\n      }\n      return (_table$options$enable3 = table.options.enableMultiRowSelection) != null ? _table$options$enable3 : true;\n    };\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect();\n      return e => {\n        var _target;\n        if (!canSelect) return;\n        row.toggleSelected((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nconst mutateRowIsSelected = (selectedRowIds, id, value, includeChildren, table) => {\n  var _row$subRows;\n  const row = table.getRow(id, true);\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key]);\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true;\n    }\n  } else {\n    delete selectedRowIds[id];\n  }\n  // }\n\n  if (includeChildren && (_row$subRows = row.subRows) != null && _row$subRows.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row => mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table));\n  }\n};\nfunction selectRowsFn(table, rowModel) {\n  const rowSelection = table.getState().rowSelection;\n  const newSelectedFlatRows = [];\n  const newSelectedRowsById = {};\n\n  // Filters top level and nested rows\n  const recurseRows = function (rows, depth) {\n    return rows.map(row => {\n      var _row$subRows2;\n      const isSelected = isRowSelected(row, rowSelection);\n      if (isSelected) {\n        newSelectedFlatRows.push(row);\n        newSelectedRowsById[row.id] = row;\n      }\n      if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length) {\n        row = {\n          ...row,\n          subRows: recurseRows(row.subRows)\n        };\n      }\n      if (isSelected) {\n        return row;\n      }\n    }).filter(Boolean);\n  };\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById\n  };\n}\nfunction isRowSelected(row, selection) {\n  var _selection$row$id;\n  return (_selection$row$id = selection[row.id]) != null ? _selection$row$id : false;\n}\nfunction isSubRowSelected(row, selection, table) {\n  var _row$subRows3;\n  if (!((_row$subRows3 = row.subRows) != null && _row$subRows3.length)) return false;\n  let allChildrenSelected = true;\n  let someSelected = false;\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return;\n    }\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection);\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true;\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true;\n        allChildrenSelected = false;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n  });\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false;\n}\n\nconst reSplitAlphaNumeric = /([0-9]+)/gm;\nconst alphanumeric = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\nconst alphanumericCaseSensitive = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\nconst datetime = (rowA, rowB, columnId) => {\n  const a = rowA.getValue(columnId);\n  const b = rowB.getValue(columnId);\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0;\n};\nconst basic = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId));\n};\n\n// Utils\n\nfunction compareBasic(a, b) {\n  return a === b ? 0 : a > b ? 1 : -1;\n}\nfunction toString(a) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return '';\n    }\n    return String(a);\n  }\n  if (typeof a === 'string') {\n    return a;\n  }\n  return '';\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr, bStr) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean);\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean);\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift();\n    const bb = b.shift();\n    const an = parseInt(aa, 10);\n    const bn = parseInt(bb, 10);\n    const combo = [an, bn].sort();\n\n    // Both are string\n    if (isNaN(combo[0])) {\n      if (aa > bb) {\n        return 1;\n      }\n      if (bb > aa) {\n        return -1;\n      }\n      continue;\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1])) {\n      return isNaN(an) ? -1 : 1;\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1;\n    }\n    if (bn > an) {\n      return -1;\n    }\n  }\n  return a.length - b.length;\n}\n\n// Exports\n\nconst sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic\n};\n\n//\n\nconst RowSorting = {\n  getInitialState: state => {\n    return {\n      sorting: [],\n      ...state\n    };\n  },\n  getDefaultColumnDef: () => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: e => {\n        return e.shiftKey;\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10);\n      let isString = false;\n      for (const row of firstRows) {\n        const value = row == null ? void 0 : row.getValue(column.id);\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime;\n        }\n        if (typeof value === 'string') {\n          isString = true;\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric;\n          }\n        }\n      }\n      if (isString) {\n        return sortingFns.text;\n      }\n      return sortingFns.basic;\n    };\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return 'asc';\n      }\n      return 'desc';\n    };\n    column.getSortingFn = () => {\n      var _table$options$sortin, _table$options$sortin2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.sortingFn) ? column.columnDef.sortingFn : column.columnDef.sortingFn === 'auto' ? column.getAutoSortingFn() : (_table$options$sortin = (_table$options$sortin2 = table.options.sortingFns) == null ? void 0 : _table$options$sortin2[column.columnDef.sortingFn]) != null ? _table$options$sortin : sortingFns[column.columnDef.sortingFn];\n    };\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder();\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null;\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old == null ? void 0 : old.find(d => d.id === column.id);\n        const existingIndex = old == null ? void 0 : old.findIndex(d => d.id === column.id);\n        let newSorting = [];\n\n        // What should we do with this sort action?\n        let sortAction;\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc';\n\n        // Multi-mode\n        if (old != null && old.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'add';\n          }\n        } else {\n          // Normal mode\n          if (old != null && old.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace';\n          } else if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'replace';\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove';\n            }\n          }\n        }\n        if (sortAction === 'add') {\n          var _table$options$maxMul;\n          newSorting = [...old, {\n            id: column.id,\n            desc: nextDesc\n          }];\n          // Take latest n columns\n          newSorting.splice(0, newSorting.length - ((_table$options$maxMul = table.options.maxMultiSortColCount) != null ? _table$options$maxMul : Number.MAX_SAFE_INTEGER));\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc\n              };\n            }\n            return d;\n          });\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id);\n        } else {\n          newSorting = [{\n            id: column.id,\n            desc: nextDesc\n          }];\n        }\n        return newSorting;\n      });\n    };\n    column.getFirstSortDir = () => {\n      var _ref, _column$columnDef$sor;\n      const sortDescFirst = (_ref = (_column$columnDef$sor = column.columnDef.sortDescFirst) != null ? _column$columnDef$sor : table.options.sortDescFirst) != null ? _ref : column.getAutoSortDir() === 'desc';\n      return sortDescFirst ? 'desc' : 'asc';\n    };\n    column.getNextSortingOrder = multi => {\n      var _table$options$enable, _table$options$enable2;\n      const firstSortDirection = column.getFirstSortDir();\n      const isSorted = column.getIsSorted();\n      if (!isSorted) {\n        return firstSortDirection;\n      }\n      if (isSorted !== firstSortDirection && ((_table$options$enable = table.options.enableSortingRemoval) != null ? _table$options$enable : true) && (\n      // If enableSortRemove, enable in general\n      multi ? (_table$options$enable2 = table.options.enableMultiRemove) != null ? _table$options$enable2 : true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false;\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc';\n    };\n    column.getCanSort = () => {\n      var _column$columnDef$ena, _table$options$enable3;\n      return ((_column$columnDef$ena = column.columnDef.enableSorting) != null ? _column$columnDef$ena : true) && ((_table$options$enable3 = table.options.enableSorting) != null ? _table$options$enable3 : true) && !!column.accessorFn;\n    };\n    column.getCanMultiSort = () => {\n      var _ref2, _column$columnDef$ena2;\n      return (_ref2 = (_column$columnDef$ena2 = column.columnDef.enableMultiSort) != null ? _column$columnDef$ena2 : table.options.enableMultiSort) != null ? _ref2 : !!column.accessorFn;\n    };\n    column.getIsSorted = () => {\n      var _table$getState$sorti;\n      const columnSort = (_table$getState$sorti = table.getState().sorting) == null ? void 0 : _table$getState$sorti.find(d => d.id === column.id);\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc';\n    };\n    column.getSortIndex = () => {\n      var _table$getState$sorti2, _table$getState$sorti3;\n      return (_table$getState$sorti2 = (_table$getState$sorti3 = table.getState().sorting) == null ? void 0 : _table$getState$sorti3.findIndex(d => d.id === column.id)) != null ? _table$getState$sorti2 : -1;\n    };\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old => old != null && old.length ? old.filter(d => d.id !== column.id) : []);\n    };\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort();\n      return e => {\n        if (!canSort) return;\n        e.persist == null || e.persist();\n        column.toggleSorting == null || column.toggleSorting(undefined, column.getCanMultiSort() ? table.options.isMultiSortEvent == null ? void 0 : table.options.isMultiSortEvent(e) : false);\n      };\n    };\n  },\n  createTable: table => {\n    table.setSorting = updater => table.options.onSortingChange == null ? void 0 : table.options.onSortingChange(updater);\n    table.resetSorting = defaultState => {\n      var _table$initialState$s, _table$initialState;\n      table.setSorting(defaultState ? [] : (_table$initialState$s = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.sorting) != null ? _table$initialState$s : []);\n    };\n    table.getPreSortedRowModel = () => table.getGroupedRowModel();\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table);\n      }\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel();\n      }\n      return table._getSortedRowModel();\n    };\n  }\n};\n\nconst builtInFeatures = [Headers, ColumnVisibility, ColumnOrdering, ColumnPinning, ColumnFaceting, ColumnFiltering, GlobalFaceting,\n//depends on ColumnFaceting\nGlobalFiltering,\n//depends on ColumnFiltering\nRowSorting, ColumnGrouping,\n//depends on RowSorting\nRowExpanding, RowPagination, RowPinning, RowSelection, ColumnSizing];\n\n//\n\nfunction createTable(options) {\n  var _options$_features, _options$initialState;\n  if ( true && (options.debugAll || options.debugTable)) {\n    console.info('Creating Table Instance...');\n  }\n  const _features = [...builtInFeatures, ...((_options$_features = options._features) != null ? _options$_features : [])];\n  let table = {\n    _features\n  };\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions == null ? void 0 : feature.getDefaultOptions(table));\n  }, {});\n  const mergeOptions = options => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options);\n    }\n    return {\n      ...defaultOptions,\n      ...options\n    };\n  };\n  const coreInitialState = {};\n  let initialState = {\n    ...coreInitialState,\n    ...((_options$initialState = options.initialState) != null ? _options$initialState : {})\n  };\n  table._features.forEach(feature => {\n    var _feature$getInitialSt;\n    initialState = (_feature$getInitialSt = feature.getInitialState == null ? void 0 : feature.getInitialState(initialState)) != null ? _feature$getInitialSt : initialState;\n  });\n  const queued = [];\n  let queuedTimeout = false;\n  const coreInstance = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb);\n      if (!queuedTimeout) {\n        queuedTimeout = true;\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve().then(() => {\n          while (queued.length) {\n            queued.shift()();\n          }\n          queuedTimeout = false;\n        }).catch(error => setTimeout(() => {\n          throw error;\n        }));\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState);\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options);\n      table.options = mergeOptions(newOptions);\n    },\n    getState: () => {\n      return table.options.state;\n    },\n    setState: updater => {\n      table.options.onStateChange == null || table.options.onStateChange(updater);\n    },\n    _getRowId: (row, index, parent) => {\n      var _table$options$getRow;\n      return (_table$options$getRow = table.options.getRowId == null ? void 0 : table.options.getRowId(row, index, parent)) != null ? _table$options$getRow : `${parent ? [parent.id, index].join('.') : index}`;\n    },\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table);\n      }\n      return table._getCoreRowModel();\n    },\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel();\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id, searchAll) => {\n      let row = (searchAll ? table.getPrePaginationRowModel() : table.getRowModel()).rowsById[id];\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id];\n        if (!row) {\n          if (true) {\n            throw new Error(`getRow could not find row with ID: ${id}`);\n          }\n          throw new Error();\n        }\n      }\n      return row;\n    },\n    _getDefaultColumnDef: memo(() => [table.options.defaultColumn], defaultColumn => {\n      var _defaultColumn;\n      defaultColumn = (_defaultColumn = defaultColumn) != null ? _defaultColumn : {};\n      return {\n        header: props => {\n          const resolvedColumnDef = props.header.column.columnDef;\n          if (resolvedColumnDef.accessorKey) {\n            return resolvedColumnDef.accessorKey;\n          }\n          if (resolvedColumnDef.accessorFn) {\n            return resolvedColumnDef.id;\n          }\n          return null;\n        },\n        // footer: props => props.header.column.id,\n        cell: props => {\n          var _props$renderValue$to, _props$renderValue;\n          return (_props$renderValue$to = (_props$renderValue = props.renderValue()) == null || _props$renderValue.toString == null ? void 0 : _props$renderValue.toString()) != null ? _props$renderValue$to : null;\n        },\n        ...table._features.reduce((obj, feature) => {\n          return Object.assign(obj, feature.getDefaultColumnDef == null ? void 0 : feature.getDefaultColumnDef());\n        }, {}),\n        ...defaultColumn\n      };\n    }, getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')),\n    _getColumnDefs: () => table.options.columns,\n    getAllColumns: memo(() => [table._getColumnDefs()], columnDefs => {\n      const recurseColumns = function (columnDefs, parent, depth) {\n        if (depth === void 0) {\n          depth = 0;\n        }\n        return columnDefs.map(columnDef => {\n          const column = createColumn(table, columnDef, depth, parent);\n          const groupingColumnDef = columnDef;\n          column.columns = groupingColumnDef.columns ? recurseColumns(groupingColumnDef.columns, column, depth + 1) : [];\n          return column;\n        });\n      };\n      return recurseColumns(columnDefs);\n    }, getMemoOptions(options, 'debugColumns', 'getAllColumns')),\n    getAllFlatColumns: memo(() => [table.getAllColumns()], allColumns => {\n      return allColumns.flatMap(column => {\n        return column.getFlatColumns();\n      });\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')),\n    _getAllFlatColumnsById: memo(() => [table.getAllFlatColumns()], flatColumns => {\n      return flatColumns.reduce((acc, column) => {\n        acc[column.id] = column;\n        return acc;\n      }, {});\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')),\n    getAllLeafColumns: memo(() => [table.getAllColumns(), table._getOrderColumnsFn()], (allColumns, orderColumns) => {\n      let leafColumns = allColumns.flatMap(column => column.getLeafColumns());\n      return orderColumns(leafColumns);\n    }, getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')),\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId];\n      if ( true && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`);\n      }\n      return column;\n    }\n  };\n  Object.assign(table, coreInstance);\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index];\n    feature == null || feature.createTable == null || feature.createTable(table);\n  }\n  return table;\n}\n\nfunction getCoreRowModel() {\n  return table => memo(() => [table.options.data], data => {\n    const rowModel = {\n      rows: [],\n      flatRows: [],\n      rowsById: {}\n    };\n    const accessRows = function (originalRows, depth, parentRow) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      const rows = [];\n      for (let i = 0; i < originalRows.length; i++) {\n        // This could be an expensive check at scale, so we should move it somewhere else, but where?\n        // if (!id) {\n        //   if (process.env.NODE_ENV !== 'production') {\n        //     throw new Error(`getRowId expected an ID, but got ${id}`)\n        //   }\n        // }\n\n        // Make the row\n        const row = createRow(table, table._getRowId(originalRows[i], i, parentRow), originalRows[i], i, depth, undefined, parentRow == null ? void 0 : parentRow.id);\n\n        // Keep track of every row in a flat array\n        rowModel.flatRows.push(row);\n        // Also keep track of every row by its ID\n        rowModel.rowsById[row.id] = row;\n        // Push table row into parent\n        rows.push(row);\n\n        // Get the original subrows\n        if (table.options.getSubRows) {\n          var _row$originalSubRows;\n          row.originalSubRows = table.options.getSubRows(originalRows[i], i);\n\n          // Then recursively access them\n          if ((_row$originalSubRows = row.originalSubRows) != null && _row$originalSubRows.length) {\n            row.subRows = accessRows(row.originalSubRows, depth + 1, row);\n          }\n        }\n      }\n      return rows;\n    };\n    rowModel.rows = accessRows(data);\n    return rowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getExpandedRowModel() {\n  return table => memo(() => [table.getState().expanded, table.getPreExpandedRowModel(), table.options.paginateExpandedRows], (expanded, rowModel, paginateExpandedRows) => {\n    if (!rowModel.rows.length || expanded !== true && !Object.keys(expanded != null ? expanded : {}).length) {\n      return rowModel;\n    }\n    if (!paginateExpandedRows) {\n      // Only expand rows at this point if they are being paginated\n      return rowModel;\n    }\n    return expandRows(rowModel);\n  }, getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel'));\n}\nfunction expandRows(rowModel) {\n  const expandedRows = [];\n  const handleRow = row => {\n    var _row$subRows;\n    expandedRows.push(row);\n    if ((_row$subRows = row.subRows) != null && _row$subRows.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow);\n    }\n  };\n  rowModel.rows.forEach(handleRow);\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById\n  };\n}\n\nfunction getFacetedMinMaxValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return undefined;\n    const uniqueValues = facetedRowModel.flatRows.flatMap(flatRow => {\n      var _flatRow$getUniqueVal;\n      return (_flatRow$getUniqueVal = flatRow.getUniqueValues(columnId)) != null ? _flatRow$getUniqueVal : [];\n    }).map(Number).filter(value => !Number.isNaN(value));\n    if (!uniqueValues.length) return;\n    let facetedMinValue = uniqueValues[0];\n    let facetedMaxValue = uniqueValues[uniqueValues.length - 1];\n    for (const value of uniqueValues) {\n      if (value < facetedMinValue) facetedMinValue = value;else if (value > facetedMaxValue) facetedMaxValue = value;\n    }\n    return [facetedMinValue, facetedMaxValue];\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues'));\n}\n\nfunction filterRows(rows, filterRowImpl, table) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table);\n  }\n  return filterRowModelFromRoot(rows, filterRowImpl, table);\n}\nfunction filterRowModelFromLeafs(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea : 100;\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    const rows = [];\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      var _row$subRows;\n      let row = rowsToFilter[i];\n      const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n      newRow.columnFilters = row.columnFilters;\n      if ((_row$subRows = row.subRows) != null && _row$subRows.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n        row = newRow;\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n      } else {\n        row = newRow;\n        if (filterRow(row)) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n        }\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\nfunction filterRowModelFromRoot(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea2;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea2 = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea2 : 100;\n\n  // Filters top level and nested rows\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    // Filter from parents downward first\n\n    const rows = [];\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i];\n      const pass = filterRow(row);\n      if (pass) {\n        var _row$subRows2;\n        if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length && depth < maxDepth) {\n          const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n          row = newRow;\n        }\n        rows.push(row);\n        newFilteredFlatRows.push(row);\n        newFilteredRowsById[row.id] = row;\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\n\nfunction getFacetedRowModel() {\n  return (table, columnId) => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter, table.getFilteredRowModel()], (preRowModel, columnFilters, globalFilter) => {\n    if (!preRowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      return preRowModel;\n    }\n    const filterableIds = [...columnFilters.map(d => d.id).filter(d => d !== columnId), globalFilter ? '__global__' : undefined].filter(Boolean);\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n    return filterRows(preRowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel'));\n}\n\nfunction getFacetedUniqueValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return new Map();\n    let facetedUniqueValues = new Map();\n    for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n      const values = facetedRowModel.flatRows[i].getUniqueValues(columnId);\n      for (let j = 0; j < values.length; j++) {\n        const value = values[j];\n        if (facetedUniqueValues.has(value)) {\n          var _facetedUniqueValues$;\n          facetedUniqueValues.set(value, ((_facetedUniqueValues$ = facetedUniqueValues.get(value)) != null ? _facetedUniqueValues$ : 0) + 1);\n        } else {\n          facetedUniqueValues.set(value, 1);\n        }\n      }\n    }\n    return facetedUniqueValues;\n  }, getMemoOptions(table.options, 'debugTable', `getFacetedUniqueValues_${columnId}`));\n}\n\nfunction getFilteredRowModel() {\n  return table => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter], (rowModel, columnFilters, globalFilter) => {\n    if (!rowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      for (let i = 0; i < rowModel.flatRows.length; i++) {\n        rowModel.flatRows[i].columnFilters = {};\n        rowModel.flatRows[i].columnFiltersMeta = {};\n      }\n      return rowModel;\n    }\n    const resolvedColumnFilters = [];\n    const resolvedGlobalFilters = [];\n    (columnFilters != null ? columnFilters : []).forEach(d => {\n      var _filterFn$resolveFilt;\n      const column = table.getColumn(d.id);\n      if (!column) {\n        return;\n      }\n      const filterFn = column.getFilterFn();\n      if (!filterFn) {\n        if (true) {\n          console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`);\n        }\n        return;\n      }\n      resolvedColumnFilters.push({\n        id: d.id,\n        filterFn,\n        resolvedValue: (_filterFn$resolveFilt = filterFn.resolveFilterValue == null ? void 0 : filterFn.resolveFilterValue(d.value)) != null ? _filterFn$resolveFilt : d.value\n      });\n    });\n    const filterableIds = (columnFilters != null ? columnFilters : []).map(d => d.id);\n    const globalFilterFn = table.getGlobalFilterFn();\n    const globallyFilterableColumns = table.getAllLeafColumns().filter(column => column.getCanGlobalFilter());\n    if (globalFilter && globalFilterFn && globallyFilterableColumns.length) {\n      filterableIds.push('__global__');\n      globallyFilterableColumns.forEach(column => {\n        var _globalFilterFn$resol;\n        resolvedGlobalFilters.push({\n          id: column.id,\n          filterFn: globalFilterFn,\n          resolvedValue: (_globalFilterFn$resol = globalFilterFn.resolveFilterValue == null ? void 0 : globalFilterFn.resolveFilterValue(globalFilter)) != null ? _globalFilterFn$resol : globalFilter\n        });\n      });\n    }\n    let currentColumnFilter;\n    let currentGlobalFilter;\n\n    // Flag the prefiltered row model with each filter state\n    for (let j = 0; j < rowModel.flatRows.length; j++) {\n      const row = rowModel.flatRows[j];\n      row.columnFilters = {};\n      if (resolvedColumnFilters.length) {\n        for (let i = 0; i < resolvedColumnFilters.length; i++) {\n          currentColumnFilter = resolvedColumnFilters[i];\n          const id = currentColumnFilter.id;\n\n          // Tag the row with the column filter state\n          row.columnFilters[id] = currentColumnFilter.filterFn(row, id, currentColumnFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          });\n        }\n      }\n      if (resolvedGlobalFilters.length) {\n        for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n          currentGlobalFilter = resolvedGlobalFilters[i];\n          const id = currentGlobalFilter.id;\n          // Tag the row with the first truthy global filter state\n          if (currentGlobalFilter.filterFn(row, id, currentGlobalFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          })) {\n            row.columnFilters.__global__ = true;\n            break;\n          }\n        }\n        if (row.columnFilters.__global__ !== true) {\n          row.columnFilters.__global__ = false;\n        }\n      }\n    }\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    // Filter final rows using all of the active filters\n    return filterRows(rowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getGroupedRowModel() {\n  return table => memo(() => [table.getState().grouping, table.getPreGroupedRowModel()], (grouping, rowModel) => {\n    if (!rowModel.rows.length || !grouping.length) {\n      rowModel.rows.forEach(row => {\n        row.depth = 0;\n        row.parentId = undefined;\n      });\n      return rowModel;\n    }\n\n    // Filter the grouping list down to columns that exist\n    const existingGrouping = grouping.filter(columnId => table.getColumn(columnId));\n    const groupedFlatRows = [];\n    const groupedRowsById = {};\n    // const onlyGroupedFlatRows: Row[] = [];\n    // const onlyGroupedRowsById: Record<RowId, Row> = {};\n    // const nonGroupedFlatRows: Row[] = [];\n    // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n    // Recursively group the data\n    const groupUpRecursively = function (rows, depth, parentId) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      // Grouping depth has been been met\n      // Stop grouping and simply rewrite thd depth and row relationships\n      if (depth >= existingGrouping.length) {\n        return rows.map(row => {\n          row.depth = depth;\n          groupedFlatRows.push(row);\n          groupedRowsById[row.id] = row;\n          if (row.subRows) {\n            row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id);\n          }\n          return row;\n        });\n      }\n      const columnId = existingGrouping[depth];\n\n      // Group the rows together for this level\n      const rowGroupsMap = groupBy(rows, columnId);\n\n      // Perform aggregations for each group\n      const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map((_ref, index) => {\n        let [groupingValue, groupedRows] = _ref;\n        let id = `${columnId}:${groupingValue}`;\n        id = parentId ? `${parentId}>${id}` : id;\n\n        // First, Recurse to group sub rows before aggregation\n        const subRows = groupUpRecursively(groupedRows, depth + 1, id);\n        subRows.forEach(subRow => {\n          subRow.parentId = id;\n        });\n\n        // Flatten the leaf rows of the rows in this group\n        const leafRows = depth ? flattenBy(groupedRows, row => row.subRows) : groupedRows;\n        const row = createRow(table, id, leafRows[0].original, index, depth, undefined, parentId);\n        Object.assign(row, {\n          groupingColumnId: columnId,\n          groupingValue,\n          subRows,\n          leafRows,\n          getValue: columnId => {\n            // Don't aggregate columns that are in the grouping\n            if (existingGrouping.includes(columnId)) {\n              if (row._valuesCache.hasOwnProperty(columnId)) {\n                return row._valuesCache[columnId];\n              }\n              if (groupedRows[0]) {\n                var _groupedRows$0$getVal;\n                row._valuesCache[columnId] = (_groupedRows$0$getVal = groupedRows[0].getValue(columnId)) != null ? _groupedRows$0$getVal : undefined;\n              }\n              return row._valuesCache[columnId];\n            }\n            if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n              return row._groupingValuesCache[columnId];\n            }\n\n            // Aggregate the values\n            const column = table.getColumn(columnId);\n            const aggregateFn = column == null ? void 0 : column.getAggregationFn();\n            if (aggregateFn) {\n              row._groupingValuesCache[columnId] = aggregateFn(columnId, leafRows, groupedRows);\n              return row._groupingValuesCache[columnId];\n            }\n          }\n        });\n        subRows.forEach(subRow => {\n          groupedFlatRows.push(subRow);\n          groupedRowsById[subRow.id] = subRow;\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        });\n        return row;\n      });\n      return aggregatedGroupedRows;\n    };\n    const groupedRows = groupUpRecursively(rowModel.rows, 0);\n    groupedRows.forEach(subRow => {\n      groupedFlatRows.push(subRow);\n      groupedRowsById[subRow.id] = subRow;\n      // if (subRow.getIsGrouped?.()) {\n      //   onlyGroupedFlatRows.push(subRow);\n      //   onlyGroupedRowsById[subRow.id] = subRow;\n      // } else {\n      //   nonGroupedFlatRows.push(subRow);\n      //   nonGroupedRowsById[subRow.id] = subRow;\n      // }\n    });\n    return {\n      rows: groupedRows,\n      flatRows: groupedFlatRows,\n      rowsById: groupedRowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n    table._queue(() => {\n      table._autoResetExpanded();\n      table._autoResetPageIndex();\n    });\n  }));\n}\nfunction groupBy(rows, columnId) {\n  const groupMap = new Map();\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`;\n    const previous = map.get(resKey);\n    if (!previous) {\n      map.set(resKey, [row]);\n    } else {\n      previous.push(row);\n    }\n    return map;\n  }, groupMap);\n}\n\nfunction getPaginationRowModel(opts) {\n  return table => memo(() => [table.getState().pagination, table.getPrePaginationRowModel(), table.options.paginateExpandedRows ? undefined : table.getState().expanded], (pagination, rowModel) => {\n    if (!rowModel.rows.length) {\n      return rowModel;\n    }\n    const {\n      pageSize,\n      pageIndex\n    } = pagination;\n    let {\n      rows,\n      flatRows,\n      rowsById\n    } = rowModel;\n    const pageStart = pageSize * pageIndex;\n    const pageEnd = pageStart + pageSize;\n    rows = rows.slice(pageStart, pageEnd);\n    let paginatedRowModel;\n    if (!table.options.paginateExpandedRows) {\n      paginatedRowModel = expandRows({\n        rows,\n        flatRows,\n        rowsById\n      });\n    } else {\n      paginatedRowModel = {\n        rows,\n        flatRows,\n        rowsById\n      };\n    }\n    paginatedRowModel.flatRows = [];\n    const handleRow = row => {\n      paginatedRowModel.flatRows.push(row);\n      if (row.subRows.length) {\n        row.subRows.forEach(handleRow);\n      }\n    };\n    paginatedRowModel.rows.forEach(handleRow);\n    return paginatedRowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel'));\n}\n\nfunction getSortedRowModel() {\n  return table => memo(() => [table.getState().sorting, table.getPreSortedRowModel()], (sorting, rowModel) => {\n    if (!rowModel.rows.length || !(sorting != null && sorting.length)) {\n      return rowModel;\n    }\n    const sortingState = table.getState().sorting;\n    const sortedFlatRows = [];\n\n    // Filter out sortings that correspond to non existing columns\n    const availableSorting = sortingState.filter(sort => {\n      var _table$getColumn;\n      return (_table$getColumn = table.getColumn(sort.id)) == null ? void 0 : _table$getColumn.getCanSort();\n    });\n    const columnInfoById = {};\n    availableSorting.forEach(sortEntry => {\n      const column = table.getColumn(sortEntry.id);\n      if (!column) return;\n      columnInfoById[sortEntry.id] = {\n        sortUndefined: column.columnDef.sortUndefined,\n        invertSorting: column.columnDef.invertSorting,\n        sortingFn: column.getSortingFn()\n      };\n    });\n    const sortData = rows => {\n      // This will also perform a stable sorting using the row index\n      // if needed.\n      const sortedData = rows.map(row => ({\n        ...row\n      }));\n      sortedData.sort((rowA, rowB) => {\n        for (let i = 0; i < availableSorting.length; i += 1) {\n          var _sortEntry$desc;\n          const sortEntry = availableSorting[i];\n          const columnInfo = columnInfoById[sortEntry.id];\n          const sortUndefined = columnInfo.sortUndefined;\n          const isDesc = (_sortEntry$desc = sortEntry == null ? void 0 : sortEntry.desc) != null ? _sortEntry$desc : false;\n          let sortInt = 0;\n\n          // All sorting ints should always return in ascending order\n          if (sortUndefined) {\n            const aValue = rowA.getValue(sortEntry.id);\n            const bValue = rowB.getValue(sortEntry.id);\n            const aUndefined = aValue === undefined;\n            const bUndefined = bValue === undefined;\n            if (aUndefined || bUndefined) {\n              if (sortUndefined === 'first') return aUndefined ? -1 : 1;\n              if (sortUndefined === 'last') return aUndefined ? 1 : -1;\n              sortInt = aUndefined && bUndefined ? 0 : aUndefined ? sortUndefined : -sortUndefined;\n            }\n          }\n          if (sortInt === 0) {\n            sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id);\n          }\n\n          // If sorting is non-zero, take care of desc and inversion\n          if (sortInt !== 0) {\n            if (isDesc) {\n              sortInt *= -1;\n            }\n            if (columnInfo.invertSorting) {\n              sortInt *= -1;\n            }\n            return sortInt;\n          }\n        }\n        return rowA.index - rowB.index;\n      });\n\n      // If there are sub-rows, sort them\n      sortedData.forEach(row => {\n        var _row$subRows;\n        sortedFlatRows.push(row);\n        if ((_row$subRows = row.subRows) != null && _row$subRows.length) {\n          row.subRows = sortData(row.subRows);\n        }\n      });\n      return sortedData;\n    };\n    return {\n      rows: sortData(rowModel.rows),\n      flatRows: sortedFlatRows,\n      rowsById: rowModel.rowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () => table._autoResetPageIndex()));\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\n");

/***/ })

};
;