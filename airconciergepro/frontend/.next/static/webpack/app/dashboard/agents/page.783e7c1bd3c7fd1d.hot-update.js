"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agents/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agents/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agents/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AgentsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(app-pages-browser)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AgentsPage() {\n    var _agentsData_data;\n    _s();\n    const { data: agentsData, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)(\"agents\", _lib_api__WEBPACK_IMPORTED_MODULE_2__.agentsAPI.getAll);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 mb-4\",\n                    children: \"Failed to load agents\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"text-primary-600 hover:text-primary-700\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    const agents = Array.isArray(agentsData === null || agentsData === void 0 ? void 0 : (_agentsData_data = agentsData.data) === null || _agentsData_data === void 0 ? void 0 : _agentsData_data.data) ? agentsData.data.data : [];\n    const getStatusColor = (status)=>{\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case \"active\":\n                return \"text-green-600 bg-green-100\";\n            case \"inactive\":\n                return \"text-red-600 bg-red-100\";\n            case \"suspended\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getRoleColor = (role)=>{\n        switch(role === null || role === void 0 ? void 0 : role.toLowerCase()){\n            case \"company_admin\":\n                return \"text-purple-600 bg-purple-100\";\n            case \"agent\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"supervisor\":\n                return \"text-indigo-600 bg-indigo-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Agents\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Manage your team of meet & greet agents\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Total: \",\n                            agents.length,\n                            \" agents\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3\",\n                children: agents.map((agent)=>{\n                    var _agent_role;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow-soft rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 truncate\",\n                                                            children: [\n                                                                agent.first_name,\n                                                                \" \",\n                                                                agent.last_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                agent.email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(agent.status)),\n                                            children: agent.status\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleColor(agent.role)),\n                                                    children: (_agent_role = agent.role) === null || _agent_role === void 0 ? void 0 : _agent_role.replace(\"_\", \" \")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                agent.last_login && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"Last login: \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTimeAgo)(agent.last_login)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Joined \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(agent.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-semibold text-gray-900\",\n                                                        children: agent.total_bookings || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Total Bookings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-semibold text-gray-900\",\n                                                        children: agent.active_bookings || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Active Bookings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                Array.isArray(agent.permissions) && agent.permissions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-4 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-medium text-gray-900 mb-2\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: [\n                                                agent.permissions.slice(0, 3).map((permission, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: permission\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                agent.permissions.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                                                    children: [\n                                                        \"+\",\n                                                        agent.permissions.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this),\n                                Array.isArray(agent.recent_bookings) && agent.recent_bookings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-4 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-medium text-gray-900 mb-2\",\n                                            children: \"Recent Bookings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: agent.recent_bookings.slice(0, 2).map((booking, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: booking.booking_reference\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 flex-shrink-0\",\n                                                            children: booking.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, agent.id, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            agents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                        children: \"No Satff\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Get started by adding your first agent.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(AgentsPage, \"ay9Tnylsk7zHXHdkG5LZi7Ywh/E=\", false, function() {\n    return [\n        react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = AgentsPage;\nvar _c;\n$RefreshReg$(_c, \"AgentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agents/page.tsx\n"));

/***/ })

});