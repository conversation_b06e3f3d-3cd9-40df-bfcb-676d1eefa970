"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agents/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agents/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agents/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AgentsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(app-pages-browser)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AgentsPage() {\n    var _agentsData_data;\n    _s();\n    const { data: agentsData, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)(\"agents\", _lib_api__WEBPACK_IMPORTED_MODULE_2__.agentsAPI.getAll);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 mb-4\",\n                    children: \"Failed to load agents\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"text-primary-600 hover:text-primary-700\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    const agents = Array.isArray(agentsData === null || agentsData === void 0 ? void 0 : (_agentsData_data = agentsData.data) === null || _agentsData_data === void 0 ? void 0 : _agentsData_data.data) ? agentsData.data.data : [];\n    const getStatusColor = (status)=>{\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case \"active\":\n                return \"text-green-600 bg-green-100\";\n            case \"inactive\":\n                return \"text-red-600 bg-red-100\";\n            case \"suspended\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getRoleColor = (role)=>{\n        switch(role === null || role === void 0 ? void 0 : role.toLowerCase()){\n            case \"company_admin\":\n                return \"text-purple-600 bg-purple-100\";\n            case \"agent\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"supervisor\":\n                return \"text-indigo-600 bg-indigo-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Agents\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Manage your team of meet & greet agents\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Total: \",\n                            agents.length,\n                            \" agents\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3\",\n                children: agents.map((agent)=>{\n                    var _agent_role;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow-soft rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 truncate\",\n                                                            children: [\n                                                                agent.first_name,\n                                                                \" \",\n                                                                agent.last_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                agent.email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(agent.status)),\n                                            children: agent.status\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleColor(agent.role)),\n                                                    children: (_agent_role = agent.role) === null || _agent_role === void 0 ? void 0 : _agent_role.replace(\"_\", \" \")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                agent.last_login && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"Last login: \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTimeAgo)(agent.last_login)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Joined \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(agent.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-semibold text-gray-900\",\n                                                        children: agent.total_bookings || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Total Bookings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-semibold text-gray-900\",\n                                                        children: agent.active_bookings || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Active Bookings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                Array.isArray(agent.permissions) && agent.permissions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-4 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-medium text-gray-900 mb-2\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: [\n                                                agent.permissions.slice(0, 3).map((permission, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: permission\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                agent.permissions.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                                                    children: [\n                                                        \"+\",\n                                                        agent.permissions.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this),\n                                Array.isArray(agent.recent_bookings) && agent.recent_bookings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-4 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-medium text-gray-900 mb-2\",\n                                            children: \"Recent Bookings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: agent.recent_bookings.slice(0, 2).map((booking, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: booking.booking_reference\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 flex-shrink-0\",\n                                                            children: booking.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, agent.id, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            agents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                        children: [\n                            \"No \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Satff, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 69\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Get started by adding your first Staff.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/app/dashboard/agents/page.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(AgentsPage, \"ay9Tnylsk7zHXHdkG5LZi7Ywh/E=\", false, function() {\n    return [\n        react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = AgentsPage;\nvar _c;\n$RefreshReg$(_c, \"AgentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agents/page.tsx\n"));

/***/ })

});