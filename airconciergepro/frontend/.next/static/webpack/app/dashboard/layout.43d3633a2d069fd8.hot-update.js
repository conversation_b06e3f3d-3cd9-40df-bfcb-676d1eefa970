"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Calendar,CreditCard,Globe,Home,Plane,Settings,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Bookings\",\n        href: \"/dashboard/bookings\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Customers\",\n        href: \"/dashboard/customers\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Services\",\n        href: \"/dashboard/services\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Airports\",\n        href: \"/dashboard/airports\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Staff\",\n        href: \"/dashboard/agents\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/dashboard/analytics\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Payments\",\n        href: \"/dashboard/payments\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"Partners\",\n        href: \"/dashboard/partners\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction Sidebar(param) {\n    let { sidebarOpen, setSidebarOpen } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, tenant, logout } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const SidebarContent = ()=>{\n        var _user_firstName, _user_lastName, _user_role;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center h-16 px-6 bg-primary-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-xl font-bold text-white\",\n                                children: \"AirConcierge Pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 bg-primary-700 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium\",\n                            children: tenant === null || tenant === void 0 ? void 0 : tenant.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-primary-200 capitalize\",\n                            children: [\n                                tenant === null || tenant === void 0 ? void 0 : tenant.plan,\n                                \" Plan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-4 py-6 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200\", isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-900\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mr-3 h-5 w-5\", isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-600\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0),\n                                                user === null || user === void 0 ? void 0 : (_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName.charAt(0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.firstName,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 capitalize\",\n                                            children: user === null || user === void 0 ? void 0 : (_user_role = user.role) === null || _user_role === void 0 ? void 0 : _user_role.replace(\"_\", \" \")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: logout,\n                            className: \"mt-3 w-full text-left text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Sign out\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Root, {\n                show: sidebarOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-50 lg:hidden\",\n                    onClose: setSidebarOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"transition-opacity ease-linear duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"transition-opacity ease-linear duration-300\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-900/80\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                enter: \"transition ease-in-out duration-300 transform\",\n                                enterFrom: \"-translate-x-full\",\n                                enterTo: \"translate-x-0\",\n                                leave: \"transition ease-in-out duration-300 transform\",\n                                leaveFrom: \"translate-x-0\",\n                                leaveTo: \"-translate-x-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog.Panel, {\n                                    className: \"relative mr-16 flex w-full max-w-xs flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                            enter: \"ease-in-out duration-300\",\n                                            enterFrom: \"opacity-0\",\n                                            enterTo: \"opacity-100\",\n                                            leave: \"ease-in-out duration-300\",\n                                            leaveFrom: \"opacity-100\",\n                                            leaveTo: \"opacity-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-full top-0 flex w-16 justify-center pt-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"-m-2.5 p-2.5\",\n                                                    onClick: ()=>setSidebarOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Close sidebar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Calendar_CreditCard_Globe_Home_Plane_Settings_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white border-r border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"Fd+aYkneB+XzaJSdAOg761J5ZFQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _lib_auth__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});