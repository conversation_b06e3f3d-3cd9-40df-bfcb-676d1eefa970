'use client'

import { useQuery } from 'react-query'
import { paymentsAPI } from '@/lib/api'
import { CreditCard, DollarSign, Calendar, CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react'
import { formatCurrency, formatDate, formatTimeAgo } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function PaymentsPage() {
  const { data: paymentsData, isLoading, error } = useQuery(
    'payments',
    paymentsAPI.getAll
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load payments</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Try again
        </button>
      </div>
    )
  }

  const payments = Array.isArray(paymentsData?.data?.data) ? paymentsData.data.data : []

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return CheckCircle
      case 'failed':
      case 'error':
        return XCircle
      case 'pending':
        return RefreshCw
      case 'refunded':
        return AlertCircle
      default:
        return CreditCard
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'text-green-600 bg-green-100'
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100'
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'refunded':
        return 'text-purple-600 bg-purple-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    return CreditCard // Could be expanded for different payment methods
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payments</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track and manage payment transactions
          </p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {payments.length} payments
        </div>
      </div>

      {/* Payments list */}
      <div className="bg-white shadow-soft rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Payments</h3>
        </div>

        {payments.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {payments.map((payment: any) => {
              const StatusIcon = getStatusIcon(payment.status)
              const PaymentIcon = getPaymentMethodIcon(payment.payment_method)

              return (
                <div key={payment.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                          <PaymentIcon className="h-5 w-5 text-primary-600" />
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900">
                            {payment.booking_reference || payment.transaction_id || 'Payment'}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {payment.status}
                          </span>
                        </div>

                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span>{payment.payment_method || 'Credit Card'}</span>
                          <span>•</span>
                          <span>{formatDate(payment.created_at)}</span>
                          {payment.customer_name && (
                            <>
                              <span>•</span>
                              <span>{payment.customer_name}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          {formatCurrency(payment.amount)}
                        </div>
                        {payment.currency && payment.currency !== 'USD' && (
                          <div className="text-xs text-gray-500">
                            {payment.currency}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Additional payment details */}
                  {(payment.description || payment.fees || payment.refunded_amount) && (
                    <div className="mt-4 pl-14">
                      <div className="space-y-1 text-sm text-gray-600">
                        {payment.description && (
                          <div>{payment.description}</div>
                        )}
                        {payment.fees && (
                          <div>Fees: {formatCurrency(payment.fees)}</div>
                        )}
                        {payment.refunded_amount && (
                          <div className="text-purple-600">
                            Refunded: {formatCurrency(payment.refunded_amount)}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ) : (
          <div className="p-6">
            <div className="text-center py-12">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payments</h3>
              <p className="mt-1 text-sm text-gray-500">
                Payment transactions will appear here once bookings are made.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Payment stats summary */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(
                      payments
                        .filter((p: any) => p.status === 'completed' || p.status === 'success')
                        .reduce((sum: number, p: any) => sum + (parseFloat(p.amount) || 0), 0)
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Successful</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {payments.filter((p: any) => p.status === 'completed' || p.status === 'success').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <RefreshCw className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {payments.filter((p: any) => p.status === 'pending').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Failed</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {payments.filter((p: any) => p.status === 'failed' || p.status === 'error').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
