'use client'

import { useQuery } from 'react-query'
import { customersAPI } from '@/lib/api'
import { Users, Mail, Phone, Calendar, Award, DollarSign } from 'lucide-react'
import { formatCurrency, formatNumber, formatDate } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function CustomersPage() {
  const { data: customersData, isLoading, error } = useQuery(
    'customers',
    customersAPI.getAll
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load customers</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Try again
        </button>
      </div>
    )
  }

  const customers = Array.isArray(customersData?.data?.data?.customers) ? customersData.data.data.customers : []

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your customer database and view booking history
          </p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {customers.length} customers
        </div>
      </div>

      {/* Customers grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {customers.map((customer: any) => (
          <div key={customer.id} className="bg-white overflow-hidden shadow-soft rounded-lg">
            <div className="p-6">
              {/* Customer header */}
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                    <Users className="h-5 w-5 text-primary-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-900 truncate">
                    {customer.first_name} {customer.last_name}
                  </h3>
                  <p className="text-sm text-gray-500 flex items-center">
                    <Mail className="h-3 w-3 mr-1" />
                    {customer.email}
                  </p>
                </div>
              </div>

              {/* Customer details */}
              <div className="space-y-3">
                {customer.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2 text-gray-400" />
                    {customer.phone}
                  </div>
                )}

                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                  Member since {formatDate(customer.created_at)}
                </div>

                <div className="flex items-center text-sm text-gray-600">
                  <Award className="h-4 w-4 mr-2 text-gray-400" />
                  {formatNumber(customer.loyalty_points)} loyalty points
                </div>
              </div>

              {/* Stats */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-2xl font-semibold text-gray-900">
                      {customer.total_bookings}
                    </div>
                    <div className="text-xs text-gray-500">Total Bookings</div>
                  </div>
                  <div>
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(customer.total_spent)}
                    </div>
                    <div className="text-xs text-gray-500">Total Spent</div>
                  </div>
                </div>
              </div>

              {/* Preferences */}
              {customer.preferences && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-xs font-medium text-gray-900 mb-2">Preferences</div>
                  <div className="space-y-1">
                    {customer.preferences.language && (
                      <div className="text-xs text-gray-600">
                        Language: {customer.preferences.language}
                      </div>
                    )}
                    {customer.preferences.specialRequirements?.length > 0 && (
                      <div className="text-xs text-gray-600">
                        Special: {customer.preferences.specialRequirements.join(', ')}
                      </div>
                    )}
                    {customer.preferences.communicationPreferences?.length > 0 && (
                      <div className="text-xs text-gray-600">
                        Contact: {customer.preferences.communicationPreferences.join(', ')}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {customers.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No customers</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first customer.
          </p>
        </div>
      )}
    </div>
  )
}
