'use client'

import { useQuery } from 'react-query'
import { analyticsAPI } from '@/lib/api'
import {
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  Activity,
  MapPin,
  Briefcase
} from 'lucide-react'
import { formatCurrency, formatNumber, formatDate } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface StatsCardProps {
  title: string
  value: string | number
  change?: string
  icon: React.ElementType
  trend?: 'up' | 'down' | 'neutral'
}

function StatsCard({ title, value, change, icon: Icon, trend = 'neutral' }: StatsCardProps) {
  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600',
  }

  return (
    <div className="bg-white overflow-hidden shadow-soft rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {change && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${trendColors[trend]}`}>
                    <TrendingUp className="self-center flex-shrink-0 h-4 w-4" />
                    <span className="ml-1">{change}</span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function AnalyticsPage() {
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery(
    'analytics-dashboard',
    analyticsAPI.getDashboard
  )

  if (dashboardLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const dashboard = dashboardData?.data?.data || {}

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
        <p className="mt-1 text-sm text-gray-500">
          Comprehensive insights into your meet & greet operations
        </p>
      </div>

      {/* Key metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Today's Bookings"
          value={dashboard.todayStats?.today_bookings || 0}
          icon={Calendar}
          trend="up"
        />
        <StatsCard
          title="Active Services"
          value={dashboard.todayStats?.active_services || 0}
          icon={Activity}
          trend="neutral"
        />
        <StatsCard
          title="Today's Revenue"
          value={formatCurrency(dashboard.todayStats?.today_revenue || 0)}
          icon={DollarSign}
          trend="up"
        />
        <StatsCard
          title="Total Customers"
          value={formatNumber(dashboard.todayStats?.total_customers || 0)}
          icon={Users}
          trend="up"
        />
      </div>

      {/* Charts and detailed analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent activities */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
            <p className="mt-1 text-sm text-gray-500">Latest booking updates and system activities</p>
          </div>
          <div className="p-6">
            {dashboard.recentActivities && dashboard.recentActivities.length > 0 ? (
              <div className="space-y-4">
                {dashboard.recentActivities.slice(0, 8).map((activity: any, index: number) => (
                  <div key={`${activity.created_at}-${index}`} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                        <Activity className="h-4 w-4 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <div className="mt-1 flex items-center space-x-2 text-xs text-gray-500">
                        {activity.booking_reference && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-800">
                            {activity.booking_reference}
                          </span>
                        )}
                        {activity.first_name && (
                          <span>{activity.first_name} {activity.last_name}</span>
                        )}
                        <span>•</span>
                        <span>{formatDate(activity.created_at)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No recent activities</p>
            )}
          </div>
        </div>

        {/* Upcoming services */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Upcoming Services</h3>
            <p className="mt-1 text-sm text-gray-500">Services scheduled for the next 4 hours</p>
          </div>
          <div className="p-6">
            {dashboard.upcomingServices && dashboard.upcomingServices.length > 0 ? (
              <div className="space-y-4">
                {dashboard.upcomingServices.map((service: any) => (
                  <div key={service.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <Briefcase className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {service.flight_number} - {service.customer_first_name} {service.customer_last_name}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center">
                        <MapPin className="inline h-3 w-3 mr-1" />
                        {service.meeting_point}
                      </p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(service.estimated_arrival).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No upcoming services in the next 4 hours</p>
            )}
          </div>
        </div>
      </div>

      {/* Performance overview */}
      <div className="bg-white shadow-soft rounded-lg">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
          <p className="mt-1 text-sm text-gray-500">Key performance indicators and trends</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Booking metrics */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <BarChart3 className="h-4 w-4 mr-2" />
                Booking Metrics
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Completion Rate</span>
                  <span className="text-sm font-medium text-green-600">95%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Average Response Time</span>
                  <span className="text-sm font-medium">2.3 min</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Customer Satisfaction</span>
                  <span className="text-sm font-medium text-green-600">4.8/5</span>
                </div>
              </div>
            </div>

            {/* Revenue metrics */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Revenue Metrics
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Average Booking Value</span>
                  <span className="text-sm font-medium">{formatCurrency(125)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Monthly Growth</span>
                  <span className="text-sm font-medium text-green-600">+12%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Revenue per Customer</span>
                  <span className="text-sm font-medium">{formatCurrency(280)}</span>
                </div>
              </div>
            </div>

            {/* Operational metrics */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                Operational Metrics
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Agent Utilization</span>
                  <span className="text-sm font-medium">78%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Peak Hours</span>
                  <span className="text-sm font-medium">6AM - 10AM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Service Coverage</span>
                  <span className="text-sm font-medium text-green-600">24/7</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
