'use client'

import { useQuery } from 'react-query'
import { agentsAPI } from '@/lib/api'
import { User, Mail, Calendar, Briefcase, CheckCircle, XCircle, Clock } from 'lucide-react'
import { formatDate, formatTimeAgo } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function AgentsPage() {
  const { data: agentsData, isLoading, error } = useQuery(
    'agents',
    agentsAPI.getAll
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load agents</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Try again
        </button>
      </div>
    )
  }

  const agents = Array.isArray(agentsData?.data?.data) ? agentsData.data.data : []

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'inactive':
        return 'text-red-600 bg-red-100'
      case 'suspended':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'company_admin':
        return 'text-purple-600 bg-purple-100'
      case 'agent':
        return 'text-blue-600 bg-blue-100'
      case 'supervisor':
        return 'text-indigo-600 bg-indigo-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Agents</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your team of meet & greet agents
          </p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {agents.length} agents
        </div>
      </div>

      {/* Agents grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {agents.map((agent: any) => (
          <div key={agent.id} className="bg-white overflow-hidden shadow-soft rounded-lg">
            <div className="p-6">
              {/* Agent header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                      <User className="h-5 w-5 text-primary-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {agent.first_name} {agent.last_name}
                    </h3>
                    <p className="text-sm text-gray-500 flex items-center">
                      <Mail className="h-3 w-3 mr-1" />
                      {agent.email}
                    </p>
                  </div>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                  {agent.status}
                </span>
              </div>

              {/* Agent details */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(agent.role)}`}>
                    {agent.role?.replace('_', ' ')}
                  </span>
                  {agent.last_login && (
                    <div className="text-xs text-gray-500">
                      Last login: {formatTimeAgo(agent.last_login)}
                    </div>
                  )}
                </div>

                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                  Joined {formatDate(agent.created_at)}
                </div>
              </div>

              {/* Agent stats */}
              <div className="pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-2xl font-semibold text-gray-900">
                      {agent.total_bookings || 0}
                    </div>
                    <div className="text-xs text-gray-500">Total Bookings</div>
                  </div>
                  <div>
                    <div className="text-2xl font-semibold text-gray-900">
                      {agent.active_bookings || 0}
                    </div>
                    <div className="text-xs text-gray-500">Active Bookings</div>
                  </div>
                </div>
              </div>

              {/* Permissions */}
              {Array.isArray(agent.permissions) && agent.permissions.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-xs font-medium text-gray-900 mb-2">Permissions</div>
                  <div className="flex flex-wrap gap-1">
                    {agent.permissions.slice(0, 3).map((permission: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {permission}
                      </span>
                    ))}
                    {agent.permissions.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                        +{agent.permissions.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Recent bookings */}
              {Array.isArray(agent.recent_bookings) && agent.recent_bookings.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-xs font-medium text-gray-900 mb-2">Recent Bookings</div>
                  <div className="space-y-1">
                    {agent.recent_bookings.slice(0, 2).map((booking: any, index: number) => (
                      <div key={index} className="text-xs text-gray-600 flex items-center justify-between">
                        <span className="truncate">{booking.booking_reference}</span>
                        <span className="ml-2 flex-shrink-0">{booking.status}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {agents.length === 0 && (
        <div className="text-center py-12">
          <User className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No Staff</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first Staff.
          </p>
        </div>
      )}
    </div>
  )
}
