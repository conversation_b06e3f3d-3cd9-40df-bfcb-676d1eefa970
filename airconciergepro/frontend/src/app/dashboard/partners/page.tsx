'use client'

import { useQuery } from 'react-query'
import { partnersAPI } from '@/lib/api'
import { Users, Key, Activity, DollarSign, Calendar, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { formatCurrency, formatNumber, formatDate, formatTimeAgo } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function PartnersPage() {
  const { data: partnersData, isLoading, error } = useQuery(
    'partners',
    partnersAPI.getAll
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load partners</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Try again
        </button>
      </div>
    )
  }

  const partners = Array.isArray(partnersData?.data?.data) ? partnersData.data.data : []

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return CheckCircle
      case 'inactive':
        return XCircle
      case 'suspended':
        return AlertTriangle
      default:
        return Key
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'inactive':
        return 'text-red-600 bg-red-100'
      case 'suspended':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getUsagePercentage = (used: number, limit: number) => {
    if (!limit) return 0
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Partners</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage API partners and integration access
          </p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {partners.length} partners
        </div>
      </div>

      {/* Partners grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {partners.map((partner: any) => {
          const StatusIcon = getStatusIcon(partner.status)
          const usagePercentage = getUsagePercentage(partner.usage_count, partner.usage_limit)

          return (
            <div key={partner.id} className="bg-white overflow-hidden shadow-soft rounded-lg">
              <div className="p-6">
                {/* Partner header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                        <Key className="h-5 w-5 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {partner.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        API Partner
                      </p>
                    </div>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(partner.status)}`}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {partner.status}
                  </span>
                </div>

                {/* Usage stats */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>API Usage</span>
                    <span>{formatNumber(partner.usage_count)} / {formatNumber(partner.usage_limit)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(usagePercentage)}`}
                      style={{ width: `${usagePercentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* Partner details */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    Created {formatDate(partner.created_at)}
                  </div>

                  {partner.last_used && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Activity className="h-4 w-4 mr-2 text-gray-400" />
                      Last used {formatTimeAgo(partner.last_used)}
                    </div>
                  )}

                  {partner.expires_at && (
                    <div className="flex items-center text-sm text-gray-600">
                      <AlertTriangle className="h-4 w-4 mr-2 text-gray-400" />
                      Expires {formatDate(partner.expires_at)}
                    </div>
                  )}
                </div>

                {/* Revenue stats */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-2xl font-semibold text-gray-900">
                        {partner.total_bookings || 0}
                      </div>
                      <div className="text-xs text-gray-500">Total Bookings</div>
                    </div>
                    <div>
                      <div className="text-2xl font-semibold text-gray-900">
                        {formatCurrency(partner.total_revenue || 0)}
                      </div>
                      <div className="text-xs text-gray-500">Total Revenue</div>
                    </div>
                  </div>
                </div>

                {/* Permissions */}
                {Array.isArray(partner.permissions) && partner.permissions.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="text-xs font-medium text-gray-900 mb-2">Permissions</div>
                    <div className="flex flex-wrap gap-1">
                      {partner.permissions.slice(0, 3).map((permission: string, index: number) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {permission.replace('_', ' ')}
                        </span>
                      ))}
                      {partner.permissions.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          +{partner.permissions.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Empty state */}
      {partners.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No partners</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first API partner.
          </p>
        </div>
      )}

      {/* Summary stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Partners</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {partners.filter((p: any) => p.status === 'active').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total API Calls</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {formatNumber(partners.reduce((sum: number, p: any) => sum + (p.usage_count || 0), 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Partner Revenue</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(partners.reduce((sum: number, p: any) => sum + (parseFloat(p.total_revenue) || 0), 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Partner Bookings</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {formatNumber(partners.reduce((sum: number, p: any) => sum + (parseInt(p.total_bookings) || 0), 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
