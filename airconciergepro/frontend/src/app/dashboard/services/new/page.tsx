'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useMutation, useQueryClient } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { ArrowLeft, Plus, X } from 'lucide-react'
import Link from 'next/link'

interface ServiceFormData {
  name: string
  description: string
  category: string
  type: string
  basePrice: number
  currency: string
  durationMinutes: number
  maxPassengers: number
  availableAirports: string[]
  availableTerminals: Record<string, string[]>
  requirements: string[]
  inclusions: string[]
}

const initialFormData: ServiceFormData = {
  name: '',
  description: '',
  category: 'meet_greet',
  type: 'arrival',
  basePrice: 0,
  currency: 'INR',
  durationMinutes: 60,
  maxPassengers: 1,
  availableAirports: [],
  availableTerminals: {},
  requirements: [],
  inclusions: []
}

const categories = [
  { value: 'meet_greet', label: 'Meet & Greet' },
  { value: 'fast_track', label: 'Fast Track' },
  { value: 'lounge_access', label: 'Lounge Access' },
  { value: 'vip_terminal', label: 'VIP Terminal' },
  { value: 'transfer', label: 'Transfer' },
  { value: 'porter', label: 'Porter' }
]

const types = [
  { value: 'arrival', label: 'Arrival' },
  { value: 'departure', label: 'Departure' },
  { value: 'transit', label: 'Transit' }
]

const commonAirports = [
  'DEL', 'BOM', 'BLR', 'MAA', 'HYD', 'CCU', 'AMD', 'COK', 'GOI', 'PNQ',
  'JAI', 'LKO', 'IXC', 'GAU', 'IXB', 'TRV', 'CJB', 'IXM', 'VNS', 'IXR'
]

export default function NewServicePage() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const [formData, setFormData] = useState<ServiceFormData>(initialFormData)
  const [newAirport, setNewAirport] = useState('')
  const [newRequirement, setNewRequirement] = useState('')
  const [newInclusion, setNewInclusion] = useState('')

  const createServiceMutation = useMutation(servicesAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries('services')
      router.push('/dashboard/services')
    },
    onError: (error: any) => {
      console.error('Failed to create service:', error)
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    createServiceMutation.mutate(formData)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  const addAirport = (airport: string) => {
    if (airport && !formData.availableAirports.includes(airport)) {
      setFormData(prev => ({
        ...prev,
        availableAirports: [...prev.availableAirports, airport]
      }))
    }
  }

  const removeAirport = (airport: string) => {
    setFormData(prev => {
      const newTerminals = { ...prev.availableTerminals };
      delete newTerminals[airport]; // Remove terminal mapping for this airport

      return {
        ...prev,
        availableAirports: prev.availableAirports.filter(a => a !== airport),
        availableTerminals: newTerminals
      };
    });
  }

  const addRequirement = () => {
    if (newRequirement.trim() && !formData.requirements.includes(newRequirement.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, newRequirement.trim()]
      }))
      setNewRequirement('')
    }
  }

  const removeRequirement = (requirement: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter(r => r !== requirement)
    }))
  }

  const addInclusion = () => {
    if (newInclusion.trim() && !formData.inclusions.includes(newInclusion.trim())) {
      setFormData(prev => ({
        ...prev,
        inclusions: [...prev.inclusions, newInclusion.trim()]
      }))
      setNewInclusion('')
    }
  }

  const removeInclusion = (inclusion: string) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.filter(i => i !== inclusion)
    }))
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/dashboard/services"
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Services
        </Link>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900">Create New Service</h1>
        <p className="mt-1 text-sm text-gray-500">
          Add a new service to your offerings
        </p>
      </div>

      {/* Service creation form */}
      <div className="bg-white shadow-soft rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Service Name *
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="e.g., Premium Meet & Greet"
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category *
                </label>
                <select
                  name="category"
                  id="category"
                  required
                  value={formData.category}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  Service Type *
                </label>
                <select
                  name="type"
                  id="type"
                  required
                  value={formData.type}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  {types.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="maxPassengers" className="block text-sm font-medium text-gray-700">
                  Max Passengers *
                </label>
                <input
                  type="number"
                  name="maxPassengers"
                  id="maxPassengers"
                  required
                  min="1"
                  value={formData.maxPassengers}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="mt-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                name="description"
                id="description"
                required
                rows={3}
                value={formData.description}
                onChange={handleInputChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Describe what this service includes..."
              />
            </div>
          </div>

          {/* Pricing & Duration */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Pricing & Duration</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
              <div>
                <label htmlFor="basePrice" className="block text-sm font-medium text-gray-700">
                  Base Price *
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">₹</span>
                  </div>
                  <input
                    type="number"
                    name="basePrice"
                    id="basePrice"
                    required
                    min="0"
                    step="0.01"
                    value={formData.basePrice}
                    onChange={handleInputChange}
                    className="block w-full pl-7 pr-12 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="0.00"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">INR</span>
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="durationMinutes" className="block text-sm font-medium text-gray-700">
                  Duration (minutes) *
                </label>
                <input
                  type="number"
                  name="durationMinutes"
                  id="durationMinutes"
                  required
                  min="1"
                  value={formData.durationMinutes}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
                  Currency
                </label>
                <input
                  type="text"
                  name="currency"
                  id="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* Available Airports */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Available Airports</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select from common airports:
                </label>
                <div className="flex flex-wrap gap-2">
                  {commonAirports.map(airport => (
                    <button
                      key={airport}
                      type="button"
                      onClick={() => addAirport(airport)}
                      disabled={formData.availableAirports.includes(airport)}
                      className={`px-3 py-1 text-sm rounded-md border ${
                        formData.availableAirports.includes(airport)
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {airport}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Or add custom airport code:
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newAirport}
                    onChange={(e) => setNewAirport(e.target.value.toUpperCase())}
                    placeholder="e.g., DEL"
                    maxLength={3}
                    className="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      addAirport(newAirport)
                      setNewAirport('')
                    }}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {formData.availableAirports.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Selected airports:
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {formData.availableAirports.map(airport => (
                      <span
                        key={airport}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800"
                      >
                        {airport}
                        <button
                          type="button"
                          onClick={() => removeAirport(airport)}
                          className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-primary-600 hover:bg-primary-200 hover:text-primary-800"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Terminal Mapping */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Terminal Mapping</h3>
            <p className="text-sm text-gray-500 mb-4">
              Specify which terminals at each airport this service is available at.
            </p>

            <div className="space-y-4">
              {formData.availableAirports.map((airportCode) => (
                <div key={`terminal-${airportCode}`} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                      {airportCode}
                    </span>
                    Terminals for {airportCode} Airport
                  </h4>

                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                    {['T1', 'T2', 'T3', 'T4'].map((terminal) => (
                      <label key={terminal} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={formData.availableTerminals[airportCode]?.includes(terminal) || false}
                          onChange={(e) => {
                            const currentTerminals = formData.availableTerminals[airportCode] || [];
                            const newTerminals = e.target.checked
                              ? [...currentTerminals, terminal]
                              : currentTerminals.filter(t => t !== terminal);

                            setFormData(prev => ({
                              ...prev,
                              availableTerminals: {
                                ...prev.availableTerminals,
                                [airportCode]: newTerminals
                              }
                            }));
                          }}
                        />
                        <div className="ml-3">
                          <span className="text-sm font-medium text-gray-900">{terminal}</span>
                          <p className="text-xs text-gray-500">Terminal {terminal.slice(1)}</p>
                        </div>
                      </label>
                    ))}
                  </div>

                  {/* Quick select options */}
                  <div className="mt-3 flex space-x-2">
                    <button
                      type="button"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          availableTerminals: {
                            ...prev.availableTerminals,
                            [airportCode]: ['T1', 'T2', 'T3', 'T4']
                          }
                        }));
                      }}
                      className="text-xs px-2 py-1 bg-primary-100 text-primary-700 rounded hover:bg-primary-200"
                    >
                      Select All
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          availableTerminals: {
                            ...prev.availableTerminals,
                            [airportCode]: []
                          }
                        }));
                      }}
                      className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                      Clear All
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          availableTerminals: {
                            ...prev.availableTerminals,
                            [airportCode]: ['T1', 'T2']
                          }
                        }));
                      }}
                      className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                    >
                      Main Terminals (T1, T2)
                    </button>
                  </div>
                </div>
              ))}

              {formData.availableAirports.length === 0 && (
                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-gray-400 mb-2">
                    <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-500">
                    Add airports above to configure terminal mapping
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Requirements */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Requirements</h3>
            <div className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newRequirement}
                  onChange={(e) => setNewRequirement(e.target.value)}
                  placeholder="e.g., Valid ID required"
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
                <button
                  type="button"
                  onClick={addRequirement}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>

              {formData.requirements.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Requirements:
                  </label>
                  <div className="space-y-2">
                    {formData.requirements.map((requirement, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                      >
                        <span className="text-sm text-gray-700">{requirement}</span>
                        <button
                          type="button"
                          onClick={() => removeRequirement(requirement)}
                          className="inline-flex items-center justify-center w-6 h-6 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Inclusions */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Inclusions</h3>
            <div className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newInclusion}
                  onChange={(e) => setNewInclusion(e.target.value)}
                  placeholder="e.g., Personal assistant"
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
                <button
                  type="button"
                  onClick={addInclusion}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>

              {formData.inclusions.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Inclusions:
                  </label>
                  <div className="space-y-2">
                    {formData.inclusions.map((inclusion, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                      >
                        <span className="text-sm text-gray-700">{inclusion}</span>
                        <button
                          type="button"
                          onClick={() => removeInclusion(inclusion)}
                          className="inline-flex items-center justify-center w-6 h-6 rounded-full text-gray-400 hover:bg-gray-200 hover:text-gray-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Link
              href="/dashboard/services"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={createServiceMutation.isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {createServiceMutation.isLoading ? 'Creating...' : 'Create Service'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
