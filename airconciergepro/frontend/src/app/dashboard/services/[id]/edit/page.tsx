'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { useForm, useFieldArray } from 'react-hook-form'
import { ArrowLeft, Plus, X, Building2 } from 'lucide-react'
import Link from 'next/link'
import Cookies from 'js-cookie'
import toast from 'react-hot-toast'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface ServiceFormData {
  name: string
  description: string
  category: string
  type: string
  base_price: string
  currency: string
  duration_minutes: number
  max_passengers: number
  available_airports: Array<{ value: string } | string>
  available_terminals: Record<string, string[]>
  requirements: Array<{ value: string } | string>
  inclusions: Array<{ value: string } | string>
  status: string
}

export default function EditServicePage() {
  const router = useRouter()
  const params = useParams()
  const queryClient = useQueryClient()
  const serviceId = params?.id as string

  // Fetch existing service data
  const { data: serviceData, isLoading: isLoadingService, error, refetch } = useQuery(
    ['service', serviceId],
    () => servicesAPI.getById(serviceId),
    {
      enabled: !!serviceId,
      staleTime: 0, // Always fetch fresh data
      cacheTime: 0, // Don't cache
    }
  )

  // Fetch tenant airports
  const { data: airportsData, isLoading: isLoadingAirports, error: airportsError } = useQuery(
    ['tenant-airports'],
    async () => {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}/airports/tenant`, {
        headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch airports: ${response.status}`);
      }

      const data = await response.json();
      console.log('Airports API response:', data);
      return data;
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  // Fetch terminals for airports
  const { data: terminalsData, isLoading: isLoadingTerminals } = useQuery(
    ['airport-terminals', airportsData?.data?.airports],
    async () => {
      if (!airportsData?.data?.airports?.length) return null

      const airportIds = airportsData.data.airports.map((airport: any) => {
        console.log('Airport object:', airport);
        return airport.airport_id || airport.id;
      })
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}/terminals/airports/batch`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Cookies.get('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ airportIds })
      })
      return response.json()
    },
    {
      enabled: !!airportsData?.data?.airports?.length,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  // Handle the nested response structure: serviceData.data.data.service
  const service = serviceData?.data?.data?.service

  // Debug logging (remove in production)
  // console.log('serviceData:', serviceData)
  // console.log('service:', service)
  // console.log('airportsData:', airportsData)
  // console.log('terminalsData:', terminalsData)

  const {
    register,
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ServiceFormData>({
    defaultValues: {
      name: '',
      description: '',
      category: 'meet_greet',
      type: 'arrival',
      base_price: '',
      currency: 'INR',
      duration_minutes: 60,
      max_passengers: 1,
      available_airports: [],
      available_terminals: {},
      requirements: [],
      inclusions: [],
      status: 'active',
    },
  })

  const {
    fields: airportFields,
    append: appendAirport,
    remove: removeAirport,
    replace: replaceAirports,
  } = useFieldArray({
    control,
    name: 'available_airports',
  })

  const {
    fields: requirementFields,
    append: appendRequirement,
    remove: removeRequirement,
    replace: replaceRequirements,
  } = useFieldArray({
    control,
    name: 'requirements',
  })

  const {
    fields: inclusionFields,
    append: appendInclusion,
    remove: removeInclusion,
    replace: replaceInclusions,
  } = useFieldArray({
    control,
    name: 'inclusions',
  })

  // Populate form with existing data
  useEffect(() => {
    if (service) {
      console.log('Loading service data:', service); // Debug log

      const serviceData = {
        name: service.name || '',
        description: service.description || '',
        category: service.category || 'meet_greet',
        type: service.type || 'arrival',
        base_price: service.base_price?.toString() || '',
        currency: service.currency || 'INR',
        duration_minutes: service.duration_minutes || 60,
        max_passengers: service.max_passengers || 1,
        available_airports: service.available_airports || [],
        available_terminals: service.available_terminals || {},
        requirements: service.requirements || [],
        inclusions: service.inclusions || [],
        status: service.status || 'active',
      };

      console.log('Service data for form:', serviceData); // Debug log

      reset(serviceData);

      // Sync field arrays - ensure we're using string values
      const airportFields = (service.available_airports || []).map(airport => ({
        value: typeof airport === 'string' ? airport : airport.value || airport
      }));
      const requirementFields = (service.requirements || []).map(req => ({
        value: typeof req === 'string' ? req : req.value || req
      }));
      const inclusionFields = (service.inclusions || []).map(inc => ({
        value: typeof inc === 'string' ? inc : inc.value || inc
      }));

      console.log('Airport fields:', airportFields); // Debug log

      replaceAirports(airportFields);
      replaceRequirements(requirementFields);
      replaceInclusions(inclusionFields);

    } else if (!isLoadingService && !service) {
      // Demo data for testing terminal mapping UI when service data is not available
      const demoData = {
        name: 'Meet n Greet Demo Service',
        description: 'Premium meet and greet service with terminal mapping',
        category: 'meet_greet',
        type: 'arrival',
        base_price: '100',
        currency: 'INR',
        duration_minutes: 60,
        max_passengers: 1,
        available_airports: ['DEL', 'COK', 'CCJ', 'TRV'],
        available_terminals: {
          'DEL': ['T1', 'T2'],
          'COK': ['T1'],
          'CCJ': ['T1'],
          'TRV': ['T1']
        },
        requirements: ['Valid ID required'],
        inclusions: ['Personal agent', 'Fast track'],
        status: 'active',
      };

      reset(demoData);

      // Sync field arrays with demo data
      replaceAirports(demoData.available_airports.map(airport => ({ value: airport })));
      replaceRequirements(demoData.requirements.map(req => ({ value: req })));
      replaceInclusions(demoData.inclusions.map(inc => ({ value: inc })));
    }
  }, [service, reset, isLoadingService, replaceAirports, replaceRequirements, replaceInclusions])

  const updateServiceMutation = useMutation(
    (data: any) => servicesAPI.update(serviceId, data),
    {
      onSuccess: () => {
        toast.success('Service updated successfully!')
        queryClient.invalidateQueries(['services'])
        queryClient.invalidateQueries(['service', serviceId])
        router.push('/dashboard/services')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update service')
      },
    }
  )

  const onSubmit = (data: ServiceFormData) => {
    console.log('Form submission data:', data); // Debug log

    // Transform data to match backend API expectations
    const formattedData = {
      name: data.name,
      description: data.description,
      category: data.category,
      type: data.type,
      basePrice: parseFloat(data.base_price),
      currency: data.currency,
      durationMinutes: Number(data.duration_minutes),
      maxPassengers: Number(data.max_passengers),
      availableAirports: data.available_airports
        .map(airport => typeof airport === 'string' ? airport : airport.value)
        .filter(airport => airport && airport.trim() !== ''),
      availableTerminals: data.available_terminals,
      requirements: data.requirements
        .map(req => typeof req === 'string' ? req : req.value)
        .filter(req => req && req.trim() !== ''),
      inclusions: data.inclusions
        .map(inc => typeof inc === 'string' ? inc : inc.value)
        .filter(inc => inc && inc.trim() !== ''),
      status: data.status,
    }

    console.log('Formatted data for API:', formattedData); // Debug log
    updateServiceMutation.mutate(formattedData)
  }

  if (isLoadingService) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load service</div>
        <div className="text-sm text-gray-600 mb-4">Error: {(error as any)?.message || 'Unknown error'}</div>
        <div className="space-x-4">
          <button
            onClick={() => refetch()}
            className="text-primary-600 hover:text-primary-700"
          >
            Retry
          </button>
          <Link
            href="/dashboard/services"
            className="text-primary-600 hover:text-primary-700"
          >
            Back to Services
          </Link>
        </div>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Service not found</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Services
          </Link>
        </div>
        <h1 className="text-2xl font-bold text-gray-900">Edit Service</h1>
        <p className="text-gray-600">Update your service details and configuration.</p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Service Name *
              </label>
              <input
                type="text"
                id="name"
                {...register('name', { required: 'Service name is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., Premium Meet & Greet"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                Category *
              </label>
              <select
                id="category"
                {...register('category', { required: 'Category is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="meet_greet">Meet & Greet</option>
                <option value="fast_track">Fast Track</option>
                <option value="lounge_access">Lounge Access</option>
                <option value="vip_terminal">VIP Terminal</option>
                <option value="transfer">Transfer</option>
                <option value="porter">Porter</option>
              </select>
              {errors.category && (
                <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
              )}
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                id="description"
                rows={3}
                {...register('description', { required: 'Description is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="Describe your service..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Service Details */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Service Details</h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Service Type *
              </label>
              <select
                id="type"
                {...register('type', { required: 'Service type is required' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="arrival">Arrival</option>
                <option value="departure">Departure</option>
                <option value="transit">Transit</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="base_price" className="block text-sm font-medium text-gray-700">
                Base Price (INR) *
              </label>
              <input
                type="number"
                step="0.01"
                id="base_price"
                {...register('base_price', { 
                  required: 'Base price is required',
                  min: { value: 0, message: 'Price must be positive' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
              {errors.base_price && (
                <p className="mt-1 text-sm text-red-600">{errors.base_price.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="duration_minutes" className="block text-sm font-medium text-gray-700">
                Duration (minutes) *
              </label>
              <input
                type="number"
                id="duration_minutes"
                {...register('duration_minutes', {
                  required: 'Duration is required',
                  min: { value: 1, message: 'Duration must be at least 1 minute' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="60"
              />
              {errors.duration_minutes && (
                <p className="mt-1 text-sm text-red-600">{errors.duration_minutes.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="max_passengers" className="block text-sm font-medium text-gray-700">
                Max Passengers *
              </label>
              <input
                type="number"
                id="max_passengers"
                {...register('max_passengers', {
                  required: 'Max passengers is required',
                  min: { value: 1, message: 'Must allow at least 1 passenger' }
                })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="1"
              />
              {errors.max_passengers && (
                <p className="mt-1 text-sm text-red-600">{errors.max_passengers.message}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Status *
            </label>
            <select
              id="status"
              {...register('status', { required: 'Status is required' })}
              className="mt-1 block w-full sm:w-48 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>
        </div>

        {/* Available Airports */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Available Airports</h2>

          <div className="space-y-3">
            {airportFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`available_airports.${index}.value` as const, {
                    required: 'Airport code is required',
                    pattern: {
                      value: /^[A-Z]{3}$/,
                      message: 'Airport code must be 3 uppercase letters (e.g., JFK)'
                    }
                  })}
                  defaultValue={field.value}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., JFK"
                />
                <button
                  type="button"
                  onClick={() => removeAirport(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendAirport('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Airport
            </button>
          </div>
        </div>

        {/* Terminal Mapping */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Terminal Mapping</h2>
          <p className="text-sm text-gray-500 mb-4">
            Specify which terminals at each airport this service is available at.
          </p>

          <div className="space-y-4">
            {watch('available_airports')?.map((airportCode, airportIndex) => {
              // Handle both string and object formats
              const code = typeof airportCode === 'string' ? airportCode : airportCode?.value || '';
              if (!code || !code.trim()) return null;

              return (
                <div key={`terminal-${code}`} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                      {code.toUpperCase()}
                    </span>
                    Terminals for {code.toUpperCase()} Airport
                  </h4>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {(() => {
                      // Get terminals for this airport from the terminals data
                      const airportTerminals = terminalsData?.data?.terminals?.[code] || [];

                      // Debug: console.log(`Terminals for ${code}:`, airportTerminals);

                      if (airportTerminals.length === 0) {
                        return (
                          <div className="col-span-full text-center py-4 text-gray-500">
                            <Building2 className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">No terminals configured for this airport</p>
                            <p className="text-xs">Use the airport management page to add terminals</p>
                          </div>
                        );
                      }

                      return airportTerminals.map((terminal: any) => (
                        <label key={terminal.terminal_code} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-white transition-colors cursor-pointer">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={watch(`available_terminals.${code}`)?.includes(terminal.terminal_code) || false}
                            onChange={(e) => {
                              const currentTerminals = watch(`available_terminals.${code}`) || [];
                              const newTerminals = e.target.checked
                                ? [...currentTerminals, terminal.terminal_code]
                                : currentTerminals.filter(t => t !== terminal.terminal_code);

                              setValue(`available_terminals.${code}`, newTerminals);
                            }}
                          />
                          <div className="ml-3 flex-1">
                            <span className="text-sm font-medium text-gray-900">{terminal.terminal_code}</span>
                            <p className="text-xs text-gray-500">{terminal.terminal_name}</p>
                            {terminal.facilities?.meeting_points?.length > 0 && (
                              <p className="text-xs text-blue-600 mt-1">
                                📍 {terminal.facilities.meeting_points[0]}
                              </p>
                            )}
                          </div>
                        </label>
                      ));
                    })()}
                  </div>

                  {/* Quick select options */}
                  <div className="mt-3 flex space-x-2">
                    {(() => {
                      const airportTerminals = terminalsData?.data?.terminals?.[code] || [];
                      if (airportTerminals.length === 0) return null;

                      const allTerminalCodes = airportTerminals.map((t: any) => t.terminal_code);
                      const mainTerminals = airportTerminals.filter((t: any) =>
                        t.terminal_code.match(/^T[12]$/) || t.terminal_name?.toLowerCase().includes('main')
                      ).map((t: any) => t.terminal_code);

                      return (
                        <>
                          <button
                            type="button"
                            onClick={() => {
                              setValue(`available_terminals.${code}`, allTerminalCodes);
                            }}
                            className="text-xs px-2 py-1 bg-primary-100 text-primary-700 rounded hover:bg-primary-200"
                          >
                            Select All
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setValue(`available_terminals.${code}`, []);
                            }}
                            className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                          >
                            Clear All
                          </button>
                          {mainTerminals.length > 0 && (
                            <button
                              type="button"
                              onClick={() => {
                                setValue(`available_terminals.${code}`, mainTerminals);
                              }}
                              className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                            >
                              Main Terminals
                            </button>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </div>
              );
            })}

            {(!watch('available_airports') || watch('available_airports').length === 0) && (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-gray-400 mb-2">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">
                  Add airports above to configure terminal mapping
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Requirements */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Requirements</h2>

          <div className="space-y-3">
            {requirementFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`requirements.${index}.value` as const, {
                    required: 'Requirement is required'
                  })}
                  defaultValue={field.value}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Valid passport"
                />
                <button
                  type="button"
                  onClick={() => removeRequirement(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendRequirement('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Requirement
            </button>
          </div>
        </div>

        {/* Inclusions */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Inclusions</h2>

          <div className="space-y-3">
            {inclusionFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-3">
                <input
                  type="text"
                  {...register(`inclusions.${index}.value` as const, {
                    required: 'Inclusion is required'
                  })}
                  defaultValue={field.value}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Personal greeter"
                />
                <button
                  type="button"
                  onClick={() => removeInclusion(index)}
                  className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={() => appendInclusion('')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Inclusion
            </button>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting || updateServiceMutation.isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting || updateServiceMutation.isLoading ? (
              <>
                <LoadingSpinner className="h-4 w-4 mr-2" />
                Updating...
              </>
            ) : (
              'Update Service'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
