'use client'

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useQuery } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { ArrowLeft, Edit, Trash2, Users, Clock, DollarSign } from 'lucide-react'
import Link from 'next/link'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function ServiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const serviceId = params?.id as string

  const { data: serviceData, isLoading, error } = useQuery(
    ['service', serviceId],
    () => servicesAPI.getById(serviceId),
    {
      enabled: !!serviceId,
    }
  )

  const service = serviceData?.data?.data?.service
  const statistics = serviceData?.data?.data?.statistics

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load service</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Service not found</div>
        <Link
          href="/dashboard/services"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Services
        </Link>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link
            href="/dashboard/services"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Services
          </Link>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
            <p className="text-gray-600 capitalize">
              {service.category?.replace('_', ' ')} • {service.type}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
              {service.status}
            </span>
            <Link
              href={`/dashboard/services/${service.id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Service
            </Link>
          </div>
        </div>
      </div>

      {/* Service Details */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white shadow-soft rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Service Details</h2>
            
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">Category</dt>
                <dd className="mt-1 text-sm text-gray-900 capitalize">
                  {service.category?.replace('_', ' ')}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Type</dt>
                <dd className="mt-1 text-sm text-gray-900 capitalize">
                  {service.type}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Base Price</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {service.currency} {service.base_price}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Duration</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {service.duration_minutes} minutes
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Max Passengers</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {service.max_passengers}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                </dd>
              </div>
            </div>

            <div className="mt-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {service.description}
              </dd>
            </div>
          </div>

          {/* Available Airports */}
          <div className="bg-white shadow-soft rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Available Airports</h2>
            <div className="flex flex-wrap gap-2">
              {service.available_airports?.map((airport: string) => (
                <span
                  key={airport}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {airport}
                </span>
              ))}
            </div>
          </div>

          {/* Requirements */}
          {service.requirements && service.requirements.length > 0 && (
            <div className="bg-white shadow-soft rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Requirements</h2>
              <ul className="list-disc list-inside space-y-1">
                {service.requirements.map((requirement: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600">
                    {requirement}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Inclusions */}
          {service.inclusions && service.inclusions.length > 0 && (
            <div className="bg-white shadow-soft rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Inclusions</h2>
              <ul className="list-disc list-inside space-y-1">
                {service.inclusions.map((inclusion: string, index: number) => (
                  <li key={index} className="text-sm text-gray-600">
                    {inclusion}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Statistics Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white shadow-soft rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statistics</h2>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-5 w-5 text-gray-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    {statistics?.total_bookings || 0}
                  </p>
                  <p className="text-sm text-gray-500">Total Bookings</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-5 w-5 text-gray-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    {statistics?.completed_bookings || 0}
                  </p>
                  <p className="text-sm text-gray-500">Completed</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    {statistics?.avg_price ? `${service.currency} ${parseFloat(statistics.avg_price).toFixed(2)}` : 'N/A'}
                  </p>
                  <p className="text-sm text-gray-500">Avg. Price</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
