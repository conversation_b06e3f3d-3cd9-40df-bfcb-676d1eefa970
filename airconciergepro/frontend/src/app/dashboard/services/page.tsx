'use client'

import { useQuery } from 'react-query'
import { servicesAPI } from '@/lib/api'
import { Briefcase, MapPin, Clock, DollarSign, Users, CheckCircle, XCircle, Plus, Edit } from 'lucide-react'
import { formatCurrency, formatNumber } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Link from 'next/link'

export default function ServicesPage() {
  const { data: servicesData, isLoading, error } = useQuery(
    ['services'],
    () => servicesAPI.getAll()
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load services</div>
        <div className="text-sm text-gray-600 mb-4">Error: {(error as any)?.message || 'Unknown error'}</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Reload Page
        </button>
      </div>
    )
  }

  const services = Array.isArray(servicesData?.data?.data?.services) ? servicesData.data.data.services : []

  const getServiceIcon = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'meet_greet':
        return Users
      case 'lounge_access':
        return Briefcase
      case 'fast_track':
        return Clock
      case 'vip_terminal':
        return Briefcase
      case 'transfer':
        return MapPin
      case 'porter':
        return Users
      default:
        return Briefcase
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'inactive':
        return 'text-red-600 bg-red-100'
      case 'draft':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Services</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your meet & greet service offerings
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Total: {services.length} services
          </div>
          <Link
            href="/dashboard/services/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Service
          </Link>
        </div>
      </div>

      {/* Services grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {services.map((service: any) => {
          const IconComponent = getServiceIcon(service.category)
          return (
            <div key={service.id} className="bg-white overflow-hidden shadow-soft rounded-lg">
              <div className="p-6">
                {/* Service header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                        <IconComponent className="h-5 w-5 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {service.name}
                      </h3>
                      <p className="text-sm text-gray-500 capitalize">
                        {service.category?.replace('_', ' ')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/dashboard/services/${service.id}/edit`}
                      className="inline-flex items-center p-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      title="Edit service"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                      {service.status}
                    </span>
                  </div>
                </div>

                {/* Service description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {service.description}
                </p>

                {/* Service details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
                    {formatCurrency(service.base_price)} base price
                  </div>

                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2 text-gray-400" />
                    {service.duration_minutes} minutes duration
                  </div>

                  {service.available_airports?.length > 0 && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                      {service.available_airports.length} airport{service.available_airports.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>

                {/* Service inclusions */}
                {Array.isArray(service.inclusions) && service.inclusions.length > 0 && (
                  <div className="mb-4">
                    <div className="text-xs font-medium text-gray-900 mb-2">Inclusions</div>
                    <div className="flex flex-wrap gap-1">
                      {service.inclusions.slice(0, 3).map((inclusion: string, index: number) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {inclusion}
                        </span>
                      ))}
                      {service.inclusions.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          +{service.inclusions.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Service availability */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm">
                      {service.status === 'active' ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600">Active</span>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-600">Inactive</span>
                        </>
                      )}
                    </div>
                    {service.max_passengers && (
                      <div className="text-xs text-gray-500">
                        Max: {service.max_passengers} passengers
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Empty state */}
      {services.length === 0 && (
        <div className="text-center py-12">
          <Briefcase className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No services</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first service.
          </p>
        </div>
      )}
    </div>
  )
}
