'use client'

import { bookingsAPI } from '@/lib/api'
import { DataTable } from './components/data-table'
import { columns } from './components/columns'
import { useCallback, useEffect, useState } from 'react'

type Booking = Record<string, any> & {
  id: string
  customerName?: string
  serviceType?: string
  date?: string
  status?: string
  amount?: number
}

export default function BookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)



  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const res = await bookingsAPI.getAll()
      console.log('API Response:', res.data) // Debug log

      // Transform backend data to match frontend expectations
      const bookingsArray = res.data.data.bookings || []
      const transformedBookings = bookingsArray.map((booking: any) => ({
        id: booking.id,
        customerName: `${booking.customer_first_name} ${booking.customer_last_name}`,
        serviceType: booking.service_name,
        date: booking.flight_date,
        status: booking.status,
        amount: parseFloat(booking.total_price),
        bookingReference: booking.booking_reference,
        flightNumber: booking.flight_number,
        airline: booking.airline,
        airport: `${booking.departure_airport} → ${booking.arrival_airport}`,
        agentName: booking.agent_first_name ? `${booking.agent_first_name} ${booking.agent_last_name}` : 'Unassigned'
      }))

      setBookings(transformedBookings)
    } catch (error) {
      setError('Service temporarily unavailable. Please try again later.')
      console.error('Failed to fetch bookings:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // Add development mode indicator
  const isDevMode = process.env.NODE_ENV === 'development'

  useEffect(() => {
    fetchData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Bookings</h1>
      {loading ? (
        <div className="text-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading bookings...</p>
          {isDevMode && (
            <p className="text-sm text-gray-500 mt-2">Development mode</p>
          )}
        </div>
      ) : error ? (
        <div className="text-center py-10">
          <p className="text-red-500 mb-4">{error}</p>
          {isDevMode && (
            <p className="text-sm text-gray-500 mb-4">
              Using mock data in development mode
            </p>
          )}
          <button 
            onClick={fetchData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      ) : bookings.length > 0 ? (
        // @ts-ignore
        <DataTable columns={columns} data={bookings} />
      ) : (
        <div className="text-center py-10">
          <p>No bookings found</p>
        </div>
      )}
    </div>
  )
}
