import { ColumnDef } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { ArrowUpDown } from 'lucide-react'

export type Booking = {
  id: string
  customerName: string
  serviceType: string
  date: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'in_progress'
  amount: number
  bookingReference: string
  flightNumber: string
  airline: string
  airport: string
  agentName: string
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const getStatusBadge = (status: string) => {
  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-blue-100 text-blue-800',
    in_progress: 'bg-purple-100 text-purple-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
  }

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
      {status.replace('_', ' ').toUpperCase()}
    </span>
  )
}

export const columns: ColumnDef<Booking>[] = [
  {
    accessorKey: 'bookingReference',
    header: 'Reference',
    cell: ({ row }) => (
      <div className="font-mono text-sm">{row.getValue('bookingReference')}</div>
    ),
  },
  {
    accessorKey: 'customerName',
    header: 'Customer',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('customerName')}</div>
    ),
  },
  {
    accessorKey: 'serviceType',
    header: 'Service',
    cell: ({ row }) => (
      <div className="max-w-[200px] truncate">{row.getValue('serviceType')}</div>
    ),
  },
  {
    accessorKey: 'flightNumber',
    header: 'Flight',
    cell: ({ row }) => (
      <div>
        <div className="font-mono text-sm">{row.getValue('flightNumber')}</div>
        <div className="text-xs text-gray-500">{row.original.airline}</div>
      </div>
    ),
  },
  {
    accessorKey: 'airport',
    header: 'Route',
    cell: ({ row }) => (
      <div className="text-sm">{row.getValue('airport')}</div>
    ),
  },
  {
    accessorKey: 'date',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => formatDate(row.getValue('date')),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => getStatusBadge(row.getValue('status')),
  },
  {
    accessorKey: 'amount',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => formatCurrency(row.getValue('amount')),
  },
  {
    accessorKey: 'agentName',
    header: 'Agent',
    cell: ({ row }) => (
      <div className="text-sm">{row.getValue('agentName')}</div>
    ),
  },
]
