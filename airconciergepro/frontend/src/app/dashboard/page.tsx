'use client'

import { useQuery } from 'react-query'
import { analyticsAPI, tenantsAPI } from '@/lib/api'
import { 
  Calendar, 
  Users, 
  Briefcase, 
  DollarSign, 
  TrendingUp, 
  Clock,
  MapPin,
  Activity
} from 'lucide-react'
import { formatCurrency, formatNumber, getStatusColor, formatTimeAgo } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface StatsCardProps {
  title: string
  value: string | number
  change?: string
  icon: React.ElementType
  trend?: 'up' | 'down' | 'neutral'
}

function StatsCard({ title, value, change, icon: Icon, trend = 'neutral' }: StatsCardProps) {
  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600',
  }

  return (
    <div className="bg-white overflow-hidden shadow-soft rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {change && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${trendColors[trend]}`}>
                    <TrendingUp className="self-center flex-shrink-0 h-4 w-4" />
                    <span className="ml-1">{change}</span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery(
    'dashboard',
    analyticsAPI.getDashboard
  )

  const { data: statsData, isLoading: statsLoading } = useQuery(
    'tenant-stats',
    tenantsAPI.getStats
  )

  if (dashboardLoading || statsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const dashboard = dashboardData?.data?.data || {}
  const stats = statsData?.data?.data || {}

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Overview of your meet & greet operations
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Today's Bookings"
          value={dashboard.todayStats?.today_bookings || 0}
          icon={Calendar}
          trend="up"
        />
        <StatsCard
          title="Active Services"
          value={dashboard.todayStats?.active_services || 0}
          icon={Activity}
          trend="neutral"
        />
        <StatsCard
          title="Today's Revenue"
          value={formatCurrency(dashboard.todayStats?.today_revenue || 0)}
          icon={DollarSign}
          trend="up"
        />
        <StatsCard
          title="Total Customers"
          value={formatNumber(stats.customers?.total_customers || 0)}
          icon={Users}
          trend="up"
        />
      </div>

      {/* Two column layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming services */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Upcoming Services</h3>
            <p className="mt-1 text-sm text-gray-500">Next 4 hours</p>
          </div>
          <div className="p-6">
            {dashboard.upcomingServices && dashboard.upcomingServices.length > 0 ? (
              <div className="space-y-4">
                {dashboard.upcomingServices.map((service: any) => (
                  <div key={service.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                        <Clock className="h-4 w-4 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {service.flight_number} - {service.customer_first_name} {service.customer_last_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        <MapPin className="inline h-3 w-3 mr-1" />
                        {service.meeting_point}
                      </p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(service.estimated_arrival).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No upcoming services in the next 4 hours</p>
            )}
          </div>
        </div>

        {/* Recent activities */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
            <p className="mt-1 text-sm text-gray-500">Latest booking updates</p>
          </div>
          <div className="p-6">
            {dashboard.recentActivities && dashboard.recentActivities.length > 0 ? (
              <div className="space-y-4">
                {dashboard.recentActivities.slice(0, 5).map((activity: any, index: number) => (
                  <div key={`${activity.created_at}-${index}`} className="flex items-start space-x-3">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.activity_type)}`}>
                      {activity.activity_type}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        {activity.booking_reference && `Booking: ${activity.booking_reference} • `}
                        {activity.first_name && `${activity.first_name} ${activity.last_name} • `}
                        {formatTimeAgo(activity.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No recent activities</p>
            )}
          </div>
        </div>
      </div>

      {/* Quick stats overview */}
      <div className="bg-white shadow-soft rounded-lg">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Overview</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Bookings overview */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Bookings</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total</span>
                  <span className="font-medium">{formatNumber(stats.bookings?.total_bookings || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Completed</span>
                  <span className="font-medium text-green-600">{formatNumber(stats.bookings?.completed_bookings || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Cancelled</span>
                  <span className="font-medium text-red-600">{formatNumber(stats.bookings?.cancelled_bookings || 0)}</span>
                </div>
              </div>
            </div>

            {/* Services overview */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Services</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total</span>
                  <span className="font-medium">{formatNumber(stats.services?.total_services || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Active</span>
                  <span className="font-medium text-green-600">{formatNumber(stats.services?.active_services || 0)}</span>
                </div>
              </div>
            </div>

            {/* Agents overview */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Agents</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total</span>
                  <span className="font-medium">{formatNumber(stats.agents?.total_agents || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Available</span>
                  <span className="font-medium text-green-600">{formatNumber(stats.agents?.available_agents || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
