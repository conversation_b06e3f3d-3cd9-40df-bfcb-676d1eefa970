'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useQuery, useMutation } from 'react-query'
import { ArrowLeft, Search, Plus, MapPin } from 'lucide-react'
import Link from 'next/link'
import Cookies from 'js-cookie'
import toast from 'react-hot-toast'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

// API functions
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

const airportsAPI = {
  getGlobalAirports: (params?: any) => {
    // Filter out undefined values and ensure string values
    const cleanParams: Record<string, string> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = String(value);
        }
      });
    }
    const queryString = new URLSearchParams(cleanParams).toString();
    const url = `${API_BASE_URL}/airports/global` + (queryString ? '?' + queryString : '');

    return fetch(url, {
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  },
  
  addAirport: (data: any) =>
    fetch('/api/v1/airports/tenant', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(data)
    }).then(res => res.json())
}

export default function AddAirportPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAirport, setSelectedAirport] = useState<any>(null)
  const [formData, setFormData] = useState({
    status: 'active',
    operational_hours: {},
    contact_info: {},
    meeting_instructions: ''
  })

  // Fetch global airports
  const { data: airportsData, isLoading } = useQuery(
    ['global-airports', { search: searchTerm }],
    () => airportsAPI.getGlobalAirports({ 
      search: searchTerm || undefined,
      status: 'active'
    }),
    {
      keepPreviousData: true,
      enabled: searchTerm.length >= 2
    }
  )

  const airports = airportsData?.data?.airports || []

  // Add airport mutation
  const addAirportMutation = useMutation(airportsAPI.addAirport, {
    onSuccess: () => {
      toast.success('Airport added successfully!')
      router.push('/dashboard/airports')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to add airport')
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedAirport) {
      toast.error('Please select an airport')
      return
    }

    addAirportMutation.mutate({
      airport_id: selectedAirport.id,
      ...formData
    })
  }

  const handleAirportSelect = (airport: any) => {
    setSelectedAirport(airport)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/dashboard/airports"
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Airports
        </Link>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900">Add Airport</h1>
        <p className="text-gray-600">Add an airport to your available locations</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Airport Selection */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Select Airport</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                Search for an airport
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Search by airport code, name, or city..."
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Type at least 2 characters to search
              </p>
            </div>

            {/* Selected Airport */}
            {selectedAirport && (
              <div className="border border-primary-200 bg-primary-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-600">
                          {selectedAirport.iata_code}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {selectedAirport.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {selectedAirport.city}, {selectedAirport.country} • {selectedAirport.iata_code}
                        {selectedAirport.icao_code && ` • ${selectedAirport.icao_code}`}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedAirport(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>
            )}

            {/* Search Results */}
            {searchTerm.length >= 2 && !selectedAirport && (
              <div className="border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : airports.length === 0 ? (
                  <div className="text-center py-8">
                    <MapPin className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">No airports found</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200">
                    {airports.map((airport: any) => (
                      <button
                        key={airport.id}
                        type="button"
                        onClick={() => handleAirportSelect(airport)}
                        className="w-full text-left px-4 py-3 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                              <span className="text-xs font-medium text-gray-600">
                                {airport.iata_code}
                              </span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {airport.name}
                            </div>
                            <div className="text-sm text-gray-500 truncate">
                              {airport.city}, {airport.country} • {airport.iata_code}
                              {airport.icao_code && ` • ${airport.icao_code}`}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Airport Settings */}
        {selectedAirport && (
          <div className="bg-white shadow-soft rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Airport Settings</h2>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label htmlFor="meeting_instructions" className="block text-sm font-medium text-gray-700 mb-1">
                  Meeting Instructions
                </label>
                <textarea
                  id="meeting_instructions"
                  rows={3}
                  value={formData.meeting_instructions}
                  onChange={(e) => setFormData({ ...formData, meeting_instructions: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Special instructions for meeting customers at this airport..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <Link
            href="/dashboard/airports"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={!selectedAirport || addAirportMutation.isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {addAirportMutation.isLoading ? (
              <>
                <LoadingSpinner className="h-4 w-4 mr-2" />
                Adding...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Add Airport
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
