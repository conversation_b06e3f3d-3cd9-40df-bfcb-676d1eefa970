'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { Plus, Search, MapPin, Globe, Clock, Edit, Trash2, Settings, Building2, Users, Navigation } from 'lucide-react'
import Link from 'next/link'
import Cookies from 'js-cookie'
import toast from 'react-hot-toast'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

// API functions for airports and terminals
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

const airportsAPI = {
  getTenantAirports: (params?: any) => {
    // Filter out undefined values and ensure string values
    const cleanParams: Record<string, string> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = String(value);
        }
      });
    }
    const queryString = new URLSearchParams(cleanParams).toString();
    const url = `${API_BASE_URL}/airports/tenant` + (queryString ? '?' + queryString : '');

    return fetch(url, {
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  },

  getGlobalAirports: (params?: any) => {
    // Filter out undefined values and ensure string values
    const cleanParams: Record<string, string> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = String(value);
        }
      });
    }
    const queryString = new URLSearchParams(cleanParams).toString();
    const url = `${API_BASE_URL}/airports/global` + (queryString ? '?' + queryString : '');

    return fetch(url, {
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  },
  
  addAirport: (data: any) =>
    fetch('/api/v1/airports/tenant', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(data)
    }).then(res => res.json()),
  
  updateAirport: (id: string, data: any) =>
    fetch(`/api/v1/airports/tenant/${id}`, {
      method: 'PUT',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(data)
    }).then(res => res.json()),
  
  removeAirport: (id: string) =>
    fetch(`/api/v1/airports/tenant/${id}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
    }).then(res => res.json())
}

const terminalsAPI = {
  getAirportTerminals: (airportId: string) => {
    return fetch(`${API_BASE_URL}/terminals/airport/${airportId}`, {
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  },

  saveTerminal: (airportId: string, terminalData: any) => {
    return fetch(`${API_BASE_URL}/terminals/airport/${airportId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Cookies.get('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(terminalData)
    }).then(res => res.json());
  },

  updateTerminal: (terminalId: string, terminalData: any) => {
    return fetch(`${API_BASE_URL}/terminals/${terminalId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${Cookies.get('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(terminalData)
    }).then(res => res.json());
  },

  deleteTerminal: (terminalId: string) => {
    return fetch(`${API_BASE_URL}/terminals/${terminalId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  }
}

export default function AirportsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showTerminalModal, setShowTerminalModal] = useState(false)
  const [selectedAirport, setSelectedAirport] = useState<any>(null)
  const [terminals, setTerminals] = useState<any[]>([])
  const [isLoadingTerminals, setIsLoadingTerminals] = useState(false)
  const queryClient = useQueryClient()

  // Fetch tenant airports
  const { data: airportsData, isLoading, error } = useQuery(
    ['tenant-airports', { search: searchTerm, status: statusFilter !== 'all' ? statusFilter : undefined }],
    () => {
      const params: any = {};
      if (searchTerm && searchTerm.trim()) {
        params.search = searchTerm.trim();
      }
      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }
      return airportsAPI.getTenantAirports(params);
    },
    {
      keepPreviousData: true,
    }
  )

  const airports = airportsData?.data?.airports || []

  // Remove airport mutation
  const removeAirportMutation = useMutation(airportsAPI.removeAirport, {
    onSuccess: () => {
      toast.success('Airport removed successfully!')
      queryClient.invalidateQueries(['tenant-airports'])
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to remove airport')
    }
  })

  const handleRemoveAirport = async (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to remove ${name} from your airports? This action cannot be undone.`)) {
      removeAirportMutation.mutate(id)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Terminal management functions
  const handleManageTerminals = async (airport: any) => {
    setSelectedAirport(airport)
    setIsLoadingTerminals(true)
    setShowTerminalModal(true)

    try {
      const response = await terminalsAPI.getAirportTerminals(airport.airport_id || airport.id)
      if (response.success) {
        setTerminals(response.data.terminals)
      } else {
        // If no terminals exist, create default ones
        const defaultTerminals = [
          { terminal_code: 'T1', terminal_name: 'Terminal 1', facilities: {}, gates: [], services: [] },
          { terminal_code: 'T2', terminal_name: 'Terminal 2', facilities: {}, gates: [], services: [] },
          { terminal_code: 'T3', terminal_name: 'Terminal 3', facilities: {}, gates: [], services: [] },
          { terminal_code: 'T4', terminal_name: 'Terminal 4', facilities: {}, gates: [], services: [] }
        ]
        setTerminals(defaultTerminals)
      }
    } catch (error) {
      console.error('Error loading terminals:', error)
      toast.error('Failed to load terminals')
    } finally {
      setIsLoadingTerminals(false)
    }
  }

  const handleSaveTerminal = async (terminal: any) => {
    try {
      const airportId = selectedAirport?.airport_id || selectedAirport?.id
      if (!airportId) {
        toast.error('Airport ID not found')
        return
      }

      const response = await terminalsAPI.saveTerminal(airportId, {
        terminal_code: terminal.terminal_code,
        terminal_name: terminal.terminal_name,
        facilities: terminal.facilities || {},
        gates: terminal.gates || [],
        services: terminal.services || []
      })

      if (response.success) {
        toast.success('Terminal saved successfully')
        // Refresh terminals
        handleManageTerminals(selectedAirport)
      } else {
        toast.error(response.error || 'Failed to save terminal')
      }
    } catch (error) {
      console.error('Error saving terminal:', error)
      toast.error('Failed to save terminal')
    }
  }

  const updateTerminalField = (terminalIndex: number, field: string, value: string) => {
    setTerminals(prev =>
      prev.map((terminal, index) =>
        index === terminalIndex
          ? { ...terminal, [field]: value }
          : terminal
      )
    )
  }

  const addMeetingPoint = (terminalIndex: number) => {
    setTerminals(prev =>
      prev.map((terminal, index) =>
        index === terminalIndex
          ? {
              ...terminal,
              facilities: {
                ...terminal.facilities,
                meeting_points: [...(terminal.facilities?.meeting_points || []), '']
              }
            }
          : terminal
      )
    )
  }

  const updateMeetingPoint = (terminalIndex: number, pointIndex: number, value: string) => {
    setTerminals(prev =>
      prev.map((terminal, index) =>
        index === terminalIndex
          ? {
              ...terminal,
              facilities: {
                ...terminal.facilities,
                meeting_points: terminal.facilities?.meeting_points?.map((point: string, pIndex: number) =>
                  pIndex === pointIndex ? value : point
                ) || []
              }
            }
          : terminal
      )
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load airports</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Reload Page
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Airport Management</h1>
          <p className="text-gray-600">Manage airports available for your services</p>
        </div>
        <Link
          href="/dashboard/airports/add"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Airport
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white shadow-soft rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search Airports
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Search by code, name, city..."
              />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MapPin className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Airports</dt>
                  <dd className="text-lg font-medium text-gray-900">{airports.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Globe className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Airports</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {airports.filter((a: any) => a.status === 'active').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Inactive Airports</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {airports.filter((a: any) => a.status === 'inactive').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-soft rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Globe className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Countries</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {new Set(airports.map((a: any) => a.country)).size}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Airports List */}
      <div className="bg-white shadow-soft rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Your Airports</h3>
        </div>

        {airports.length === 0 ? (
          <div className="text-center py-12">
            <MapPin className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No airports found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding your first airport.
            </p>
            <div className="mt-6">
              <Link
                href="/dashboard/airports/add"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Airport
              </Link>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Airport
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timezone
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Terminals
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {airports.map((airport: any) => (
                  <tr key={airport.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600">
                              {airport.iata_code}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {airport.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {airport.iata_code} {airport.icao_code && `• ${airport.icao_code}`}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{airport.city}</div>
                      <div className="text-sm text-gray-500">{airport.country}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {airport.timezone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Building2 className="h-4 w-4 text-gray-400 mr-1" />
                        <span>{airport.terminals?.length || 0} Terminal{(airport.terminals?.length || 0) !== 1 ? 's' : ''}</span>
                        {airport.terminals?.length > 0 && (
                          <span className="ml-2 text-xs text-gray-500">
                            ({airport.terminals.map((t: any) => t.terminal_code).join(', ')})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(airport.status)}`}>
                        {airport.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleManageTerminals(airport)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Manage terminals"
                        >
                          <Building2 className="h-4 w-4" />
                        </button>
                        <Link
                          href={`/dashboard/airports/${airport.id}/edit`}
                          className="text-primary-600 hover:text-primary-900"
                          title="Edit airport settings"
                        >
                          <Settings className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleRemoveAirport(airport.id, airport.name)}
                          className="text-red-600 hover:text-red-900"
                          title="Remove airport"
                          disabled={removeAirportMutation.isLoading}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Terminal Management Modal */}
      {showTerminalModal && selectedAirport && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex items-center justify-between pb-4 border-b">
                <h3 className="text-lg font-medium text-gray-900">
                  Manage Terminals - {selectedAirport.name} ({selectedAirport.iata_code})
                </h3>
                <button
                  onClick={() => setShowTerminalModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Terminal Configuration */}
              <div className="mt-6">
                {isLoadingTerminals ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-500">Loading terminals...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {terminals.map((terminal, index) => (
                      <div key={terminal.terminal_code} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <Building2 className="h-5 w-5 text-blue-500 mr-2" />
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                              {terminal.terminal_code}
                            </span>
                          </div>
                          <button
                            onClick={() => handleSaveTerminal(terminal)}
                            className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                          >
                            Save
                          </button>
                        </div>

                        <div className="space-y-4">
                          {/* Terminal Name */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Terminal Name
                            </label>
                            <input
                              type="text"
                              value={terminal.terminal_name || ''}
                              onChange={(e) => updateTerminalField(index, 'terminal_name', e.target.value)}
                              placeholder="e.g., Terminal 1 - Domestic"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>

                          {/* Meeting Points */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              <Navigation className="h-4 w-4 inline mr-1" />
                              Meeting Points
                            </label>
                            {(terminal.facilities?.meeting_points || []).map((point: string, pointIndex: number) => (
                              <div key={pointIndex} className="flex items-center space-x-2 mb-2">
                                <input
                                  type="text"
                                  value={point}
                                  onChange={(e) => updateMeetingPoint(index, pointIndex, e.target.value)}
                                  placeholder="e.g., Gate 5, Arrival Hall, Coffee Shop near Exit A"
                                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                                <button
                                  onClick={() => {
                                    const newPoints = terminal.facilities?.meeting_points?.filter((_: any, i: number) => i !== pointIndex) || []
                                    updateTerminalField(index, 'facilities', { ...terminal.facilities, meeting_points: newPoints })
                                  }}
                                  className="px-2 py-2 text-red-600 hover:text-red-800"
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                            <button
                              onClick={() => addMeetingPoint(index)}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              + Add Meeting Point
                            </button>
                          </div>

                          {/* Facilities */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              <Users className="h-4 w-4 inline mr-1" />
                              Available Facilities
                            </label>
                            <textarea
                              value={JSON.stringify(terminal.facilities || {}, null, 2)}
                              onChange={(e) => {
                                try {
                                  const facilities = JSON.parse(e.target.value)
                                  updateTerminalField(index, 'facilities', facilities)
                                } catch (error) {
                                  // Invalid JSON, ignore
                                }
                              }}
                              placeholder='{"wifi": true, "lounge": true, "restaurants": ["Food Court"], "shops": ["Duty Free"]}'
                              rows={4}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Modal Footer */}
              <div className="mt-6 flex items-center justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => setShowTerminalModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowTerminalModal(false)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
