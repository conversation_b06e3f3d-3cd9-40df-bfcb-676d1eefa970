'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { ArrowLeft, Save } from 'lucide-react'
import Link from 'next/link'
import Cookies from 'js-cookie'
import toast from 'react-hot-toast'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

// API functions
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

const airportsAPI = {
  getTenantAirports: (params?: any) => {
    // Filter out undefined values and ensure string values
    const cleanParams: Record<string, string> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = String(value);
        }
      });
    }
    const queryString = new URLSearchParams(cleanParams).toString();
    const url = `${API_BASE_URL}/airports/tenant` + (queryString ? '?' + queryString : '');

    return fetch(url, {
      headers: { 'Authorization': `Bearer ${Cookies.get('token')}` }
    }).then(res => res.json());
  },
  
  updateAirport: (id: string, data: any) =>
    fetch(`/api/v1/airports/tenant/${id}`, {
      method: 'PUT',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(data)
    }).then(res => res.json())
}

export default function EditAirportPage() {
  const router = useRouter()
  const params = useParams()
  const queryClient = useQueryClient()
  const airportId = params?.id as string

  const [formData, setFormData] = useState({
    status: 'active',
    operational_hours: {},
    contact_info: {},
    meeting_instructions: ''
  })

  // Fetch tenant airports to find the specific one
  const { data: airportsData, isLoading, error } = useQuery(
    ['tenant-airports'],
    () => airportsAPI.getTenantAirports(),
    {
      enabled: !!airportId,
    }
  )

  const airport = airportsData?.data?.airports?.find((a: any) => a.id === airportId)

  // Populate form with existing data
  useEffect(() => {
    if (airport) {
      setFormData({
        status: airport.status || 'active',
        operational_hours: airport.operational_hours || {},
        contact_info: airport.contact_info || {},
        meeting_instructions: airport.meeting_instructions || ''
      })
    }
  }, [airport])

  // Update airport mutation
  const updateAirportMutation = useMutation(
    (data: any) => airportsAPI.updateAirport(airportId, data),
    {
      onSuccess: () => {
        toast.success('Airport updated successfully!')
        queryClient.invalidateQueries(['tenant-airports'])
        router.push('/dashboard/airports')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update airport')
      }
    }
  )

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateAirportMutation.mutate(formData)
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOperationalHoursChange = (day: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      operational_hours: {
        ...prev.operational_hours,
        [day]: {
          ...(prev.operational_hours as any)[day],
          [field]: value
        }
      }
    }))
  }

  const handleContactInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      contact_info: {
        ...prev.contact_info,
        [field]: value
      }
    }))
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load airport</div>
        <Link
          href="/dashboard/airports"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Airports
        </Link>
      </div>
    )
  }

  if (!airport) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Airport not found</div>
        <Link
          href="/dashboard/airports"
          className="text-primary-600 hover:text-primary-700"
        >
          Back to Airports
        </Link>
      </div>
    )
  }

  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/dashboard/airports"
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Airports
        </Link>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900">Edit Airport Settings</h1>
        <p className="text-gray-600">
          {airport.name} ({airport.iata_code}) - {airport.city}, {airport.country}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Settings */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Settings</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              <p className="mt-1 text-sm text-gray-500">
                Inactive airports won't be available for new bookings
              </p>
            </div>

            <div>
              <label htmlFor="meeting_instructions" className="block text-sm font-medium text-gray-700 mb-1">
                Meeting Instructions
              </label>
              <textarea
                id="meeting_instructions"
                rows={4}
                value={formData.meeting_instructions}
                onChange={(e) => handleInputChange('meeting_instructions', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Special instructions for meeting customers at this airport..."
              />
              <p className="mt-1 text-sm text-gray-500">
                These instructions will be shown to agents and customers
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h2>
          
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                id="contact_phone"
                value={(formData.contact_info as any)?.phone || ''}
                onChange={(e) => handleContactInfoChange('phone', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="+****************"
              />
            </div>

            <div>
              <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="contact_email"
                value={(formData.contact_info as any)?.email || ''}
                onChange={(e) => handleContactInfoChange('email', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="contact_address" className="block text-sm font-medium text-gray-700 mb-1">
                Office Address
              </label>
              <textarea
                id="contact_address"
                rows={2}
                value={(formData.contact_info as any)?.address || ''}
                onChange={(e) => handleContactInfoChange('address', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Office address at the airport..."
              />
            </div>
          </div>
        </div>

        {/* Operational Hours */}
        <div className="bg-white shadow-soft rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Operational Hours</h2>
          
          <div className="space-y-4">
            {daysOfWeek.map(day => (
              <div key={day} className="grid grid-cols-1 gap-4 sm:grid-cols-4 items-center">
                <div className="sm:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 capitalize">
                    {day}
                  </label>
                </div>
                <div className="sm:col-span-1">
                  <input
                    type="time"
                    value={(formData.operational_hours as any)?.[day]?.open || ''}
                    onChange={(e) => handleOperationalHoursChange(day, 'open', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div className="sm:col-span-1">
                  <input
                    type="time"
                    value={(formData.operational_hours as any)?.[day]?.close || ''}
                    onChange={(e) => handleOperationalHoursChange(day, 'close', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div className="sm:col-span-1">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={(formData.operational_hours as any)?.[day]?.closed || false}
                      onChange={(e) => handleOperationalHoursChange(day, 'closed', e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    />
                    <span className="ml-2 text-sm text-gray-600">Closed</span>
                  </label>
                </div>
              </div>
            ))}
          </div>
          <p className="mt-2 text-sm text-gray-500">
            Set your operational hours for each day of the week
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <Link
            href="/dashboard/airports"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={updateAirportMutation.isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {updateAirportMutation.isLoading ? (
              <>
                <LoadingSpinner className="h-4 w-4 mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
