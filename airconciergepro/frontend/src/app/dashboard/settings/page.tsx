'use client'

import { useQuery } from 'react-query'
import { tenantsAPI } from '@/lib/api'
import {
  Settings as SettingsIcon,
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  CreditCard,
  Bell,
  Shield,
  Users
} from 'lucide-react'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function SettingsPage() {
  const { data: settingsData, isLoading, error } = useQuery(
    'tenant-settings',
    tenantsAPI.getSettings
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load settings</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700"
        >
          Try again
        </button>
      </div>
    )
  }

  const settings = settingsData?.data?.data || {}

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your organization settings and preferences
        </p>
      </div>

      {/* Settings sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Company Information */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Company Information
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Company Name</label>
              <div className="mt-1 text-sm text-gray-900">{settings.name || 'Not set'}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Subdomain</label>
              <div className="mt-1 text-sm text-gray-900">{settings.subdomain || 'Not set'}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Plan</label>
              <div className="mt-1">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 capitalize">
                  {settings.plan || 'basic'}
                </span>
              </div>
            </div>

            {settings.description && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <div className="mt-1 text-sm text-gray-900">{settings.description}</div>
              </div>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Contact Information
            </h3>
          </div>
          <div className="p-6 space-y-4">
            {settings.contact_email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-700">Email</div>
                  <div className="text-sm text-gray-900">{settings.contact_email}</div>
                </div>
              </div>
            )}

            {settings.contact_phone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-700">Phone</div>
                  <div className="text-sm text-gray-900">{settings.contact_phone}</div>
                </div>
              </div>
            )}

            {settings.address && (
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <div className="text-sm font-medium text-gray-700">Address</div>
                  <div className="text-sm text-gray-900">{settings.address}</div>
                </div>
              </div>
            )}

            {settings.website && (
              <div className="flex items-center space-x-3">
                <Globe className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-700">Website</div>
                  <div className="text-sm text-gray-900">
                    <a href={settings.website} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">
                      {settings.website}
                    </a>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Business Settings */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <SettingsIcon className="h-5 w-5 mr-2" />
              Business Settings
            </h3>
          </div>
          <div className="p-6 space-y-4">
            {settings.timezone && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Timezone</label>
                <div className="mt-1 text-sm text-gray-900">{settings.timezone}</div>
              </div>
            )}

            {settings.currency && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Currency</label>
                <div className="mt-1 text-sm text-gray-900">{settings.currency}</div>
              </div>
            )}

            {settings.language && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Language</label>
                <div className="mt-1 text-sm text-gray-900">{settings.language}</div>
              </div>
            )}

            {settings.business_hours && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Business Hours</label>
                <div className="mt-1 text-sm text-gray-900">
                  {typeof settings.business_hours === 'object'
                    ? JSON.stringify(settings.business_hours, null, 2)
                    : settings.business_hours
                  }
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white shadow-soft rounded-lg">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Bell className="h-5 w-5 mr-2" />
              Notification Settings
            </h3>
          </div>
          <div className="p-6 space-y-4">
            {settings.notification_settings && typeof settings.notification_settings === 'object' && (
              <div className="space-y-3">
                {Object.entries(settings.notification_settings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 capitalize">
                      {key.replace('_', ' ')}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {value ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Additional Settings */}
      <div className="bg-white shadow-soft rounded-lg">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Additional Configuration</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Features */}
            {settings.features && typeof settings.features === 'object' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Features
                </h4>
                <div className="space-y-2">
                  {Object.entries(settings.features).map(([feature, enabled]) => (
                    <div key={feature} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 capitalize">
                        {feature.replace('_', ' ')}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {enabled ? 'On' : 'Off'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Integrations */}
            {settings.integrations && typeof settings.integrations === 'object' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Globe className="h-4 w-4 mr-2" />
                  Integrations
                </h4>
                <div className="space-y-2">
                  {Object.entries(settings.integrations).map(([integration, config]) => (
                    <div key={integration} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 capitalize">
                        {integration.replace('_', ' ')}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        config && typeof config === 'object' && Object.keys(config).length > 0
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {config && typeof config === 'object' && Object.keys(config).length > 0 ? 'Connected' : 'Not Connected'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Limits */}
            {settings.limits && typeof settings.limits === 'object' && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Account Limits
                </h4>
                <div className="space-y-2">
                  {Object.entries(settings.limits).map(([limit, value]) => (
                    <div key={limit} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 capitalize">
                        {limit.replace('_', ' ')}
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {typeof value === 'number' ? value.toLocaleString() : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
