import './globals.css'
import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from '@/components/Providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AirConcierge Pro - Meet & Greet Management Platform',
  description: 'Comprehensive SaaS platform for managing airport meet & greet services globally',
  keywords: 'airport services, meet and greet, VIP services, concierge, travel management',
  authors: [{ name: 'AirConcierge Pro' }],
  robots: 'noindex, nofollow', // Remove in production
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
