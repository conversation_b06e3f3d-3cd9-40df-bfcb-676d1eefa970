import axios from 'axios'
import toast from 'react-hot-toast'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

export const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // You can add loading states here
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.error || error.message || 'Something went wrong'
    
    // Handle different error status codes
    switch (error.response?.status) {
      case 401:
        // Unauthorized - redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
        break
      case 403:
        toast.error('You do not have permission to perform this action')
        break
      case 404:
        toast.error('Resource not found')
        break
      case 429:
        toast.error('Too many requests. Please try again later.')
        break
      case 500:
        toast.error('Server error. Please try again later.')
        break
      default:
        if (error.response?.status >= 400) {
          toast.error(message)
        }
    }

    return Promise.reject(error)
  }
)

// API endpoints
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  register: (data: any) => api.post('/auth/register', data),
  me: () => api.get('/auth/me'),
  refresh: () => api.post('/auth/refresh'),
}

export const bookingsAPI = {
  getAll: (params?: any) => api.get('/bookings', { params }),
  getById: (id: string) => api.get(`/bookings/${id}`),
  create: (data: any) => api.post('/bookings', data),
  update: (id: string, data: any) => api.put(`/bookings/${id}`, data),
  assign: (id: string, agentId: string) => 
    api.post(`/bookings/${id}/assign`, { agentId }),
  cancel: (id: string, reason?: string) => 
    api.post(`/bookings/${id}/cancel`, { reason }),
}

export const customersAPI = {
  getAll: (params?: any) => api.get('/customers', { params }),
  getById: (id: string) => api.get(`/customers/${id}`),
  create: (data: any) => api.post('/customers', data),
  update: (id: string, data: any) => api.put(`/customers/${id}`, data),
  delete: (id: string) => api.delete(`/customers/${id}`),
  getBookings: (id: string, params?: any) => 
    api.get(`/customers/${id}/bookings`, { params }),
  search: (query: string) => api.get(`/customers/search/${query}`),
}

export const servicesAPI = {
  getAll: (params?: any) => api.get('/services', { params }),
  getById: (id: string) => api.get(`/services/${id}`),
  create: (data: any) => api.post('/services', data),
  update: (id: string, data: any) => api.put(`/services/${id}`, data),
  delete: (id: string) => api.delete(`/services/${id}`),
  getAvailable: (airport: string, type: string, params?: any) =>
    api.get(`/services/available/${airport}/${type}`, { params }),
  getStats: () => api.get('/services/stats/categories'),
  duplicate: (id: string) => api.post(`/services/${id}/duplicate`),
}

export const analyticsAPI = {
  getBookings: (params?: any) => api.get('/analytics/bookings', { params }),
  getRevenue: (params?: any) => api.get('/analytics/revenue', { params }),
  getCustomers: () => api.get('/analytics/customers'),
  getServices: () => api.get('/analytics/services'),
  getAgents: () => api.get('/analytics/agents'),
  getAirports: () => api.get('/analytics/airports'),
  getDashboard: () => api.get('/analytics/dashboard'),
}

export const agentsAPI = {
  getAll: (params?: any) => api.get('/agents', { params }),
  getById: (id: string) => api.get(`/agents/${id}`),
  create: (data: any) => api.post('/agents', data),
  update: (id: string, data: any) => api.put(`/agents/${id}`, data),
  delete: (id: string) => api.delete(`/agents/${id}`),
  getBookings: (id: string, params?: any) =>
    api.get(`/agents/${id}/bookings`, { params }),
}

export const paymentsAPI = {
  getAll: (params?: any) => api.get('/payments', { params }),
  getById: (id: string) => api.get(`/payments/${id}`),
  getStats: () => api.get('/payments/stats'),
  process: (data: any) => api.post('/payments/process', data),
  refund: (id: string, data: any) => api.post(`/payments/${id}/refund`, data),
}

export const partnersAPI = {
  getAll: (params?: any) => api.get('/partners', { params }),
  getById: (id: string) => api.get(`/partners/${id}`),
  create: (data: any) => api.post('/partners', data),
  update: (id: string, data: any) => api.put(`/partners/${id}`, data),
  delete: (id: string) => api.delete(`/partners/${id}`),
  getStats: (id: string) => api.get(`/partners/${id}/stats`),
}

export const tenantsAPI = {
  getSettings: () => api.get('/tenants/settings'),
  updateSettings: (data: any) => api.put('/tenants/settings', data),
  getUsers: () => api.get('/tenants/users'),
  getStats: () => api.get('/tenants/stats'),
}

export default api
