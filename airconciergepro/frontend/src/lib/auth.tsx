'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Cookies from 'js-cookie'
import { api } from './api'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  permissions: string[]
}

interface Tenant {
  id: string
  name: string
  subdomain: string
  plan: string
}

interface AuthContextType {
  user: User | null
  tenant: Tenant | null
  token: string | null
  login: (email: string, password: string) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  loading: boolean
  isAuthenticated: boolean
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  tenantName: string
  subdomain: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [tenant, setTenant] = useState<Tenant | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const initAuth = async () => {
      const savedToken = Cookies.get('token')
      if (savedToken) {
        setToken(savedToken)
        api.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`
        
        try {
          const response = await api.get('/auth/me')
          setUser(response.data.data.user)
          setTenant(response.data.data.tenant)
        } catch (error) {
          console.error('Failed to fetch user data:', error)
          Cookies.remove('token')
          delete api.defaults.headers.common['Authorization']
        }
      }
      setLoading(false)
    }

    initAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await api.post('/auth/login', { email, password })
      const { token: newToken, user: userData, tenant: tenantData } = response.data.data

      setToken(newToken)
      setUser(userData)
      setTenant(tenantData)
      
      Cookies.set('token', newToken, { expires: 7 }) // 7 days
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      router.push('/dashboard')
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Login failed')
    }
  }

  const register = async (data: RegisterData) => {
    try {
      const response = await api.post('/auth/register', data)
      const { token: newToken, user: userData, tenant: tenantData } = response.data.data

      setToken(newToken)
      setUser(userData)
      setTenant(tenantData)
      
      Cookies.set('token', newToken, { expires: 7 })
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      router.push('/dashboard')
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Registration failed')
    }
  }

  const logout = () => {
    setUser(null)
    setTenant(null)
    setToken(null)
    Cookies.remove('token')
    delete api.defaults.headers.common['Authorization']
    router.push('/login')
  }

  const value = {
    user,
    tenant,
    token,
    login,
    register,
    logout,
    loading,
    isAuthenticated: !!user,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
