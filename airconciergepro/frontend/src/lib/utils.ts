import { format, formatDistanceToNow, parseISO } from 'date-fns'

export const formatDate = (date: string | Date) => {
  return format(typeof date === 'string' ? parseISO(date) : date, 'MMM dd, yyyy')
}

export const formatDateTime = (date: string | Date) => {
  return format(typeof date === 'string' ? parseISO(date) : date, 'MMM dd, yyyy HH:mm')
}

export const formatTime = (date: string | Date) => {
  return format(typeof date === 'string' ? parseISO(date) : date, 'HH:mm')
}

export const formatTimeAgo = (date: string | Date) => {
  return formatDistanceToNow(typeof date === 'string' ? parseISO(date) : date, { addSuffix: true })
}

export const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

export const formatNumber = (number: number) => {
  return new Intl.NumberFormat('en-US').format(number)
}

export const formatPercentage = (value: number, decimals: number = 1) => {
  return `${value.toFixed(decimals)}%`
}

export const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export const capitalizeFirst = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export const formatPhoneNumber = (phone: string) => {
  // Simple US phone number formatting
  const cleaned = phone.replace(/\D/g, '')
  const match = cleaned.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/)
  if (match) {
    return `+${match[1]} (${match[2]}) ${match[3]}-${match[4]}`
  }
  return phone
}

export const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    confirmed: 'bg-blue-100 text-blue-800',
    in_progress: 'bg-yellow-100 text-yellow-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    no_show: 'bg-gray-100 text-gray-800',
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    available: 'bg-green-100 text-green-800',
    busy: 'bg-yellow-100 text-yellow-800',
    offline: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-purple-100 text-purple-800',
  }
  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800'
}

export const getStatusIcon = (status: string) => {
  const statusIcons: Record<string, string> = {
    confirmed: '✓',
    in_progress: '⏳',
    completed: '✅',
    cancelled: '❌',
    no_show: '👻',
    active: '🟢',
    inactive: '⚪',
    available: '🟢',
    busy: '🟡',
    offline: '🔴',
    pending: '⏳',
    paid: '💰',
    failed: '❌',
    refunded: '↩️',
  }
  return statusIcons[status.toLowerCase()] || '❓'
}

export const generateBookingReference = () => {
  const prefix = 'ACG'
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 5).toUpperCase()
  return `${prefix}${timestamp}${random}`
}

export const validateEmail = (email: string) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return re.test(email)
}

export const validatePhone = (phone: string) => {
  const re = /^\+?[\d\s\-\(\)]{10,}$/
  return re.test(phone)
}

export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): T => {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

export const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ')
}

export const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const downloadCSV = (data: any[], filename: string) => {
  if (!data.length) return

  const headers = Object.keys(data[0])
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        return typeof value === 'string' && value.includes(',') 
          ? `"${value}"` 
          : value
      }).join(',')
    )
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${filename}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
