FROM node:22-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
# Debug: see what was actually built
RUN ls -la /app/.next/

FROM node:22-alpine AS runner
WORKDIR /app
ENV NODE_ENV production

# Only copy what exists
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Copy public only if your project has it
# COPY --from=builder /app/public ./public

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]