{"name": "airconciergepro-frontend", "version": "1.0.0", "description": "AirConcierge Pro Frontend Dashboard", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.0", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@tanstack/react-table": "^8.21.3", "@types/js-cookie": "^3.0.4", "@types/node": "^20.5.0", "@types/react": "^18.2.0", "@types/react-datepicker": "^4.15.0", "@types/react-dom": "^18.2.0", "@types/react-table": "^7.7.20", "autoprefixer": "^10.4.0", "axios": "^1.5.0", "class-variance-authority": "^0.7.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "js-cookie": "^3.0.5", "lucide-react": "^0.263.1", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-hot-toast": "^2.4.0", "react-query": "^3.39.0", "react-router-dom": "^6.15.0", "react-select": "^5.7.0", "react-table": "^7.8.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5.1.6", "zustand": "^4.4.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "eslint-config-next": "^14.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}