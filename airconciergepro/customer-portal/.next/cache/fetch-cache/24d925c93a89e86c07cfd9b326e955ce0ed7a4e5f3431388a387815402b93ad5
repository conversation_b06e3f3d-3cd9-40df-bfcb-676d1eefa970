{"kind": "FETCH", "data": {"headers": {"access-control-allow-headers": "*", "access-control-allow-methods": "GET, POST, HEAD, OPTIONS", "access-control-allow-origin": "*", "cache-control": "no-cache, private", "cf-cache-status": "DYNAMIC", "cf-ray": "95bfbdd1bf9d9146-MAA", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json", "date": "Tu<PERSON>, 08 Jul 2025 12:56:25 GMT", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=51CPg2959BYjEJPpO0wVAeESvKGn3TNRKUyg554UGHnSmE5LEcqU1dCzY65fzyfHoNJZS3E1%2FtnD6GQCmYajmoNd3JlZzuzkYJgrs0imR0qOHQ%3D%3D\"}]}", "server": "cloudflare", "transfer-encoding": "chunked", "x-apilayer-transaction-id": "347e330b-8bb0-4a2d-81aa-5b47e4203311", "x-increment-usage": "1", "x-quota-limit": "100", "x-quota-remaining": "11", "x-request-time": "0.019, 0.048"}, "body": "eyJwYWdpbmF0aW9uIjp7Im9mZnNldCI6MCwibGltaXQiOjEwMCwiY291bnQiOjEsInRvdGFsIjoxfSwiZGF0YSI6W3siaWQiOiI0MjA4NTU4IiwiZ210IjoiNS4zMCIsImFpcnBvcnRfaWQiOiI4NzIiLCJpYXRhX2NvZGUiOiJCT00iLCJjaXR5X2lhdGFfY29kZSI6IkJPTSIsImljYW9fY29kZSI6IlZBQkIiLCJjb3VudHJ5X2lzbzIiOiJJTiIsImdlb25hbWVfaWQiOiI2MzAxMDMyIiwibGF0aXR1ZGUiOiIxOS4wOTU1MDkiLCJsb25naXR1ZGUiOiI3Mi44NzQ5NyIsImFpcnBvcnRfbmFtZSI6IkNoaGF0cmFwYXRpIFNoaXZhamkgSW50ZXJuYXRpb25hbCAoU2FoYXIgSW50ZXJuYXRpb25hbCkiLCJjb3VudHJ5X25hbWUiOiJJbmRpYSIsInBob25lX251bWJlciI6IigwMjIpIDI2MjYtNDAiLCJ0aW1lem9uZSI6IkFzaWFcL0tvbGthdGEifV19", "status": 200, "url": "https://api.aviationstack.com/v1/airports?access_key=********************************&iata_code=BOM"}, "revalidate": 31536000, "tags": []}