{"kind": "FETCH", "data": {"headers": {"access-control-allow-headers": "*", "access-control-allow-methods": "GET, POST, HEAD, OPTIONS", "access-control-allow-origin": "*", "cache-control": "no-cache, private", "cf-cache-status": "DYNAMIC", "cf-ray": "95bfbddd4d0f9146-MAA", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json", "date": "<PERSON><PERSON>, 08 Jul 2025 12:56:27 GMT", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=p162cerJfWcOFSa%2Fxv5u4loZ3qDq5ZQWxjgohGA6zvYC3piAzzrC7%2BUwbik8E5%2FY4yl735rCHj5p8ksInEX7tzL7GLoNQady%2B25%2Fy39qxlvGvw%3D%3D\"}]}", "server": "cloudflare", "transfer-encoding": "chunked", "x-apilayer-transaction-id": "a0979998-b9c6-4a60-ab14-5109b2ee0261", "x-increment-usage": "1", "x-quota-limit": "100", "x-quota-remaining": "8", "x-request-time": "0.027, 0.052"}, "body": "eyJwYWdpbmF0aW9uIjp7Im9mZnNldCI6MCwibGltaXQiOjEwMCwiY291bnQiOjEsInRvdGFsIjoxfSwiZGF0YSI6W3siaWQiOiI0MjE0Nzk3IiwiZ210IjoiOCIsImFpcnBvcnRfaWQiOiI3MTExIiwiaWF0YV9jb2RlIjoiU0lOIiwiY2l0eV9pYXRhX2NvZGUiOiJTSU4iLCJpY2FvX2NvZGUiOiJXU1NTIiwiY291bnRyeV9pc28yIjoiU0ciLCJnZW9uYW1lX2lkIjoiMTg4MDcyNSIsImxhdGl0dWRlIjoiMS4zNjExNzMiLCJsb25naXR1ZGUiOiIxMDMuOTkwMjA0IiwiYWlycG9ydF9uYW1lIjoiU2luZ2Fwb3JlIENoYW5naSIsImNvdW50cnlfbmFtZSI6IlNpbmdhcG9yZSIsInBob25lX251bWJlciI6Iig2NSkgNjU5NSA2ODYiLCJ0aW1lem9uZSI6IkFzaWFcL1NpbmdhcG9yZSJ9XX0=", "status": 200, "url": "https://api.aviationstack.com/v1/airports?access_key=********************************&iata_code=SIN"}, "revalidate": 31536000, "tags": []}