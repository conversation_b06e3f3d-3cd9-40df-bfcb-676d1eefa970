{"kind": "FETCH", "data": {"headers": {"access-control-allow-headers": "*", "access-control-allow-methods": "GET, POST, HEAD, OPTIONS", "access-control-allow-origin": "*", "cache-control": "no-cache, private", "cf-cache-status": "DYNAMIC", "cf-ray": "95bfbdd8af4e9146-MAA", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json", "date": "Tu<PERSON>, 08 Jul 2025 12:56:25 GMT", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=rEFhhxlPEvKSaQSLy97f9Nj1%2BYoKZNWiYQFYQaFfbLjjot%2FUp%2FwjyVe97eLOMquHluOc6RVRsIaI%2Flzpk8N4ujuSwB18Y1yBlYRVbfsSc73Ovg%3D%3D\"}]}", "server": "cloudflare", "transfer-encoding": "chunked", "x-apilayer-transaction-id": "c70b6c86-6ace-4a5f-886c-bdfeccc78541", "x-increment-usage": "1", "x-quota-limit": "100", "x-quota-remaining": "10", "x-request-time": "0.017, 0.044"}, "body": "eyJwYWdpbmF0aW9uIjp7Im9mZnNldCI6MCwibGltaXQiOjEwMCwiY291bnQiOjEsInRvdGFsIjoxfSwiZGF0YSI6W3siaWQiOiI0MjA4NDkwIiwiZ210IjoiNS4zMCIsImFpcnBvcnRfaWQiOiI4MDQiLCJpYXRhX2NvZGUiOiJCTFIiLCJjaXR5X2lhdGFfY29kZSI6IkJMUiIsImljYW9fY29kZSI6IlZPQkwiLCJjb3VudHJ5X2lzbzIiOiJJTiIsImdlb25hbWVfaWQiOiI3MzAzMzg2IiwibGF0aXR1ZGUiOiIxMy4xOTg4ODkiLCJsb25naXR1ZGUiOiI3Ny43MDU1NiIsImFpcnBvcnRfbmFtZSI6IkJhbmdhbG9yZSBJbnRlcm5hdGlvbmFsIEFpcnBvcnQiLCJjb3VudHJ5X25hbWUiOiJJbmRpYSIsInBob25lX251bWJlciI6IjA4MC02Njc4LTI0MjUiLCJ0aW1lem9uZSI6IkFzaWFcL0tvbGthdGEifV19", "status": 200, "url": "https://api.aviationstack.com/v1/airports?access_key=********************************&iata_code=BLR"}, "revalidate": 31536000, "tags": []}