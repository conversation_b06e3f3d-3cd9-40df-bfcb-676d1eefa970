{"kind": "FETCH", "data": {"headers": {"access-control-allow-headers": "*", "access-control-allow-methods": "GET, POST, HEAD, OPTIONS", "access-control-allow-origin": "*", "cache-control": "no-cache, private", "cf-cache-status": "DYNAMIC", "cf-ray": "95bfbddb7ab99146-MAA", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json", "date": "Tu<PERSON>, 08 Jul 2025 12:56:26 GMT", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=erWq9UjLyCZtWQXT8BxVD%2BSorygUaF8qBXHsu59XaYFNAaoky%2BNY5%2BXMqJGovKiBaIcEIgX6IEiJxfnuy5Eib551J4F8fqtkNGS7uOSC2e8n8w%3D%3D\"}]}", "server": "cloudflare", "transfer-encoding": "chunked", "x-apilayer-transaction-id": "936cd678-9ffb-4a65-a3ad-1c023a2ccb6e", "x-increment-usage": "1", "x-quota-limit": "100", "x-quota-remaining": "9", "x-request-time": "0.023, 0.053"}, "body": "eyJwYWdpbmF0aW9uIjp7Im9mZnNldCI6MCwibGltaXQiOjEwMCwiY291bnQiOjEsInRvdGFsIjoxfSwiZGF0YSI6W3siaWQiOiI0MjA5NjUyIiwiZ210IjoiNCIsImFpcnBvcnRfaWQiOiIxOTY2IiwiaWF0YV9jb2RlIjoiRFhCIiwiY2l0eV9pYXRhX2NvZGUiOiJEWEIiLCJpY2FvX2NvZGUiOiJPTURCIiwiY291bnRyeV9pc28yIjoiQUUiLCJnZW9uYW1lX2lkIjoiNjMwMDA5NiIsImxhdGl0dWRlIjoiMjUuMjQ4NjY1IiwibG9uZ2l0dWRlIjoiNTUuMzUyOTE3IiwiYWlycG9ydF9uYW1lIjoiRHViYWkiLCJjb3VudHJ5X25hbWUiOiJVbml0ZWQgQXJhYiBFbWlyYXRlcyIsInBob25lX251bWJlciI6IiArOTcxIDQgMjI0IDU1NTUiLCJ0aW1lem9uZSI6IkFzaWFcL0R1YmFpIn1dfQ==", "status": 200, "url": "https://api.aviationstack.com/v1/airports?access_key=********************************&iata_code=DXB"}, "revalidate": 31536000, "tags": []}