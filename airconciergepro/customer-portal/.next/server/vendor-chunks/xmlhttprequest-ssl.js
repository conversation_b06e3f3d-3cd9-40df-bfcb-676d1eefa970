/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlhttprequest-ssl";
exports.ids = ["vendor-chunks/xmlhttprequest-ssl"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js":
/*!***************************************************************!*\
  !*** ./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> DeFelippi <<EMAIL>>\n * @contributor David Ellis <<EMAIL>>\n * @license MIT\n */ var fs = __webpack_require__(/*! fs */ \"fs\");\nvar Url = __webpack_require__(/*! url */ \"url\");\nvar spawn = (__webpack_require__(/*! child_process */ \"child_process\").spawn);\n/**\n * Module exports.\n */ module.exports = XMLHttpRequest;\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */ function XMLHttpRequest(opts) {\n    \"use strict\";\n    opts = opts || {};\n    /**\n   * Private variables\n   */ var self = this;\n    var http = __webpack_require__(/*! http */ \"http\");\n    var https = __webpack_require__(/*! https */ \"https\");\n    // Holds http.js objects\n    var request;\n    var response;\n    // Request settings\n    var settings = {};\n    // Disable header blacklist.\n    // Not part of XHR specs.\n    var disableHeaderCheck = false;\n    // Set some default headers\n    var defaultHeaders = {\n        \"User-Agent\": \"node-XMLHttpRequest\",\n        \"Accept\": \"*/*\"\n    };\n    var headers = Object.assign({}, defaultHeaders);\n    // These headers are not user setable.\n    // The following are allowed but banned in the spec:\n    // * user-agent\n    var forbiddenRequestHeaders = [\n        \"accept-charset\",\n        \"accept-encoding\",\n        \"access-control-request-headers\",\n        \"access-control-request-method\",\n        \"connection\",\n        \"content-length\",\n        \"content-transfer-encoding\",\n        \"cookie\",\n        \"cookie2\",\n        \"date\",\n        \"expect\",\n        \"host\",\n        \"keep-alive\",\n        \"origin\",\n        \"referer\",\n        \"te\",\n        \"trailer\",\n        \"transfer-encoding\",\n        \"upgrade\",\n        \"via\"\n    ];\n    // These request methods are not allowed\n    var forbiddenRequestMethods = [\n        \"TRACE\",\n        \"TRACK\",\n        \"CONNECT\"\n    ];\n    // Send flag\n    var sendFlag = false;\n    // Error flag, used when errors occur or abort is called\n    var errorFlag = false;\n    var abortedFlag = false;\n    // Event listeners\n    var listeners = {};\n    /**\n   * Constants\n   */ this.UNSENT = 0;\n    this.OPENED = 1;\n    this.HEADERS_RECEIVED = 2;\n    this.LOADING = 3;\n    this.DONE = 4;\n    /**\n   * Public vars\n   */ // Current state\n    this.readyState = this.UNSENT;\n    // default ready state change handler in case one is not set or is set late\n    this.onreadystatechange = null;\n    // Result & response\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n    this.status = null;\n    this.statusText = null;\n    /**\n   * Private methods\n   */ /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */ var isAllowedHttpHeader = function(header) {\n        return disableHeaderCheck || header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1;\n    };\n    /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */ var isAllowedHttpMethod = function(method) {\n        return method && forbiddenRequestMethods.indexOf(method) === -1;\n    };\n    /**\n   * Public methods\n   */ /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */ this.open = function(method, url, async, user, password) {\n        this.abort();\n        errorFlag = false;\n        abortedFlag = false;\n        // Check for valid request method\n        if (!isAllowedHttpMethod(method)) {\n            throw new Error(\"SecurityError: Request method not allowed\");\n        }\n        settings = {\n            \"method\": method,\n            \"url\": url.toString(),\n            \"async\": typeof async !== \"boolean\" ? true : async,\n            \"user\": user || null,\n            \"password\": password || null\n        };\n        setState(this.OPENED);\n    };\n    /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */ this.setDisableHeaderCheck = function(state) {\n        disableHeaderCheck = state;\n    };\n    /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */ this.setRequestHeader = function(header, value) {\n        if (this.readyState != this.OPENED) {\n            throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n        }\n        if (!isAllowedHttpHeader(header)) {\n            console.warn('Refused to set unsafe header \"' + header + '\"');\n            return false;\n        }\n        if (sendFlag) {\n            throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n        }\n        headers[header] = value;\n        return true;\n    };\n    /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */ this.getResponseHeader = function(header) {\n        if (typeof header === \"string\" && this.readyState > this.OPENED && response.headers[header.toLowerCase()] && !errorFlag) {\n            return response.headers[header.toLowerCase()];\n        }\n        return null;\n    };\n    /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */ this.getAllResponseHeaders = function() {\n        if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n            return \"\";\n        }\n        var result = \"\";\n        for(var i in response.headers){\n            // Cookie headers are excluded\n            if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n                result += i + \": \" + response.headers[i] + \"\\r\\n\";\n            }\n        }\n        return result.substr(0, result.length - 2);\n    };\n    /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */ this.getRequestHeader = function(name) {\n        // @TODO Make this case insensitive\n        if (typeof name === \"string\" && headers[name]) {\n            return headers[name];\n        }\n        return \"\";\n    };\n    /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */ this.send = function(data) {\n        if (this.readyState != this.OPENED) {\n            throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n        }\n        if (sendFlag) {\n            throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n        }\n        var ssl = false, local = false;\n        var url = Url.parse(settings.url);\n        var host;\n        // Determine the server\n        switch(url.protocol){\n            case \"https:\":\n                ssl = true;\n            // SSL & non-SSL both need host, no break here.\n            case \"http:\":\n                host = url.hostname;\n                break;\n            case \"file:\":\n                local = true;\n                break;\n            case undefined:\n            case \"\":\n                host = \"localhost\";\n                break;\n            default:\n                throw new Error(\"Protocol not supported.\");\n        }\n        // Load files off the local filesystem (file://)\n        if (local) {\n            if (settings.method !== \"GET\") {\n                throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n            }\n            if (settings.async) {\n                fs.readFile(unescape(url.pathname), function(error, data) {\n                    if (error) {\n                        self.handleError(error, error.errno || -1);\n                    } else {\n                        self.status = 200;\n                        self.responseText = data.toString(\"utf8\");\n                        self.response = data;\n                        setState(self.DONE);\n                    }\n                });\n            } else {\n                try {\n                    this.response = fs.readFileSync(unescape(url.pathname));\n                    this.responseText = this.response.toString(\"utf8\");\n                    this.status = 200;\n                    setState(self.DONE);\n                } catch (e) {\n                    this.handleError(e, e.errno || -1);\n                }\n            }\n            return;\n        }\n        // Default to port 80. If accessing localhost on another port be sure\n        // to use http://localhost:port/path\n        var port = url.port || (ssl ? 443 : 80);\n        // Add query string if one is used\n        var uri = url.pathname + (url.search ? url.search : \"\");\n        // Set the Host header or the server may reject the request\n        headers[\"Host\"] = host;\n        if (!(ssl && port === 443 || port === 80)) {\n            headers[\"Host\"] += \":\" + url.port;\n        }\n        // Set Basic Auth if necessary\n        if (settings.user) {\n            if (typeof settings.password == \"undefined\") {\n                settings.password = \"\";\n            }\n            var authBuf = new Buffer(settings.user + \":\" + settings.password);\n            headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n        }\n        // Set content length header\n        if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n            data = null;\n        } else if (data) {\n            headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n            var headersKeys = Object.keys(headers);\n            if (!headersKeys.some(function(h) {\n                return h.toLowerCase() === \"content-type\";\n            })) {\n                headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n            }\n        } else if (settings.method === \"POST\") {\n            // For a post with no data set Content-Length: 0.\n            // This is required by buggy servers that don't meet the specs.\n            headers[\"Content-Length\"] = 0;\n        }\n        var agent = opts.agent || false;\n        var options = {\n            host: host,\n            port: port,\n            path: uri,\n            method: settings.method,\n            headers: headers,\n            agent: agent\n        };\n        if (ssl) {\n            options.pfx = opts.pfx;\n            options.key = opts.key;\n            options.passphrase = opts.passphrase;\n            options.cert = opts.cert;\n            options.ca = opts.ca;\n            options.ciphers = opts.ciphers;\n            options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n        }\n        // Reset error flag\n        errorFlag = false;\n        // Handle async requests\n        if (settings.async) {\n            // Use the proper protocol\n            var doRequest = ssl ? https.request : http.request;\n            // Request is being sent, set send flag\n            sendFlag = true;\n            // As per spec, this is called here for historical reasons.\n            self.dispatchEvent(\"readystatechange\");\n            // Handler for the response\n            var responseHandler = function(resp) {\n                // Set response var to the response we got back\n                // This is so it remains accessable outside this scope\n                response = resp;\n                // Check for redirect\n                // @TODO Prevent looped redirects\n                if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n                    // Change URL to the redirect location\n                    settings.url = response.headers.location;\n                    var url = Url.parse(settings.url);\n                    // Set host var in case it's used later\n                    host = url.hostname;\n                    // Options for the new request\n                    var newOptions = {\n                        hostname: url.hostname,\n                        port: url.port,\n                        path: url.path,\n                        method: response.statusCode === 303 ? \"GET\" : settings.method,\n                        headers: headers\n                    };\n                    if (ssl) {\n                        newOptions.pfx = opts.pfx;\n                        newOptions.key = opts.key;\n                        newOptions.passphrase = opts.passphrase;\n                        newOptions.cert = opts.cert;\n                        newOptions.ca = opts.ca;\n                        newOptions.ciphers = opts.ciphers;\n                        newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n                    }\n                    // Issue the new request\n                    request = doRequest(newOptions, responseHandler).on(\"error\", errorHandler);\n                    request.end();\n                    // @TODO Check if an XHR event needs to be fired here\n                    return;\n                }\n                setState(self.HEADERS_RECEIVED);\n                self.status = response.statusCode;\n                response.on(\"data\", function(chunk) {\n                    // Make sure there's some data\n                    if (chunk) {\n                        var data = Buffer.from(chunk);\n                        self.response = Buffer.concat([\n                            self.response,\n                            data\n                        ]);\n                    }\n                    // Don't emit state changes if the connection has been aborted.\n                    if (sendFlag) {\n                        setState(self.LOADING);\n                    }\n                });\n                response.on(\"end\", function() {\n                    if (sendFlag) {\n                        // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n                        // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n                        sendFlag = false;\n                        // Discard the 'end' event if the connection has been aborted\n                        setState(self.DONE);\n                        // Construct responseText from response\n                        self.responseText = self.response.toString(\"utf8\");\n                    }\n                });\n                response.on(\"error\", function(error) {\n                    self.handleError(error);\n                });\n            };\n            // Error handler for the request\n            var errorHandler = function(error) {\n                // In the case of https://nodejs.org/api/http.html#requestreusedsocket triggering an ECONNRESET,\n                // don't fail the xhr request, attempt again.\n                if (request.reusedSocket && error.code === \"ECONNRESET\") return doRequest(options, responseHandler).on(\"error\", errorHandler);\n                self.handleError(error);\n            };\n            // Create the request\n            request = doRequest(options, responseHandler).on(\"error\", errorHandler);\n            if (opts.autoUnref) {\n                request.on(\"socket\", (socket)=>{\n                    socket.unref();\n                });\n            }\n            // Node 0.4 and later won't accept empty data. Make sure it's needed.\n            if (data) {\n                request.write(data);\n            }\n            request.end();\n            self.dispatchEvent(\"loadstart\");\n        } else {\n            // Create a temporary file for communication with the other Node process\n            var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n            var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n            fs.writeFileSync(syncFile, \"\", \"utf8\");\n            // The async request the other Node process executes\n            var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\" + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\" + \"var options = \" + JSON.stringify(options) + \";\" + \"var responseText = '';\" + \"var responseData = Buffer.alloc(0);\" + \"var req = doRequest(options, function(response) {\" + \"response.on('data', function(chunk) {\" + \"  var data = Buffer.from(chunk);\" + \"  responseText += data.toString('utf8');\" + \"  responseData = Buffer.concat([responseData, data]);\" + \"});\" + \"response.on('end', function() {\" + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + \"response.on('error', function(error) {\" + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + \"}).on('error', function(error) {\" + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + (data ? \"req.write('\" + JSON.stringify(data).slice(1, -1).replace(/'/g, \"\\\\'\") + \"');\" : \"\") + \"req.end();\";\n            // Start the other Node Process, executing this string\n            var syncProc = spawn(process.argv[0], [\n                \"-e\",\n                execString\n            ]);\n            var statusText;\n            while(fs.existsSync(syncFile)){\n            // Wait while the sync file is empty\n            }\n            self.responseText = fs.readFileSync(contentFile, \"utf8\");\n            // Kill the child process once the file has data\n            syncProc.stdin.end();\n            // Remove the temporary file\n            fs.unlinkSync(contentFile);\n            if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n                // If the file returned an error, handle it\n                var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n                self.handleError(errorObj, 503);\n            } else {\n                // If the file returned okay, parse its data and move to the DONE state\n                self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n                var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n                response = {\n                    statusCode: self.status,\n                    headers: resp.data.headers\n                };\n                self.responseText = resp.data.text;\n                self.response = Buffer.from(resp.data.data, \"base64\");\n                setState(self.DONE, true);\n            }\n        }\n    };\n    /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */ this.handleError = function(error, status) {\n        this.status = status || 0;\n        this.statusText = error;\n        this.responseText = error.stack;\n        errorFlag = true;\n        setState(this.DONE);\n    };\n    /**\n   * Aborts a request.\n   */ this.abort = function() {\n        if (request) {\n            request.abort();\n            request = null;\n        }\n        headers = Object.assign({}, defaultHeaders);\n        this.responseText = \"\";\n        this.responseXML = \"\";\n        this.response = Buffer.alloc(0);\n        errorFlag = abortedFlag = true;\n        if (this.readyState !== this.UNSENT && (this.readyState !== this.OPENED || sendFlag) && this.readyState !== this.DONE) {\n            sendFlag = false;\n            setState(this.DONE);\n        }\n        this.readyState = this.UNSENT;\n    };\n    /**\n   * Adds an event listener. Preferred method of binding to events.\n   */ this.addEventListener = function(event, callback) {\n        if (!(event in listeners)) {\n            listeners[event] = [];\n        }\n        // Currently allows duplicate callbacks. Should it?\n        listeners[event].push(callback);\n    };\n    /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */ this.removeEventListener = function(event, callback) {\n        if (event in listeners) {\n            // Filter will return a new array with the callback removed\n            listeners[event] = listeners[event].filter(function(ev) {\n                return ev !== callback;\n            });\n        }\n    };\n    /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */ this.dispatchEvent = function(event) {\n        if (typeof self[\"on\" + event] === \"function\") {\n            if (this.readyState === this.DONE && settings.async) setTimeout(function() {\n                self[\"on\" + event]();\n            }, 0);\n            else self[\"on\" + event]();\n        }\n        if (event in listeners) {\n            for(let i = 0, len = listeners[event].length; i < len; i++){\n                if (this.readyState === this.DONE) setTimeout(function() {\n                    listeners[event][i].call(self);\n                }, 0);\n                else listeners[event][i].call(self);\n            }\n        }\n    };\n    /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */ var setState = function(state) {\n        if (self.readyState === state || self.readyState === self.UNSENT && abortedFlag) return;\n        self.readyState = state;\n        if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n            self.dispatchEvent(\"readystatechange\");\n        }\n        if (self.readyState === self.DONE) {\n            let fire;\n            if (abortedFlag) fire = \"abort\";\n            else if (errorFlag) fire = \"error\";\n            else fire = \"load\";\n            self.dispatchEvent(fire);\n            // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n            self.dispatchEvent(\"loadend\");\n        }\n    };\n}\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\n");

/***/ })

};
;