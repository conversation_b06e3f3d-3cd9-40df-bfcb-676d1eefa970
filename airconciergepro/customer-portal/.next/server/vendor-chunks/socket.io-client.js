/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socket.io-client";
exports.ids = ["vendor-chunks/socket.io-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/browser.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-env browser */ /**\n * This is the web browser implementation of `debug()`.\n */ exports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (()=>{\n    let warned = false;\n    return ()=>{\n        if (!warned) {\n            warned = true;\n            console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n        }\n    };\n})();\n/**\n * Colors.\n */ exports.colors = [\n    \"#0000CC\",\n    \"#0000FF\",\n    \"#0033CC\",\n    \"#0033FF\",\n    \"#0066CC\",\n    \"#0066FF\",\n    \"#0099CC\",\n    \"#0099FF\",\n    \"#00CC00\",\n    \"#00CC33\",\n    \"#00CC66\",\n    \"#00CC99\",\n    \"#00CCCC\",\n    \"#00CCFF\",\n    \"#3300CC\",\n    \"#3300FF\",\n    \"#3333CC\",\n    \"#3333FF\",\n    \"#3366CC\",\n    \"#3366FF\",\n    \"#3399CC\",\n    \"#3399FF\",\n    \"#33CC00\",\n    \"#33CC33\",\n    \"#33CC66\",\n    \"#33CC99\",\n    \"#33CCCC\",\n    \"#33CCFF\",\n    \"#6600CC\",\n    \"#6600FF\",\n    \"#6633CC\",\n    \"#6633FF\",\n    \"#66CC00\",\n    \"#66CC33\",\n    \"#9900CC\",\n    \"#9900FF\",\n    \"#9933CC\",\n    \"#9933FF\",\n    \"#99CC00\",\n    \"#99CC33\",\n    \"#CC0000\",\n    \"#CC0033\",\n    \"#CC0066\",\n    \"#CC0099\",\n    \"#CC00CC\",\n    \"#CC00FF\",\n    \"#CC3300\",\n    \"#CC3333\",\n    \"#CC3366\",\n    \"#CC3399\",\n    \"#CC33CC\",\n    \"#CC33FF\",\n    \"#CC6600\",\n    \"#CC6633\",\n    \"#CC9900\",\n    \"#CC9933\",\n    \"#CCCC00\",\n    \"#CCCC33\",\n    \"#FF0000\",\n    \"#FF0033\",\n    \"#FF0066\",\n    \"#FF0099\",\n    \"#FF00CC\",\n    \"#FF00FF\",\n    \"#FF3300\",\n    \"#FF3333\",\n    \"#FF3366\",\n    \"#FF3399\",\n    \"#FF33CC\",\n    \"#FF33FF\",\n    \"#FF6600\",\n    \"#FF6633\",\n    \"#FF9900\",\n    \"#FF9933\",\n    \"#FFCC00\",\n    \"#FFCC33\"\n];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */ // eslint-disable-next-line complexity\nfunction useColors() {\n    // NB: In an Electron preload script, document will be defined but not fully\n    // initialized. Since we know we're in Chrome, we'll just detect this case\n    // explicitly\n    if (false) {}\n    // Internet Explorer and Edge do not support colors.\n    if (typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n        return false;\n    }\n    let m;\n    // Is webkit? http://stackoverflow.com/a/16459606/376773\n    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n    return typeof document !== \"undefined\" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n     false && (0) || // Is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    typeof navigator !== \"undefined\" && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n    typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    args[0] = (this.useColors ? \"%c\" : \"\") + this.namespace + (this.useColors ? \" %c\" : \" \") + args[0] + (this.useColors ? \"%c \" : \" \") + \"+\" + module.exports.humanize(this.diff);\n    if (!this.useColors) {\n        return;\n    }\n    const c = \"color: \" + this.color;\n    args.splice(1, 0, c, \"color: inherit\");\n    // The final \"%c\" is somewhat tricky, because there could be other\n    // arguments passed either before or after the %c, so we need to\n    // figure out the correct index to insert the CSS into\n    let index = 0;\n    let lastC = 0;\n    args[0].replace(/%[a-zA-Z%]/g, (match)=>{\n        if (match === \"%%\") {\n            return;\n        }\n        index++;\n        if (match === \"%c\") {\n            // We only are interested in the *last* %c\n            // (the user may have provided their own)\n            lastC = index;\n        }\n    });\n    args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */ exports.log = console.debug || console.log || (()=>{});\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    try {\n        if (namespaces) {\n            exports.storage.setItem(\"debug\", namespaces);\n        } else {\n            exports.storage.removeItem(\"debug\");\n        }\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    let r;\n    try {\n        r = exports.storage.getItem(\"debug\");\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n    if (!r && typeof process !== \"undefined\" && \"env\" in process) {\n        r = process.env.DEBUG;\n    }\n    return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */ function localstorage() {\n    try {\n        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n        // The Browser also has localStorage in the global context.\n        return localStorage;\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */ formatters.j = function(v) {\n    try {\n        return JSON.stringify(v);\n    } catch (error) {\n        return \"[UnexpectedJSONParseError]: \" + error.message;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2Jyb3dzZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsc0JBQXNCLEdBRXRCOztDQUVDLEdBRURBLGtCQUFrQixHQUFHQztBQUNyQkQsWUFBWSxHQUFHRTtBQUNmRixZQUFZLEdBQUdHO0FBQ2ZILGlCQUFpQixHQUFHSTtBQUNwQkosZUFBZSxHQUFHTTtBQUNsQk4sZUFBZSxHQUFHLENBQUM7SUFDbEIsSUFBSVEsU0FBUztJQUViLE9BQU87UUFDTixJQUFJLENBQUNBLFFBQVE7WUFDWkEsU0FBUztZQUNUQyxRQUFRQyxJQUFJLENBQUM7UUFDZDtJQUNEO0FBQ0Q7QUFFQTs7Q0FFQyxHQUVEVixjQUFjLEdBQUc7SUFDaEI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDQTtBQUVEOzs7Ozs7Q0FNQyxHQUVELHNDQUFzQztBQUN0QyxTQUFTSTtJQUNSLDRFQUE0RTtJQUM1RSwwRUFBMEU7SUFDMUUsYUFBYTtJQUNiLElBQUksS0FBZ0gsRUFBRSxFQUVySDtJQUVELG9EQUFvRDtJQUNwRCxJQUFJLE9BQU9ZLGNBQWMsZUFBZUEsVUFBVUMsU0FBUyxJQUFJRCxVQUFVQyxTQUFTLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLDBCQUEwQjtRQUNoSSxPQUFPO0lBQ1I7SUFFQSxJQUFJQztJQUVKLHdEQUF3RDtJQUN4RCw0RkFBNEY7SUFDNUYsT0FBTyxPQUFRQyxhQUFhLGVBQWVBLFNBQVNDLGVBQWUsSUFBSUQsU0FBU0MsZUFBZSxDQUFDQyxLQUFLLElBQUlGLFNBQVNDLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDQyxnQkFBZ0IsSUFDdkosdURBQXVEO0lBQ3RELE1BQStDLElBQUtaLENBQUFBLENBQTJFLEtBQ2hJLHFCQUFxQjtJQUNyQiw4RUFBOEU7SUFDN0UsT0FBT0ksY0FBYyxlQUFlQSxVQUFVQyxTQUFTLElBQUtHLENBQUFBLElBQUlKLFVBQVVDLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsaUJBQWdCLEtBQU1TLFNBQVNSLENBQUMsQ0FBQyxFQUFFLEVBQUUsT0FBTyxNQUNySixtRUFBbUU7SUFDbEUsT0FBT0osY0FBYyxlQUFlQSxVQUFVQyxTQUFTLElBQUlELFVBQVVDLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxLQUFLLENBQUM7QUFDdEc7QUFFQTs7OztDQUlDLEdBRUQsU0FBU2xCLFdBQVc0QixJQUFJO0lBQ3ZCQSxJQUFJLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSSxDQUFDekIsU0FBUyxHQUFHLE9BQU8sRUFBQyxJQUNuQyxJQUFJLENBQUMwQixTQUFTLEdBQ2IsS0FBSSxDQUFDMUIsU0FBUyxHQUFHLFFBQVEsR0FBRSxJQUM1QnlCLElBQUksQ0FBQyxFQUFFLEdBQ04sS0FBSSxDQUFDekIsU0FBUyxHQUFHLFFBQVEsR0FBRSxJQUM1QixNQUFNMkIsT0FBTy9CLE9BQU8sQ0FBQ2dDLFFBQVEsQ0FBQyxJQUFJLENBQUNDLElBQUk7SUFFeEMsSUFBSSxDQUFDLElBQUksQ0FBQzdCLFNBQVMsRUFBRTtRQUNwQjtJQUNEO0lBRUEsTUFBTThCLElBQUksWUFBWSxJQUFJLENBQUNDLEtBQUs7SUFDaENOLEtBQUtPLE1BQU0sQ0FBQyxHQUFHLEdBQUdGLEdBQUc7SUFFckIsa0VBQWtFO0lBQ2xFLGdFQUFnRTtJQUNoRSxzREFBc0Q7SUFDdEQsSUFBSUcsUUFBUTtJQUNaLElBQUlDLFFBQVE7SUFDWlQsSUFBSSxDQUFDLEVBQUUsQ0FBQ1UsT0FBTyxDQUFDLGVBQWVwQixDQUFBQTtRQUM5QixJQUFJQSxVQUFVLE1BQU07WUFDbkI7UUFDRDtRQUNBa0I7UUFDQSxJQUFJbEIsVUFBVSxNQUFNO1lBQ25CLDBDQUEwQztZQUMxQyx5Q0FBeUM7WUFDekNtQixRQUFRRDtRQUNUO0lBQ0Q7SUFFQVIsS0FBS08sTUFBTSxDQUFDRSxPQUFPLEdBQUdKO0FBQ3ZCO0FBRUE7Ozs7Ozs7Q0FPQyxHQUNEbEMsV0FBVyxHQUFHUyxRQUFRZ0MsS0FBSyxJQUFJaEMsUUFBUStCLEdBQUcsSUFBSyxNQUFPO0FBRXREOzs7OztDQUtDLEdBQ0QsU0FBU3RDLEtBQUt3QyxVQUFVO0lBQ3ZCLElBQUk7UUFDSCxJQUFJQSxZQUFZO1lBQ2YxQyxRQUFRSyxPQUFPLENBQUNzQyxPQUFPLENBQUMsU0FBU0Q7UUFDbEMsT0FBTztZQUNOMUMsUUFBUUssT0FBTyxDQUFDdUMsVUFBVSxDQUFDO1FBQzVCO0lBQ0QsRUFBRSxPQUFPQyxPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztBQUNEO0FBRUE7Ozs7O0NBS0MsR0FDRCxTQUFTMUM7SUFDUixJQUFJMkM7SUFDSixJQUFJO1FBQ0hBLElBQUk5QyxRQUFRSyxPQUFPLENBQUMwQyxPQUFPLENBQUM7SUFDN0IsRUFBRSxPQUFPRixPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztJQUVBLHNFQUFzRTtJQUN0RSxJQUFJLENBQUNDLEtBQUssT0FBT2pDLFlBQVksZUFBZSxTQUFTQSxTQUFTO1FBQzdEaUMsSUFBSWpDLFFBQVFtQyxHQUFHLENBQUNDLEtBQUs7SUFDdEI7SUFFQSxPQUFPSDtBQUNSO0FBRUE7Ozs7Ozs7OztDQVNDLEdBRUQsU0FBU3hDO0lBQ1IsSUFBSTtRQUNILHVHQUF1RztRQUN2RywyREFBMkQ7UUFDM0QsT0FBTzRDO0lBQ1IsRUFBRSxPQUFPTCxPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztBQUNEO0FBRUFkLE9BQU8vQixPQUFPLEdBQUdtRCxtQkFBT0EsQ0FBQywwRkFBWW5EO0FBRXJDLE1BQU0sRUFBQ29ELFVBQVUsRUFBQyxHQUFHckIsT0FBTy9CLE9BQU87QUFFbkM7O0NBRUMsR0FFRG9ELFdBQVdDLENBQUMsR0FBRyxTQUFVQyxDQUFDO0lBQ3pCLElBQUk7UUFDSCxPQUFPQyxLQUFLQyxTQUFTLENBQUNGO0lBQ3ZCLEVBQUUsT0FBT1QsT0FBTztRQUNmLE9BQU8saUNBQWlDQSxNQUFNWSxPQUFPO0lBQ3REO0FBQ0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJjb25jaWVyZ2Vwcm8tY3VzdG9tZXItcG9ydGFsLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1jbGllbnQvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9icm93c2VyLmpzP2MwNjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWVudiBicm93c2VyICovXG5cbi8qKlxuICogVGhpcyBpcyB0aGUgd2ViIGJyb3dzZXIgaW1wbGVtZW50YXRpb24gb2YgYGRlYnVnKClgLlxuICovXG5cbmV4cG9ydHMuZm9ybWF0QXJncyA9IGZvcm1hdEFyZ3M7XG5leHBvcnRzLnNhdmUgPSBzYXZlO1xuZXhwb3J0cy5sb2FkID0gbG9hZDtcbmV4cG9ydHMudXNlQ29sb3JzID0gdXNlQ29sb3JzO1xuZXhwb3J0cy5zdG9yYWdlID0gbG9jYWxzdG9yYWdlKCk7XG5leHBvcnRzLmRlc3Ryb3kgPSAoKCkgPT4ge1xuXHRsZXQgd2FybmVkID0gZmFsc2U7XG5cblx0cmV0dXJuICgpID0+IHtcblx0XHRpZiAoIXdhcm5lZCkge1xuXHRcdFx0d2FybmVkID0gdHJ1ZTtcblx0XHRcdGNvbnNvbGUud2FybignSW5zdGFuY2UgbWV0aG9kIGBkZWJ1Zy5kZXN0cm95KClgIGlzIGRlcHJlY2F0ZWQgYW5kIG5vIGxvbmdlciBkb2VzIGFueXRoaW5nLiBJdCB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbiBvZiBgZGVidWdgLicpO1xuXHRcdH1cblx0fTtcbn0pKCk7XG5cbi8qKlxuICogQ29sb3JzLlxuICovXG5cbmV4cG9ydHMuY29sb3JzID0gW1xuXHQnIzAwMDBDQycsXG5cdCcjMDAwMEZGJyxcblx0JyMwMDMzQ0MnLFxuXHQnIzAwMzNGRicsXG5cdCcjMDA2NkNDJyxcblx0JyMwMDY2RkYnLFxuXHQnIzAwOTlDQycsXG5cdCcjMDA5OUZGJyxcblx0JyMwMENDMDAnLFxuXHQnIzAwQ0MzMycsXG5cdCcjMDBDQzY2Jyxcblx0JyMwMENDOTknLFxuXHQnIzAwQ0NDQycsXG5cdCcjMDBDQ0ZGJyxcblx0JyMzMzAwQ0MnLFxuXHQnIzMzMDBGRicsXG5cdCcjMzMzM0NDJyxcblx0JyMzMzMzRkYnLFxuXHQnIzMzNjZDQycsXG5cdCcjMzM2NkZGJyxcblx0JyMzMzk5Q0MnLFxuXHQnIzMzOTlGRicsXG5cdCcjMzNDQzAwJyxcblx0JyMzM0NDMzMnLFxuXHQnIzMzQ0M2NicsXG5cdCcjMzNDQzk5Jyxcblx0JyMzM0NDQ0MnLFxuXHQnIzMzQ0NGRicsXG5cdCcjNjYwMENDJyxcblx0JyM2NjAwRkYnLFxuXHQnIzY2MzNDQycsXG5cdCcjNjYzM0ZGJyxcblx0JyM2NkNDMDAnLFxuXHQnIzY2Q0MzMycsXG5cdCcjOTkwMENDJyxcblx0JyM5OTAwRkYnLFxuXHQnIzk5MzNDQycsXG5cdCcjOTkzM0ZGJyxcblx0JyM5OUNDMDAnLFxuXHQnIzk5Q0MzMycsXG5cdCcjQ0MwMDAwJyxcblx0JyNDQzAwMzMnLFxuXHQnI0NDMDA2NicsXG5cdCcjQ0MwMDk5Jyxcblx0JyNDQzAwQ0MnLFxuXHQnI0NDMDBGRicsXG5cdCcjQ0MzMzAwJyxcblx0JyNDQzMzMzMnLFxuXHQnI0NDMzM2NicsXG5cdCcjQ0MzMzk5Jyxcblx0JyNDQzMzQ0MnLFxuXHQnI0NDMzNGRicsXG5cdCcjQ0M2NjAwJyxcblx0JyNDQzY2MzMnLFxuXHQnI0NDOTkwMCcsXG5cdCcjQ0M5OTMzJyxcblx0JyNDQ0NDMDAnLFxuXHQnI0NDQ0MzMycsXG5cdCcjRkYwMDAwJyxcblx0JyNGRjAwMzMnLFxuXHQnI0ZGMDA2NicsXG5cdCcjRkYwMDk5Jyxcblx0JyNGRjAwQ0MnLFxuXHQnI0ZGMDBGRicsXG5cdCcjRkYzMzAwJyxcblx0JyNGRjMzMzMnLFxuXHQnI0ZGMzM2NicsXG5cdCcjRkYzMzk5Jyxcblx0JyNGRjMzQ0MnLFxuXHQnI0ZGMzNGRicsXG5cdCcjRkY2NjAwJyxcblx0JyNGRjY2MzMnLFxuXHQnI0ZGOTkwMCcsXG5cdCcjRkY5OTMzJyxcblx0JyNGRkNDMDAnLFxuXHQnI0ZGQ0MzMydcbl07XG5cbi8qKlxuICogQ3VycmVudGx5IG9ubHkgV2ViS2l0LWJhc2VkIFdlYiBJbnNwZWN0b3JzLCBGaXJlZm94ID49IHYzMSxcbiAqIGFuZCB0aGUgRmlyZWJ1ZyBleHRlbnNpb24gKGFueSBGaXJlZm94IHZlcnNpb24pIGFyZSBrbm93blxuICogdG8gc3VwcG9ydCBcIiVjXCIgQ1NTIGN1c3RvbWl6YXRpb25zLlxuICpcbiAqIFRPRE86IGFkZCBhIGBsb2NhbFN0b3JhZ2VgIHZhcmlhYmxlIHRvIGV4cGxpY2l0bHkgZW5hYmxlL2Rpc2FibGUgY29sb3JzXG4gKi9cblxuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGNvbXBsZXhpdHlcbmZ1bmN0aW9uIHVzZUNvbG9ycygpIHtcblx0Ly8gTkI6IEluIGFuIEVsZWN0cm9uIHByZWxvYWQgc2NyaXB0LCBkb2N1bWVudCB3aWxsIGJlIGRlZmluZWQgYnV0IG5vdCBmdWxseVxuXHQvLyBpbml0aWFsaXplZC4gU2luY2Ugd2Uga25vdyB3ZSdyZSBpbiBDaHJvbWUsIHdlJ2xsIGp1c3QgZGV0ZWN0IHRoaXMgY2FzZVxuXHQvLyBleHBsaWNpdGx5XG5cdGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cucHJvY2VzcyAmJiAod2luZG93LnByb2Nlc3MudHlwZSA9PT0gJ3JlbmRlcmVyJyB8fCB3aW5kb3cucHJvY2Vzcy5fX253anMpKSB7XG5cdFx0cmV0dXJuIHRydWU7XG5cdH1cblxuXHQvLyBJbnRlcm5ldCBFeHBsb3JlciBhbmQgRWRnZSBkbyBub3Qgc3VwcG9ydCBjb2xvcnMuXG5cdGlmICh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJyAmJiBuYXZpZ2F0b3IudXNlckFnZW50ICYmIG5hdmlnYXRvci51c2VyQWdlbnQudG9Mb3dlckNhc2UoKS5tYXRjaCgvKGVkZ2V8dHJpZGVudClcXC8oXFxkKykvKSkge1xuXHRcdHJldHVybiBmYWxzZTtcblx0fVxuXG5cdGxldCBtO1xuXG5cdC8vIElzIHdlYmtpdD8gaHR0cDovL3N0YWNrb3ZlcmZsb3cuY29tL2EvMTY0NTk2MDYvMzc2NzczXG5cdC8vIGRvY3VtZW50IGlzIHVuZGVmaW5lZCBpbiByZWFjdC1uYXRpdmU6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC1uYXRpdmUvcHVsbC8xNjMyXG5cdHJldHVybiAodHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyAmJiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQgJiYgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlICYmIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5XZWJraXRBcHBlYXJhbmNlKSB8fFxuXHRcdC8vIElzIGZpcmVidWc/IGh0dHA6Ly9zdGFja292ZXJmbG93LmNvbS9hLzM5ODEyMC8zNzY3NzNcblx0XHQodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmNvbnNvbGUgJiYgKHdpbmRvdy5jb25zb2xlLmZpcmVidWcgfHwgKHdpbmRvdy5jb25zb2xlLmV4Y2VwdGlvbiAmJiB3aW5kb3cuY29uc29sZS50YWJsZSkpKSB8fFxuXHRcdC8vIElzIGZpcmVmb3ggPj0gdjMxP1xuXHRcdC8vIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvVG9vbHMvV2ViX0NvbnNvbGUjU3R5bGluZ19tZXNzYWdlc1xuXHRcdCh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJyAmJiBuYXZpZ2F0b3IudXNlckFnZW50ICYmIChtID0gbmF2aWdhdG9yLnVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpLm1hdGNoKC9maXJlZm94XFwvKFxcZCspLykpICYmIHBhcnNlSW50KG1bMV0sIDEwKSA+PSAzMSkgfHxcblx0XHQvLyBEb3VibGUgY2hlY2sgd2Via2l0IGluIHVzZXJBZ2VudCBqdXN0IGluIGNhc2Ugd2UgYXJlIGluIGEgd29ya2VyXG5cdFx0KHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci51c2VyQWdlbnQgJiYgbmF2aWdhdG9yLnVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpLm1hdGNoKC9hcHBsZXdlYmtpdFxcLyhcXGQrKS8pKTtcbn1cblxuLyoqXG4gKiBDb2xvcml6ZSBsb2cgYXJndW1lbnRzIGlmIGVuYWJsZWQuXG4gKlxuICogQGFwaSBwdWJsaWNcbiAqL1xuXG5mdW5jdGlvbiBmb3JtYXRBcmdzKGFyZ3MpIHtcblx0YXJnc1swXSA9ICh0aGlzLnVzZUNvbG9ycyA/ICclYycgOiAnJykgK1xuXHRcdHRoaXMubmFtZXNwYWNlICtcblx0XHQodGhpcy51c2VDb2xvcnMgPyAnICVjJyA6ICcgJykgK1xuXHRcdGFyZ3NbMF0gK1xuXHRcdCh0aGlzLnVzZUNvbG9ycyA/ICclYyAnIDogJyAnKSArXG5cdFx0JysnICsgbW9kdWxlLmV4cG9ydHMuaHVtYW5pemUodGhpcy5kaWZmKTtcblxuXHRpZiAoIXRoaXMudXNlQ29sb3JzKSB7XG5cdFx0cmV0dXJuO1xuXHR9XG5cblx0Y29uc3QgYyA9ICdjb2xvcjogJyArIHRoaXMuY29sb3I7XG5cdGFyZ3Muc3BsaWNlKDEsIDAsIGMsICdjb2xvcjogaW5oZXJpdCcpO1xuXG5cdC8vIFRoZSBmaW5hbCBcIiVjXCIgaXMgc29tZXdoYXQgdHJpY2t5LCBiZWNhdXNlIHRoZXJlIGNvdWxkIGJlIG90aGVyXG5cdC8vIGFyZ3VtZW50cyBwYXNzZWQgZWl0aGVyIGJlZm9yZSBvciBhZnRlciB0aGUgJWMsIHNvIHdlIG5lZWQgdG9cblx0Ly8gZmlndXJlIG91dCB0aGUgY29ycmVjdCBpbmRleCB0byBpbnNlcnQgdGhlIENTUyBpbnRvXG5cdGxldCBpbmRleCA9IDA7XG5cdGxldCBsYXN0QyA9IDA7XG5cdGFyZ3NbMF0ucmVwbGFjZSgvJVthLXpBLVolXS9nLCBtYXRjaCA9PiB7XG5cdFx0aWYgKG1hdGNoID09PSAnJSUnKSB7XG5cdFx0XHRyZXR1cm47XG5cdFx0fVxuXHRcdGluZGV4Kys7XG5cdFx0aWYgKG1hdGNoID09PSAnJWMnKSB7XG5cdFx0XHQvLyBXZSBvbmx5IGFyZSBpbnRlcmVzdGVkIGluIHRoZSAqbGFzdCogJWNcblx0XHRcdC8vICh0aGUgdXNlciBtYXkgaGF2ZSBwcm92aWRlZCB0aGVpciBvd24pXG5cdFx0XHRsYXN0QyA9IGluZGV4O1xuXHRcdH1cblx0fSk7XG5cblx0YXJncy5zcGxpY2UobGFzdEMsIDAsIGMpO1xufVxuXG4vKipcbiAqIEludm9rZXMgYGNvbnNvbGUuZGVidWcoKWAgd2hlbiBhdmFpbGFibGUuXG4gKiBOby1vcCB3aGVuIGBjb25zb2xlLmRlYnVnYCBpcyBub3QgYSBcImZ1bmN0aW9uXCIuXG4gKiBJZiBgY29uc29sZS5kZWJ1Z2AgaXMgbm90IGF2YWlsYWJsZSwgZmFsbHMgYmFja1xuICogdG8gYGNvbnNvbGUubG9nYC5cbiAqXG4gKiBAYXBpIHB1YmxpY1xuICovXG5leHBvcnRzLmxvZyA9IGNvbnNvbGUuZGVidWcgfHwgY29uc29sZS5sb2cgfHwgKCgpID0+IHt9KTtcblxuLyoqXG4gKiBTYXZlIGBuYW1lc3BhY2VzYC5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gbmFtZXNwYWNlc1xuICogQGFwaSBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIHNhdmUobmFtZXNwYWNlcykge1xuXHR0cnkge1xuXHRcdGlmIChuYW1lc3BhY2VzKSB7XG5cdFx0XHRleHBvcnRzLnN0b3JhZ2Uuc2V0SXRlbSgnZGVidWcnLCBuYW1lc3BhY2VzKTtcblx0XHR9IGVsc2Uge1xuXHRcdFx0ZXhwb3J0cy5zdG9yYWdlLnJlbW92ZUl0ZW0oJ2RlYnVnJyk7XG5cdFx0fVxuXHR9IGNhdGNoIChlcnJvcikge1xuXHRcdC8vIFN3YWxsb3dcblx0XHQvLyBYWFggKEBRaXgtKSBzaG91bGQgd2UgYmUgbG9nZ2luZyB0aGVzZT9cblx0fVxufVxuXG4vKipcbiAqIExvYWQgYG5hbWVzcGFjZXNgLlxuICpcbiAqIEByZXR1cm4ge1N0cmluZ30gcmV0dXJucyB0aGUgcHJldmlvdXNseSBwZXJzaXN0ZWQgZGVidWcgbW9kZXNcbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBsb2FkKCkge1xuXHRsZXQgcjtcblx0dHJ5IHtcblx0XHRyID0gZXhwb3J0cy5zdG9yYWdlLmdldEl0ZW0oJ2RlYnVnJyk7XG5cdH0gY2F0Y2ggKGVycm9yKSB7XG5cdFx0Ly8gU3dhbGxvd1xuXHRcdC8vIFhYWCAoQFFpeC0pIHNob3VsZCB3ZSBiZSBsb2dnaW5nIHRoZXNlP1xuXHR9XG5cblx0Ly8gSWYgZGVidWcgaXNuJ3Qgc2V0IGluIExTLCBhbmQgd2UncmUgaW4gRWxlY3Ryb24sIHRyeSB0byBsb2FkICRERUJVR1xuXHRpZiAoIXIgJiYgdHlwZW9mIHByb2Nlc3MgIT09ICd1bmRlZmluZWQnICYmICdlbnYnIGluIHByb2Nlc3MpIHtcblx0XHRyID0gcHJvY2Vzcy5lbnYuREVCVUc7XG5cdH1cblxuXHRyZXR1cm4gcjtcbn1cblxuLyoqXG4gKiBMb2NhbHN0b3JhZ2UgYXR0ZW1wdHMgdG8gcmV0dXJuIHRoZSBsb2NhbHN0b3JhZ2UuXG4gKlxuICogVGhpcyBpcyBuZWNlc3NhcnkgYmVjYXVzZSBzYWZhcmkgdGhyb3dzXG4gKiB3aGVuIGEgdXNlciBkaXNhYmxlcyBjb29raWVzL2xvY2Fsc3RvcmFnZVxuICogYW5kIHlvdSBhdHRlbXB0IHRvIGFjY2VzcyBpdC5cbiAqXG4gKiBAcmV0dXJuIHtMb2NhbFN0b3JhZ2V9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5mdW5jdGlvbiBsb2NhbHN0b3JhZ2UoKSB7XG5cdHRyeSB7XG5cdFx0Ly8gVFZNTEtpdCAoQXBwbGUgVFYgSlMgUnVudGltZSkgZG9lcyBub3QgaGF2ZSBhIHdpbmRvdyBvYmplY3QsIGp1c3QgbG9jYWxTdG9yYWdlIGluIHRoZSBnbG9iYWwgY29udGV4dFxuXHRcdC8vIFRoZSBCcm93c2VyIGFsc28gaGFzIGxvY2FsU3RvcmFnZSBpbiB0aGUgZ2xvYmFsIGNvbnRleHQuXG5cdFx0cmV0dXJuIGxvY2FsU3RvcmFnZTtcblx0fSBjYXRjaCAoZXJyb3IpIHtcblx0XHQvLyBTd2FsbG93XG5cdFx0Ly8gWFhYIChAUWl4LSkgc2hvdWxkIHdlIGJlIGxvZ2dpbmcgdGhlc2U/XG5cdH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2NvbW1vbicpKGV4cG9ydHMpO1xuXG5jb25zdCB7Zm9ybWF0dGVyc30gPSBtb2R1bGUuZXhwb3J0cztcblxuLyoqXG4gKiBNYXAgJWogdG8gYEpTT04uc3RyaW5naWZ5KClgLCBzaW5jZSBubyBXZWIgSW5zcGVjdG9ycyBkbyB0aGF0IGJ5IGRlZmF1bHQuXG4gKi9cblxuZm9ybWF0dGVycy5qID0gZnVuY3Rpb24gKHYpIHtcblx0dHJ5IHtcblx0XHRyZXR1cm4gSlNPTi5zdHJpbmdpZnkodik7XG5cdH0gY2F0Y2ggKGVycm9yKSB7XG5cdFx0cmV0dXJuICdbVW5leHBlY3RlZEpTT05QYXJzZUVycm9yXTogJyArIGVycm9yLm1lc3NhZ2U7XG5cdH1cbn07XG4iXSwibmFtZXMiOlsiZXhwb3J0cyIsImZvcm1hdEFyZ3MiLCJzYXZlIiwibG9hZCIsInVzZUNvbG9ycyIsInN0b3JhZ2UiLCJsb2NhbHN0b3JhZ2UiLCJkZXN0cm95Iiwid2FybmVkIiwiY29uc29sZSIsIndhcm4iLCJjb2xvcnMiLCJ3aW5kb3ciLCJwcm9jZXNzIiwidHlwZSIsIl9fbndqcyIsIm5hdmlnYXRvciIsInVzZXJBZ2VudCIsInRvTG93ZXJDYXNlIiwibWF0Y2giLCJtIiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJzdHlsZSIsIldlYmtpdEFwcGVhcmFuY2UiLCJmaXJlYnVnIiwiZXhjZXB0aW9uIiwidGFibGUiLCJwYXJzZUludCIsImFyZ3MiLCJuYW1lc3BhY2UiLCJtb2R1bGUiLCJodW1hbml6ZSIsImRpZmYiLCJjIiwiY29sb3IiLCJzcGxpY2UiLCJpbmRleCIsImxhc3RDIiwicmVwbGFjZSIsImxvZyIsImRlYnVnIiwibmFtZXNwYWNlcyIsInNldEl0ZW0iLCJyZW1vdmVJdGVtIiwiZXJyb3IiLCJyIiwiZ2V0SXRlbSIsImVudiIsIkRFQlVHIiwibG9jYWxTdG9yYWdlIiwicmVxdWlyZSIsImZvcm1hdHRlcnMiLCJqIiwidiIsIkpTT04iLCJzdHJpbmdpZnkiLCJtZXNzYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js":
/*!************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/common.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */ function setup(env) {\n    createDebug.debug = createDebug;\n    createDebug.default = createDebug;\n    createDebug.coerce = coerce;\n    createDebug.disable = disable;\n    createDebug.enable = enable;\n    createDebug.enabled = enabled;\n    createDebug.humanize = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n    createDebug.destroy = destroy;\n    Object.keys(env).forEach((key)=>{\n        createDebug[key] = env[key];\n    });\n    /**\n\t* The currently active debug mode names, and names to skip.\n\t*/ createDebug.names = [];\n    createDebug.skips = [];\n    /**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/ createDebug.formatters = {};\n    /**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/ function selectColor(namespace) {\n        let hash = 0;\n        for(let i = 0; i < namespace.length; i++){\n            hash = (hash << 5) - hash + namespace.charCodeAt(i);\n            hash |= 0; // Convert to 32bit integer\n        }\n        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n    }\n    createDebug.selectColor = selectColor;\n    /**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/ function createDebug(namespace) {\n        let prevTime;\n        let enableOverride = null;\n        let namespacesCache;\n        let enabledCache;\n        function debug(...args) {\n            // Disabled?\n            if (!debug.enabled) {\n                return;\n            }\n            const self = debug;\n            // Set `diff` timestamp\n            const curr = Number(new Date());\n            const ms = curr - (prevTime || curr);\n            self.diff = ms;\n            self.prev = prevTime;\n            self.curr = curr;\n            prevTime = curr;\n            args[0] = createDebug.coerce(args[0]);\n            if (typeof args[0] !== \"string\") {\n                // Anything else let's inspect with %O\n                args.unshift(\"%O\");\n            }\n            // Apply any `formatters` transformations\n            let index = 0;\n            args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format)=>{\n                // If we encounter an escaped % then don't increase the array index\n                if (match === \"%%\") {\n                    return \"%\";\n                }\n                index++;\n                const formatter = createDebug.formatters[format];\n                if (typeof formatter === \"function\") {\n                    const val = args[index];\n                    match = formatter.call(self, val);\n                    // Now we need to remove `args[index]` since it's inlined in the `format`\n                    args.splice(index, 1);\n                    index--;\n                }\n                return match;\n            });\n            // Apply env-specific formatting (colors, etc.)\n            createDebug.formatArgs.call(self, args);\n            const logFn = self.log || createDebug.log;\n            logFn.apply(self, args);\n        }\n        debug.namespace = namespace;\n        debug.useColors = createDebug.useColors();\n        debug.color = createDebug.selectColor(namespace);\n        debug.extend = extend;\n        debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n        Object.defineProperty(debug, \"enabled\", {\n            enumerable: true,\n            configurable: false,\n            get: ()=>{\n                if (enableOverride !== null) {\n                    return enableOverride;\n                }\n                if (namespacesCache !== createDebug.namespaces) {\n                    namespacesCache = createDebug.namespaces;\n                    enabledCache = createDebug.enabled(namespace);\n                }\n                return enabledCache;\n            },\n            set: (v)=>{\n                enableOverride = v;\n            }\n        });\n        // Env-specific initialization logic for debug instances\n        if (typeof createDebug.init === \"function\") {\n            createDebug.init(debug);\n        }\n        return debug;\n    }\n    function extend(namespace, delimiter) {\n        const newDebug = createDebug(this.namespace + (typeof delimiter === \"undefined\" ? \":\" : delimiter) + namespace);\n        newDebug.log = this.log;\n        return newDebug;\n    }\n    /**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/ function enable(namespaces) {\n        createDebug.save(namespaces);\n        createDebug.namespaces = namespaces;\n        createDebug.names = [];\n        createDebug.skips = [];\n        let i;\n        const split = (typeof namespaces === \"string\" ? namespaces : \"\").split(/[\\s,]+/);\n        const len = split.length;\n        for(i = 0; i < len; i++){\n            if (!split[i]) {\n                continue;\n            }\n            namespaces = split[i].replace(/\\*/g, \".*?\");\n            if (namespaces[0] === \"-\") {\n                createDebug.skips.push(new RegExp(\"^\" + namespaces.slice(1) + \"$\"));\n            } else {\n                createDebug.names.push(new RegExp(\"^\" + namespaces + \"$\"));\n            }\n        }\n    }\n    /**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/ function disable() {\n        const namespaces = [\n            ...createDebug.names.map(toNamespace),\n            ...createDebug.skips.map(toNamespace).map((namespace)=>\"-\" + namespace)\n        ].join(\",\");\n        createDebug.enable(\"\");\n        return namespaces;\n    }\n    /**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/ function enabled(name) {\n        if (name[name.length - 1] === \"*\") {\n            return true;\n        }\n        let i;\n        let len;\n        for(i = 0, len = createDebug.skips.length; i < len; i++){\n            if (createDebug.skips[i].test(name)) {\n                return false;\n            }\n        }\n        for(i = 0, len = createDebug.names.length; i < len; i++){\n            if (createDebug.names[i].test(name)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/ function toNamespace(regexp) {\n        return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, \"*\");\n    }\n    /**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/ function coerce(val) {\n        if (val instanceof Error) {\n            return val.stack || val.message;\n        }\n        return val;\n    }\n    /**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/ function destroy() {\n        console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n    }\n    createDebug.enable(createDebug.load());\n    return createDebug;\n}\nmodule.exports = setup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/index.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */ if (typeof process === \"undefined\" || process.type === \"renderer\" || false === true || process.__nwjs) {\n    module.exports = __webpack_require__(/*! ./browser.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\");\n} else {\n    module.exports = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUVELElBQUksT0FBT0EsWUFBWSxlQUFlQSxRQUFRQyxJQUFJLEtBQUssY0FBY0QsS0FBZSxLQUFLLFFBQVFBLFFBQVFHLE1BQU0sRUFBRTtJQUNoSEMsbUlBQXlCO0FBQzFCLE9BQU87SUFDTkEsNkhBQXlCO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWlyY29uY2llcmdlcHJvLWN1c3RvbWVyLXBvcnRhbC8uL25vZGVfbW9kdWxlcy9zb2NrZXQuaW8tY2xpZW50L25vZGVfbW9kdWxlcy9kZWJ1Zy9zcmMvaW5kZXguanM/MDgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIERldGVjdCBFbGVjdHJvbiByZW5kZXJlciAvIG53anMgcHJvY2Vzcywgd2hpY2ggaXMgbm9kZSwgYnV0IHdlIHNob3VsZFxuICogdHJlYXQgYXMgYSBicm93c2VyLlxuICovXG5cbmlmICh0eXBlb2YgcHJvY2VzcyA9PT0gJ3VuZGVmaW5lZCcgfHwgcHJvY2Vzcy50eXBlID09PSAncmVuZGVyZXInIHx8IHByb2Nlc3MuYnJvd3NlciA9PT0gdHJ1ZSB8fCBwcm9jZXNzLl9fbndqcykge1xuXHRtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vYnJvd3Nlci5qcycpO1xufSBlbHNlIHtcblx0bW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL25vZGUuanMnKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwidHlwZSIsImJyb3dzZXIiLCJfX253anMiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js":
/*!**********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/node.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */ const tty = __webpack_require__(/*! tty */ \"tty\");\nconst util = __webpack_require__(/*! util */ \"util\");\n/**\n * This is the Node.js implementation of `debug()`.\n */ exports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(()=>{}, \"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n/**\n * Colors.\n */ exports.colors = [\n    6,\n    2,\n    3,\n    4,\n    5,\n    1\n];\ntry {\n    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const supportsColor = __webpack_require__(/*! supports-color */ \"(ssr)/./node_modules/supports-color/index.js\");\n    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n        exports.colors = [\n            20,\n            21,\n            26,\n            27,\n            32,\n            33,\n            38,\n            39,\n            40,\n            41,\n            42,\n            43,\n            44,\n            45,\n            56,\n            57,\n            62,\n            63,\n            68,\n            69,\n            74,\n            75,\n            76,\n            77,\n            78,\n            79,\n            80,\n            81,\n            92,\n            93,\n            98,\n            99,\n            112,\n            113,\n            128,\n            129,\n            134,\n            135,\n            148,\n            149,\n            160,\n            161,\n            162,\n            163,\n            164,\n            165,\n            166,\n            167,\n            168,\n            169,\n            170,\n            171,\n            172,\n            173,\n            178,\n            179,\n            184,\n            185,\n            196,\n            197,\n            198,\n            199,\n            200,\n            201,\n            202,\n            203,\n            204,\n            205,\n            206,\n            207,\n            208,\n            209,\n            214,\n            215,\n            220,\n            221\n        ];\n    }\n} catch (error) {\n// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */ exports.inspectOpts = Object.keys(process.env).filter((key)=>{\n    return /^debug_/i.test(key);\n}).reduce((obj, key)=>{\n    // Camel-case\n    const prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, (_, k)=>{\n        return k.toUpperCase();\n    });\n    // Coerce string value into JS value\n    let val = process.env[key];\n    if (/^(yes|on|true|enabled)$/i.test(val)) {\n        val = true;\n    } else if (/^(no|off|false|disabled)$/i.test(val)) {\n        val = false;\n    } else if (val === \"null\") {\n        val = null;\n    } else {\n        val = Number(val);\n    }\n    obj[prop] = val;\n    return obj;\n}, {});\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */ function useColors() {\n    return \"colors\" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);\n}\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    const { namespace: name, useColors } = this;\n    if (useColors) {\n        const c = this.color;\n        const colorCode = \"\\x1b[3\" + (c < 8 ? c : \"8;5;\" + c);\n        const prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n        args[0] = prefix + args[0].split(\"\\n\").join(\"\\n\" + prefix);\n        args.push(colorCode + \"m+\" + module.exports.humanize(this.diff) + \"\\x1b[0m\");\n    } else {\n        args[0] = getDate() + name + \" \" + args[0];\n    }\n}\nfunction getDate() {\n    if (exports.inspectOpts.hideDate) {\n        return \"\";\n    }\n    return new Date().toISOString() + \" \";\n}\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */ function log(...args) {\n    return process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + \"\\n\");\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    if (namespaces) {\n        process.env.DEBUG = namespaces;\n    } else {\n        // If you set a process.env field to null or undefined, it gets cast to the\n        // string 'null' or 'undefined'. Just delete instead.\n        delete process.env.DEBUG;\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    return process.env.DEBUG;\n}\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */ function init(debug) {\n    debug.inspectOpts = {};\n    const keys = Object.keys(exports.inspectOpts);\n    for(let i = 0; i < keys.length; i++){\n        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */ formatters.o = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts).split(\"\\n\").map((str)=>str.trim()).join(\" \");\n};\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */ formatters.O = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Backoff: () => (/* binding */ Backoff)\n/* harmony export */ });\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */ function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */ Backoff.prototype.duration = function() {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */ Backoff.prototype.reset = function() {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */ Backoff.prototype.setMin = function(min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */ Backoff.prototype.setMax = function(max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */ Backoff.prototype.setJitter = function(jitter) {\n    this.jitter = jitter;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.Fetch),\n/* harmony export */   Manager: () => (/* reexport safe */ _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager),\n/* harmony export */   NodeWebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeWebSocket),\n/* harmony export */   NodeXHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeXHR),\n/* harmony export */   Socket: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket),\n/* harmony export */   WebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebSocket),\n/* harmony export */   WebTransport: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebTransport),\n/* harmony export */   XHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.XHR),\n/* harmony export */   connect: () => (/* binding */ lookup),\n/* harmony export */   \"default\": () => (/* binding */ lookup),\n/* harmony export */   io: () => (/* binding */ lookup),\n/* harmony export */   protocol: () => (/* reexport safe */ socket_io_parser__WEBPACK_IMPORTED_MODULE_4__.protocol)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\");\n/* harmony import */ var _manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./manager.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client\"); // debug()\n/**\n * Managers cache.\n */ const cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n    } else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager: _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager,\n    Socket: _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket,\n    io: lookup,\n    connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */ \n/**\n * Expose constructors for standalone build.\n *\n * @public\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js":
/*!******************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/manager.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Manager: () => (/* binding */ Manager)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contrib/backo2.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__(\"socket.io-client:manager\"); // debug()\nclass Manager extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__.Emitter {\n    constructor(uri, opts){\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__.Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor()\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || socket_io_parser__WEBPACK_IMPORTED_MODULE_2__;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect) this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length) return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined) return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined) return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length) return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */ maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */ open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\")) return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = new engine_io_client__WEBPACK_IMPORTED_MODULE_0__.Socket(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"open\", function() {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err)=>{\n            debug(\"error\");\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            } else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            // set timer\n            const timer = this.setTimeoutFn(()=>{\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */ connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */ onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"ping\", this.onping.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"data\", this.ondata.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", this.onerror.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"close\", this.onclose.bind(this)), // @ts-ignore\n        (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */ onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */ ondata(data) {\n        try {\n            this.decoder.add(data);\n        } catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */ ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.nextTick)(()=>{\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */ onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */ socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new _socket_js__WEBPACK_IMPORTED_MODULE_1__.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        } else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */ _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps){\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */ _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for(let i = 0; i < encodedPackets.length; i++){\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */ cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy)=>subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */ _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */ disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */ onclose(reason, description) {\n        var _a;\n        debug(\"closed due to %s\", reason);\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */ reconnect() {\n        if (this._reconnecting || this.skipReconnect) return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        } else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(()=>{\n                if (self.skipReconnect) return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect) return;\n                self.open((err)=>{\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    } else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */ onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js":
/*!*************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/on.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLEdBQUdDLEdBQUcsRUFBRUMsRUFBRSxFQUFFQyxFQUFFO0lBQzFCRixJQUFJRCxFQUFFLENBQUNFLElBQUlDO0lBQ1gsT0FBTyxTQUFTQztRQUNaSCxJQUFJSSxHQUFHLENBQUNILElBQUlDO0lBQ2hCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJjb25jaWVyZ2Vwcm8tY3VzdG9tZXItcG9ydGFsLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1jbGllbnQvYnVpbGQvZXNtLWRlYnVnL29uLmpzP2Y0NDMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIG9uKG9iaiwgZXYsIGZuKSB7XG4gICAgb2JqLm9uKGV2LCBmbik7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHN1YkRlc3Ryb3koKSB7XG4gICAgICAgIG9iai5vZmYoZXYsIGZuKTtcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbIm9uIiwib2JqIiwiZXYiLCJmbiIsInN1YkRlc3Ryb3kiLCJvZmYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/socket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Socket: () => (/* binding */ Socket)\n/* harmony export */ });\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client:socket\"); // debug()\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */ const RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */ class Socket extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__.Emitter {\n    /**\n     * `Socket` constructor.\n     */ constructor(io, nsp, opts){\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */ this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */ this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */ this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */ this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */ this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */ this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */ this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect) this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */ get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */ subEvents() {\n        if (this.subs) return;\n        const io = this.io;\n        this.subs = [\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"open\", this.onopen.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"error\", this.onerror.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"close\", this.onclose.bind(this))\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */ get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */ connect() {\n        if (this.connected) return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState) this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */ open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */ send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */ emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT,\n            data: args\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            debug(\"emitting packet with ack id %d\", id);\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        } else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        } else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */ _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(()=>{\n            delete this.acks[id];\n            for(let i = 0; i < this.sendBuffer.length; i++){\n                if (this.sendBuffer[i].id === id) {\n                    debug(\"removing packet with ack id %d from the buffer\", id);\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            debug(\"event with ack id %d has timed out after %d ms\", id, timeout);\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args)=>{\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */ emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject)=>{\n            const fn = (arg1, arg2)=>{\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */ _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({\n                fromQueue: true\n            }, this.flags)\n        };\n        args.push((err, ...responseArgs)=>{\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    debug(\"packet [%d] is discarded after %d tries\", packet.id, packet.tryCount);\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            } else {\n                debug(\"packet [%d] was successfully sent\", packet.id);\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */ _drainQueue(force = false) {\n        debug(\"draining queue\");\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            debug(\"packet [%d] has already been sent and is waiting for an ack\", packet.id);\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        debug(\"sending packet [%d] (try n\\xb0%d)\", packet.id, packet.tryCount);\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */ packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */ onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data)=>{\n                this._sendConnectPacket(data);\n            });\n        } else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */ _sendConnectPacket(data) {\n        this.packet({\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT,\n            data: this._pid ? Object.assign({\n                pid: this._pid,\n                offset: this._lastOffset\n            }, data) : data\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */ onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */ onclose(reason, description) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */ _clearAcks() {\n        Object.keys(this.acks).forEach((id)=>{\n            const isBuffered = this.sendBuffer.some((packet)=>String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */ onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace) return;\n        switch(packet.type){\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                } else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */ onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        } else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */ ack(id) {\n        const self = this;\n        let sent = false;\n        return function(...args) {\n            // prevent double callbacks\n            if (sent) return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK,\n                id: id,\n                data: args\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */ onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            debug(\"bad ack %s\", packet.id);\n            return;\n        }\n        delete this.acks[packet.id];\n        debug(\"calling ack %s with %j\", packet.id, packet.data);\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */ onconnect(id, pid) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */ emitBuffered() {\n        this.receiveBuffer.forEach((args)=>this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet)=>{\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */ ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */ destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy)=>subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */ disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT\n            });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */ close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */ compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */ get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */ timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */ offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */ offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */ notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js":
/*!**************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   url: () => (/* binding */ url)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_1__(\"socket.io-client:url\"); // debug()\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */ function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || typeof location !== \"undefined\" && location;\n    if (null == uri) uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            } else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            } else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.parse)(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        } else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href = obj.protocol + \"://\" + host + (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\n");

/***/ })

};
;