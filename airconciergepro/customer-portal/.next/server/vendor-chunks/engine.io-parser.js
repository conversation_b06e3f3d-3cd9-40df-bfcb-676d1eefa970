"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/engine.io-parser";
exports.ids = ["vendor-chunks/engine.io-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/engine.io-parser/build/esm/commons.js":
/*!************************************************************!*\
  !*** ./node_modules/engine.io-parser/build/esm/commons.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_PACKET: () => (/* binding */ ERROR_PACKET),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PACKET_TYPES_REVERSE: () => (/* binding */ PACKET_TYPES_REVERSE)\n/* harmony export */ });\nconst PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key)=>{\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = {\n    type: \"error\",\n    data: \"parser error\"\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLXBhcnNlci9idWlsZC9lc20vY29tbW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxNQUFNQSxlQUFlQyxPQUFPQyxNQUFNLENBQUMsT0FBTyx1QkFBdUI7QUFDakVGLFlBQVksQ0FBQyxPQUFPLEdBQUc7QUFDdkJBLFlBQVksQ0FBQyxRQUFRLEdBQUc7QUFDeEJBLFlBQVksQ0FBQyxPQUFPLEdBQUc7QUFDdkJBLFlBQVksQ0FBQyxPQUFPLEdBQUc7QUFDdkJBLFlBQVksQ0FBQyxVQUFVLEdBQUc7QUFDMUJBLFlBQVksQ0FBQyxVQUFVLEdBQUc7QUFDMUJBLFlBQVksQ0FBQyxPQUFPLEdBQUc7QUFDdkIsTUFBTUcsdUJBQXVCRixPQUFPQyxNQUFNLENBQUM7QUFDM0NELE9BQU9HLElBQUksQ0FBQ0osY0FBY0ssT0FBTyxDQUFDLENBQUNDO0lBQy9CSCxvQkFBb0IsQ0FBQ0gsWUFBWSxDQUFDTSxJQUFJLENBQUMsR0FBR0E7QUFDOUM7QUFDQSxNQUFNQyxlQUFlO0lBQUVDLE1BQU07SUFBU0MsTUFBTTtBQUFlO0FBQ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJjb25jaWVyZ2Vwcm8tY3VzdG9tZXItcG9ydGFsLy4vbm9kZV9tb2R1bGVzL2VuZ2luZS5pby1wYXJzZXIvYnVpbGQvZXNtL2NvbW1vbnMuanM/MWMxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBQQUNLRVRfVFlQRVMgPSBPYmplY3QuY3JlYXRlKG51bGwpOyAvLyBubyBNYXAgPSBubyBwb2x5ZmlsbFxuUEFDS0VUX1RZUEVTW1wib3BlblwiXSA9IFwiMFwiO1xuUEFDS0VUX1RZUEVTW1wiY2xvc2VcIl0gPSBcIjFcIjtcblBBQ0tFVF9UWVBFU1tcInBpbmdcIl0gPSBcIjJcIjtcblBBQ0tFVF9UWVBFU1tcInBvbmdcIl0gPSBcIjNcIjtcblBBQ0tFVF9UWVBFU1tcIm1lc3NhZ2VcIl0gPSBcIjRcIjtcblBBQ0tFVF9UWVBFU1tcInVwZ3JhZGVcIl0gPSBcIjVcIjtcblBBQ0tFVF9UWVBFU1tcIm5vb3BcIl0gPSBcIjZcIjtcbmNvbnN0IFBBQ0tFVF9UWVBFU19SRVZFUlNFID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcbk9iamVjdC5rZXlzKFBBQ0tFVF9UWVBFUykuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgUEFDS0VUX1RZUEVTX1JFVkVSU0VbUEFDS0VUX1RZUEVTW2tleV1dID0ga2V5O1xufSk7XG5jb25zdCBFUlJPUl9QQUNLRVQgPSB7IHR5cGU6IFwiZXJyb3JcIiwgZGF0YTogXCJwYXJzZXIgZXJyb3JcIiB9O1xuZXhwb3J0IHsgUEFDS0VUX1RZUEVTLCBQQUNLRVRfVFlQRVNfUkVWRVJTRSwgRVJST1JfUEFDS0VUIH07XG4iXSwibmFtZXMiOlsiUEFDS0VUX1RZUEVTIiwiT2JqZWN0IiwiY3JlYXRlIiwiUEFDS0VUX1RZUEVTX1JFVkVSU0UiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsIkVSUk9SX1BBQ0tFVCIsInR5cGUiLCJkYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-parser/build/esm/commons.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-parser/build/esm/decodePacket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/engine.io-parser/build/esm/decodePacket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodePacket: () => (/* binding */ decodePacket)\n/* harmony export */ });\n/* harmony import */ var _commons_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./commons.js */ \"(ssr)/./node_modules/engine.io-parser/build/esm/commons.js\");\n\nconst decodePacket = (encodedPacket, binaryType)=>{\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        const buffer = Buffer.from(encodedPacket.substring(1), \"base64\");\n        return {\n            type: \"message\",\n            data: mapBinary(buffer, binaryType)\n        };\n    }\n    if (!_commons_js__WEBPACK_IMPORTED_MODULE_0__.PACKET_TYPES_REVERSE[type]) {\n        return _commons_js__WEBPACK_IMPORTED_MODULE_0__.ERROR_PACKET;\n    }\n    return encodedPacket.length > 1 ? {\n        type: _commons_js__WEBPACK_IMPORTED_MODULE_0__.PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n    } : {\n        type: _commons_js__WEBPACK_IMPORTED_MODULE_0__.PACKET_TYPES_REVERSE[type]\n    };\n};\nconst mapBinary = (data, binaryType)=>{\n    switch(binaryType){\n        case \"arraybuffer\":\n            if (data instanceof ArrayBuffer) {\n                // from WebSocket & binaryType \"arraybuffer\"\n                return data;\n            } else if (Buffer.isBuffer(data)) {\n                // from HTTP long-polling\n                return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);\n            } else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n        case \"nodebuffer\":\n        default:\n            if (Buffer.isBuffer(data)) {\n                // from HTTP long-polling or WebSocket & binaryType \"nodebuffer\" (default)\n                return data;\n            } else {\n                // from WebTransport (Uint8Array)\n                return Buffer.from(data);\n            }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-parser/build/esm/decodePacket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-parser/build/esm/encodePacket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/engine.io-parser/build/esm/encodePacket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodePacket: () => (/* binding */ encodePacket),\n/* harmony export */   encodePacketToBinary: () => (/* binding */ encodePacketToBinary)\n/* harmony export */ });\n/* harmony import */ var _commons_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./commons.js */ \"(ssr)/./node_modules/engine.io-parser/build/esm/commons.js\");\n\nconst encodePacket = ({ type, data }, supportsBinary, callback)=>{\n    if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n        return callback(supportsBinary ? data : \"b\" + toBuffer(data, true).toString(\"base64\"));\n    }\n    // plain string\n    return callback(_commons_js__WEBPACK_IMPORTED_MODULE_0__.PACKET_TYPES[type] + (data || \"\"));\n};\nconst toBuffer = (data, forceBufferConversion)=>{\n    if (Buffer.isBuffer(data) || data instanceof Uint8Array && !forceBufferConversion) {\n        return data;\n    } else if (data instanceof ArrayBuffer) {\n        return Buffer.from(data);\n    } else {\n        return Buffer.from(data.buffer, data.byteOffset, data.byteLength);\n    }\n};\nlet TEXT_ENCODER;\nfunction encodePacketToBinary(packet, callback) {\n    if (packet.data instanceof ArrayBuffer || ArrayBuffer.isView(packet.data)) {\n        return callback(toBuffer(packet.data, false));\n    }\n    encodePacket(packet, true, (encoded)=>{\n        if (!TEXT_ENCODER) {\n            // lazily created for compatibility with Node.js 10\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-parser/build/esm/encodePacket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-parser/build/esm/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/engine.io-parser/build/esm/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPacketDecoderStream: () => (/* binding */ createPacketDecoderStream),\n/* harmony export */   createPacketEncoderStream: () => (/* binding */ createPacketEncoderStream),\n/* harmony export */   decodePacket: () => (/* reexport safe */ _decodePacket_js__WEBPACK_IMPORTED_MODULE_1__.decodePacket),\n/* harmony export */   decodePayload: () => (/* binding */ decodePayload),\n/* harmony export */   encodePacket: () => (/* reexport safe */ _encodePacket_js__WEBPACK_IMPORTED_MODULE_0__.encodePacket),\n/* harmony export */   encodePayload: () => (/* binding */ encodePayload),\n/* harmony export */   protocol: () => (/* binding */ protocol)\n/* harmony export */ });\n/* harmony import */ var _encodePacket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encodePacket.js */ \"(ssr)/./node_modules/engine.io-parser/build/esm/encodePacket.js\");\n/* harmony import */ var _decodePacket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./decodePacket.js */ \"(ssr)/./node_modules/engine.io-parser/build/esm/decodePacket.js\");\n/* harmony import */ var _commons_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commons.js */ \"(ssr)/./node_modules/engine.io-parser/build/esm/commons.js\");\n\n\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback)=>{\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i)=>{\n        // force base64 encoding for binary packets\n        (0,_encodePacket_js__WEBPACK_IMPORTED_MODULE_0__.encodePacket)(packet, false, (encodedPacket)=>{\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType)=>{\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for(let i = 0; i < encodedPackets.length; i++){\n        const decodedPacket = (0,_decodePacket_js__WEBPACK_IMPORTED_MODULE_1__.decodePacket)(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nfunction createPacketEncoderStream() {\n    return new TransformStream({\n        transform (packet, controller) {\n            (0,_encodePacket_js__WEBPACK_IMPORTED_MODULE_0__.encodePacketToBinary)(packet, (encodedPacket)=>{\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                } else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                } else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk)=>acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for(let i = 0; i < size; i++){\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nfunction createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */ ;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform (chunk, controller) {\n            chunks.push(chunk);\n            while(true){\n                if (state === 0 /* State.READ_HEADER */ ) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */ ;\n                    } else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */ ;\n                    } else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */ ;\n                    }\n                } else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */ ) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */ ;\n                } else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */ ) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(_commons_js__WEBPACK_IMPORTED_MODULE_2__.ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */ ;\n                } else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue((0,_decodePacket_js__WEBPACK_IMPORTED_MODULE_1__.decodePacket)(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */ ;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(_commons_js__WEBPACK_IMPORTED_MODULE_2__.ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nconst protocol = 4;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-parser/build/esm/index.js\n");

/***/ })

};
;