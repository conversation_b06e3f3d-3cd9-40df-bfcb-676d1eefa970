"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel";
exports.ids = ["vendor-chunks/side-channel"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel/index.js":
/*!********************************************!*\
  !*** ./node_modules/side-channel/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelList = __webpack_require__(/*! side-channel-list */ \"(rsc)/./node_modules/side-channel-list/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar getSideChannelWeakMap = __webpack_require__(/*! side-channel-weakmap */ \"(rsc)/./node_modules/side-channel-weakmap/index.js\");\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n/** @type {import('.')} */ module.exports = function getSideChannel() {\n    /** @typedef {ReturnType<typeof getSideChannel>} Channel */ /** @type {Channel | undefined} */ var $channelData;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            return !!$channelData && $channelData[\"delete\"](key);\n        },\n        get: function(key) {\n            return $channelData && $channelData.get(key);\n        },\n        has: function(key) {\n            return !!$channelData && $channelData.has(key);\n        },\n        set: function(key, value) {\n            if (!$channelData) {\n                $channelData = makeChannel();\n            }\n            $channelData.set(key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why this is erroring\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel/index.js\n");

/***/ })

};
;