/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fnotifications%2FRealTimeNotifications.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2FProviders.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fui%2FConnectionStatus.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fnotifications%2FRealTimeNotifications.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2FProviders.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fui%2FConnectionStatus.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notifications/RealTimeNotifications.tsx */ \"(ssr)/./src/components/notifications/RealTimeNotifications.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ConnectionStatus.tsx */ \"(ssr)/./src/components/ui/ConnectionStatus.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fnotifications%2FRealTimeNotifications.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2FProviders.tsx&modules=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fcomponents%2Fui%2FConnectionStatus.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"(ssr)/./node_modules/react-query/devtools/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    cacheTime: 5 * 60 * 1000,\n                    retry: 1,\n                    refetchOnWindowFocus: false\n                },\n                mutations: {\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/Providers.tsx\",\n                lineNumber: 28,\n                columnNumber: 50\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/Providers.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notifications/RealTimeNotifications.tsx":
/*!****************************************************************!*\
  !*** ./src/components/notifications/RealTimeNotifications.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealTimeNotifications),\n/* harmony export */   notificationHelpers: () => (/* binding */ notificationHelpers),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_websocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* harmony import */ var _lib_realtime_pricing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-pricing */ \"(ssr)/./src/lib/realtime-pricing.ts\");\n/* __next_internal_client_entry_do_not_use__ default,useNotifications,notificationHelpers auto */ \n\n\n\n\nfunction RealTimeNotifications({ position = \"top-right\", maxNotifications = 5 }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Subscribe to WebSocket messages for real-time notifications\n        const unsubscribeAvailability = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.subscribe(\"availability_update\", (data)=>{\n            addNotification({\n                type: \"info\",\n                title: \"Availability Updated\",\n                message: `${data.availableSlots} slots now available for your selected service`,\n                autoClose: true,\n                duration: 5000\n            });\n        });\n        const unsubscribePrice = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.subscribe(\"price_update\", (data)=>{\n            const priceChange = data.dynamicPrice - data.basePrice;\n            const isIncrease = priceChange > 0;\n            addNotification({\n                type: isIncrease ? \"warning\" : \"success\",\n                title: \"Price Updated\",\n                message: `Price ${isIncrease ? \"increased\" : \"decreased\"} by ${(0,_lib_realtime_pricing__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(Math.abs(priceChange), data.currency)}`,\n                autoClose: true,\n                duration: 7000\n            });\n        });\n        const unsubscribeBooking = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.subscribe(\"booking_update\", (data)=>{\n            let type = \"info\";\n            switch(data.status){\n                case \"confirmed\":\n                    type = \"success\";\n                    break;\n                case \"cancelled\":\n                    type = \"error\";\n                    break;\n                case \"modified\":\n                    type = \"warning\";\n                    break;\n                default:\n                    type = \"info\";\n            }\n            addNotification({\n                type,\n                title: \"Booking Update\",\n                message: data.message,\n                autoClose: type !== \"error\",\n                duration: type === \"success\" ? 5000 : 10000\n            });\n        });\n        const unsubscribeSystem = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.subscribe(\"system_notification\", (data)=>{\n            addNotification({\n                type: data.level || \"info\",\n                title: data.title || \"System Notification\",\n                message: data.message,\n                autoClose: data.autoClose !== false,\n                duration: data.duration || 8000\n            });\n        });\n        // Cleanup subscriptions\n        return ()=>{\n            unsubscribeAvailability();\n            unsubscribePrice();\n            unsubscribeBooking();\n            unsubscribeSystem();\n        };\n    }, []);\n    const addNotification = (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n            timestamp: new Date()\n        };\n        setNotifications((prev)=>{\n            const updated = [\n                newNotification,\n                ...prev\n            ].slice(0, maxNotifications);\n            return updated;\n        });\n        // Auto-close notification if specified\n        if (notification.autoClose) {\n            setTimeout(()=>{\n                removeNotification(newNotification.id);\n            }, notification.duration || 5000);\n        }\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationColors = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-50 border-green-200 text-green-800\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200 text-yellow-800\";\n            case \"error\":\n                return \"bg-red-50 border-red-200 text-red-800\";\n            default:\n                return \"bg-blue-50 border-blue-200 text-blue-800\";\n        }\n    };\n    const getPositionClasses = ()=>{\n        switch(position){\n            case \"top-left\":\n                return \"top-4 left-4\";\n            case \"bottom-right\":\n                return \"bottom-4 right-4\";\n            case \"bottom-left\":\n                return \"bottom-4 left-4\";\n            default:\n                return \"top-4 right-4\";\n        }\n    };\n    if (notifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed ${getPositionClasses()} z-50 space-y-3 max-w-sm w-full`,\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n            ${getNotificationColors(notification.type)}\n            border rounded-lg p-4 shadow-lg\n            transform transition-all duration-300 ease-in-out\n            animate-in slide-in-from-right-full\n          `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: getNotificationIcon(notification.type)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold\",\n                                            children: notification.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeNotification(notification.id),\n                                            className: \"flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1 opacity-90\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                notification.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: notification.action.onClick,\n                                    className: \"text-sm font-medium underline mt-2 hover:no-underline transition-all\",\n                                    children: notification.action.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs opacity-70 mt-2\",\n                                    children: notification.timestamp.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this)\n            }, notification.id, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n// Hook for programmatically adding notifications\nfunction useNotifications() {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addNotification = (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n            timestamp: new Date()\n        };\n        setNotifications((prev)=>[\n                newNotification,\n                ...prev\n            ]);\n        if (notification.autoClose) {\n            setTimeout(()=>{\n                removeNotification(newNotification.id);\n            }, notification.duration || 5000);\n        }\n        return newNotification.id;\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    const clearAllNotifications = ()=>{\n        setNotifications([]);\n    };\n    return {\n        notifications,\n        addNotification,\n        removeNotification,\n        clearAllNotifications\n    };\n}\n// Specific notification functions for common use cases\nconst notificationHelpers = {\n    priceIncrease: (amount, currency = \"USD\")=>({\n            type: \"warning\",\n            title: \"Price Increase\",\n            message: `Service price increased by ${(0,_lib_realtime_pricing__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(amount, currency)}`,\n            autoClose: true,\n            duration: 7000\n        }),\n    priceDecrease: (amount, currency = \"USD\")=>({\n            type: \"success\",\n            title: \"Price Drop\",\n            message: `Service price decreased by ${(0,_lib_realtime_pricing__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(amount, currency)}`,\n            autoClose: true,\n            duration: 7000\n        }),\n    availabilityChange: (available, slots)=>({\n            type: available ? \"success\" : \"warning\",\n            title: \"Availability Update\",\n            message: available ? `${slots ? `${slots} slots` : \"Service\"} now available` : \"Service no longer available for selected time\",\n            autoClose: true,\n            duration: 6000\n        }),\n    bookingConfirmed: (reference)=>({\n            type: \"success\",\n            title: \"Booking Confirmed\",\n            message: `Your booking ${reference} has been confirmed`,\n            autoClose: true,\n            duration: 8000\n        }),\n    connectionStatus: (connected)=>({\n            type: connected ? \"success\" : \"warning\",\n            title: connected ? \"Connected\" : \"Connection Lost\",\n            message: connected ? \"Real-time updates are active\" : \"Real-time updates temporarily unavailable\",\n            autoClose: connected,\n            duration: connected ? 3000 : undefined\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notifications/RealTimeNotifications.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ConnectionStatus.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/ConnectionStatus.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactConnectionStatus: () => (/* binding */ CompactConnectionStatus),\n/* harmony export */   \"default\": () => (/* binding */ ConnectionStatus),\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _lib_websocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* __next_internal_client_entry_do_not_use__ default,CompactConnectionStatus,useConnectionStatus auto */ \n\n\n\nfunction ConnectionStatus({ className = \"\", showText = true, position = \"relative\" }) {\n    const [connectionState, setConnectionState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"disconnected\");\n    const [lastConnected, setLastConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check initial connection state\n        setConnectionState(_lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connectionState);\n        // Set up polling to check connection state\n        const checkConnection = ()=>{\n            const currentState = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connectionState;\n            setConnectionState(currentState);\n            if (currentState === \"connected\") {\n                setLastConnected(new Date());\n            }\n        };\n        // Check connection state every second\n        const interval = setInterval(checkConnection, 1000);\n        // Also listen for WebSocket events if available\n        const handleConnectionChange = ()=>{\n            checkConnection();\n        };\n        // Try to connect if not already connected\n        if (_lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connectionState === \"disconnected\") {\n            _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connect().catch(console.error);\n        }\n        return ()=>{\n            clearInterval(interval);\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(connectionState){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            case \"connecting\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n            case \"closing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(connectionState){\n            case \"connected\":\n                return \"Live updates active\";\n            case \"connecting\":\n                return \"Connecting...\";\n            case \"disconnected\":\n                return \"Offline mode\";\n            case \"closing\":\n                return \"Disconnecting...\";\n            default:\n                return \"Unknown status\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(connectionState){\n            case \"connected\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            case \"connecting\":\n                return \"text-blue-600 bg-blue-50 border-blue-200\";\n            case \"disconnected\":\n            case \"closing\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            default:\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n        }\n    };\n    const handleReconnect = ()=>{\n        if (connectionState === \"disconnected\") {\n            _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connect().catch(console.error);\n        }\n    };\n    const positionClasses = position === \"fixed\" ? \"fixed bottom-4 left-4 z-40\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${positionClasses} ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        inline-flex items-center space-x-2 px-3 py-2 rounded-lg border text-sm\n        ${getStatusColor()}\n        transition-all duration-200\n      `,\n                children: [\n                    getStatusIcon(),\n                    showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    connectionState === \"disconnected\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleReconnect,\n                        className: \"ml-2 text-xs underline hover:no-underline transition-all\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    lastConnected && connectionState === \"connected\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs opacity-75\",\n                        children: lastConnected.toLocaleTimeString()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-full left-0 mb-2 hidden group-hover:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Status: \",\n                                    connectionState\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            lastConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Last connected: \",\n                                    lastConnected.toLocaleString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"WebSocket URL: \",\n                                    \"http://localhost:8000\" || 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n// Compact version for minimal UI space\nfunction CompactConnectionStatus({ className = \"\" }) {\n    const [connectionState, setConnectionState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"disconnected\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkConnection = ()=>{\n            setConnectionState(_lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connectionState);\n        };\n        const interval = setInterval(checkConnection, 2000);\n        checkConnection() // Initial check\n        ;\n        return ()=>clearInterval(interval);\n    }, []);\n    const getStatusDot = ()=>{\n        switch(connectionState){\n            case \"connected\":\n                return \"bg-green-500\";\n            case \"connecting\":\n                return \"bg-blue-500 animate-pulse\";\n            case \"disconnected\":\n            case \"closing\":\n                return \"bg-red-500\";\n            default:\n                return \"bg-yellow-500\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `inline-flex items-center space-x-2 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `w-2 h-2 rounded-full ${getStatusDot()}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-600\",\n                children: connectionState === \"connected\" ? \"Live\" : \"Offline\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n// Hook for connection status\nfunction useConnectionStatus() {\n    const [connectionState, setConnectionState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"disconnected\");\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkConnection = ()=>{\n            const state = _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connectionState;\n            setConnectionState(state);\n            setIsConnected(state === \"connected\");\n        };\n        const interval = setInterval(checkConnection, 1000);\n        checkConnection();\n        return ()=>clearInterval(interval);\n    }, []);\n    const reconnect = ()=>{\n        return _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.connect();\n    };\n    const disconnect = ()=>{\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_2__.wsService.disconnect();\n    };\n    return {\n        connectionState,\n        isConnected,\n        reconnect,\n        disconnect\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   airportService: () => (/* binding */ airportService),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   bookingService: () => (/* binding */ bookingService),\n/* harmony export */   flightService: () => (/* binding */ flightService),\n/* harmony export */   serviceService: () => (/* binding */ serviceService)\n/* harmony export */ });\n// API configuration and utilities for connecting to the backend\nconst API_BASE_URL = \"http://localhost:8000/api/customer/v1\" || 0;\n// API client class\nclass ApiClient {\n    constructor(){\n        this.baseUrl = API_BASE_URL;\n        this.defaultHeaders = {\n            \"Content-Type\": \"application/json\",\n            \"X-Tenant-ID\": \"37de875f-170d-47e5-8f38-8c36b2112475\" || 0\n        };\n    }\n    getAuthHeaders() {\n        // Customer API endpoints are public, no authentication required\n        return this.defaultHeaders;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const config = {\n            headers: this.getAuthHeaders(),\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(`API request failed: ${endpoint}`, error);\n            throw error;\n        }\n    }\n    // Airport endpoints - Updated for dynamic filtering\n    async getAvailableAirports(params) {\n        const searchParams = new URLSearchParams();\n        if (params?.category) searchParams.append(\"category\", params.category);\n        if (params?.type) searchParams.append(\"type\", params.type);\n        if (params?.passengers) searchParams.append(\"passengers\", params.passengers.toString());\n        const response = await this.request(`/airports/available?${searchParams.toString()}`);\n        // Map backend response to frontend Airport interface\n        return response.data.map((airport)=>({\n                id: airport.id,\n                iata_code: airport.iata_code,\n                code: airport.iata_code,\n                name: airport.name,\n                city: airport.city,\n                country: airport.country,\n                timezone: airport.timezone,\n                available_services: airport.available_services,\n                starting_price: airport.starting_price,\n                currency: airport.currency\n            }));\n    }\n    async getAirport(id) {\n        const response = await this.request(`/airports/${id}`);\n        return response.data;\n    }\n    async searchAirports(query) {\n        // Use available airports endpoint with search functionality\n        const response = await this.request(`/airports/available`);\n        const airports = response.data;\n        // Filter client-side for now (can be optimized later)\n        return airports.filter((airport)=>airport.name.toLowerCase().includes(query.toLowerCase()) || airport.code.toLowerCase().includes(query.toLowerCase()) || airport.city.toLowerCase().includes(query.toLowerCase()));\n    }\n    async getPopularAirports() {\n        // Use available airports endpoint to get only airports with active services\n        const response = await this.request(\"/airports/available\");\n        return response.data.slice(0, 8) // Return top 8 airports\n        ;\n    }\n    // Terminal endpoints\n    async getTerminals(airportCode, params) {\n        try {\n            const searchParams = new URLSearchParams();\n            if (params?.type) searchParams.append(\"type\", params.type);\n            if (params?.passengers) searchParams.append(\"passengers\", params.passengers.toString());\n            const response = await this.request(`/airports/${airportCode}/terminals?${searchParams.toString()}`);\n            return response.data;\n        } catch (error) {\n            console.error(`Failed to fetch terminals for airport ${airportCode}:`, error);\n            return [];\n        }\n    }\n    // Service endpoints - Updated for dynamic filtering\n    async getServices(airportCode, params) {\n        const searchParams = new URLSearchParams();\n        if (params?.category) searchParams.append(\"category\", params.category);\n        if (params?.type) searchParams.append(\"type\", params.type);\n        if (params?.passengers) searchParams.append(\"passengers\", params.passengers.toString());\n        // Use terminal-specific endpoint if terminal is provided\n        const endpoint = params?.terminal ? `/airports/${airportCode}/terminals/${params.terminal}/services?${searchParams.toString()}` : `/airports/${airportCode}/services?${searchParams.toString()}`;\n        const response = await this.request(endpoint);\n        return response.data;\n    }\n    async getService(id) {\n        const response = await this.request(`/services/${id}`);\n        return response.data;\n    }\n    async getAvailableServices(params) {\n        try {\n            const searchParams = new URLSearchParams();\n            if (params?.airport) searchParams.append(\"airport\", params.airport);\n            if (params?.type) searchParams.append(\"type\", params.type);\n            if (params?.passengers) searchParams.append(\"passengers\", params.passengers.toString());\n            if (params?.category) searchParams.append(\"category\", params.category);\n            const response = await this.request(`/services/available?${searchParams.toString()}`);\n            return response.data;\n        } catch (error) {\n            console.error(\"Failed to fetch available services:\", error);\n            return [];\n        }\n    }\n    // Booking endpoints\n    async createBooking(bookingData) {\n        return this.request(\"/bookings\", {\n            method: \"POST\",\n            body: JSON.stringify(bookingData)\n        });\n    }\n    async getBookings() {\n        return this.request(\"/bookings\");\n    }\n    async getBooking(id) {\n        return this.request(`/bookings/${id}`);\n    }\n    async updateBooking(id, updates) {\n        return this.request(`/bookings/${id}`, {\n            method: \"PATCH\",\n            body: JSON.stringify(updates)\n        });\n    }\n    async cancelBooking(id) {\n        return this.request(`/bookings/${id}/cancel`, {\n            method: \"POST\"\n        });\n    }\n    // Availability endpoints\n    async checkAvailability(airportId, serviceIds, datetime) {\n        const response = await this.request(\"/availability/check\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                airport_id: airportId,\n                service_ids: serviceIds,\n                datetime\n            })\n        });\n        return response.data;\n    }\n    // Pricing endpoints\n    async calculatePrice(serviceIds, passengerCount, datetime) {\n        return this.request(\"/pricing/calculate\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                service_ids: serviceIds,\n                passenger_count: passengerCount,\n                datetime\n            })\n        });\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n// Utility functions for common operations\nconst airportService = {\n    async searchAirports (query, filters) {\n        if (!query || query.length < 2) return [];\n        try {\n            return await apiClient.searchAirports(query);\n        } catch (error) {\n            console.error(\"Airport search failed:\", error);\n            return [];\n        }\n    },\n    async getAvailableAirports (filters) {\n        try {\n            return await apiClient.getAvailableAirports(filters);\n        } catch (error) {\n            console.error(\"Failed to fetch available airports:\", error);\n            return [];\n        }\n    },\n    async getPopularAirports () {\n        try {\n            return await apiClient.getPopularAirports();\n        } catch (error) {\n            console.error(\"Failed to fetch popular airports:\", error);\n            return [];\n        }\n    }\n};\nconst serviceService = {\n    async getServicesForAirport (airportCode, filters) {\n        try {\n            return await apiClient.getServices(airportCode, filters);\n        } catch (error) {\n            console.error(\"Failed to fetch services:\", error);\n            return [];\n        }\n    }\n};\nconst flightService = {\n    async lookupFlight (flightNumber, flightDate, serviceType) {\n        try {\n            const response = await apiClient.request(\"/flights/lookup\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    flight_number: flightNumber,\n                    flight_date: flightDate,\n                    service_type: serviceType\n                })\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Failed to lookup flight:\", error);\n            return null;\n        }\n    },\n    async getFlightStatus (flightNumber, date) {\n        try {\n            const params = new URLSearchParams();\n            if (date) params.append(\"date\", date);\n            const response = await apiClient.request(`/flights/${flightNumber}/status?${params.toString()}`);\n            return response.data;\n        } catch (error) {\n            console.error(\"Failed to get flight status:\", error);\n            return null;\n        }\n    }\n};\nconst bookingService = {\n    async createBooking (bookingData) {\n        try {\n            return await apiClient.createBooking(bookingData);\n        } catch (error) {\n            console.error(\"Booking creation failed:\", error);\n            throw error;\n        }\n    },\n    async getUserBookings () {\n        try {\n            return await apiClient.getBookings();\n        } catch (error) {\n            console.error(\"Failed to fetch user bookings:\", error);\n            return [];\n        }\n    }\n};\n// Mock data fallbacks\nfunction getMockAirports() {\n    return [\n        {\n            id: \"1\",\n            code: \"JFK\",\n            name: \"John F. Kennedy International Airport\",\n            city: \"New York\",\n            country: \"USA\"\n        },\n        {\n            id: \"2\",\n            code: \"LAX\",\n            name: \"Los Angeles International Airport\",\n            city: \"Los Angeles\",\n            country: \"USA\"\n        },\n        {\n            id: \"3\",\n            code: \"LHR\",\n            name: \"London Heathrow Airport\",\n            city: \"London\",\n            country: \"UK\"\n        },\n        {\n            id: \"4\",\n            code: \"CDG\",\n            name: \"Charles de Gaulle Airport\",\n            city: \"Paris\",\n            country: \"France\"\n        },\n        {\n            id: \"5\",\n            code: \"DXB\",\n            name: \"Dubai International Airport\",\n            city: \"Dubai\",\n            country: \"UAE\"\n        },\n        {\n            id: \"6\",\n            code: \"NRT\",\n            name: \"Narita International Airport\",\n            city: \"Tokyo\",\n            country: \"Japan\"\n        },\n        {\n            id: \"7\",\n            code: \"SIN\",\n            name: \"Singapore Changi Airport\",\n            city: \"Singapore\",\n            country: \"Singapore\"\n        },\n        {\n            id: \"8\",\n            code: \"FRA\",\n            name: \"Frankfurt Airport\",\n            city: \"Frankfurt\",\n            country: \"Germany\"\n        }\n    ];\n}\nfunction getMockServices() {\n    return [\n        {\n            id: \"1\",\n            name: \"Meet & Greet\",\n            description: \"Personal assistance from arrival to departure\",\n            base_price: \"4150\",\n            currency: \"INR\",\n            duration_minutes: 60,\n            category: \"assistance\",\n            type: \"arrival\",\n            max_passengers: 4,\n            inclusions: [\n                \"Personal assistant\",\n                \"Baggage assistance\",\n                \"Navigation help\"\n            ],\n            requirements: [\n                \"Valid ID required\"\n            ]\n        },\n        {\n            id: \"2\",\n            name: \"Fast Track Security\",\n            description: \"Skip the regular security lines\",\n            base_price: \"3320\",\n            currency: \"INR\",\n            duration_minutes: 15,\n            category: \"security\",\n            type: \"departure\",\n            max_passengers: 2,\n            inclusions: [\n                \"Priority security screening\",\n                \"Dedicated lanes\",\n                \"Time saving\"\n            ],\n            requirements: [\n                \"Valid boarding pass required\"\n            ]\n        },\n        {\n            id: \"3\",\n            name: \"VIP Lounge Access\",\n            description: \"Access to premium airport lounges\",\n            base_price: \"6225\",\n            currency: \"INR\",\n            duration_minutes: 180,\n            category: \"lounge\",\n            type: \"arrival\",\n            max_passengers: 6,\n            inclusions: [\n                \"Premium lounge access\",\n                \"Food & beverages\",\n                \"WiFi & charging\"\n            ],\n            requirements: [\n                \"Valid boarding pass required\"\n            ]\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/realtime-pricing.ts":
/*!*************************************!*\
  !*** ./src/lib/realtime-pricing.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealTimePricingService: () => (/* binding */ RealTimePricingService),\n/* harmony export */   calculatePriceDifference: () => (/* binding */ calculatePriceDifference),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   realTimePricingService: () => (/* binding */ realTimePricingService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _websocket__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* __next_internal_client_entry_do_not_use__ RealTimePricingService,realTimePricingService,formatPrice,calculatePriceDifference auto */ \n\nclass RealTimePricingService {\n    constructor(){\n        this.priceCache = new Map();\n        this.subscriptions = new Map();\n        this.updateCallbacks = new Map();\n        // Subscribe to price updates from WebSocket\n        _websocket__WEBPACK_IMPORTED_MODULE_1__.wsService.subscribe(\"price_update\", this.handlePriceUpdate.bind(this));\n    }\n    getCacheKey(context) {\n        return `${context.airportId}-${context.serviceIds.join(\",\")}-${context.date}-${context.time}-${context.passengerCount}`;\n    }\n    handlePriceUpdate(update) {\n        // Find relevant cached prices and update them\n        const entries = Array.from(this.priceCache.entries());\n        for (const [key, cachedResult] of entries){\n            if (key.includes(update.serviceId) && key.includes(update.airportId)) {\n                // Update the cached result with new pricing\n                const updatedResult = this.applyPriceUpdate(cachedResult, update);\n                this.priceCache.set(key, updatedResult);\n                // Notify subscribers\n                const callbacks = this.updateCallbacks.get(key);\n                if (callbacks) {\n                    callbacks.forEach((callback)=>callback(updatedResult));\n                }\n            }\n        }\n    }\n    applyPriceUpdate(cachedResult, update) {\n        const updatedBreakdown = cachedResult.breakdown.map((item)=>{\n            if (item.serviceId === update.serviceId) {\n                return {\n                    ...item,\n                    finalPrice: update.dynamicPrice,\n                    adjustments: update.adjustments.map((adj)=>({\n                            type: adj.type,\n                            name: adj.reason,\n                            amount: adj.amount,\n                            reason: adj.reason\n                        }))\n                };\n            }\n            return item;\n        });\n        const newSubtotal = updatedBreakdown.reduce((sum, item)=>sum + item.basePrice, 0);\n        const newTotal = updatedBreakdown.reduce((sum, item)=>sum + item.finalPrice, 0);\n        return {\n            ...cachedResult,\n            subtotal: newSubtotal,\n            total: newTotal,\n            breakdown: updatedBreakdown,\n            lastUpdated: new Date().toISOString()\n        };\n    }\n    async calculatePrice(context) {\n        const cacheKey = this.getCacheKey(context);\n        // Check cache first\n        const cached = this.priceCache.get(cacheKey);\n        if (cached && this.isCacheValid(cached)) {\n            return cached;\n        }\n        try {\n            // Call API for initial pricing\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.calculatePrice(context.serviceIds, context.passengerCount, `${context.date}T${context.time}`);\n            // Transform API response to our format\n            const result = {\n                subtotal: response.total,\n                adjustments: [],\n                total: response.total,\n                currency: response.currency,\n                breakdown: response.breakdown.map((item)=>({\n                        serviceId: item.serviceId,\n                        serviceName: item.serviceName,\n                        basePrice: item.basePrice,\n                        adjustments: item.adjustments || [],\n                        finalPrice: item.finalPrice\n                    })),\n                validUntil: new Date(Date.now() + 5 * 60 * 1000).toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Cache the result\n            this.priceCache.set(cacheKey, result);\n            // Subscribe to real-time updates for this pricing context\n            this.subscribeToUpdates(context);\n            return result;\n        } catch (error) {\n            console.error(\"Failed to calculate price:\", error);\n            // Return fallback pricing if API fails\n            return this.getFallbackPricing(context);\n        }\n    }\n    isCacheValid(result) {\n        return new Date(result.validUntil) > new Date();\n    }\n    subscribeToUpdates(context) {\n        const cacheKey = this.getCacheKey(context);\n        // Don't subscribe if already subscribed\n        if (this.subscriptions.has(cacheKey)) {\n            return;\n        }\n        // Subscribe to price updates for each service\n        context.serviceIds.forEach((serviceId)=>{\n            _websocket__WEBPACK_IMPORTED_MODULE_1__.wsService.requestPriceUpdates(serviceId, context.airportId, context.date, context.passengerCount);\n        });\n        // Mark as subscribed\n        this.subscriptions.set(cacheKey, ()=>{\n        // Cleanup function if needed\n        });\n    }\n    getFallbackPricing(context) {\n        // Fallback pricing when API is unavailable\n        const basePrice = 50 // Default base price per service\n        ;\n        const breakdown = context.serviceIds.map((serviceId, index)=>({\n                serviceId,\n                serviceName: `Service ${index + 1}`,\n                basePrice,\n                adjustments: [],\n                finalPrice: basePrice\n            }));\n        const subtotal = breakdown.reduce((sum, item)=>sum + item.basePrice, 0);\n        const total = subtotal * context.passengerCount;\n        return {\n            subtotal,\n            adjustments: [],\n            total,\n            currency: \"USD\",\n            breakdown,\n            validUntil: new Date(Date.now() + 2 * 60 * 1000).toISOString(),\n            lastUpdated: new Date().toISOString()\n        };\n    }\n    // Subscribe to price updates for a specific context\n    subscribeToPriceUpdates(context, callback) {\n        const cacheKey = this.getCacheKey(context);\n        if (!this.updateCallbacks.has(cacheKey)) {\n            this.updateCallbacks.set(cacheKey, new Set());\n        }\n        this.updateCallbacks.get(cacheKey).add(callback);\n        // Return unsubscribe function\n        return ()=>{\n            const callbacks = this.updateCallbacks.get(cacheKey);\n            if (callbacks) {\n                callbacks.delete(callback);\n                if (callbacks.size === 0) {\n                    this.updateCallbacks.delete(cacheKey);\n                    // Also cleanup subscription\n                    const unsubscribe = this.subscriptions.get(cacheKey);\n                    if (unsubscribe) {\n                        unsubscribe();\n                        this.subscriptions.delete(cacheKey);\n                    }\n                }\n            }\n        };\n    }\n    // Get cached price if available\n    getCachedPrice(context) {\n        const cacheKey = this.getCacheKey(context);\n        const cached = this.priceCache.get(cacheKey);\n        if (cached && this.isCacheValid(cached)) {\n            return cached;\n        }\n        return null;\n    }\n    // Clear cache for specific context\n    clearCache(context) {\n        if (context) {\n            const cacheKey = this.getCacheKey(context);\n            this.priceCache.delete(cacheKey);\n            // Cleanup subscriptions\n            const unsubscribe = this.subscriptions.get(cacheKey);\n            if (unsubscribe) {\n                unsubscribe();\n                this.subscriptions.delete(cacheKey);\n            }\n            this.updateCallbacks.delete(cacheKey);\n        } else {\n            // Clear all cache\n            this.priceCache.clear();\n            this.subscriptions.forEach((unsubscribe)=>unsubscribe());\n            this.subscriptions.clear();\n            this.updateCallbacks.clear();\n        }\n    }\n    // Get pricing statistics\n    getPricingStats() {\n        return {\n            cacheSize: this.priceCache.size,\n            activeSubscriptions: this.subscriptions.size,\n            activeCallbacks: Array.from(this.updateCallbacks.values()).reduce((sum, set)=>sum + set.size, 0)\n        };\n    }\n}\n// Create singleton instance\nconst realTimePricingService = new RealTimePricingService();\n// Utility function for formatting prices\nfunction formatPrice(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n// Utility function for calculating price difference\nfunction calculatePriceDifference(oldPrice, newPrice) {\n    const amount = newPrice - oldPrice;\n    const percentage = oldPrice > 0 ? amount / oldPrice * 100 : 0;\n    let direction;\n    if (amount > 0) direction = \"increase\";\n    else if (amount < 0) direction = \"decrease\";\n    else direction = \"same\";\n    return {\n        amount,\n        percentage,\n        direction\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/realtime-pricing.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/websocket.ts":
/*!******************************!*\
  !*** ./src/lib/websocket.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketService: () => (/* binding */ WebSocketService),\n/* harmony export */   wsService: () => (/* binding */ wsService)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ WebSocketService,wsService auto */ \nclass WebSocketService {\n    constructor(url = \"http://localhost:8000\" || 0){\n        this.url = url;\n        this.socket = null;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectDelay = 1000;\n        this.listeners = new Map();\n        this.isConnecting = false;\n    }\n    connect() {\n        return new Promise((resolve, reject)=>{\n            if (this.socket?.connected) {\n                resolve();\n                return;\n            }\n            if (this.isConnecting) {\n                return;\n            }\n            this.isConnecting = true;\n            try {\n                this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(this.url, {\n                    transports: [\n                        \"websocket\",\n                        \"polling\"\n                    ],\n                    timeout: 20000,\n                    forceNew: true\n                });\n                this.socket.on(\"connect\", ()=>{\n                    console.log(\"Socket.IO connected\");\n                    this.isConnecting = false;\n                    this.reconnectAttempts = 0;\n                    resolve();\n                });\n                this.socket.on(\"availability_update\", (data)=>{\n                    this.handleMessage({\n                        type: \"availability_update\",\n                        data,\n                        timestamp: new Date().toISOString()\n                    });\n                });\n                this.socket.on(\"price_update\", (data)=>{\n                    this.handleMessage({\n                        type: \"price_update\",\n                        data,\n                        timestamp: new Date().toISOString()\n                    });\n                });\n                this.socket.on(\"booking_update\", (data)=>{\n                    this.handleMessage({\n                        type: \"booking_update\",\n                        data,\n                        timestamp: new Date().toISOString()\n                    });\n                });\n                this.socket.on(\"system_notification\", (data)=>{\n                    this.handleMessage({\n                        type: \"system_notification\",\n                        data,\n                        timestamp: new Date().toISOString()\n                    });\n                });\n                this.socket.on(\"disconnect\", (reason)=>{\n                    console.log(\"Socket.IO disconnected:\", reason);\n                    this.isConnecting = false;\n                    if (reason === \"io server disconnect\") {\n                        // Server initiated disconnect, reconnect manually\n                        this.scheduleReconnect();\n                    }\n                });\n                this.socket.on(\"connect_error\", (error)=>{\n                    console.error(\"Socket.IO connection error:\", error);\n                    this.isConnecting = false;\n                    reject(error);\n                });\n            } catch (error) {\n                this.isConnecting = false;\n                reject(error);\n            }\n        });\n    }\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n            this.socket = null;\n        }\n    }\n    scheduleReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.error(\"Max reconnection attempts reached\");\n            return;\n        }\n        this.reconnectAttempts++;\n        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        setTimeout(()=>{\n            this.connect().catch((error)=>{\n                console.error(\"Reconnection failed:\", error);\n            });\n        }, delay);\n    }\n    handleMessage(message) {\n        const listeners = this.listeners.get(message.type);\n        if (listeners) {\n            listeners.forEach((callback)=>{\n                try {\n                    callback(message.data);\n                } catch (error) {\n                    console.error(\"Error in WebSocket message handler:\", error);\n                }\n            });\n        }\n        // Handle global listeners\n        const globalListeners = this.listeners.get(\"*\");\n        if (globalListeners) {\n            globalListeners.forEach((callback)=>{\n                try {\n                    callback(message);\n                } catch (error) {\n                    console.error(\"Error in global WebSocket message handler:\", error);\n                }\n            });\n        }\n    }\n    subscribe(messageType, callback) {\n        if (!this.listeners.has(messageType)) {\n            this.listeners.set(messageType, new Set());\n        }\n        this.listeners.get(messageType).add(callback);\n        // Return unsubscribe function\n        return ()=>{\n            const listeners = this.listeners.get(messageType);\n            if (listeners) {\n                listeners.delete(callback);\n                if (listeners.size === 0) {\n                    this.listeners.delete(messageType);\n                }\n            }\n        };\n    }\n    // Subscribe to availability updates for specific airport/service\n    subscribeToAvailability(airportId, serviceId, callback) {\n        return this.subscribe(\"availability_update\", (data)=>{\n            if (data.airportId === airportId && data.serviceId === serviceId) {\n                callback(data);\n            }\n        });\n    }\n    // Subscribe to price updates for specific service\n    subscribeToPricing(serviceId, callback) {\n        return this.subscribe(\"price_update\", (data)=>{\n            if (data.serviceId === serviceId) {\n                callback(data);\n            }\n        });\n    }\n    // Subscribe to booking updates for specific booking\n    subscribeToBooking(bookingId, callback) {\n        return this.subscribe(\"booking_update\", (data)=>{\n            if (data.bookingId === bookingId) {\n                callback(data);\n            }\n        });\n    }\n    // Send message to server\n    send(eventName, data) {\n        if (this.socket?.connected) {\n            this.socket.emit(eventName, data);\n        } else {\n            console.warn(\"Socket.IO is not connected. Message not sent:\", {\n                eventName,\n                data\n            });\n        }\n    }\n    // Request real-time updates for specific data\n    requestAvailabilityUpdates(airportId, serviceId, date) {\n        this.send(\"subscribe_availability\", {\n            airportId,\n            serviceId,\n            date\n        });\n    }\n    requestPriceUpdates(serviceId, airportId, date, passengerCount) {\n        this.send(\"subscribe_pricing\", {\n            serviceId,\n            airportId,\n            date,\n            passengerCount\n        });\n    }\n    requestBookingUpdates(bookingId) {\n        this.send(\"subscribe_booking\", {\n            bookingId\n        });\n    }\n    // Get connection status\n    get isConnected() {\n        return this.socket?.connected || false;\n    }\n    get connectionState() {\n        if (!this.socket) return \"disconnected\";\n        if (this.socket.connected) {\n            return \"connected\";\n        } else if (this.isConnecting) {\n            return \"connecting\";\n        } else {\n            return \"disconnected\";\n        }\n    }\n}\n// Create singleton instance\nconst wsService = new WebSocketService();\n// Auto-connect when the module is imported (only in browser)\nif (false) {}\n// Cleanup on page unload\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/websocket.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0793cd218ae1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWlyY29uY2llcmdlcHJvLWN1c3RvbWVyLXBvcnRhbC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MjQ5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA3OTNjZDIxOGFlMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_notifications_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/notifications/RealTimeNotifications */ \"(rsc)/./src/components/notifications/RealTimeNotifications.tsx\");\n/* harmony import */ var _components_ui_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ConnectionStatus */ \"(rsc)/./src/components/ui/ConnectionStatus.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"AirConcierge Pro - Premium Airport Meet & Greet Services\",\n    description: \"Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.\",\n    keywords: \"airport meet and greet, VIP services, airport assistance, travel concierge, airport transfer\",\n    authors: [\n        {\n            name: \"AirConcierge Pro\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"AirConcierge Pro - Premium Airport Meet & Greet Services\",\n        description: \"Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"AirConcierge Pro - Premium Airport Meet & Greet Services\",\n        description: \"Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        position: \"top-right\",\n                        maxNotifications: 3\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        position: \"fixed\",\n                        className: \"bottom-4 left-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/layout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/Providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/Providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/notifications/RealTimeNotifications.tsx":
/*!****************************************************************!*\
  !*** ./src/components/notifications/RealTimeNotifications.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   notificationHelpers: () => (/* binding */ e1),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);
const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/notifications/RealTimeNotifications.tsx#notificationHelpers`);


/***/ }),

/***/ "(rsc)/./src/components/ui/ConnectionStatus.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/ConnectionStatus.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   CompactConnectionStatus: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useConnectionStatus: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);
const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx#CompactConnectionStatus`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/components/ui/ConnectionStatus.tsx#useConnectionStatus`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-query","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/lucide-react","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/react-hot-toast","vendor-chunks/match-sorter","vendor-chunks/engine.io-parser","vendor-chunks/remove-accents","vendor-chunks/@socket.io","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();