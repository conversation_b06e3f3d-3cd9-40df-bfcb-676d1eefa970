"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/send-confirmation-email/route";
exports.ids = ["app/api/send-confirmation-email/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsend-confirmation-email%2Froute&page=%2Fapi%2Fsend-confirmation-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-confirmation-email%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsend-confirmation-email%2Froute&page=%2Fapi%2Fsend-confirmation-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-confirmation-email%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_send_confirmation_email_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/send-confirmation-email/route.ts */ \"(rsc)/./src/app/api/send-confirmation-email/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/send-confirmation-email/route\",\n        pathname: \"/api/send-confirmation-email\",\n        filename: \"route\",\n        bundlePath: \"app/api/send-confirmation-email/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/api/send-confirmation-email/route.ts\",\n    nextConfigOutput,\n    userland: _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_send_confirmation_email_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/send-confirmation-email/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsend-confirmation-email%2Froute&page=%2Fapi%2Fsend-confirmation-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-confirmation-email%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/send-confirmation-email/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/send-confirmation-email/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        const emailData = await request.json();\n        // Validate required fields\n        if (!emailData.booking_reference || !emailData.payment_intent_id || !emailData.booking_data) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate email content\n        const emailContent = generateConfirmationEmail(emailData);\n        // In a real implementation, you would send the email here\n        // Example with SendGrid:\n        /*\n    const sgMail = require('@sendgrid/mail')\n    sgMail.setApiKey(process.env.SENDGRID_API_KEY)\n\n    const msg = {\n      to: emailData.customer_email || '<EMAIL>',\n      from: '<EMAIL>',\n      subject: `Booking Confirmation - ${emailData.booking_reference}`,\n      html: emailContent.html,\n      text: emailContent.text,\n    }\n\n    await sgMail.send(msg)\n    */ // For now, we'll just log the email content and return success\n        console.log(\"Email would be sent with content:\", emailContent);\n        // Store booking in real database via backend API\n        try {\n            const backendApiUrl = process.env.BACKEND_API_URL || \"http://localhost:8000/api/v1\";\n            const tenantId = \"37de875f-170d-47e5-8f38-8c36b2112475\" // TODO: Get from environment or context\n            ;\n            // Use the customer booking schema format\n            const primaryServiceId = emailData.booking_data.services[0] // Take the first service\n            ;\n            const airportCode = emailData.booking_data.airport.split(\" - \")[0] // Extract airport code (e.g., \"DEL\")\n            ;\n            const bookingPayload = {\n                serviceId: primaryServiceId,\n                flightNumber: emailData.booking_data.flightNumber || \"WEB001\",\n                airline: \"Customer Portal\",\n                departureAirport: emailData.booking_data.serviceType === \"departure\" ? airportCode : \"WEB\",\n                arrivalAirport: emailData.booking_data.serviceType === \"arrival\" ? airportCode : \"WEB\",\n                flightDate: `${emailData.booking_data.date}T${emailData.booking_data.time}:00.000Z`,\n                estimatedArrival: `${emailData.booking_data.date}T${emailData.booking_data.time}:00.000Z`,\n                serviceType: emailData.booking_data.serviceType,\n                passengerCount: emailData.booking_data.passengers,\n                passengers: [\n                    {\n                        firstName: emailData.customer_name?.split(\" \")[0] || \"Customer\",\n                        lastName: emailData.customer_name?.split(\" \").slice(1).join(\" \") || \"\",\n                        age: 30,\n                        specialRequirements: []\n                    }\n                ],\n                specialRequirements: [],\n                meetingPoint: emailData.booking_data.terminal ? `${emailData.booking_data.terminal} - Meeting Point` : \"Terminal - Meeting Point\"\n            };\n            const response = await fetch(`${backendApiUrl.replace(\"/api/v1\", \"\")}/api/customer/v1/bookings`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Tenant-ID\": tenantId\n                },\n                body: JSON.stringify(bookingPayload)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.error(\"Failed to save booking to backend:\", errorData);\n            // Continue anyway - email was sent successfully\n            } else {\n                const savedBooking = await response.json();\n                console.log(\"Booking saved to backend successfully:\", savedBooking);\n            }\n        } catch (error) {\n            console.error(\"Error saving booking to backend:\", error);\n        // Continue anyway - email was sent successfully\n        }\n        const bookingRecord = {\n            id: emailData.booking_reference,\n            payment_intent_id: emailData.payment_intent_id,\n            ...emailData.booking_data,\n            status: \"confirmed\",\n            created_at: new Date().toISOString()\n        };\n        console.log(\"Booking record created:\", bookingRecord);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Confirmation email sent successfully\",\n            booking_reference: emailData.booking_reference\n        });\n    } catch (error) {\n        console.error(\"Email sending error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Failed to send confirmation email\",\n            success: false\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateConfirmationEmail(emailData) {\n    const { booking_reference, booking_data } = emailData;\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            weekday: \"long\",\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Booking Confirmation - AirConcierge Pro</title>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: #1e40af; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; background: #f9f9f9; }\n        .booking-details { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; }\n        .reference { background: #dbeafe; padding: 15px; text-align: center; margin: 20px 0; border-radius: 8px; }\n        .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }\n        .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>AirConcierge Pro</h1>\n          <h2>Booking Confirmation</h2>\n        </div>\n        \n        <div class=\"content\">\n          <p>Dear Valued Customer,</p>\n          <p>Thank you for choosing AirConcierge Pro! Your booking has been confirmed and payment processed successfully.</p>\n          \n          <div class=\"reference\">\n            <h3>Booking Reference</h3>\n            <h2 style=\"color: #1e40af; margin: 0;\">${booking_reference}</h2>\n            <p>Please save this reference number for your records</p>\n          </div>\n          \n          <div class=\"booking-details\">\n            <h3>Booking Details</h3>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Airport:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">${booking_data.airport}</td>\n              </tr>\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Service Type:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee; text-transform: capitalize;\">${booking_data.serviceType}</td>\n              </tr>\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Services:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">${booking_data.services.join(\", \")}</td>\n              </tr>\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Date & Time:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">${formatDate(booking_data.date)} at ${booking_data.time}</td>\n              </tr>\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Passengers:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">${booking_data.passengers}</td>\n              </tr>\n              ${booking_data.flightNumber ? `\n              <tr>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Flight Number:</strong></td>\n                <td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">${booking_data.flightNumber}</td>\n              </tr>\n              ` : \"\"}\n              <tr>\n                <td style=\"padding: 8px 0; font-weight: bold;\"><strong>Total Paid:</strong></td>\n                <td style=\"padding: 8px 0; font-weight: bold;\">${booking_data.currency} ${booking_data.totalAmount.toFixed(2)}</td>\n              </tr>\n            </table>\n          </div>\n          \n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${\"http://localhost:3001\"}/my-bookings\" class=\"button\">View My Bookings</a>\n          </div>\n          \n          <div style=\"background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n            <h4 style=\"margin-top: 0;\">What happens next?</h4>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n              <li>Our team will contact you 24 hours before your service</li>\n              <li>Arrive at the designated meeting point 15 minutes early</li>\n              <li>Present this confirmation or your booking reference</li>\n              <li>Enjoy your premium airport experience!</li>\n            </ul>\n          </div>\n          \n          <p>If you need to make any changes or have questions, please contact our 24/7 customer support:</p>\n          <p>\n            <strong>Phone:</strong> ******-123-4567<br>\n            <strong>Email:</strong> <EMAIL>\n          </p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>Thank you for choosing AirConcierge Pro</p>\n          <p>© 2024 AirConcierge Pro. All rights reserved.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n    const text = `\n    AirConcierge Pro - Booking Confirmation\n    \n    Dear Valued Customer,\n    \n    Thank you for choosing AirConcierge Pro! Your booking has been confirmed and payment processed successfully.\n    \n    Booking Reference: ${booking_reference}\n    \n    Booking Details:\n    - Airport: ${booking_data.airport}\n    - Service Type: ${booking_data.serviceType}\n    - Services: ${booking_data.services.join(\", \")}\n    - Date & Time: ${formatDate(booking_data.date)} at ${booking_data.time}\n    - Passengers: ${booking_data.passengers}\n    ${booking_data.flightNumber ? `- Flight Number: ${booking_data.flightNumber}` : \"\"}\n    - Total Paid: ${booking_data.currency} ${booking_data.totalAmount.toFixed(2)}\n    \n    What happens next?\n    1. Our team will contact you 24 hours before your service\n    2. Arrive at the designated meeting point 15 minutes early\n    3. Present this confirmation or your booking reference\n    4. Enjoy your premium airport experience!\n    \n    For support: ******-123-4567 or <EMAIL>\n    \n    Thank you for choosing AirConcierge Pro\n  `;\n    return {\n        html,\n        text\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/send-confirmation-email/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsend-confirmation-email%2Froute&page=%2Fapi%2Fsend-confirmation-email%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsend-confirmation-email%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();