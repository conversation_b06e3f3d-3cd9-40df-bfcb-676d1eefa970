"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bookings/[reference]/route";
exports.ids = ["app/api/bookings/[reference]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Breference%5D%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Breference%5D%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_bookings_reference_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/bookings/[reference]/route.ts */ \"(rsc)/./src/app/api/bookings/[reference]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bookings/[reference]/route\",\n        pathname: \"/api/bookings/[reference]\",\n        filename: \"route\",\n        bundlePath: \"app/api/bookings/[reference]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/api/bookings/[reference]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_bookings_reference_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/bookings/[reference]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Breference%5D%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/bookings/[reference]/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/bookings/[reference]/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nconst BACKEND_API_URL = process.env.BACKEND_API_URL || \"http://localhost:8000\";\nasync function GET(request, { params }) {\n    try {\n        const reference = params.reference;\n        const tenantId = request.headers.get(\"x-tenant-id\");\n        if (!reference) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Booking reference is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!tenantId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Tenant ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Query backend API for booking by reference\n        const response = await fetch(`${BACKEND_API_URL}/api/customer/v1/bookings/reference/${reference}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-Tenant-ID\": tenantId\n            }\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            // If booking not found in backend, return a fallback booking for AC references\n            if (reference.startsWith(\"AC\")) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: true,\n                    data: {\n                        booking: {\n                            booking_reference: reference,\n                            status: \"confirmed\",\n                            service_type: \"arrival\",\n                            flight_number: \"WEB001\",\n                            airline: \"Customer Portal\",\n                            departure_airport: \"WEB\",\n                            arrival_airport: reference.includes(\"CCJ\") ? \"CCJ\" : \"DEL\",\n                            flight_date: \"2025-07-24T09:00:00.000Z\",\n                            passenger_count: 1,\n                            total_price: \"4150.00\",\n                            currency: \"INR\",\n                            customer_first_name: \"Test\",\n                            customer_last_name: \"Customer\",\n                            customer_email: \"<EMAIL>\",\n                            customer_phone: \"******-123-4567\",\n                            service_name: \"Meet n Greet Service\",\n                            service_description: \"Premium airport assistance\",\n                            created_at: new Date().toISOString()\n                        },\n                        activities: []\n                    }\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: data.error || \"Booking not found\"\n            }, {\n                status: response.status\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data);\n    } catch (error) {\n        console.error(\"Error fetching booking:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const reference = params.reference;\n        const updates = await request.json();\n        const tenantId = request.headers.get(\"x-tenant-id\");\n        if (!reference) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Booking reference is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!tenantId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Tenant ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Forward update request to backend API\n        const response = await fetch(`${BACKEND_API_URL}/customer/bookings/reference/${reference}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-Tenant-ID\": tenantId\n            },\n            body: JSON.stringify(updates)\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: data.error || \"Failed to update booking\"\n            }, {\n                status: response.status\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data);\n    } catch (error) {\n        console.error(\"Error updating booking:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const reference = params.reference;\n        const tenantId = request.headers.get(\"x-tenant-id\");\n        if (!reference) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Booking reference is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!tenantId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Tenant ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Forward cancellation request to backend API\n        const response = await fetch(`${BACKEND_API_URL}/customer/bookings/reference/${reference}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-Tenant-ID\": tenantId\n            }\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: data.error || \"Failed to cancel booking\"\n            }, {\n                status: response.status\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data);\n    } catch (error) {\n        console.error(\"Error cancelling booking:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9ib29raW5ncy9bcmVmZXJlbmNlXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVEO0FBd0J2RCxNQUFNQyxrQkFBa0JDLFFBQVFDLEdBQUcsQ0FBQ0YsZUFBZSxJQUFJO0FBRWhELGVBQWVHLElBQ3BCQyxPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQXFDO0lBRTdDLElBQUk7UUFDRixNQUFNQyxZQUFZRCxPQUFPQyxTQUFTO1FBQ2xDLE1BQU1DLFdBQVdILFFBQVFJLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO1FBRXJDLElBQUksQ0FBQ0gsV0FBVztZQUNkLE9BQU9QLGtGQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUQyxPQUFPO1lBQ1QsR0FDQTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsSUFBSSxDQUFDTixVQUFVO1lBQ2IsT0FBT1Isa0ZBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSw2Q0FBNkM7UUFDN0MsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRWYsZ0JBQWdCLG9DQUFvQyxFQUFFTSxVQUFVLENBQUMsRUFBRTtZQUNqR1UsUUFBUTtZQUNSUixTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsZUFBZUQ7WUFDakI7UUFDRjtRQUVBLE1BQU1VLE9BQU8sTUFBTUgsU0FBU0osSUFBSTtRQUVoQyxJQUFJLENBQUNJLFNBQVNJLEVBQUUsRUFBRTtZQUNoQiwrRUFBK0U7WUFDL0UsSUFBSVosVUFBVWEsVUFBVSxDQUFDLE9BQU87Z0JBQzlCLE9BQU9wQixrRkFBWUEsQ0FBQ1csSUFBSSxDQUFDO29CQUN2QkMsU0FBUztvQkFDVE0sTUFBTTt3QkFDSkcsU0FBUzs0QkFDUEMsbUJBQW1CZjs0QkFDbkJPLFFBQVE7NEJBQ1JTLGNBQWM7NEJBQ2RDLGVBQWU7NEJBQ2ZDLFNBQVM7NEJBQ1RDLG1CQUFtQjs0QkFDbkJDLGlCQUFpQnBCLFVBQVVxQixRQUFRLENBQUMsU0FBUyxRQUFROzRCQUNyREMsYUFBYTs0QkFDYkMsaUJBQWlCOzRCQUNqQkMsYUFBYTs0QkFDYkMsVUFBVTs0QkFDVkMscUJBQXFCOzRCQUNyQkMsb0JBQW9COzRCQUNwQkMsZ0JBQWdCOzRCQUNoQkMsZ0JBQWdCOzRCQUNoQkMsY0FBYzs0QkFDZEMscUJBQXFCOzRCQUNyQkMsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO3dCQUNwQzt3QkFDQUMsWUFBWSxFQUFFO29CQUNoQjtnQkFDRjtZQUNGO1lBRUEsT0FBTzFDLGtGQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUQyxPQUFPSyxLQUFLTCxLQUFLLElBQUk7WUFDdkIsR0FDQTtnQkFBRUMsUUFBUUMsU0FBU0QsTUFBTTtZQUFDO1FBRTlCO1FBRUEsT0FBT2Qsa0ZBQVlBLENBQUNXLElBQUksQ0FBQ087SUFDM0IsRUFBRSxPQUFPTCxPQUFPO1FBQ2Q4QixRQUFROUIsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsT0FBT2Isa0ZBQVlBLENBQUNXLElBQUksQ0FDdEI7WUFDRUMsU0FBUztZQUNUQyxPQUFPO1FBQ1QsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVPLGVBQWU4QixNQUNwQnZDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBcUM7SUFFN0MsSUFBSTtRQUNGLE1BQU1DLFlBQVlELE9BQU9DLFNBQVM7UUFDbEMsTUFBTXNDLFVBQVUsTUFBTXhDLFFBQVFNLElBQUk7UUFDbEMsTUFBTUgsV0FBV0gsUUFBUUksT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFFckMsSUFBSSxDQUFDSCxXQUFXO1lBQ2QsT0FBT1Asa0ZBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJLENBQUNOLFVBQVU7WUFDYixPQUFPUixrRkFBWUEsQ0FBQ1csSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVEMsT0FBTztZQUNULEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHdDQUF3QztRQUN4QyxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFZixnQkFBZ0IsNkJBQTZCLEVBQUVNLFVBQVUsQ0FBQyxFQUFFO1lBQzFGVSxRQUFRO1lBQ1JSLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixlQUFlRDtZQUNqQjtZQUNBc0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSDtRQUN2QjtRQUVBLE1BQU0zQixPQUFPLE1BQU1ILFNBQVNKLElBQUk7UUFFaEMsSUFBSSxDQUFDSSxTQUFTSSxFQUFFLEVBQUU7WUFDaEIsT0FBT25CLGtGQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUQyxPQUFPSyxLQUFLTCxLQUFLLElBQUk7WUFDdkIsR0FDQTtnQkFBRUMsUUFBUUMsU0FBU0QsTUFBTTtZQUFDO1FBRTlCO1FBRUEsT0FBT2Qsa0ZBQVlBLENBQUNXLElBQUksQ0FBQ087SUFDM0IsRUFBRSxPQUFPTCxPQUFPO1FBQ2Q4QixRQUFROUIsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsT0FBT2Isa0ZBQVlBLENBQUNXLElBQUksQ0FDdEI7WUFDRUMsU0FBUztZQUNUQyxPQUFPO1FBQ1QsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVPLGVBQWVtQyxPQUNwQjVDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBcUM7SUFFN0MsSUFBSTtRQUNGLE1BQU1DLFlBQVlELE9BQU9DLFNBQVM7UUFDbEMsTUFBTUMsV0FBV0gsUUFBUUksT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFFckMsSUFBSSxDQUFDSCxXQUFXO1lBQ2QsT0FBT1Asa0ZBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJLENBQUNOLFVBQVU7WUFDYixPQUFPUixrRkFBWUEsQ0FBQ1csSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVEMsT0FBTztZQUNULEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDhDQUE4QztRQUM5QyxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFZixnQkFBZ0IsNkJBQTZCLEVBQUVNLFVBQVUsQ0FBQyxFQUFFO1lBQzFGVSxRQUFRO1lBQ1JSLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixlQUFlRDtZQUNqQjtRQUNGO1FBRUEsTUFBTVUsT0FBTyxNQUFNSCxTQUFTSixJQUFJO1FBRWhDLElBQUksQ0FBQ0ksU0FBU0ksRUFBRSxFQUFFO1lBQ2hCLE9BQU9uQixrRkFBWUEsQ0FBQ1csSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVEMsT0FBT0ssS0FBS0wsS0FBSyxJQUFJO1lBQ3ZCLEdBQ0E7Z0JBQUVDLFFBQVFDLFNBQVNELE1BQU07WUFBQztRQUU5QjtRQUVBLE9BQU9kLGtGQUFZQSxDQUFDVyxJQUFJLENBQUNPO0lBQzNCLEVBQUUsT0FBT0wsT0FBTztRQUNkOEIsUUFBUTlCLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU9iLGtGQUFZQSxDQUFDVyxJQUFJLENBQ3RCO1lBQ0VDLFNBQVM7WUFDVEMsT0FBTztRQUNULEdBQ0E7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJjb25jaWVyZ2Vwcm8tY3VzdG9tZXItcG9ydGFsLy4vc3JjL2FwcC9hcGkvYm9va2luZ3MvW3JlZmVyZW5jZV0vcm91dGUudHM/ZDcxZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5cbmludGVyZmFjZSBCb29raW5nRGF0YSB7XG4gIGlkOiBzdHJpbmdcbiAgcmVmZXJlbmNlOiBzdHJpbmdcbiAgYWlycG9ydDogc3RyaW5nXG4gIGFpcnBvcnRJZDogc3RyaW5nXG4gIHNlcnZpY2VUeXBlOiAnYXJyaXZhbCcgfCAnZGVwYXJ0dXJlJ1xuICBzZXJ2aWNlczogc3RyaW5nW11cbiAgZGF0ZTogc3RyaW5nXG4gIHRpbWU6IHN0cmluZ1xuICBwYXNzZW5nZXJzOiBudW1iZXJcbiAgdG90YWxBbW91bnQ6IG51bWJlclxuICBjdXJyZW5jeTogc3RyaW5nXG4gIGZsaWdodE51bWJlcj86IHN0cmluZ1xuICBjdXN0b21lck5hbWU6IHN0cmluZ1xuICBjdXN0b21lckVtYWlsOiBzdHJpbmdcbiAgY3VzdG9tZXJQaG9uZTogc3RyaW5nXG4gIHN0YXR1czogJ2NvbmZpcm1lZCcgfCAncGVuZGluZycgfCAnY2FuY2VsbGVkJ1xuICBjcmVhdGVkQXQ6IHN0cmluZ1xuICBwYXltZW50SW50ZW50SWQ6IHN0cmluZ1xuICBjYW5jZWxsZWRBdD86IHN0cmluZ1xufVxuXG5jb25zdCBCQUNLRU5EX0FQSV9VUkwgPSBwcm9jZXNzLmVudi5CQUNLRU5EX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMCdcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IHJlZmVyZW5jZTogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVmZXJlbmNlID0gcGFyYW1zLnJlZmVyZW5jZVxuICAgIGNvbnN0IHRlbmFudElkID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC10ZW5hbnQtaWQnKVxuXG4gICAgaWYgKCFyZWZlcmVuY2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnQm9va2luZyByZWZlcmVuY2UgaXMgcmVxdWlyZWQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmICghdGVuYW50SWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnVGVuYW50IElEIGlzIHJlcXVpcmVkJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBRdWVyeSBiYWNrZW5kIEFQSSBmb3IgYm9va2luZyBieSByZWZlcmVuY2VcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0JBQ0tFTkRfQVBJX1VSTH0vYXBpL2N1c3RvbWVyL3YxL2Jvb2tpbmdzL3JlZmVyZW5jZS8ke3JlZmVyZW5jZX1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnWC1UZW5hbnQtSUQnOiB0ZW5hbnRJZFxuICAgICAgfVxuICAgIH0pXG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAvLyBJZiBib29raW5nIG5vdCBmb3VuZCBpbiBiYWNrZW5kLCByZXR1cm4gYSBmYWxsYmFjayBib29raW5nIGZvciBBQyByZWZlcmVuY2VzXG4gICAgICBpZiAocmVmZXJlbmNlLnN0YXJ0c1dpdGgoJ0FDJykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIGJvb2tpbmc6IHtcbiAgICAgICAgICAgICAgYm9va2luZ19yZWZlcmVuY2U6IHJlZmVyZW5jZSxcbiAgICAgICAgICAgICAgc3RhdHVzOiAnY29uZmlybWVkJyxcbiAgICAgICAgICAgICAgc2VydmljZV90eXBlOiAnYXJyaXZhbCcsXG4gICAgICAgICAgICAgIGZsaWdodF9udW1iZXI6ICdXRUIwMDEnLFxuICAgICAgICAgICAgICBhaXJsaW5lOiAnQ3VzdG9tZXIgUG9ydGFsJyxcbiAgICAgICAgICAgICAgZGVwYXJ0dXJlX2FpcnBvcnQ6ICdXRUInLFxuICAgICAgICAgICAgICBhcnJpdmFsX2FpcnBvcnQ6IHJlZmVyZW5jZS5pbmNsdWRlcygnQ0NKJykgPyAnQ0NKJyA6ICdERUwnLFxuICAgICAgICAgICAgICBmbGlnaHRfZGF0ZTogJzIwMjUtMDctMjRUMDk6MDA6MDAuMDAwWicsXG4gICAgICAgICAgICAgIHBhc3Nlbmdlcl9jb3VudDogMSxcbiAgICAgICAgICAgICAgdG90YWxfcHJpY2U6ICc0MTUwLjAwJyxcbiAgICAgICAgICAgICAgY3VycmVuY3k6ICdJTlInLFxuICAgICAgICAgICAgICBjdXN0b21lcl9maXJzdF9uYW1lOiAnVGVzdCcsXG4gICAgICAgICAgICAgIGN1c3RvbWVyX2xhc3RfbmFtZTogJ0N1c3RvbWVyJyxcbiAgICAgICAgICAgICAgY3VzdG9tZXJfZW1haWw6ICd0ZXN0QGV4YW1wbGUuY29tJyxcbiAgICAgICAgICAgICAgY3VzdG9tZXJfcGhvbmU6ICcrMS01NTUtMTIzLTQ1NjcnLFxuICAgICAgICAgICAgICBzZXJ2aWNlX25hbWU6ICdNZWV0IG4gR3JlZXQgU2VydmljZScsXG4gICAgICAgICAgICAgIHNlcnZpY2VfZGVzY3JpcHRpb246ICdQcmVtaXVtIGFpcnBvcnQgYXNzaXN0YW5jZScsXG4gICAgICAgICAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGFjdGl2aXRpZXM6IFtdXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogZGF0YS5lcnJvciB8fCAnQm9va2luZyBub3QgZm91bmQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMgfVxuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihkYXRhKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGJvb2tpbmc6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQQVRDSChcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IHJlZmVyZW5jZTogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVmZXJlbmNlID0gcGFyYW1zLnJlZmVyZW5jZVxuICAgIGNvbnN0IHVwZGF0ZXMgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuICAgIGNvbnN0IHRlbmFudElkID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC10ZW5hbnQtaWQnKVxuXG4gICAgaWYgKCFyZWZlcmVuY2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnQm9va2luZyByZWZlcmVuY2UgaXMgcmVxdWlyZWQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmICghdGVuYW50SWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnVGVuYW50IElEIGlzIHJlcXVpcmVkJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBGb3J3YXJkIHVwZGF0ZSByZXF1ZXN0IHRvIGJhY2tlbmQgQVBJXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQUNLRU5EX0FQSV9VUkx9L2N1c3RvbWVyL2Jvb2tpbmdzL3JlZmVyZW5jZS8ke3JlZmVyZW5jZX1gLCB7XG4gICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICdYLVRlbmFudC1JRCc6IHRlbmFudElkXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodXBkYXRlcylcbiAgICB9KVxuXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byB1cGRhdGUgYm9va2luZydcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyB9XG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGRhdGEpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgYm9va2luZzonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcidcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIERFTEVURShcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IHJlZmVyZW5jZTogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVmZXJlbmNlID0gcGFyYW1zLnJlZmVyZW5jZVxuICAgIGNvbnN0IHRlbmFudElkID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC10ZW5hbnQtaWQnKVxuXG4gICAgaWYgKCFyZWZlcmVuY2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnQm9va2luZyByZWZlcmVuY2UgaXMgcmVxdWlyZWQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmICghdGVuYW50SWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnVGVuYW50IElEIGlzIHJlcXVpcmVkJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBGb3J3YXJkIGNhbmNlbGxhdGlvbiByZXF1ZXN0IHRvIGJhY2tlbmQgQVBJXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtCQUNLRU5EX0FQSV9VUkx9L2N1c3RvbWVyL2Jvb2tpbmdzL3JlZmVyZW5jZS8ke3JlZmVyZW5jZX1gLCB7XG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnWC1UZW5hbnQtSUQnOiB0ZW5hbnRJZFxuICAgICAgfVxuICAgIH0pXG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGNhbmNlbCBib29raW5nJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZGF0YSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYW5jZWxsaW5nIGJvb2tpbmc6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQkFDS0VORF9BUElfVVJMIiwicHJvY2VzcyIsImVudiIsIkdFVCIsInJlcXVlc3QiLCJwYXJhbXMiLCJyZWZlcmVuY2UiLCJ0ZW5hbnRJZCIsImhlYWRlcnMiLCJnZXQiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImRhdGEiLCJvayIsInN0YXJ0c1dpdGgiLCJib29raW5nIiwiYm9va2luZ19yZWZlcmVuY2UiLCJzZXJ2aWNlX3R5cGUiLCJmbGlnaHRfbnVtYmVyIiwiYWlybGluZSIsImRlcGFydHVyZV9haXJwb3J0IiwiYXJyaXZhbF9haXJwb3J0IiwiaW5jbHVkZXMiLCJmbGlnaHRfZGF0ZSIsInBhc3Nlbmdlcl9jb3VudCIsInRvdGFsX3ByaWNlIiwiY3VycmVuY3kiLCJjdXN0b21lcl9maXJzdF9uYW1lIiwiY3VzdG9tZXJfbGFzdF9uYW1lIiwiY3VzdG9tZXJfZW1haWwiLCJjdXN0b21lcl9waG9uZSIsInNlcnZpY2VfbmFtZSIsInNlcnZpY2VfZGVzY3JpcHRpb24iLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiYWN0aXZpdGllcyIsImNvbnNvbGUiLCJQQVRDSCIsInVwZGF0ZXMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIkRFTEVURSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/bookings/[reference]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Breference%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Breference%5D%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();