"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/create-payment-intent/route";
exports.ids = ["app/api/create-payment-intent/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-payment-intent%2Froute&page=%2Fapi%2Fcreate-payment-intent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-payment-intent%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-payment-intent%2Froute&page=%2Fapi%2Fcreate-payment-intent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-payment-intent%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_create_payment_intent_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/create-payment-intent/route.ts */ \"(rsc)/./src/app/api/create-payment-intent/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/create-payment-intent/route\",\n        pathname: \"/api/create-payment-intent\",\n        filename: \"route\",\n        bundlePath: \"app/api/create-payment-intent/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/meetngreet/airconciergepro/customer-portal/src/app/api/create-payment-intent/route.ts\",\n    nextConfigOutput,\n    userland: _Users_sajil_meetngreet_airconciergepro_customer_portal_src_app_api_create_payment_intent_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/create-payment-intent/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-payment-intent%2Froute&page=%2Fapi%2Fcreate-payment-intent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-payment-intent%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/create-payment-intent/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/create-payment-intent/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n\n// Check if Stripe secret key is available\nif (!process.env.STRIPE_SECRET_KEY) {\n    console.error(\"STRIPE_SECRET_KEY environment variable is not set\");\n}\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY || \"sk_test_placeholder\", {\n    apiVersion: \"2023-10-16\"\n});\nasync function POST(request) {\n    try {\n        const { amount, currency, provider = \"stripe\", payment_method, booking_data } = await request.json();\n        // Validate required fields\n        if (!amount || !currency || !booking_data) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate provider\n        if (![\n            \"stripe\",\n            \"razorpay\"\n        ].includes(provider)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid payment provider\"\n            }, {\n                status: 400\n            });\n        }\n        // Handle different providers\n        if (provider === \"stripe\") {\n            // Check if Stripe is properly configured\n            if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === \"sk_test_placeholder\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.\"\n                }, {\n                    status: 500\n                });\n            }\n            // Create Stripe payment intent\n            const paymentIntent = await stripe.paymentIntents.create({\n                amount: Math.round(amount),\n                currency: currency.toLowerCase(),\n                payment_method: payment_method,\n                confirmation_method: payment_method ? \"manual\" : \"automatic\",\n                confirm: !!payment_method,\n                return_url: `${\"http://localhost:3001\"}/booking/success`,\n                metadata: {\n                    booking_airport: booking_data.airport,\n                    booking_service_type: booking_data.serviceType,\n                    booking_services: booking_data.services.join(\",\"),\n                    booking_date: booking_data.date,\n                    booking_time: booking_data.time,\n                    booking_passengers: booking_data.passengers.toString(),\n                    booking_flight_number: booking_data.flightNumber || \"\"\n                }\n            });\n            return handleStripeResponse(paymentIntent);\n        } else if (provider === \"razorpay\") {\n            // For Razorpay, we'll proxy to the backend\n            const backendResponse = await fetch(`${\"http://localhost:8000/api/customer/v1\"}/payments/create-payment-intent`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Tenant-ID\": \"fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff\"\n                },\n                body: JSON.stringify({\n                    amount: amount,\n                    currency,\n                    provider: \"razorpay\",\n                    booking_data\n                })\n            });\n            const backendData = await backendResponse.json();\n            if (!backendResponse.ok) {\n                throw new Error(backendData.error || \"Backend payment creation failed\");\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(backendData);\n        }\n        throw new Error(\"Unsupported payment provider\");\n    } catch (error) {\n        console.error(\"Payment intent creation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"An error occurred while processing payment\",\n            type: error.type || \"api_error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to handle Stripe response\nfunction handleStripeResponse(paymentIntent) {\n    if (paymentIntent.status === \"requires_action\" && paymentIntent.next_action?.type === \"use_stripe_sdk\") {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            requires_action: true,\n            data: {\n                payment_intent: {\n                    id: paymentIntent.id,\n                    client_secret: paymentIntent.client_secret\n                }\n            }\n        });\n    } else if (paymentIntent.status === \"succeeded\") {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                payment_intent: {\n                    id: paymentIntent.id,\n                    status: paymentIntent.status\n                }\n            }\n        });\n    } else if (paymentIntent.status === \"requires_payment_method\") {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                payment_intent: {\n                    id: paymentIntent.id,\n                    client_secret: paymentIntent.client_secret,\n                    status: paymentIntent.status\n                }\n            }\n        });\n    } else {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Payment failed\",\n            data: {\n                payment_intent: {\n                    id: paymentIntent.id,\n                    status: paymentIntent.status\n                }\n            }\n        }, {\n            status: 400\n        });\n    }\n}\n// Handle payment intent confirmation\nasync function PUT(request) {\n    try {\n        const { payment_intent_id } = await request.json();\n        if (!payment_intent_id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Payment intent ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            payment_intent: {\n                id: paymentIntent.id,\n                status: paymentIntent.status\n            }\n        });\n    } catch (error) {\n        console.error(\"Payment intent confirmation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"An error occurred while confirming payment\",\n            type: error.type || \"api_error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/create-payment-intent/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-payment-intent%2Froute&page=%2Fapi%2Fcreate-payment-intent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-payment-intent%2Froute.ts&appDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsajil%2Fmeetngreet%2Fairconciergepro%2Fcustomer-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();