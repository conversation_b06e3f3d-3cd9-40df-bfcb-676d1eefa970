'use client'

import { motion } from 'framer-motion'
import { Plane, Star, Sparkles } from 'lucide-react'

interface LuxuryLoaderProps {
  size?: 'sm' | 'md' | 'lg'
  type?: 'spinner' | 'plane' | 'dots' | 'shimmer'
  message?: string
  className?: string
}

export default function LuxuryLoader({ 
  size = 'md', 
  type = 'spinner', 
  message,
  className = '' 
}: LuxuryLoaderProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-12 h-12',
    lg: 'w-20 h-20'
  }

  const SpinnerLoader = () => (
    <motion.div
      className={`${sizeClasses[size]} relative ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <div className="absolute inset-0 rounded-full border-4 border-luxury-200" />
      <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-gold-500 border-r-primary-600" />
      <motion.div
        className="absolute inset-2 rounded-full bg-gradient-to-r from-gold-500 to-primary-600 opacity-20"
        animate={{ scale: [1, 1.2, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
    </motion.div>
  )

  const PlaneLoader = () => (
    <motion.div
      className={`${sizeClasses[size]} relative ${className}`}
      animate={{ 
        x: [0, 100, 0],
        y: [0, -20, 0]
      }}
      transition={{ 
        duration: 3, 
        repeat: Infinity, 
        ease: "easeInOut" 
      }}
    >
      <motion.div
        animate={{ rotate: [0, 10, -10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Plane className="w-full h-full text-primary-600" />
      </motion.div>
      <motion.div
        className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-gold-500 to-transparent opacity-50"
        animate={{ scaleX: [0.5, 1, 0.5] }}
        transition={{ duration: 3, repeat: Infinity }}
      />
    </motion.div>
  )

  const DotsLoader = () => (
    <div className={`flex space-x-2 ${className}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`${size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'} rounded-full bg-gradient-to-r from-gold-500 to-primary-600`}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: index * 0.2
          }}
        />
      ))}
    </div>
  )

  const ShimmerLoader = () => (
    <div className={`relative overflow-hidden bg-luxury-100 rounded-lg ${className}`}>
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
        animate={{ x: ['-100%', '100%'] }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
      />
    </div>
  )

  const renderLoader = () => {
    switch (type) {
      case 'plane':
        return <PlaneLoader />
      case 'dots':
        return <DotsLoader />
      case 'shimmer':
        return <ShimmerLoader />
      default:
        return <SpinnerLoader />
    }
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      {renderLoader()}
      {message && (
        <motion.p
          className="text-luxury-600 font-medium text-center"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {message}
        </motion.p>
      )}
    </div>
  )
}

// Skeleton Loader Component
export function LuxurySkeleton({ 
  className = '',
  lines = 1,
  avatar = false 
}: { 
  className?: string
  lines?: number
  avatar?: boolean 
}) {
  return (
    <div className={`animate-pulse ${className}`}>
      {avatar && (
        <div className="w-12 h-12 bg-luxury-200 rounded-full mb-4" />
      )}
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`bg-luxury-200 rounded-lg h-4 mb-3 ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  )
}

// Loading Overlay Component
export function LuxuryLoadingOverlay({ 
  isVisible, 
  message = "Loading...",
  type = 'spinner' 
}: { 
  isVisible: boolean
  message?: string
  type?: 'spinner' | 'plane' | 'dots'
}) {
  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl p-8 shadow-luxury-xl max-w-sm w-full mx-4"
      >
        <div className="text-center">
          <LuxuryLoader type={type} size="lg" message={message} />
        </div>
      </motion.div>
    </motion.div>
  )
}

// Success Animation Component
export function LuxurySuccessAnimation({ 
  isVisible, 
  message = "Success!",
  onComplete 
}: { 
  isVisible: boolean
  message?: string
  onComplete?: () => void
}) {
  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0 }}
      onAnimationComplete={onComplete}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <motion.div
        initial={{ scale: 0.5 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 200, damping: 15 }}
        className="bg-white rounded-2xl p-8 shadow-luxury-xl max-w-sm w-full mx-4 text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-gradient-to-r from-gold-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.4 }}
          >
            ✓
          </motion.div>
        </motion.div>
        <motion.h3
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-xl font-bold text-luxury-900 mb-2"
        >
          {message}
        </motion.h3>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="flex justify-center space-x-1"
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: [0, 1, 0] }}
              transition={{ 
                delay: 0.6 + index * 0.1,
                duration: 0.6,
                repeat: 2
              }}
            >
              <Star className="w-4 h-4 text-gold-500" />
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  )
}

// Floating Action Button with Luxury Animation
export function LuxuryFloatingButton({ 
  onClick, 
  icon: Icon, 
  className = '' 
}: { 
  onClick: () => void
  icon: React.ComponentType<any>
  className?: string
}) {
  return (
    <motion.button
      onClick={onClick}
      className={`fixed bottom-8 right-8 w-16 h-16 bg-gradient-to-r from-gold-500 to-primary-600 rounded-full shadow-luxury-lg text-white z-40 ${className}`}
      whileHover={{ 
        scale: 1.1,
        boxShadow: "0 20px 60px rgba(245, 158, 11, 0.4)"
      }}
      whileTap={{ scale: 0.9 }}
      animate={{ 
        y: [0, -5, 0],
      }}
      transition={{ 
        y: { duration: 2, repeat: Infinity, ease: "easeInOut" },
        scale: { type: "spring", stiffness: 300 }
      }}
    >
      <Icon className="w-6 h-6" />
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-gold-400"
        animate={{ scale: [1, 1.3, 1], opacity: [1, 0, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
    </motion.button>
  )
}
