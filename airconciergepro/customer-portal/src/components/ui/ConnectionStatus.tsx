'use client'

import { useState, useEffect } from 'react'
import { Wifi, WifiOff, AlertCircle, CheckCircle } from 'lucide-react'
import { wsService } from '@/lib/websocket'

interface ConnectionStatusProps {
  className?: string
  showText?: boolean
  position?: 'fixed' | 'relative'
}

export default function ConnectionStatus({
  className = '',
  showText = true,
  position = 'relative'
}: ConnectionStatusProps) {
  const [connectionState, setConnectionState] = useState<string>('disconnected')
  const [lastConnected, setLastConnected] = useState<Date | null>(null)

  useEffect(() => {
    // Check initial connection state
    setConnectionState(wsService.connectionState)

    // Set up polling to check connection state
    const checkConnection = () => {
      const currentState = wsService.connectionState
      setConnectionState(currentState)
      
      if (currentState === 'connected') {
        setLastConnected(new Date())
      }
    }

    // Check connection state every second
    const interval = setInterval(checkConnection, 1000)

    // Also listen for WebSocket events if available
    const handleConnectionChange = () => {
      checkConnection()
    }

    // Try to connect if not already connected
    if (wsService.connectionState === 'disconnected') {
      wsService.connect().catch(console.error)
    }

    return () => {
      clearInterval(interval)
    }
  }, [])

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'connecting':
        return <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      case 'disconnected':
      case 'closing':
        return <WifiOff className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusText = () => {
    switch (connectionState) {
      case 'connected':
        return 'Live updates active'
      case 'connecting':
        return 'Connecting...'
      case 'disconnected':
        return 'Offline mode'
      case 'closing':
        return 'Disconnecting...'
      default:
        return 'Unknown status'
    }
  }

  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'connecting':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'disconnected':
      case 'closing':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    }
  }

  const handleReconnect = () => {
    if (connectionState === 'disconnected') {
      wsService.connect().catch(console.error)
    }
  }

  const positionClasses = position === 'fixed' 
    ? 'fixed bottom-4 left-4 z-40' 
    : ''

  return (
    <div className={`${positionClasses} ${className}`}>
      <div className={`
        inline-flex items-center space-x-2 px-3 py-2 rounded-lg border text-sm
        ${getStatusColor()}
        transition-all duration-200
      `}>
        {getStatusIcon()}
        
        {showText && (
          <span className="font-medium">
            {getStatusText()}
          </span>
        )}

        {connectionState === 'disconnected' && (
          <button
            onClick={handleReconnect}
            className="ml-2 text-xs underline hover:no-underline transition-all"
          >
            Retry
          </button>
        )}

        {lastConnected && connectionState === 'connected' && (
          <span className="text-xs opacity-75">
            {lastConnected.toLocaleTimeString()}
          </span>
        )}
      </div>

      {/* Detailed status tooltip on hover */}
      <div className="group relative">
        <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block">
          <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap">
            <div>Status: {connectionState}</div>
            {lastConnected && (
              <div>Last connected: {lastConnected.toLocaleString()}</div>
            )}
            <div>WebSocket URL: {process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:8000'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Compact version for minimal UI space
export function CompactConnectionStatus({ className = '' }: { className?: string }) {
  const [connectionState, setConnectionState] = useState<string>('disconnected')

  useEffect(() => {
    const checkConnection = () => {
      setConnectionState(wsService.connectionState)
    }

    const interval = setInterval(checkConnection, 2000)
    checkConnection() // Initial check

    return () => clearInterval(interval)
  }, [])

  const getStatusDot = () => {
    switch (connectionState) {
      case 'connected':
        return 'bg-green-500'
      case 'connecting':
        return 'bg-blue-500 animate-pulse'
      case 'disconnected':
      case 'closing':
        return 'bg-red-500'
      default:
        return 'bg-yellow-500'
    }
  }

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusDot()}`} />
      <span className="text-xs text-gray-600">
        {connectionState === 'connected' ? 'Live' : 'Offline'}
      </span>
    </div>
  )
}

// Hook for connection status
export function useConnectionStatus() {
  const [connectionState, setConnectionState] = useState<string>('disconnected')
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    const checkConnection = () => {
      const state = wsService.connectionState
      setConnectionState(state)
      setIsConnected(state === 'connected')
    }

    const interval = setInterval(checkConnection, 1000)
    checkConnection()

    return () => clearInterval(interval)
  }, [])

  const reconnect = () => {
    return wsService.connect()
  }

  const disconnect = () => {
    wsService.disconnect()
  }

  return {
    connectionState,
    isConnected,
    reconnect,
    disconnect
  }
}
