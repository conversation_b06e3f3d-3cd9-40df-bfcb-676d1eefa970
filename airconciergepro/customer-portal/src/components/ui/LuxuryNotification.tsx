'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  X, 
  Star,
  Sparkles
} from 'lucide-react'

export interface NotificationProps {
  id: string
  type: 'success' | 'error' | 'warning' | 'info' | 'luxury'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface LuxuryNotificationProps extends NotificationProps {
  onClose: (id: string) => void
}

export default function LuxuryNotification({ 
  id, 
  type, 
  title, 
  message, 
  duration = 5000,
  action,
  onClose 
}: LuxuryNotificationProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, duration)
      return () => clearTimeout(timer)
    }
  }, [duration])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose(id), 300)
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case 'error':
        return <AlertCircle className="w-6 h-6 text-red-500" />
      case 'warning':
        return <AlertCircle className="w-6 h-6 text-yellow-500" />
      case 'info':
        return <Info className="w-6 h-6 text-blue-500" />
      case 'luxury':
        return <Star className="w-6 h-6 text-gold-500" />
      default:
        return <Info className="w-6 h-6 text-blue-500" />
    }
  }

  const getStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      case 'luxury':
        return 'bg-gradient-to-r from-gold-50 to-primary-50 border-gold-200 text-luxury-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className={`relative overflow-hidden rounded-xl border-2 p-4 shadow-luxury ${getStyles()}`}
    >
      {/* Luxury shimmer effect for luxury type */}
      {type === 'luxury' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
          animate={{ x: ['-100%', '100%'] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        />
      )}

      <div className="flex items-start space-x-3 relative z-10">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
          className="flex-shrink-0"
        >
          {getIcon()}
        </motion.div>

        <div className="flex-1 min-w-0">
          <motion.h4
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="font-semibold text-sm"
          >
            {title}
          </motion.h4>
          {message && (
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="mt-1 text-sm opacity-90"
            >
              {message}
            </motion.p>
          )}
          {action && (
            <motion.button
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              onClick={action.onClick}
              className="mt-2 text-sm font-medium underline hover:no-underline transition-all duration-200"
            >
              {action.label}
            </motion.button>
          )}
        </div>

        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
          onClick={handleClose}
          className="flex-shrink-0 p-1 rounded-full hover:bg-black/10 transition-colors duration-200"
        >
          <X className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Progress bar for timed notifications */}
      {duration > 0 && (
        <motion.div
          className="absolute bottom-0 left-0 h-1 bg-current opacity-30"
          initial={{ width: '100%' }}
          animate={{ width: '0%' }}
          transition={{ duration: duration / 1000, ease: "linear" }}
        />
      )}
    </motion.div>
  )
}

// Notification Container Component
export function LuxuryNotificationContainer() {
  const [notifications, setNotifications] = useState<NotificationProps[]>([])

  const addNotification = (notification: Omit<NotificationProps, 'id'>) => {
    const id = Date.now().toString()
    setNotifications(prev => [...prev, { ...notification, id }])
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  // Expose methods globally (you might want to use a context or state management library)
  useEffect(() => {
    // @ts-ignore
    window.showLuxuryNotification = addNotification
  }, [])

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full">
      <AnimatePresence>
        {notifications.map((notification) => (
          <LuxuryNotification
            key={notification.id}
            {...notification}
            onClose={removeNotification}
          />
        ))}
      </AnimatePresence>
    </div>
  )
}

// Utility functions for showing notifications
export const showLuxuryNotification = {
  success: (title: string, message?: string, action?: NotificationProps['action']) => {
    // @ts-ignore
    window.showLuxuryNotification?.({ type: 'success', title, message, action })
  },
  error: (title: string, message?: string, action?: NotificationProps['action']) => {
    // @ts-ignore
    window.showLuxuryNotification?.({ type: 'error', title, message, action })
  },
  warning: (title: string, message?: string, action?: NotificationProps['action']) => {
    // @ts-ignore
    window.showLuxuryNotification?.({ type: 'warning', title, message, action })
  },
  info: (title: string, message?: string, action?: NotificationProps['action']) => {
    // @ts-ignore
    window.showLuxuryNotification?.({ type: 'info', title, message, action })
  },
  luxury: (title: string, message?: string, action?: NotificationProps['action']) => {
    // @ts-ignore
    window.showLuxuryNotification?.({ type: 'luxury', title, message, action, duration: 7000 })
  }
}

// Luxury Toast Component for quick messages
export function LuxuryToast({ 
  message, 
  type = 'info',
  isVisible,
  onClose 
}: { 
  message: string
  type?: 'success' | 'error' | 'info' | 'luxury'
  isVisible: boolean
  onClose: () => void
}) {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(onClose, 3000)
      return () => clearTimeout(timer)
    }
  }, [isVisible, onClose])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50"
        >
          <div className={`px-6 py-3 rounded-full shadow-luxury text-white font-medium ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'luxury' ? 'bg-gradient-to-r from-gold-500 to-primary-600' :
            'bg-blue-500'
          }`}>
            <div className="flex items-center space-x-2">
              {type === 'luxury' && <Sparkles className="w-4 h-4" />}
              <span>{message}</span>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Luxury Confirmation Dialog
export function LuxuryConfirmDialog({
  isOpen,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
  type = 'info'
}: {
  isOpen: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
  onCancel: () => void
  type?: 'info' | 'warning' | 'error' | 'luxury'
}) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl shadow-luxury-xl max-w-md w-full p-6"
          >
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
                className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                  type === 'luxury' ? 'bg-gradient-to-r from-gold-500 to-primary-600' :
                  type === 'warning' ? 'bg-yellow-500' :
                  type === 'error' ? 'bg-red-500' :
                  'bg-blue-500'
                }`}
              >
                {type === 'luxury' ? <Star className="w-8 h-8 text-white" /> :
                 type === 'warning' ? <AlertCircle className="w-8 h-8 text-white" /> :
                 type === 'error' ? <AlertCircle className="w-8 h-8 text-white" /> :
                 <Info className="w-8 h-8 text-white" />}
              </motion.div>
              <h3 className="text-xl font-bold text-luxury-900 mb-2">{title}</h3>
              <p className="text-luxury-600">{message}</p>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={onCancel}
                className="flex-1 btn-outline"
              >
                {cancelText}
              </button>
              <button
                onClick={onConfirm}
                className={`flex-1 ${
                  type === 'luxury' ? 'btn-gold' :
                  type === 'error' ? 'bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300' :
                  'btn-primary'
                }`}
              >
                {confirmText}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
