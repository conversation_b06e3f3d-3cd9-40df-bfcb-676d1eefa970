'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Plane, 
  Star, 
  Shield, 
  Clock, 
  Users,
  ArrowRight,
  Play,
  CheckCircle
} from 'lucide-react'
import BookingWidget from '../booking/BookingWidget'

interface LuxuryHeroProps {
  tenantConfig?: {
    brandName?: string
    primaryColor?: string
    isB2B?: boolean
    customMessage?: string
  }
}

export default function LuxuryHero({ tenantConfig }: LuxuryHeroProps) {
  const [currentSlide, setCurrentSlide] = useState(0)

  const heroSlides = [
    {
      title: "Premium Airport Concierge",
      subtitle: "Experience luxury travel like never before",
      image: "/api/placeholder/1920/1080?text=Luxury+Airport+Lounge",
      features: ["VIP Lounge Access", "Fast Track Security", "Personal Assistant"]
    },
    {
      title: "Seamless Business Travel",
      subtitle: "Elevate your corporate journey",
      image: "/api/placeholder/1920/1080?text=Business+Traveler",
      features: ["Priority Check-in", "Meet & Greet", "Transfer Services"]
    },
    {
      title: "Exclusive Meet & Greet",
      subtitle: "Your personal travel companion",
      image: "/api/placeholder/1920/1080?text=Meet+and+Greet",
      features: ["Personal Escort", "Baggage Assistance", "24/7 Support"]
    }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 6000)
    return () => clearInterval(timer)
  }, [heroSlides.length])

  const brandName = tenantConfig?.brandName || "AirConcierge Pro"
  const isB2B = tenantConfig?.isB2B || false

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Slides */}
      <div className="absolute inset-0">
        {heroSlides.map((slide, index) => (
          <motion.div
            key={index}
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: currentSlide === index ? 1 : 0,
              scale: currentSlide === index ? 1 : 1.1
            }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          >
            <div 
              className="w-full h-full bg-cover bg-center bg-no-repeat"
              style={{ backgroundImage: `url(${slide.image})` }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30" />
          </motion.div>
        ))}
      </div>

      {/* Luxury Overlay Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      {/* Main Content */}
      <div className="relative z-10 container-max px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-white"
          >
            {/* Premium Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center space-x-2 bg-gold-500 backdrop-blur-sm border border-gold-500 rounded-full px-6 py-3 mb-8"
            >
              <Star className="h-5 w-5 text-white" />
              <span className="text-white font-medium">
                {isB2B ? "Enterprise Solutions" : "Premium Experience"}
              </span>
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              key={currentSlide}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="heading-xl text-white mb-6"
            >
              {heroSlides[currentSlide].title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              key={`subtitle-${currentSlide}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-xl text-gray-200 mb-8 leading-relaxed"
            >
              {tenantConfig?.customMessage || heroSlides[currentSlide].subtitle}
            </motion.p>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-10"
            >
              {heroSlides[currentSlide].features.map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 1.2 + index * 0.1 }}
                  className="flex items-center space-x-3 glass rounded-lg p-4"
                >
                  <CheckCircle className="h-5 w-5 text-gold-400 flex-shrink-0" />
                  <span className="text-white font-medium">{feature}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link href="/book">
                <button className="btn-gold group">
                  <span>Start Booking</span>
                  <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
              </Link>
              
              <button className="btn-outline border-white text-white hover:bg-white hover:text-primary-600 group">
                <Play className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
                <span>Watch Demo</span>
              </button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.6 }}
              className="flex items-center space-x-8 mt-12 pt-8 border-t border-white/20"
            >
              <div className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-gold-400" />
                <div>
                  <div className="text-white font-semibold">Secure</div>
                  <div className="text-gray-300 text-sm">SSL Protected</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-6 w-6 text-gold-400" />
                <div>
                  <div className="text-white font-semibold">24/7</div>
                  <div className="text-gray-300 text-sm">Support</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-gold-400" />
                <div>
                  <div className="text-white font-semibold">150+</div>
                  <div className="text-gray-300 text-sm">Airports</div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Booking Widget */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="lg:pl-8"
          >
            <div className="glass rounded-2xl p-8 backdrop-blur-luxury">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-4 font-display">Ready to Book?</h3>
                <p className="text-white/80 mb-6">Start your luxury travel experience today</p>
                <Link href="/book">
                  <button className="btn-gold w-full">
                    Book Now
                  </button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              currentSlide === index 
                ? 'bg-gold-400 w-8' 
                : 'bg-white/50 hover:bg-white/70'
            }`}
          />
        ))}
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 right-20 animate-float">
        <div className="w-20 h-20 bg-gold-500/20 rounded-full blur-xl" />
      </div>
      <div className="absolute bottom-32 left-16 animate-float" style={{ animationDelay: '1s' }}>
        <div className="w-16 h-16 bg-primary-500/20 rounded-full blur-xl" />
      </div>


    </section>
  )
}
