'use client'

import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'
import { CheckCircle, Star, Sparkles } from 'lucide-react'

interface Service {
  id: string
  title: string
  description: string
  icon: LucideIcon
  price: string
  features: string[]
  popular?: boolean
  premium?: boolean
}

interface ServiceCardProps {
  service: Service
  onSelect: (serviceId: string) => void
  index?: number
}

export default function ServiceCard({ service, onSelect, index = 0 }: ServiceCardProps) {
  const Icon = service.icon

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={`service-card-luxury h-full relative ${
        service.popular ? 'ring-2 ring-gold-500 ring-offset-4' : ''
      }`}
    >
      {/* Popular Badge */}
      {service.popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gold-500 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center space-x-2 shadow-gold">
            <Star className="h-4 w-4" />
            <span>Most Popular</span>
          </div>
        </div>
      )}

      {/* Premium Badge */}
      {service.premium && (
        <div className="absolute top-6 right-6 z-10">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white p-2 rounded-full shadow-luxury">
            <Sparkles className="h-5 w-5" />
          </div>
        </div>
      )}

      <div className="flex flex-col h-full relative z-10">
        {/* Icon and title */}
        <div className="text-center mb-8">
          <motion.div
            className="bg-gradient-to-br from-gold-100 to-primary-100 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-luxury group-hover:shadow-gold transition-all duration-300"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon className="h-10 w-10 text-primary-600 group-hover:text-gold-600 transition-colors duration-300" />
          </motion.div>
          <h3 className="text-2xl font-bold text-luxury-900 mb-3 font-display">{service.title}</h3>
          <p className="text-luxury-600 leading-relaxed">{service.description}</p>
        </div>

        {/* Price */}
        <div className="text-center mb-8">
          <div className="relative">
            <div className="text-4xl font-bold text-luxury-gradient mb-2 font-display">
              {service.price}
            </div>
            <div className="text-sm text-luxury-500 font-medium">per person</div>
            {service.popular && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gold-500 rounded-full animate-pulse" />
            )}
          </div>
        </div>

        {/* Features */}
        <div className="flex-grow mb-8">
          <ul className="space-y-4">
            {service.features.map((feature, featureIndex) => (
              <motion.li
                key={featureIndex}
                className="flex items-start space-x-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: featureIndex * 0.1 }}
                viewport={{ once: true }}
              >
                <CheckCircle className="h-5 w-5 text-gold-500 flex-shrink-0 mt-1" />
                <span className="text-luxury-700 font-medium">{feature}</span>
              </motion.li>
            ))}
          </ul>
        </div>

        {/* CTA Button */}
        <motion.button
          onClick={() => onSelect(service.id)}
          className={`w-full ${
            service.popular
              ? 'btn-gold'
              : service.premium
                ? 'btn-primary'
                : 'btn-outline'
          } group`}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span>Select Service</span>
          <motion.div
            className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            initial={{ x: -10 }}
            whileHover={{ x: 0 }}
          >
            →
          </motion.div>
        </motion.button>
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-gold-500/5 to-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none" />
    </motion.div>
  )
}
