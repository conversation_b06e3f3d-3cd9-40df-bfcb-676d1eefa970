'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Baby, User, <PERSON><PERSON><PERSON><PERSON><PERSON>, Heart, HelpCircle } from 'lucide-react'

interface PassengerSelectorProps {
  passengers: number
  onPassengersChange: (passengers: number) => void
}

interface PassengerBreakdown {
  adults: number
  children: number
  infants: number
}

interface SpecialRequirements {
  wheelchair: boolean
  elderly: boolean
  unaccompaniedMinor: boolean
  medicalAssistance: boolean
  language: string
}

export default function PassengerSelector({ 
  passengers, 
  onPassengersChange 
}: PassengerSelectorProps) {
  const [breakdown, setBreakdown] = useState<PassengerBreakdown>({
    adults: passengers || 1,
    children: 0,
    infants: 0
  })
  
  const [specialRequirements, setSpecialRequirements] = useState<SpecialRequirements>({
    wheelchair: false,
    elderly: false,
    unaccompaniedMinor: false,
    medicalAssistance: false,
    language: 'English'
  })

  const [showSpecialRequirements, setShowSpecialRequirements] = useState(false)

  const updateBreakdown = (type: keyof PassengerBreakdown, value: number) => {
    const newBreakdown = { ...breakdown, [type]: Math.max(0, value) }
    
    // Ensure at least 1 adult
    if (type === 'adults' && value < 1) {
      newBreakdown.adults = 1
    }
    
    setBreakdown(newBreakdown)
    
    // Update total passengers
    const total = newBreakdown.adults + newBreakdown.children + newBreakdown.infants
    onPassengersChange(total)
  }

  const updateSpecialRequirement = (requirement: keyof SpecialRequirements, value: boolean | string) => {
    setSpecialRequirements(prev => ({ ...prev, [requirement]: value }))
  }

  const totalPassengers = breakdown.adults + breakdown.children + breakdown.infants

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Passenger Count - Compact for mobile */}
      <div className="text-center bg-primary-50 rounded-lg p-3 lg:p-4">
        <div className="text-base lg:text-lg font-semibold text-primary-900">
          How many passengers need assistance?
        </div>
        <div className="text-sm text-primary-700 mt-1">
          Select the number and type of passengers
        </div>
      </div>

      {/* Passenger Breakdown - Mobile optimized */}
      <div className="space-y-4 lg:space-y-6">
        {/* Adults */}
        <div className="flex items-center justify-between p-3 lg:p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <User className="h-5 w-5 lg:h-6 lg:w-6 text-primary-600" />
            <div>
              <div className="font-semibold text-gray-900 text-sm lg:text-base">Adults</div>
              <div className="text-xs lg:text-sm text-gray-600">Age 12+</div>
            </div>
          </div>
          <div className="flex items-center space-x-2 lg:space-x-3">
            <button
              onClick={() => updateBreakdown('adults', breakdown.adults - 1)}
              disabled={breakdown.adults <= 1}
              className="w-10 h-10 lg:w-12 lg:h-12 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation active:bg-gray-100"
              style={{ minHeight: '44px', minWidth: '44px' }} // Ensure minimum touch target
            >
              <Minus className="h-4 w-4 lg:h-5 lg:w-5" />
            </button>
            <span className="w-8 lg:w-12 text-center font-semibold text-base lg:text-lg">{breakdown.adults}</span>
            <button
              onClick={() => updateBreakdown('adults', breakdown.adults + 1)}
              className="w-10 h-10 lg:w-12 lg:h-12 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 touch-manipulation active:bg-gray-100"
              style={{ minHeight: '44px', minWidth: '44px' }} // Ensure minimum touch target
            >
              <Plus className="h-4 w-4 lg:h-5 lg:w-5" />
            </button>
          </div>
        </div>

        {/* Children */}
        <div className="flex items-center justify-between p-3 lg:p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <UserCheck className="h-5 w-5 lg:h-6 lg:w-6 text-primary-600" />
            <div>
              <div className="font-semibold text-gray-900 text-sm lg:text-base">Children</div>
              <div className="text-xs lg:text-sm text-gray-600">Age 2-11</div>
            </div>
          </div>
          <div className="flex items-center space-x-2 lg:space-x-3">
            <button
              onClick={() => updateBreakdown('children', breakdown.children - 1)}
              disabled={breakdown.children <= 0}
              className="w-10 h-10 lg:w-12 lg:h-12 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation active:bg-gray-100"
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              <Minus className="h-4 w-4 lg:h-5 lg:w-5" />
            </button>
            <span className="w-8 lg:w-12 text-center font-semibold text-base lg:text-lg">{breakdown.children}</span>
            <button
              onClick={() => updateBreakdown('children', breakdown.children + 1)}
              className="w-10 h-10 lg:w-12 lg:h-12 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 touch-manipulation active:bg-gray-100"
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              <Plus className="h-4 w-4 lg:h-5 lg:w-5" />
            </button>
          </div>
        </div>

        {/* Infants */}
        <div className="flex items-center justify-between p-3 lg:p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <Baby className="h-5 w-5 lg:h-6 lg:w-6 text-primary-600" />
            <div>
              <div className="font-semibold text-gray-900 text-sm lg:text-base">Infants</div>
              <div className="text-xs lg:text-sm text-gray-600">Under 2 years</div>
            </div>
          </div>
          <div className="flex items-center space-x-2 lg:space-x-3">
            <button
              onClick={() => updateBreakdown('infants', breakdown.infants - 1)}
              disabled={breakdown.infants <= 0}
              className="w-10 h-10 lg:w-12 lg:h-12 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation active:bg-gray-100"
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              <Minus className="h-4 w-4" />
            </button>
            <span className="w-12 text-center font-semibold text-lg">{breakdown.infants}</span>
            <button
              onClick={() => updateBreakdown('infants', breakdown.infants + 1)}
              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Total Summary */}
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Users className="h-6 w-6 text-primary-600" />
            <div>
              <div className="font-semibold text-primary-900">Total Passengers</div>
              <div className="text-sm text-primary-700">
                {breakdown.adults} Adult{breakdown.adults !== 1 ? 's' : ''}
                {breakdown.children > 0 && `, ${breakdown.children} Child${breakdown.children !== 1 ? 'ren' : ''}`}
                {breakdown.infants > 0 && `, ${breakdown.infants} Infant${breakdown.infants !== 1 ? 's' : ''}`}
              </div>
            </div>
          </div>
          <div className="text-3xl font-bold text-primary-600">
            {totalPassengers}
          </div>
        </div>
      </div>

      {/* Special Requirements */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-semibold text-gray-900">Special Requirements</h4>
          <button
            onClick={() => setShowSpecialRequirements(!showSpecialRequirements)}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm"
          >
            {showSpecialRequirements ? 'Hide' : 'Add Special Requirements'}
          </button>
        </div>

        {showSpecialRequirements && (
          <div className="space-y-4 bg-gray-50 rounded-lg p-6">
            {/* Accessibility Requirements */}
            <div className="space-y-3">
              <h5 className="font-medium text-gray-900">Accessibility & Medical</h5>
              
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={specialRequirements.wheelchair}
                  onChange={(e) => updateSpecialRequirement('wheelchair', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <HelpCircle className="h-5 w-5 text-gray-600" />
                <span className="text-gray-700">Wheelchair assistance required</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={specialRequirements.elderly}
                  onChange={(e) => updateSpecialRequirement('elderly', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <User className="h-5 w-5 text-gray-600" />
                <span className="text-gray-700">Elderly passenger assistance</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={specialRequirements.medicalAssistance}
                  onChange={(e) => updateSpecialRequirement('medicalAssistance', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <Heart className="h-5 w-5 text-gray-600" />
                <span className="text-gray-700">Medical assistance needed</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={specialRequirements.unaccompaniedMinor}
                  onChange={(e) => updateSpecialRequirement('unaccompaniedMinor', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <UserCheck className="h-5 w-5 text-gray-600" />
                <span className="text-gray-700">Unaccompanied minor</span>
              </label>
            </div>

            {/* Language Preference */}
            <div className="space-y-3">
              <h5 className="font-medium text-gray-900">Language Preference</h5>
              <select
                value={specialRequirements.language}
                onChange={(e) => updateSpecialRequirement('language', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="English">English</option>
                <option value="Spanish">Spanish</option>
                <option value="French">French</option>
                <option value="German">German</option>
                <option value="Italian">Italian</option>
                <option value="Portuguese">Portuguese</option>
                <option value="Arabic">Arabic</option>
                <option value="Chinese">Chinese</option>
                <option value="Japanese">Japanese</option>
                <option value="Korean">Korean</option>
                <option value="Other">Other (specify in notes)</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Pricing Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="text-sm text-yellow-800">
          <div className="font-medium mb-1">Pricing Information</div>
          <ul className="space-y-1">
            <li>• Adults and children (2+) are charged full price</li>
            <li>• Infants under 2 years travel free</li>
            <li>• Special assistance services may have additional charges</li>
            <li>• Group discounts available for 5+ passengers</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
