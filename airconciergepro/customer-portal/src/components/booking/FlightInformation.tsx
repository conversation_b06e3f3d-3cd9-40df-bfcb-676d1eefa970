'use client'

import { useState, useEffect } from 'react'
import { Plane, Clock, MapPin, AlertCircle, CheckCircle, Info } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface FlightData {
  flight_number: string
  airline_name: string
  airline_code: string
  departure_airport: string
  arrival_airport: string
  departure_terminal?: string
  arrival_terminal?: string
  scheduled_departure?: string
  scheduled_arrival?: string
  estimated_departure?: string
  estimated_arrival?: string
  actual_departure?: string
  actual_arrival?: string
  flight_status: string
  aircraft_type?: string
  gate?: string
  baggage_belt?: string
}

interface TimeSlotSuggestion {
  recommended_time: string
  buffer_minutes: number
  reason: string
  confidence: 'high' | 'medium' | 'low'
}

interface FlightInformationProps {
  serviceType: 'arrival' | 'departure'
  onFlightSelect: (flightData: FlightData, suggestedTime: string) => void
  onTimeSlotSelect: (time: string) => void
  selectedDate: string
}

export default function FlightInformation({
  serviceType,
  onFlightSelect,
  onTimeSlotSelect,
  selectedDate
}: FlightInformationProps) {
  const [flightNumber, setFlightNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [flightData, setFlightData] = useState<FlightData | null>(null)
  const [timeSlotSuggestions, setTimeSlotSuggestions] = useState<TimeSlotSuggestion[]>([])
  const [error, setError] = useState('')
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('')

  const lookupFlight = async () => {
    if (!flightNumber.trim()) {
      setError('Please enter a flight number')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/customer/v1/flights/lookup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': process.env.NEXT_PUBLIC_TENANT_ID || ''
        },
        body: JSON.stringify({
          flight_number: flightNumber.toUpperCase(),
          flight_date: selectedDate,
          service_type: serviceType
        })
      })

      const result = await response.json()

      if (result.success) {
        setFlightData(result.data.flight)
        setTimeSlotSuggestions(result.data.time_slot_suggestions)
        onFlightSelect(result.data.flight, result.data.time_slot_suggestions[0]?.recommended_time || '')
      } else {
        setError(result.error || 'Flight not found')
        setFlightData(null)
        setTimeSlotSuggestions([])
      }
    } catch (error) {
      setError('Unable to fetch flight information. Please try again.')
      setFlightData(null)
      setTimeSlotSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleTimeSlotSelect = (suggestion: TimeSlotSuggestion) => {
    setSelectedTimeSlot(suggestion.recommended_time)
    onTimeSlotSelect(suggestion.recommended_time)
  }

  const formatTime = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'scheduled':
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'delayed':
        return 'text-yellow-600 bg-yellow-100'
      case 'cancelled':
        return 'text-red-600 bg-red-100'
      case 'landed':
        return 'text-blue-600 bg-blue-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high':
        return 'border-green-200 bg-green-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-gray-200 bg-gray-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="space-y-6">
      {/* Flight Number Input */}
      <div>
        <label htmlFor="flight-number" className="block text-sm font-medium text-gray-700 mb-2">
          Flight Number (Optional)
        </label>
        <div className="flex space-x-3">
          <div className="flex-1">
            <input
              type="text"
              id="flight-number"
              value={flightNumber}
              onChange={(e) => setFlightNumber(e.target.value.toUpperCase())}
              className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., AI 131, 6E 2134"
              onKeyPress={(e) => e.key === 'Enter' && lookupFlight()}
            />
          </div>
          <button
            onClick={lookupFlight}
            disabled={isLoading || !flightNumber.trim()}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Looking up...' : 'Lookup'}
          </button>
        </div>
        <p className="mt-2 text-sm text-gray-500">
          Enter your flight number to get automatic time suggestions and flight details
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg"
        >
          <AlertCircle className="h-5 w-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </motion.div>
      )}

      {/* Flight Information Display */}
      <AnimatePresence>
        {flightData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="bg-primary-100 p-2 rounded-lg">
                  <Plane className="h-5 w-5 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {flightData.flight_number}
                  </h3>
                  <p className="text-sm text-gray-600">{flightData.airline_name}</p>
                </div>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(flightData.flight_status)}`}>
                {flightData.flight_status}
              </span>
            </div>

            {/* Flight Route */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-sm text-gray-500">From</div>
                <div className="text-lg font-semibold text-gray-900">{flightData.departure_airport}</div>
                {flightData.departure_terminal && (
                  <div className="text-sm text-gray-600">Terminal {flightData.departure_terminal}</div>
                )}
              </div>
              
              <div className="flex items-center justify-center">
                <div className="w-full h-px bg-gray-300 relative">
                  <Plane className="h-4 w-4 text-gray-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white" />
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-sm text-gray-500">To</div>
                <div className="text-lg font-semibold text-gray-900">{flightData.arrival_airport}</div>
                {flightData.arrival_terminal && (
                  <div className="text-sm text-gray-600">Terminal {flightData.arrival_terminal}</div>
                )}
              </div>
            </div>

            {/* Flight Times */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-700 mb-2">Departure</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Scheduled:</span>
                    <span className="text-sm font-medium">{formatTime(flightData.scheduled_departure)}</span>
                  </div>
                  {flightData.estimated_departure && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Estimated:</span>
                      <span className="text-sm font-medium">{formatTime(flightData.estimated_departure)}</span>
                    </div>
                  )}
                  {flightData.gate && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Gate:</span>
                      <span className="text-sm font-medium">{flightData.gate}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-700 mb-2">Arrival</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Scheduled:</span>
                    <span className="text-sm font-medium">{formatTime(flightData.scheduled_arrival)}</span>
                  </div>
                  {flightData.estimated_arrival && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Estimated:</span>
                      <span className="text-sm font-medium">{formatTime(flightData.estimated_arrival)}</span>
                    </div>
                  )}
                  {flightData.baggage_belt && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Baggage:</span>
                      <span className="text-sm font-medium">Belt {flightData.baggage_belt}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Time Slot Suggestions */}
      <AnimatePresence>
        {timeSlotSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-semibold text-gray-900">Recommended Service Times</h4>
            <div className="grid grid-cols-1 gap-3">
              {timeSlotSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleTimeSlotSelect(suggestion)}
                  className={`text-left p-4 border-2 rounded-lg transition-all duration-200 hover:border-primary-300 ${
                    selectedTimeSlot === suggestion.recommended_time
                      ? 'border-primary-500 bg-primary-50'
                      : `border-gray-200 ${getConfidenceColor(suggestion.confidence)}`
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <Clock className="h-5 w-5 text-gray-600" />
                      <span className="font-semibold text-gray-900">
                        {formatTime(suggestion.recommended_time)}
                      </span>
                      <span className="text-sm text-gray-600">
                        ({formatDate(suggestion.recommended_time)})
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        suggestion.confidence === 'high' ? 'bg-green-100 text-green-800' :
                        suggestion.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {suggestion.confidence} confidence
                      </span>
                      {selectedTimeSlot === suggestion.recommended_time && (
                        <CheckCircle className="h-5 w-5 text-primary-600" />
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{suggestion.reason}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {suggestion.buffer_minutes} minutes buffer time
                  </p>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Manual Time Selection Note */}
      {!flightData && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Info className="h-5 w-5 text-blue-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900">No flight information?</h4>
              <p className="text-sm text-blue-700 mt-1">
                You can skip this step and manually select your preferred service time in the next step.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
