'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  MapPin, 
  Calendar, 
  Clock, 
  Users, 
  Plane,
  Search,
  ArrowRight
} from 'lucide-react'
import AirportSearch from './AirportSearch'
import FlightSearch from './FlightSearch'
import ServiceSelector from './ServiceSelector'
import TerminalSelector from './TerminalSelector'
import DateTimeSelector from './DateTimeSelector'
import PassengerSelector from './PassengerSelector'
import FlightInformation from './FlightInformation'
import CheckoutForm from '../payment/CheckoutForm'
import PaymentSuccess from '../payment/PaymentSuccess'
import { realTimePricingService } from '@/lib/realtime-pricing'
import { Airport, Flight } from '@/lib/aviationstack'

interface BookingWidgetProps {
  compact?: boolean
  onBookingStart?: () => void
  onClose?: () => void
}

interface BookingData {
  airport: string
  airportId: string
  terminal?: string
  service: string
  date: string
  time: string
  passengers: number
  flightNumber?: string
  serviceType: 'arrival' | 'departure'
  flightData?: any
}

export default function BookingWidget({
  compact = false,
  onBookingStart,
  onClose
}: BookingWidgetProps) {
  const [step, setStep] = useState(1)
  const [showPayment, setShowPayment] = useState(false)
  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [bookingReference, setBookingReference] = useState('')
  const [selectedDeparture, setSelectedDeparture] = useState<Airport | null>(null)
  const [selectedArrival, setSelectedArrival] = useState<Airport | null>(null)
  const [selectedFlight, setSelectedFlight] = useState<Flight | null>(null)
  const [selectedServiceDetails, setSelectedServiceDetails] = useState<any>(null)
  const [selectedTerminal, setSelectedTerminal] = useState('')
  const [availableTerminals, setAvailableTerminals] = useState<any[]>([])
  const [flightData, setFlightData] = useState<any>(null)
  const [bookingData, setBookingData] = useState<BookingData>({
    airport: '',
    airportId: '',
    terminal: '',
    service: '',
    date: '',
    time: '',
    passengers: 1,
    serviceType: 'arrival',
    flightData: null
  })

  // Ref for the booking widget container
  const bookingWidgetRef = useRef<HTMLDivElement>(null)

  const scrollToBookingWidget = () => {
    if (bookingWidgetRef.current) {
      const rect = bookingWidgetRef.current.getBoundingClientRect()
      const scrollTop = window.pageYOffset + rect.top - 20 // 20px offset from top
      window.scrollTo({ top: scrollTop, behavior: 'smooth' })
    }
  }

  const handleNext = () => {
    if (step < 6) {
      setStep(step + 1)
      // Scroll to booking widget after step change
      setTimeout(() => scrollToBookingWidget(), 100)
    } else {
      // Proceed to payment
      setShowPayment(true)
      // Scroll to booking widget when proceeding to payment
      setTimeout(() => scrollToBookingWidget(), 100)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
      // Scroll to booking widget after step change
      setTimeout(() => scrollToBookingWidget(), 100)
    }
  }

  const updateBookingData = (field: keyof BookingData, value: any) => {
    setBookingData(prev => ({ ...prev, [field]: value }))
  }

  const handleAirportSelect = (airport: string, airportId: string) => {
    updateBookingData('airport', airport)
    updateBookingData('airportId', airportId)
  }

  const handleServiceTypeChange = (type: 'arrival' | 'departure') => {
    updateBookingData('serviceType', type)
    // Reset terminal selection when service type changes
    setSelectedTerminal('')
    updateBookingData('terminal', '')
  }

  const handleTerminalSelect = (terminal: string) => {
    setSelectedTerminal(terminal)
    updateBookingData('terminal', terminal)
  }

  const handleFlightSelect = (flightData: any, suggestedTime: string) => {
    setFlightData(flightData)
    updateBookingData('flightData', flightData)
    updateBookingData('flightNumber', flightData.flight_number)
    if (suggestedTime) {
      updateBookingData('time', suggestedTime)
    }
  }

  const handleTimeSlotSelect = (time: string) => {
    updateBookingData('time', time)
  }

  const handlePaymentSuccess = (paymentIntent: any) => {
    // Generate booking reference
    const reference = 'AC' + Date.now().toString().slice(-8).toUpperCase()
    setBookingReference(reference)
    setPaymentSuccess(true)
    setShowPayment(false)
  }

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error)
    // Handle payment error (show error message, etc.)
  }

  const calculateTotalAmount = () => {
    // Try to get real-time pricing first
    if (bookingData.service && bookingData.date && bookingData.time && bookingData.airportId) {
      const pricingContext = {
        serviceIds: [bookingData.service],
        airportId: bookingData.airportId,
        date: bookingData.date,
        time: bookingData.time,
        passengerCount: bookingData.passengers
      }

      // Check if we have cached pricing
      const cachedPrice = realTimePricingService.getCachedPrice(pricingContext)
      if (cachedPrice) {
        return cachedPrice.total
      }
    }

    // Fallback to mock pricing calculation (in INR)
    let basePrice = 4150 // Base meet & greet price (50 USD * 83)
    if (bookingData.service.includes('VIP Lounge')) basePrice += 6225 // 75 USD * 83
    if (bookingData.service.includes('Fast Track')) basePrice += 3320 // 40 USD * 83
    if (bookingData.service.includes('Transfer')) basePrice += 9960 // 120 USD * 83
    return basePrice * bookingData.passengers
  }

  const isStepValid = () => {
    switch (step) {
      case 1: // Airport selection
        return bookingData.airport !== ''
      case 2: // Service type selection
        return bookingData.serviceType !== ''
      case 3: // Terminal selection
        return bookingData.terminal !== ''
      case 4: // Service selection
        return bookingData.service !== ''
      case 5: // Flight information (optional)
        return true // This step is always valid as flight info is optional
      case 6: // Date/time and passengers
        return bookingData.date !== '' && bookingData.time !== '' && bookingData.passengers > 0
      default:
        return false
    }
  }

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="glass rounded-2xl p-8 border border-white/30 backdrop-blur-luxury"
      >
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-white mb-2 font-display">Quick Booking</h3>
          <p className="text-white/80">Start your luxury travel experience</p>
        </div>

        <div className="grid grid-cols-1 gap-6">
          <motion.div
            className="relative"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gold-400" />
            <input
              type="text"
              placeholder="Select Airport"
              className="w-full pl-12 pr-4 py-4 bg-white/20 border-2 border-white/30 rounded-xl text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-gold-500 transition-all duration-300 backdrop-blur-sm"
            />
          </motion.div>

          <div className="grid grid-cols-2 gap-4">
            <motion.div
              className="relative"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gold-400" />
              <input
                type="date"
                className="w-full pl-12 pr-4 py-4 bg-white/20 border-2 border-white/30 rounded-xl text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-gold-500 transition-all duration-300 backdrop-blur-sm"
              />
            </motion.div>
            <motion.div
              className="relative"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Clock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gold-400" />
              <input
                type="time"
                className="w-full pl-12 pr-4 py-4 bg-white/20 border-2 border-white/30 rounded-xl text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-gold-500 transition-all duration-300 backdrop-blur-sm"
              />
            </motion.div>
          </div>

          <motion.button
            onClick={onBookingStart}
            className="w-full btn-gold group relative overflow-hidden"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-center space-x-3">
              <Search className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="font-semibold">Find Luxury Services</span>
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </motion.button>
        </div>
      </motion.div>
    )
  }

  return (
    <div ref={bookingWidgetRef} className="bg-white rounded-2xl shadow-luxury-lg overflow-hidden">
      {/* Compact Progress indicator */}
      <div className="bg-gray-50 px-4 lg:px-8 py-4 lg:py-6 border-b border-gray-200">
        <motion.div
          className="flex items-center justify-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {[1, 2, 3, 4, 5, 6].map((stepNumber) => (
            <div key={stepNumber} className="flex items-center">
              <motion.div
                className={`relative flex items-center justify-center w-8 h-8 lg:w-10 lg:h-10 rounded-full border-2 font-bold text-sm lg:text-base transition-all duration-500 ${
                  stepNumber === step
                    ? 'bg-primary-500 border-primary-500 text-white'
                    : stepNumber < step
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-white border-gray-300 text-gray-500'
                }`}
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {stepNumber < step ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500 }}
                  >
                    ✓
                  </motion.div>
                ) : (
                  stepNumber
                )}
              </motion.div>
              {stepNumber < 6 && (
                <motion.div
                  className={`w-8 lg:w-12 h-0.5 mx-2 lg:mx-3 rounded-full transition-all duration-500 ${
                    stepNumber < step
                      ? 'bg-green-500'
                      : 'bg-gray-300'
                  }`}
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: stepNumber < step ? 1 : 0.3 }}
                  transition={{ duration: 0.5, delay: stepNumber * 0.1 }}
                />
              )}
            </div>
          ))}
        </motion.div>

        {/* Step labels - Compact version */}
        <motion.div
          className="grid grid-cols-4 gap-2 lg:gap-4 mt-3 lg:mt-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {[
            { label: 'Airport', icon: '✈️' },
            { label: 'Service', icon: '⭐' },
            { label: 'Date & Time', icon: '📅' },
            { label: 'Passengers', icon: '👥' }
          ].map((stepInfo, index) => (
            <motion.div
              key={index}
              className={`transition-all duration-300 ${
                step >= index + 1
                  ? 'text-primary-600 font-semibold'
                  : 'text-gray-500'
              }`}
            >
              <div className="text-lg lg:text-xl mb-1">{stepInfo.icon}</div>
              <div className="text-xs lg:text-sm font-medium">{stepInfo.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Optimized Step content */}
      <motion.div
        key={step}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="px-4 lg:px-8 py-6 lg:py-8"
      >
        {/* Step 1: Airport Selection */}
        {step === 1 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Select Airport
            </motion.h3>
            <div className="space-y-4 lg:space-y-6">
              <AirportSearch
                value={bookingData.airport}
                onChange={handleAirportSelect}
                onServiceTypeChange={handleServiceTypeChange}
                serviceType={bookingData.serviceType}
                passengers={bookingData.passengers}
              />
            </div>
          </div>
        )}

        {/* Step 2: Service Type Selection */}
        {step === 2 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Service Type
            </motion.h3>
            <div className="space-y-4 lg:space-y-6">
              <div className="flex justify-center">
                <div className="bg-gray-100 p-1 rounded-lg flex w-full max-w-sm">
                  <button
                    type="button"
                    onClick={() => handleServiceTypeChange('arrival')}
                    className={`flex-1 px-4 lg:px-6 py-3 lg:py-4 rounded-md font-medium transition-all duration-200 text-sm lg:text-base ${
                      bookingData.serviceType === 'arrival'
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <Plane className="h-4 w-4 inline mr-2 transform rotate-45" />
                    Arrival
                  </button>
                  <button
                    type="button"
                    onClick={() => handleServiceTypeChange('departure')}
                    className={`flex-1 px-4 lg:px-6 py-3 lg:py-4 rounded-md font-medium transition-all duration-200 text-sm lg:text-base ${
                      bookingData.serviceType === 'departure'
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <Plane className="h-4 w-4 inline mr-2 transform -rotate-45" />
                    Departure
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Terminal Selection */}
        {step === 3 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Select Terminal
            </motion.h3>
            <TerminalSelector
              airportCode={bookingData.airportId}
              serviceType={bookingData.serviceType}
              selectedTerminal={selectedTerminal}
              onTerminalSelect={handleTerminalSelect}
              passengers={bookingData.passengers}
            />
          </div>
        )}

        {/* Step 4: Service Selection */}
        {step === 4 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Choose Your Service
            </motion.h3>
            <ServiceSelector
              selectedService={bookingData.service}
              onServiceSelect={(service) => updateBookingData('service', service)}
              airportId={bookingData.airportId}
              serviceType={bookingData.serviceType}
              terminal={bookingData.terminal}
              date={bookingData.date}
              time={bookingData.time}
              passengerCount={bookingData.passengers}
            />
          </div>
        )}

        {/* Step 5: Flight Information */}
        {step === 5 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Flight Information
            </motion.h3>
            <FlightInformation
              serviceType={bookingData.serviceType}
              onFlightSelect={handleFlightSelect}
              onTimeSlotSelect={handleTimeSlotSelect}
              selectedDate={bookingData.date}
            />
          </div>
        )}

        {/* Step 6: Date, Time & Passengers */}
        {step === 6 && (
          <div>
            <motion.h3
              className="text-xl lg:text-2xl font-bold text-center mb-4 lg:mb-6 text-primary-500"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Select Date, Time & Passengers
            </motion.h3>
            <div className="space-y-6">
              <DateTimeSelector
                date={bookingData.date}
                time={bookingData.time}
                flightNumber={bookingData.flightNumber}
                onDateChange={(date) => updateBookingData('date', date)}
                onTimeChange={(time) => updateBookingData('time', time)}
                onFlightNumberChange={(flightNumber) => updateBookingData('flightNumber', flightNumber)}
                serviceType={bookingData.serviceType}
                airportId={bookingData.airportId}
                serviceIds={bookingData.service ? [bookingData.service] : []}
                passengerCount={bookingData.passengers}
                flightData={flightData}
              />
              <PassengerSelector
                passengers={bookingData.passengers}
                onPassengerChange={(passengers) => updateBookingData('passengers', passengers)}
              />
            </div>
          </div>
        )}



        {showPayment && !paymentSuccess && (
          <div>
            <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-4 lg:mb-6 text-center">
              Complete Your Booking
            </h3>
            <CheckoutForm
              bookingData={{
                airport: bookingData.airport,
                terminal: bookingData.terminal,
                serviceType: bookingData.serviceType,
                services: bookingData.service.split(', '),
                date: bookingData.date,
                time: bookingData.time,
                passengers: bookingData.passengers,
                totalAmount: calculateTotalAmount(),
                currency: 'INR',
                flightNumber: bookingData.flightNumber,
                flightData: bookingData.flightData
              }}
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
            />
          </div>
        )}

        {paymentSuccess && (
          <div>
            <PaymentSuccess
              paymentIntent={{ id: 'mock_payment_intent' }}
              bookingData={{
                airport: bookingData.airport,
                terminal: bookingData.terminal,
                serviceType: bookingData.serviceType,
                services: bookingData.service.split(', '),
                selectedServices: selectedServiceDetails ? [selectedServiceDetails] : undefined,
                date: bookingData.date,
                time: bookingData.time,
                passengers: bookingData.passengers,
                totalAmount: calculateTotalAmount(),
                currency: 'INR',
                flightNumber: bookingData.flightNumber,
                flightData: bookingData.flightData
              }}
              bookingReference={bookingReference}
            />
          </div>
        )}
      </motion.div>

      {/* Back button only - Continue is now sticky */}
      {!showPayment && !paymentSuccess && step > 1 && (
        <div className="bg-gray-50 px-4 lg:px-8 py-3 lg:py-4 border-t border-gray-200">
          <div className="flex justify-start">
            <motion.button
              onClick={handleBack}
              className="px-4 lg:px-6 py-2 lg:py-3 rounded-lg font-medium transition-all duration-300 text-sm lg:text-base bg-white border border-gray-300 text-gray-600 hover:border-primary-500 hover:text-primary-600"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              ← Back to Step {step - 1}
            </motion.button>
          </div>
        </div>
      )}

      {showPayment && (
        <div className="bg-gray-50 px-4 lg:px-8 py-4 lg:py-6 border-t border-gray-200">
          <div className="flex justify-center">
            <button
              onClick={() => setShowPayment(false)}
              className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors duration-200 bg-white border border-gray-300 rounded-lg hover:border-primary-500"
            >
              ← Back to Details
            </button>
          </div>
        </div>
      )}

      {paymentSuccess && (
        <div className="bg-gray-50 px-4 lg:px-8 py-4 lg:py-6 border-t border-gray-200">
          <div className="flex justify-center">
            <button
              onClick={onClose}
              className="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-semibold transition-colors duration-200"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Sticky Continue Button - Always visible */}
      {!showPayment && !paymentSuccess && (
        <motion.div
          className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50 safe-area-pb"
          initial={{ y: 100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <div className="container-max px-4 py-3 lg:py-4">
            <div className="flex items-center justify-between max-w-7xl mx-auto">
              {/* Mobile: Price and button stacked */}
              <div className="flex items-center justify-between w-full lg:hidden">
                <div className="text-left">
                  <div className="text-xs text-gray-600 font-medium">Total Amount</div>
                  <div className="text-lg font-bold text-primary-600">
                    ₹{calculateTotalAmount().toLocaleString()}
                  </div>
                </div>
                <motion.button
                  onClick={handleNext}
                  disabled={!isStepValid()}
                  className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-sm flex items-center space-x-2 min-w-[140px] justify-center ${
                    isStepValid()
                      ? 'bg-primary-500 hover:bg-primary-600 text-white shadow-lg'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  style={{ minHeight: '44px' }} // Ensure minimum touch target
                  whileHover={isStepValid() ? { scale: 1.02 } : {}}
                  whileTap={isStepValid() ? { scale: 0.98 } : {}}
                  animate={isStepValid() ? {
                    boxShadow: [
                      "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                      "0 10px 15px -3px rgba(59, 130, 246, 0.3)",
                      "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                    ]
                  } : {}}
                  transition={{
                    boxShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <span>{step === 6 ? 'Continue to Payment' : `Continue to Step ${step + 1}`}</span>
                  <ArrowRight className="h-4 w-4" />
                </motion.button>
              </div>

              {/* Desktop: Price and button side by side */}
              <div className="hidden lg:flex items-center justify-between w-full">
                <div className="flex items-center space-x-8">
                  <div className="text-left">
                    <div className="text-sm text-gray-600 font-medium">Total Amount</div>
                    <div className="text-xl font-bold text-primary-600">
                      ₹{calculateTotalAmount().toLocaleString()}
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    Step {step} of 6
                  </div>
                </div>

                <motion.button
                  onClick={handleNext}
                  disabled={!isStepValid()}
                  className={`px-8 py-4 rounded-lg font-semibold transition-all duration-300 text-base flex items-center space-x-3 ${
                    isStepValid()
                      ? 'bg-primary-500 hover:bg-primary-600 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  whileHover={isStepValid() ? { scale: 1.02 } : {}}
                  whileTap={isStepValid() ? { scale: 0.98 } : {}}
                  animate={isStepValid() ? {
                    boxShadow: [
                      "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                      "0 10px 15px -3px rgba(59, 130, 246, 0.3)",
                      "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                    ]
                  } : {}}
                  transition={{
                    boxShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <span>{step === 6 ? 'Continue to Payment' : `Continue to Step ${step + 1}`}</span>
                  <ArrowRight className="h-5 w-5" />
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Bottom padding to account for sticky button */}
      {!showPayment && !paymentSuccess && (
        <div className="h-20 lg:h-24"></div>
      )}
    </div>
  )
}
