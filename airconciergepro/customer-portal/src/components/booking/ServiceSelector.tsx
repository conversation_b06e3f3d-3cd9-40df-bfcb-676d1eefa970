'use client'

import { useState, useEffect } from 'react'
import { Users, Star, Clock, Plane, CheckCircle, TrendingUp, TrendingDown } from 'lucide-react'
import { serviceService, Service } from '@/lib/api'
import { realTimePricingService, PricingResult, formatPrice, calculatePriceDifference } from '@/lib/realtime-pricing'

interface ServiceSelectorProps {
  selectedService: string
  onServiceSelect: (serviceId: string) => void
  airportId: string
  serviceType: 'arrival' | 'departure'
  terminal?: string
  date?: string
  time?: string
  passengerCount?: number
}

// Icon mapping for service categories
const getServiceIcon = (category: string) => {
  switch (category) {
    case 'assistance':
      return Users
    case 'lounge':
      return Star
    case 'security':
      return Clock
    case 'transfer':
      return Plane
    default:
      return Users
  }
}

export default function ServiceSelector({
  selectedService,
  onServiceSelect,
  airportId,
  serviceType,
  terminal,
  date,
  time,
  passengerCount = 1
}: ServiceSelectorProps) {
  const [hoveredService, setHoveredService] = useState<string | null>(null)
  const [services, setServices] = useState<Service[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pricing, setPricing] = useState<Map<string, PricingResult>>(new Map())
  const [priceUpdates, setPriceUpdates] = useState<Map<string, { oldPrice: number; newPrice: number }>>(new Map())

  // Load services when airport or service type changes
  useEffect(() => {
    const loadServices = async () => {
      if (!airportId) return

      setIsLoading(true)
      setError(null)
      try {
        const serviceData = await serviceService.getServicesForAirport(airportId, {
          type: serviceType,
          passengers: passengerCount,
          terminal: terminal
        })
        setServices(serviceData)

        // Load real-time pricing if date and time are available
        if (date && time && serviceData.length > 0) {
          loadRealTimePricing(serviceData)
        }
      } catch (error) {
        console.error('Failed to load services:', error)
        setError('Failed to load services. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    loadServices()
  }, [airportId, serviceType, terminal, date, time, passengerCount])

  // Load real-time pricing for services
  const loadRealTimePricing = async (servicesToPrice: Service[]) => {
    const newPricing = new Map<string, PricingResult>()

    for (const service of servicesToPrice) {
      try {
        const pricingContext = {
          serviceIds: [service.id],
          airportId,
          date: date!,
          time: time!,
          passengerCount
        }

        const pricingResult = await realTimePricingService.calculatePrice(pricingContext)
        newPricing.set(service.id, pricingResult)

        // Subscribe to price updates
        realTimePricingService.subscribeToPriceUpdates(pricingContext, (updatedResult) => {
          setPricing(prev => {
            const oldPrice = prev.get(service.id)?.total || parseFloat(service.base_price)
            const newPrice = updatedResult.total

            // Track price changes for animation
            if (oldPrice !== newPrice) {
              setPriceUpdates(prevUpdates => new Map(prevUpdates.set(service.id, { oldPrice, newPrice })))
              // Clear the update indicator after 3 seconds
              setTimeout(() => {
                setPriceUpdates(prevUpdates => {
                  const updated = new Map(prevUpdates)
                  updated.delete(service.id)
                  return updated
                })
              }, 3000)
            }

            return new Map(prev.set(service.id, updatedResult))
          })
        })
      } catch (error) {
        console.error(`Failed to load pricing for service ${service.id}:`, error)
      }
    }

    setPricing(newPricing)
  }

  return (
    <div className="space-y-6">
      {/* Service Type Info */}
      <div className="text-center bg-primary-50 rounded-lg p-4">
        <div className="text-lg font-semibold text-primary-900">
          {serviceType === 'arrival' ? 'Arrival Services' : 'Departure Services'}
        </div>
        <div className="text-sm text-primary-700 mt-1">
          {serviceType === 'arrival'
            ? 'Services to assist you upon arrival at the airport'
            : 'Services to help you before departure from the airport'
          }
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading services...</div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Retry
          </button>
        </div>
      )}

      {/* Services Grid - Optimized for mobile */}
      {!isLoading && !error && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          {services.map((service) => {
            const Icon = getServiceIcon(service.category)
            const isSelected = selectedService === service.id
            const isHovered = hoveredService === service.id
            const servicePricing = pricing.get(service.id)
            const priceUpdate = priceUpdates.get(service.id)

            return (
              <div
                key={service.id}
                onClick={() => onServiceSelect(service.id)}
                onMouseEnter={() => setHoveredService(service.id)}
                onMouseLeave={() => setHoveredService(null)}
                className={`relative cursor-pointer rounded-lg border-2 p-4 lg:p-6 transition-all duration-200 touch-manipulation ${
                  isSelected
                    ? 'border-primary-500 bg-primary-50 shadow-lg'
                    : isHovered
                    ? 'border-primary-300 bg-primary-25 shadow-md'
                    : 'border-gray-200 bg-white hover:border-gray-300 active:border-primary-300 active:bg-primary-25'
                }`}
                style={{ minHeight: '44px' }} // Ensure minimum touch target
              >
                {/* Selected indicator */}
                {isSelected && (
                  <div className="absolute top-3 lg:top-4 right-3 lg:right-4">
                    <CheckCircle className="h-5 w-5 lg:h-6 lg:w-6 text-primary-600" />
                  </div>
              )}

              {/* Service content */}
              <div className="space-y-3 lg:space-y-4">
                {/* Icon and title */}
                <div className="flex items-start space-x-3 lg:space-x-4">
                  <div className={`p-2 lg:p-3 rounded-lg ${
                    isSelected ? 'bg-primary-600' : 'bg-primary-100'
                  }`}>
                    <Icon className={`h-5 w-5 lg:h-6 lg:w-6 ${
                      isSelected ? 'text-white' : 'text-primary-600'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-900">{service.name}</h3>
                    <p className="text-gray-600 text-sm mt-1">{service.description}</p>
                  </div>
                </div>

                {/* Price and duration */}
                <div className="flex justify-between items-center">
                  <div className="relative">
                    {servicePricing ? (
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <div className="text-2xl font-bold text-primary-600">
                            {formatPrice(servicePricing.total, servicePricing.currency)}
                          </div>
                          {priceUpdate && (
                            <div className="flex items-center space-x-1">
                              {priceUpdate.newPrice > priceUpdate.oldPrice ? (
                                <TrendingUp className="h-4 w-4 text-red-500" />
                              ) : priceUpdate.newPrice < priceUpdate.oldPrice ? (
                                <TrendingDown className="h-4 w-4 text-green-500" />
                              ) : null}
                              <span className={`text-xs font-medium ${
                                priceUpdate.newPrice > priceUpdate.oldPrice ? 'text-red-500' : 'text-green-500'
                              }`}>
                                {priceUpdate.newPrice > priceUpdate.oldPrice ? '+' : ''}
                                {formatPrice(priceUpdate.newPrice - priceUpdate.oldPrice, servicePricing.currency)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          per person • Live pricing
                        </div>
                        {servicePricing.breakdown[0]?.adjustments.length > 0 && (
                          <div className="text-xs text-gray-600">
                            {servicePricing.breakdown[0].adjustments.map((adj, idx) => (
                              <div key={idx} className={`${adj.amount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                {adj.name}: {adj.amount > 0 ? '+' : ''}{formatPrice(adj.amount, servicePricing.currency)}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div>
                        <div className="text-2xl font-bold text-primary-600">
                          {service.currency} {service.base_price}
                        </div>
                        <div className="text-xs text-gray-500">per person</div>
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {Math.floor(service.duration_minutes / 60)}h {service.duration_minutes % 60}m
                    </div>
                    <div className="text-xs text-gray-500">duration</div>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2">
                  {service.inclusions?.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-success-600 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Select button */}
                <button
                  className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    isSelected
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {isSelected ? 'Selected' : 'Select Service'}
                </button>
              </div>
            </div>
          )
        })}
        </div>
      )}

      {/* No services available */}
      {!isLoading && !error && services.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">No services available for this airport and service type.</div>
          <div className="text-sm text-gray-400">Please try selecting a different airport or service type.</div>
        </div>
      )}

      {/* Service combinations - only show if services are available */}
      {!isLoading && !error && services.length > 0 && (
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 mb-4">Popular Combinations</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="font-medium text-gray-900 mb-2">Complete VIP Experience</div>
            <div className="text-sm text-gray-600 mb-3">Meet & Greet + VIP Lounge + Fast Track</div>
            <div className="flex justify-between items-center">
              <div className="text-lg font-bold text-primary-600">$135</div>
              <div className="text-sm text-success-600 font-medium">Save $10</div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="font-medium text-gray-900 mb-2">Express Service</div>
            <div className="text-sm text-gray-600 mb-3">Meet & Greet + Fast Track</div>
            <div className="flex justify-between items-center">
              <div className="text-lg font-bold text-primary-600">$75</div>
              <div className="text-sm text-success-600 font-medium">Save $5</div>
            </div>
          </div>
        </div>
        </div>
      )}
    </div>
  )
}
