'use client'

import { useState } from 'react'
import { Calendar, Clock, Plane, AlertCircle } from 'lucide-react'
import AvailabilityIndicator, { CompactAvailabilityIndicator } from './AvailabilityIndicator'

interface DateTimeSelectorProps {
  date: string
  time: string
  flightNumber?: string
  onDateChange: (date: string) => void
  onTimeChange: (time: string) => void
  onFlightNumberChange: (flightNumber: string) => void
  serviceType: 'arrival' | 'departure'
  airportId?: string
  serviceIds?: string[]
  passengerCount?: number
}

export default function DateTimeSelector({
  date,
  time,
  flightNumber,
  onDateChange,
  onTimeChange,
  onFlightNumberChange,
  serviceType,
  airportId,
  serviceIds = [],
  passengerCount = 1
}: DateTimeSelectorProps) {
  const [showFlightInput, setShowFlightInput] = useState(false)

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0]
  
  // Get max date (6 months from now)
  const maxDate = new Date()
  maxDate.setMonth(maxDate.getMonth() + 6)
  const maxDateString = maxDate.toISOString().split('T')[0]

  // Generate time slots
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        slots.push(timeString)
      }
    }
    return slots
  }

  const timeSlots = generateTimeSlots()

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Service Type Info - Compact for mobile */}
      <div className="text-center bg-primary-50 rounded-lg p-3 lg:p-4">
        <div className="text-base lg:text-lg font-semibold text-primary-900">
          Select {serviceType === 'arrival' ? 'Arrival' : 'Departure'} Date & Time
        </div>
        <div className="text-sm text-primary-700 mt-1">
          {serviceType === 'arrival'
            ? 'When do you arrive at the airport?'
            : 'When do you depart from the airport?'
          }
        </div>
      </div>

      {/* Date and Time Grid - Side by side on desktop */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Date Selection */}
        <div className="space-y-4">
          <label className="block text-base lg:text-lg font-semibold text-gray-900">
            <Calendar className="inline h-4 w-4 lg:h-5 lg:w-5 mr-2" />
            Select Date
          </label>
          <input
            type="date"
            value={date}
            onChange={(e) => onDateChange(e.target.value)}
            min={today}
            max={maxDateString}
            className="w-full px-3 py-3 lg:px-4 lg:py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base lg:text-lg touch-manipulation"
            style={{ fontSize: '16px' }} // Prevent zoom on iOS
          />
          <div className="text-xs lg:text-sm text-gray-600">
            You can book services up to 6 months in advance
          </div>
        </div>

        {/* Time Selection */}
        <div className="space-y-4">
          <label className="block text-base lg:text-lg font-semibold text-gray-900">
            <Clock className="inline h-4 w-4 lg:h-5 lg:w-5 mr-2" />
            Select Time
          </label>

          {/* Quick time selection - Mobile optimized */}
          <div className="grid grid-cols-3 lg:grid-cols-6 gap-2 lg:gap-3">
            {['06:00', '09:00', '12:00', '15:00', '18:00', '21:00'].map((quickTime) => (
              <div key={quickTime} className="relative">
                <button
                  onClick={() => onTimeChange(quickTime)}
                  className={`w-full py-3 px-2 lg:px-4 rounded-lg border font-medium transition-all duration-200 text-sm lg:text-base touch-manipulation ${
                    time === quickTime
                      ? 'bg-primary-600 text-white border-primary-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-primary-300 hover:bg-primary-50 active:border-primary-300 active:bg-primary-50'
                  }`}
                  style={{ minHeight: '44px' }} // Ensure minimum touch target
                >
                {quickTime}
              </button>
              {/* Compact availability indicator */}
              {date && airportId && serviceIds.length > 0 && (
                <div className="absolute -bottom-1 -right-1">
                  <CompactAvailabilityIndicator
                    airportId={airportId}
                    serviceIds={serviceIds}
                    date={date}
                    time={quickTime}
                    passengerCount={passengerCount}
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Custom time input */}
        <div className="relative">
          <select
            value={time}
            onChange={(e) => onTimeChange(e.target.value)}
            className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg appearance-none bg-white"
          >
            <option value="">Select specific time</option>
            {timeSlots.map((slot) => (
              <option key={slot} value={slot}>
                {slot}
              </option>
            ))}
          </select>
          <Clock className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
        </div>
        </div>
      </div>

      {/* Availability indicator for selected date */}
      {date && airportId && serviceIds.length > 0 && (
        <div className="p-3 lg:p-4 bg-gray-50 rounded-lg">
          <div className="lg:hidden">
            <CompactAvailabilityIndicator
              airportId={airportId}
              serviceIds={serviceIds}
              date={date}
              passengerCount={passengerCount}
            />
          </div>
          <div className="hidden lg:block">
            <AvailabilityIndicator
              airportId={airportId}
              serviceIds={serviceIds}
              date={date}
              passengerCount={passengerCount}
            />
          </div>
        </div>
      )}

      {/* Flight Number (Optional) - Mobile optimized */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="block text-base lg:text-lg font-semibold text-gray-900">
            <Plane className="inline h-4 w-4 lg:h-5 lg:w-5 mr-2" />
            Flight Number (Optional)
          </label>
          <button
            onClick={() => setShowFlightInput(!showFlightInput)}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm lg:text-base px-3 py-2 rounded-lg hover:bg-primary-50 touch-manipulation"
            style={{ minHeight: '44px' }} // Ensure minimum touch target
          >
            {showFlightInput ? 'Hide' : 'Add Flight Info'}
          </button>
        </div>

        {showFlightInput && (
          <div className="space-y-4">
            <input
              type="text"
              placeholder="e.g., AA123, BA456, EK789"
              value={flightNumber || ''}
              onChange={(e) => onFlightNumberChange(e.target.value)}
              className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg"
            />
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <div className="font-medium mb-1">Why provide flight information?</div>
                  <ul className="space-y-1 text-blue-700">
                    <li>• Real-time flight tracking and delay notifications</li>
                    <li>• Automatic service time adjustments</li>
                    <li>• Better coordination with airport operations</li>
                    <li>• Priority assistance during disruptions</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selected Date/Time Summary */}
      {date && time && (
        <div className="bg-success-50 border border-success-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="bg-success-600 p-2 rounded-lg">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <div>
              <div className="font-semibold text-success-900">
                Service Scheduled
              </div>
              <div className="text-success-800">
                {new Date(date).toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })} at {time}
              </div>
              {flightNumber && (
                <div className="text-sm text-success-700 mt-1">
                  Flight: {flightNumber}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Important Notes */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 mb-3">Important Notes</h4>
        <ul className="space-y-2 text-sm text-gray-700">
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 font-bold">•</span>
            <span>Please arrive at the meeting point 15 minutes before your scheduled time</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 font-bold">•</span>
            <span>Our team will contact you 24 hours before your service</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 font-bold">•</span>
            <span>Free cancellation up to 4 hours before your scheduled time</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 font-bold">•</span>
            <span>24/7 customer support available for any changes or assistance</span>
          </li>
        </ul>
      </div>
    </div>
  )
}
