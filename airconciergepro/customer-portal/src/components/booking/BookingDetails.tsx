'use client'

import { MapPin, Calendar, Clock, Users, Plane, CreditCard, User, Phone, Mail } from 'lucide-react'

interface BookingData {
  id: string
  reference: string
  airport: string
  airportId: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
  flightNumber?: string
  customerName: string
  customerEmail: string
  customerPhone: string
  status: 'confirmed' | 'pending' | 'cancelled'
  createdAt: string
  paymentIntentId: string
}

interface BookingDetailsProps {
  booking: BookingData
  showCustomerInfo?: boolean
}

export default function BookingDetails({ booking, showCustomerInfo = true }: BookingDetailsProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatCurrency = (amount: number, currency: string) => {
    // Use Indian locale for INR formatting
    const locale = currency === 'INR' ? 'en-IN' : 'en-US'
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Service Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <MapPin className="h-5 w-5 text-primary-600 mr-2" />
          Service Details
        </h3>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Airport</label>
              <div className="text-gray-900 font-medium">
                {booking.service_type === 'arrival'
                  ? `${booking.arrival_airport} (Arrival from ${booking.departure_airport})`
                  : `${booking.departure_airport} (Departure to ${booking.arrival_airport})`
                }
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Service Type</label>
              <div className="text-gray-900 font-medium capitalize">
                {booking.service_type} Assistance
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Service</label>
              <div className="space-y-1">
                <div className="text-gray-900 font-medium">
                  • {booking.service_name || 'Service details not available'}
                </div>
                {booking.service_description && (
                  <div className="text-sm text-gray-600 ml-4">
                    {booking.service_description}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Flight Date
              </label>
              <div className="text-gray-900 font-medium">{formatDate(booking.flight_date)}</div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                Estimated Arrival
              </label>
              <div className="text-gray-900 font-medium">
                {booking.estimated_arrival ? formatDate(booking.estimated_arrival) : 'Not specified'}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Users className="h-4 w-4 mr-1" />
                Passengers
              </label>
              <div className="text-gray-900 font-medium">
                {booking.passenger_count} {booking.passenger_count === 1 ? 'person' : 'people'}
              </div>
            </div>

            {booking.flight_number && (
              <div>
                <label className="text-sm font-medium text-gray-500 flex items-center">
                  <Plane className="h-4 w-4 mr-1" />
                  Flight Number
                </label>
                <div className="text-gray-900 font-medium">{booking.flight_number}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Customer Information */}
      {showCustomerInfo && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <User className="h-5 w-5 text-primary-600 mr-2" />
            Customer Information
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <div className="text-gray-900 font-medium">
                  {booking.customer_first_name} {booking.customer_last_name}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500 flex items-center">
                  <Mail className="h-4 w-4 mr-1" />
                  Email
                </label>
                <div className="text-gray-900 font-medium">{booking.customer_email}</div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500 flex items-center">
                  <Phone className="h-4 w-4 mr-1" />
                  Phone
                </label>
                <div className="text-gray-900 font-medium">{booking.customer_phone}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <CreditCard className="h-5 w-5 text-primary-600 mr-2" />
          Payment Information
        </h3>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Total Amount</label>
              <div className="text-2xl font-bold text-primary-600">
                {formatCurrency(parseFloat(booking.total_price), booking.currency)}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Payment Status</label>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                booking.payment_status === 'completed' ? 'bg-green-100 text-green-800' :
                booking.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {booking.payment_status ? booking.payment_status.charAt(0).toUpperCase() + booking.payment_status.slice(1) : 'Unknown'}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Booking Reference</label>
              <div className="text-gray-900 font-mono font-medium">{booking.booking_reference}</div>
            </div>

            {booking.payment_reference && (
              <div>
                <label className="text-sm font-medium text-gray-500">Payment ID</label>
                <div className="text-gray-900 font-mono text-sm">{booking.payment_reference}</div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-500">Booking Date</label>
              <div className="text-gray-900 font-medium">
                {new Date(booking.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Important Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Important Information</h3>
        <div className="space-y-2 text-blue-800">
          <p>• Please arrive at the designated meeting point 15 minutes before your scheduled time</p>
          <p>• Keep your booking reference handy for easy identification</p>
          <p>• Our representative will be holding a sign with your name</p>
          <p>• For any changes or cancellations, contact us at least 24 hours in advance</p>
          <p>• Emergency contact: +****************</p>
        </div>
      </div>
    </div>
  )
}
