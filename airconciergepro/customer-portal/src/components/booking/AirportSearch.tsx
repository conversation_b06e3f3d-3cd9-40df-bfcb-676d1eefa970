'use client'

import { useState, useEffect } from 'react'
import { Search, MapPin, Plane } from 'lucide-react'
import { airportService, Airport } from '@/lib/api'

interface AirportSearchProps {
  value: string
  onChange: (airport: string, airportId: string) => void
  onServiceTypeChange: (type: 'arrival' | 'departure') => void
  serviceType: 'arrival' | 'departure'
  passengers?: number
  category?: string
}



export default function AirportSearch({
  value,
  onChange,
  onServiceTypeChange,
  serviceType,
  passengers = 1,
  category
}: AirportSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredAirports, setFilteredAirports] = useState<Airport[]>([])
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedAirport, setSelectedAirport] = useState<Airport | null>(null)
  const [popularAirports, setPopularAirports] = useState<Airport[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Load available airports on component mount with filters
  useEffect(() => {
    const loadAvailableAirports = async () => {
      try {
        const airports = await airportService.getAvailableAirports({
          type: serviceType,
          passengers,
          category
        })
        setPopularAirports(airports)
        setFilteredAirports(airports) // Show available airports initially
      } catch (error) {
        console.error('Failed to load available airports:', error)
      }
    }
    loadAvailableAirports()
  }, [serviceType, passengers, category])

  // Search airports when query changes
  useEffect(() => {
    const searchAirports = async () => {
      if (searchTerm.length < 2) {
        setFilteredAirports(popularAirports)
        setShowDropdown(false)
        return
      }

      setIsLoading(true)
      setShowDropdown(true)
      try {
        const results = await airportService.searchAirports(searchTerm, {
          type: serviceType,
          passengers,
          category
        })
        setFilteredAirports(results)
      } catch (error) {
        console.error('Airport search failed:', error)
        setFilteredAirports([])
      } finally {
        setIsLoading(false)
      }
    }

    const debounceTimer = setTimeout(searchAirports, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchTerm, popularAirports, serviceType, passengers, category])

  const handleAirportSelect = (airport: Airport) => {
    setSelectedAirport(airport)
    const airportCode = airport.iata_code || airport.code
    setSearchTerm(`${airportCode} - ${airport.name}`)
    // Pass the IATA code for API calls, not the UUID
    onChange(`${airportCode} - ${airport.name}`, airportCode)
    setShowDropdown(false)
  }

  return (
    <div className="space-y-6">
      {/* Service Type Selection - Mobile optimized */}
      <div className="flex justify-center">
        <div className="bg-gray-100 p-1 rounded-lg flex w-full max-w-sm">
          <button
            type="button"
            onClick={() => onServiceTypeChange('arrival')}
            className={`flex-1 px-4 lg:px-6 py-2 lg:py-3 rounded-md font-medium transition-all duration-200 text-sm lg:text-base touch-manipulation ${
              serviceType === 'arrival'
                ? 'bg-white text-primary-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800 active:text-gray-900'
            }`}
            style={{ minHeight: '44px' }} // Ensure minimum touch target
          >
            <Plane className="h-4 w-4 inline mr-2 transform rotate-45" />
            Arrival
          </button>
          <button
            type="button"
            onClick={() => onServiceTypeChange('departure')}
            className={`flex-1 px-4 lg:px-6 py-2 lg:py-3 rounded-md font-medium transition-all duration-200 text-sm lg:text-base touch-manipulation ${
              serviceType === 'departure'
                ? 'bg-white text-primary-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800 active:text-gray-900'
            }`}
            style={{ minHeight: '44px' }} // Ensure minimum touch target
          >
            <Plane className="h-4 w-4 inline mr-2 transform -rotate-45" />
            Departure
          </button>
        </div>
      </div>

      {/* Airport Search - Mobile optimized */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 lg:left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 lg:h-5 lg:w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for airport (e.g., JFK, London, Dubai)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 lg:pl-12 pr-4 py-3 lg:py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base lg:text-lg touch-manipulation"
            style={{ fontSize: '16px' }} // Prevent zoom on iOS
          />
        </div>

        {/* Loading State */}
        {showDropdown && isLoading && (
          <div className="airport-dropdown">
            <div className="px-4 py-6 text-center text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
              <div>Searching airports...</div>
            </div>
          </div>
        )}

        {/* Dropdown */}
        {showDropdown && !isLoading && filteredAirports.length > 0 && (
          <div className="airport-dropdown">
            {filteredAirports.map((airport) => (
              <div
                key={airport.code}
                onClick={() => handleAirportSelect(airport)}
                className="airport-option"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-primary-100 p-2 rounded-lg">
                    <MapPin className="h-4 w-4 text-primary-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {airport.code} - {airport.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {airport.city}, {airport.country}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No results */}
        {showDropdown && !isLoading && searchTerm.length >= 2 && filteredAirports.length === 0 && (
          <div className="airport-dropdown">
            <div className="px-4 py-6 text-center text-gray-500">
              <MapPin className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <div>No airports found</div>
              <div className="text-sm">Try searching with airport code, city, or airport name</div>
            </div>
          </div>
        )}
      </div>

      {/* Popular Airports - Mobile optimized */}
      {!showDropdown && searchTerm.length < 2 && (
        <div>
          <h4 className="text-base lg:text-lg font-semibold text-gray-900 mb-3 lg:mb-4">Popular Airports</h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 lg:gap-3">
            {popularAirports.slice(0, 6).map((airport) => {
              const airportCode = airport.iata_code || airport.code
              return (
                <button
                  key={airportCode}
                  onClick={() => handleAirportSelect(airport)}
                  className="text-left p-3 lg:p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 touch-manipulation active:border-primary-300 active:bg-primary-50"
                  style={{ minHeight: '44px' }} // Ensure minimum touch target
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-primary-100 p-1.5 lg:p-2 rounded-lg">
                        <MapPin className="h-3 w-3 lg:h-4 lg:w-4 text-primary-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 text-sm lg:text-base">{airportCode}</div>
                        <div className="text-xs lg:text-sm text-gray-600">{airport.city}</div>
                      </div>
                    </div>
                    {airport.available_services && (
                      <div className="text-right">
                        <div className="text-xs text-gray-500">{airport.available_services} services</div>
                        {airport.starting_price && (
                          <div className="text-xs font-medium text-primary-600">
                            from {airport.currency} {airport.starting_price}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </button>
              )
            })}
          </div>
        </div>
      )}

      {/* Selected Airport Info */}
      {selectedAirport && (
        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="bg-primary-600 p-2 rounded-lg">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div>
              <div className="font-semibold text-primary-900">
                Selected Airport: {selectedAirport.code}
              </div>
              <div className="text-sm text-primary-700">
                {selectedAirport.name}, {selectedAirport.city}
              </div>
              <div className="text-sm text-primary-600 mt-1">
                Service Type: {serviceType === 'arrival' ? 'Arrival Assistance' : 'Departure Assistance'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
