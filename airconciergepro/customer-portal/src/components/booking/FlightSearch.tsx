'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Plane, MapPin, Clock, Calendar, ArrowRight } from 'lucide-react'
import { Airport, Flight } from '@/lib/aviationstack'

interface FlightSearchProps {
  onFlightSelect?: (flight: Flight) => void
  onAirportSelect?: (airport: Airport, type: 'departure' | 'arrival') => void
  selectedDeparture?: Airport
  selectedArrival?: Airport
}

export default function FlightSearch({ 
  onFlightSelect, 
  onAirportSelect,
  selectedDeparture,
  selectedArrival 
}: FlightSearchProps) {
  const [searchType, setSearchType] = useState<'airport' | 'flight'>('airport')
  const [airportQuery, setAirportQuery] = useState('')
  const [flightQuery, setFlightQuery] = useState('')
  const [searchingFor, setSearchingFor] = useState<'departure' | 'arrival'>('departure')
  const [airports, setAirports] = useState<Airport[]>([])
  const [flights, setFlights] = useState<Flight[]>([])
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [flightFilters, setFlightFilters] = useState({
    status: '',
    airline: '',
    date: ''
  })
  const searchRef = useRef<HTMLDivElement>(null)

  // Load popular airports on mount
  useEffect(() => {
    // Only load if we're in airport search mode
    if (searchType === 'airport') {
      loadPopularAirports()
    }
  }, [searchType])

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const loadPopularAirports = async () => {
    try {
      const response = await fetch('/api/airports/popular')
      const data = await response.json()
      if (data.success) {
        setAirports(data.data)
      } else {
        setAirports([])
        console.log('Popular airports service unavailable')
      }
    } catch (error) {
      console.error('Failed to load popular airports:', error)
      setAirports([])
    }
  }

  const searchAirports = async (query: string) => {
    if (!query.trim()) {
      loadPopularAirports()
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/airports/search?q=${encodeURIComponent(query)}&limit=10`)
      const data = await response.json()
      if (data.success) {
        setAirports(data.data)
      } else {
        setAirports([])
        console.log('Airport search service unavailable:', data.error)
      }
    } catch (error) {
      console.error('Airport search failed:', error)
      setAirports([])
    } finally {
      setLoading(false)
    }
  }

  const searchFlights = async (query: string) => {
    if (!query.trim()) return

    setLoading(true)
    try {
      // Build query parameters with filters
      const params = new URLSearchParams({
        flight_number: query
      })

      if (selectedDeparture) params.append('departure', selectedDeparture.iata_code)
      if (selectedArrival) params.append('arrival', selectedArrival.iata_code)
      if (flightFilters.status) params.append('status', flightFilters.status)
      if (flightFilters.airline) params.append('airline', flightFilters.airline)
      if (flightFilters.date) params.append('date', flightFilters.date)

      const response = await fetch(`/api/flights/search?${params.toString()}`)
      const data = await response.json()
      if (data.success) {
        setFlights(data.data)
      } else {
        setFlights([])
        console.log('Flight search service unavailable:', data.error)
      }
    } catch (error) {
      console.error('Flight search failed:', error)
      setFlights([])
    } finally {
      setLoading(false)
    }
  }

  const searchFlightTimetable = async (iataCode: string, type: 'departure' | 'arrival') => {
    setLoading(true)
    setShowResults(true)
    try {
      // Build query parameters with filters
      const params = new URLSearchParams({
        iataCode,
        type,
        limit: '20'
      })

      if (flightFilters.status) params.append('status', flightFilters.status)
      if (flightFilters.airline) params.append('airline', flightFilters.airline)
      if (flightFilters.date) params.append('date', flightFilters.date)

      const response = await fetch(`/api/flights/timetable?${params.toString()}`)
      const data = await response.json()
      if (data.success) {
        setFlights(data.data)
        setSearchType('flight') // Switch to flight view to show results
      } else {
        setFlights([])
        console.log('Flight timetable service unavailable:', data.error)
      }
    } catch (error) {
      console.error('Flight timetable search failed:', error)
      setFlights([])
    } finally {
      setLoading(false)
    }
  }

  const handleAirportSearch = (query: string) => {
    setAirportQuery(query)
    setShowResults(true)
    searchAirports(query)
  }

  const handleFlightSearch = (query: string) => {
    setFlightQuery(query)
    setShowResults(true)
    searchFlights(query)
  }

  const handleAirportSelect = (airport: Airport) => {
    onAirportSelect?.(airport, searchingFor)
    setShowResults(false)
    setAirportQuery('')
  }

  const handleFlightSelect = (flight: Flight) => {
    onFlightSelect?.(flight)
    setShowResults(false)
    setFlightQuery('')
  }

  return (
    <div ref={searchRef} className="relative">
      {/* Search Type Toggle */}
      <div className="flex bg-gray-100 rounded-lg p-1 mb-4">
        <button
          onClick={() => setSearchType('airport')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
            searchType === 'airport'
              ? 'bg-white text-primary-500 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <MapPin className="h-4 w-4 inline mr-2" />
          Search Airports
        </button>
        <button
          onClick={() => setSearchType('flight')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
            searchType === 'flight'
              ? 'bg-white text-primary-500 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Plane className="h-4 w-4 inline mr-2" />
          Search Flights
        </button>
      </div>

      {/* Airport Search */}
      {searchType === 'airport' && (
        <div className="space-y-4">
          {/* Departure/Arrival Toggle */}
          <div className="flex space-x-2">
            <button
              onClick={() => setSearchingFor('departure')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                searchingFor === 'departure'
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Departure
            </button>
            <button
              onClick={() => setSearchingFor('arrival')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                searchingFor === 'arrival'
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Arrival
            </button>
          </div>

          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={airportQuery}
              onChange={(e) => handleAirportSearch(e.target.value)}
              placeholder={`Search ${searchingFor} airport...`}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            {loading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-5 w-5 border-2 border-primary-500 border-t-transparent rounded-full" />
              </div>
            )}
          </div>

          {/* Flight Filters - Improved spacing and mobile layout */}
          <div className="mt-4 p-3 lg:p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3 text-sm lg:text-base">Flight Filters</h4>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-4">
              <div>
                <label className="block text-xs lg:text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={flightFilters.status}
                  onChange={(e) => setFlightFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 lg:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm touch-manipulation"
                  style={{ fontSize: '16px' }} // Prevent zoom on iOS
                >
                  <option value="">All Statuses</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="active">Active</option>
                  <option value="landed">Landed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="diverted">Diverted</option>
                </select>
              </div>

              <div>
                <label className="block text-xs lg:text-sm font-medium text-gray-700 mb-1">Airline (IATA)</label>
                <input
                  type="text"
                  value={flightFilters.airline}
                  onChange={(e) => setFlightFilters(prev => ({ ...prev, airline: e.target.value }))}
                  placeholder="e.g., EK, AI, SQ"
                  className="w-full px-3 py-2 lg:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm touch-manipulation"
                  style={{ fontSize: '16px' }} // Prevent zoom on iOS
                />
              </div>

              <div>
                <label className="block text-xs lg:text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  value={flightFilters.date}
                  onChange={(e) => setFlightFilters(prev => ({ ...prev, date: e.target.value }))}
                  className="w-full px-3 py-2 lg:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm touch-manipulation"
                  style={{ fontSize: '16px' }} // Prevent zoom on iOS
                />
              </div>
            </div>
          </div>

          {/* Quick Airport Timetable Button */}
          {selectedDeparture && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">View Flight Timetable</h4>
              <div className="flex space-x-2">
                <button
                  onClick={() => searchFlightTimetable(selectedDeparture.iata_code, 'departure')}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm"
                >
                  Departures from {selectedDeparture.iata_code}
                </button>
                <button
                  onClick={() => searchFlightTimetable(selectedDeparture.iata_code, 'arrival')}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
                >
                  Arrivals to {selectedDeparture.iata_code}
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Flight Search */}
      {searchType === 'flight' && (
        <div className="relative">
          <Plane className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            value={flightQuery}
            onChange={(e) => handleFlightSearch(e.target.value)}
            placeholder="Enter flight number (e.g., EK512, AI101)"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          {loading && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin h-5 w-5 border-2 border-primary-500 border-t-transparent rounded-full" />
            </div>
          )}
        </div>
      )}

      {/* Search Results */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
          >
            {searchType === 'airport' && (
              <div className="p-2">
                {airports.length > 0 ? (
                  airports.map((airport) => (
                    <button
                      key={airport.iata_code}
                      onClick={() => handleAirportSelect(airport)}
                      className="w-full text-left p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">
                            {airport.airport_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {airport.city_name}, {airport.country_name}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-mono text-sm font-bold text-primary-500">
                            {airport.iata_code}
                          </div>
                          <div className="text-xs text-gray-400">
                            {airport.icao_code}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {loading
                      ? 'Searching airports...'
                      : airportQuery.trim()
                        ? 'No airports found. Airport search service may be temporarily unavailable.'
                        : 'Airport data service temporarily unavailable. Please try again later.'
                    }
                  </div>
                )}
              </div>
            )}

            {searchType === 'flight' && (
              <div className="p-2">
                {flights.length > 0 ? (
                  flights.map((flight, index) => (
                    <button
                      key={index}
                      onClick={() => handleFlightSelect(flight)}
                      className="w-full text-left p-4 hover:bg-gray-50 rounded-lg transition-colors duration-200 border border-gray-100"
                    >
                      <div className="space-y-3">
                        {/* Flight Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                              <Plane className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {flight.airline?.name || 'Unknown Airline'} {flight.flight?.number || flight.flight?.iataNumber}
                              </div>
                              <div className="text-sm text-gray-500">
                                {flight.flight?.iataNumber || flight.flight?.icaoNumber}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm font-medium px-2 py-1 rounded-full ${
                              flight.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                              flight.status === 'active' ? 'bg-green-100 text-green-800' :
                              flight.status === 'landed' ? 'bg-gray-100 text-gray-800' :
                              flight.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {flight.status}
                            </div>
                          </div>
                        </div>

                        {/* Route Information */}
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="text-sm text-gray-500">From</div>
                            <div className="font-medium">{flight.departure?.iataCode || 'N/A'}</div>
                            <div className="text-xs text-gray-400">
                              {flight.departure?.terminal && `Terminal ${flight.departure.terminal}`}
                              {flight.departure?.gate && ` • Gate ${flight.departure.gate}`}
                            </div>
                          </div>

                          <div className="flex-shrink-0 mx-4">
                            <ArrowRight className="h-5 w-5 text-gray-400" />
                          </div>

                          <div className="flex-1 text-right">
                            <div className="text-sm text-gray-500">To</div>
                            <div className="font-medium">{flight.arrival?.iataCode || 'N/A'}</div>
                            <div className="text-xs text-gray-400">
                              {flight.arrival?.terminal && `Terminal ${flight.arrival.terminal}`}
                              {flight.arrival?.gate && ` • Gate ${flight.arrival.gate}`}
                            </div>
                          </div>
                        </div>

                        {/* Time Information */}
                        <div className="flex items-center justify-between text-sm">
                          <div>
                            <div className="text-gray-500">Departure</div>
                            <div className="font-medium">
                              {flight.departure?.scheduledTime ?
                                new Date(flight.departure.scheduledTime).toLocaleTimeString('en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                }) : 'N/A'
                              }
                            </div>
                            {flight.departure?.delay && parseInt(flight.departure.delay) > 0 && (
                              <div className="text-red-500 text-xs">
                                Delayed {flight.departure.delay}min
                              </div>
                            )}
                          </div>

                          <div className="text-right">
                            <div className="text-gray-500">Arrival</div>
                            <div className="font-medium">
                              {flight.arrival?.scheduledTime ?
                                new Date(flight.arrival.scheduledTime).toLocaleTimeString('en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                }) : 'N/A'
                              }
                            </div>
                            {flight.arrival?.delay && parseInt(flight.arrival.delay) > 0 && (
                              <div className="text-red-500 text-xs">
                                Delayed {flight.arrival.delay}min
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Date */}
                        <div className="text-xs text-gray-400 border-t pt-2">
                          {flight.departure?.scheduledTime ?
                            new Date(flight.departure.scheduledTime).toLocaleDateString('en-US') :
                            'Date not available'
                          }
                        </div>
                      </div>
                    </button>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {loading
                      ? 'Searching flights...'
                      : 'No flights found. Flight search service may be temporarily unavailable.'
                    }
                  </div>
                )}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected Airports Display */}
      {(selectedDeparture || selectedArrival) && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Selected Route</h4>
          <div className="flex items-center space-x-4">
            {selectedDeparture && (
              <div className="flex-1">
                <div className="text-sm text-gray-500">From</div>
                <div className="font-medium">{selectedDeparture.iata_code}</div>
                <div className="text-sm text-gray-600">{selectedDeparture.city_name}</div>
              </div>
            )}
            {selectedDeparture && selectedArrival && (
              <Plane className="h-5 w-5 text-gray-400" />
            )}
            {selectedArrival && (
              <div className="flex-1">
                <div className="text-sm text-gray-500">To</div>
                <div className="font-medium">{selectedArrival.iata_code}</div>
                <div className="text-sm text-gray-600">{selectedArrival.city_name}</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
