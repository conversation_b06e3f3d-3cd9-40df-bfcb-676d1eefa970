'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Clock, Users, AlertTriangle } from 'lucide-react'
import { realTimeAvailabilityService, AvailabilityResult, formatAvailabilityStatus } from '@/lib/realtime-availability'

interface AvailabilityIndicatorProps {
  airportId: string
  serviceIds: string[]
  date: string
  time?: string
  passengerCount: number
  className?: string
}

export default function AvailabilityIndicator({
  airportId,
  serviceIds,
  date,
  time,
  passengerCount,
  className = ''
}: AvailabilityIndicatorProps) {
  const [availability, setAvailability] = useState<AvailabilityResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!airportId || serviceIds.length === 0 || !date) {
      setAvailability(null)
      return
    }

    const checkAvailability = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const context = {
          airportId,
          serviceIds,
          date,
          passengerCount
        }

        const result = await realTimeAvailabilityService.checkAvailability(context)
        setAvailability(result)

        // Subscribe to real-time updates
        const unsubscribe = realTimeAvailabilityService.subscribeToAvailabilityUpdates(
          context,
          (updatedResult) => {
            setAvailability(updatedResult)
          }
        )

        // Cleanup subscription on unmount or dependency change
        return () => {
          unsubscribe()
        }
      } catch (error) {
        console.error('Failed to check availability:', error)
        setError('Failed to check availability')
      } finally {
        setIsLoading(false)
      }
    }

    checkAvailability()
  }, [airportId, serviceIds, date, passengerCount])

  if (!airportId || serviceIds.length === 0 || !date) {
    return null
  }

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-600 border-t-transparent"></div>
        <span className="text-sm text-gray-600">Checking availability...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-yellow-500" />
        <span className="text-sm text-yellow-600">Unable to check availability</span>
      </div>
    )
  }

  if (!availability) {
    return null
  }

  const getAvailabilityIcon = () => {
    if (availability.available) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getAvailabilityColor = () => {
    if (availability.available) {
      if (availability.availableCapacity > availability.totalCapacity * 0.5) {
        return 'text-green-600'
      } else if (availability.availableCapacity > availability.totalCapacity * 0.2) {
        return 'text-yellow-600'
      } else {
        return 'text-orange-600'
      }
    }
    return 'text-red-600'
  }

  const getAvailabilityMessage = () => {
    if (availability.available) {
      const percentage = (availability.availableCapacity / availability.totalCapacity) * 100
      if (percentage > 50) {
        return 'Good availability'
      } else if (percentage > 20) {
        return 'Limited availability'
      } else {
        return 'Very limited availability'
      }
    } else {
      return 'No availability'
    }
  }

  // Check specific time slot availability if time is provided
  const timeSlotAvailable = time ? availability.timeSlots.find(slot => slot.time === time)?.available : null

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Overall availability */}
      <div className="flex items-center space-x-2">
        {getAvailabilityIcon()}
        <span className={`text-sm font-medium ${getAvailabilityColor()}`}>
          {getAvailabilityMessage()}
        </span>
        <span className="text-xs text-gray-500">
          ({availability.availableCapacity} of {availability.totalCapacity} slots)
        </span>
      </div>

      {/* Specific time slot availability */}
      {time && timeSlotAvailable !== null && (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-gray-400" />
          <span className={`text-sm ${timeSlotAvailable ? 'text-green-600' : 'text-red-600'}`}>
            {time}: {timeSlotAvailable ? 'Available' : 'Not available'}
          </span>
        </div>
      )}

      {/* Passenger count check */}
      {availability.available && (
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-600">
            For {passengerCount} passenger{passengerCount > 1 ? 's' : ''}
          </span>
        </div>
      )}

      {/* Next available option */}
      {!availability.available && (availability.nextAvailableDate || availability.nextAvailableTime) && (
        <div className="text-sm text-gray-600">
          Next available: {availability.nextAvailableDate} {availability.nextAvailableTime}
        </div>
      )}

      {/* Restrictions */}
      {availability.restrictions && availability.restrictions.length > 0 && (
        <div className="text-xs text-gray-500">
          <div className="font-medium">Restrictions:</div>
          <ul className="list-disc list-inside">
            {availability.restrictions.map((restriction, index) => (
              <li key={index}>{restriction}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Last updated */}
      <div className="text-xs text-gray-400">
        Updated: {new Date(availability.lastUpdated).toLocaleTimeString()}
      </div>
    </div>
  )
}

// Compact version for inline display
export function CompactAvailabilityIndicator({
  airportId,
  serviceIds,
  date,
  time,
  passengerCount,
  className = ''
}: AvailabilityIndicatorProps) {
  const [availability, setAvailability] = useState<AvailabilityResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (!airportId || serviceIds.length === 0 || !date) {
      setAvailability(null)
      return
    }

    const checkAvailability = async () => {
      setIsLoading(true)
      try {
        const context = { airportId, serviceIds, date, passengerCount }
        const result = await realTimeAvailabilityService.checkAvailability(context)
        setAvailability(result)

        // Subscribe to updates
        const unsubscribe = realTimeAvailabilityService.subscribeToAvailabilityUpdates(
          context,
          setAvailability
        )
        return () => unsubscribe()
      } catch (error) {
        console.error('Failed to check availability:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAvailability()
  }, [airportId, serviceIds, date, passengerCount])

  if (isLoading) {
    return (
      <div className={`inline-flex items-center space-x-1 ${className}`}>
        <div className="animate-spin rounded-full h-3 w-3 border border-primary-600 border-t-transparent"></div>
        <span className="text-xs text-gray-500">Checking...</span>
      </div>
    )
  }

  if (!availability) {
    return null
  }

  const timeSlotAvailable = time ? availability.timeSlots.find(slot => slot.time === time)?.available : null

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      {timeSlotAvailable !== null ? (
        timeSlotAvailable ? (
          <CheckCircle className="h-3 w-3 text-green-500" />
        ) : (
          <XCircle className="h-3 w-3 text-red-500" />
        )
      ) : availability.available ? (
        <CheckCircle className="h-3 w-3 text-green-500" />
      ) : (
        <XCircle className="h-3 w-3 text-red-500" />
      )}
      <span className={`text-xs ${
        (timeSlotAvailable !== null ? timeSlotAvailable : availability.available) 
          ? 'text-green-600' 
          : 'text-red-600'
      }`}>
        {(timeSlotAvailable !== null ? timeSlotAvailable : availability.available) 
          ? 'Available' 
          : 'Unavailable'}
      </span>
    </div>
  )
}
