'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  MapPin, 
  Calendar, 
  Clock, 
  Users, 
  Plane,
  ArrowRight,
  Star,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { apiClient } from '@/lib/api'

interface Service {
  id: string
  name: string
  description: string
  category: string
  type: string
  base_price: number
  currency: string
  duration_minutes: number
  max_passengers: number
  inclusions: string[]
  starting_price: number
}

interface Airport {
  id: string
  iata_code: string
  name: string
  city: string
  country: string
  available_services: number
  starting_price: number
  currency: string
}

export default function OptimizedBookingWidget() {
  const [airports, setAirports] = useState<Airport[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [selectedAirport, setSelectedAirport] = useState<Airport | null>(null)
  const [selectedServiceType, setSelectedServiceType] = useState<'arrival' | 'departure'>('arrival')
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [passengers, setPassengers] = useState(1)
  const [date, setDate] = useState('')
  const [time, setTime] = useState('')
  const [flightNumber, setFlightNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [servicesLoading, setServicesLoading] = useState(false)

  // Load airports on component mount
  useEffect(() => {
    loadAirports()
  }, [])

  // Load services when airport or service type changes
  useEffect(() => {
    if (selectedAirport) {
      loadServices()
    }
  }, [selectedAirport, selectedServiceType, passengers])

  const loadAirports = async () => {
    try {
      setIsLoading(true)
      const airportsData = await apiClient.getAvailableAirports()
      setAirports(airportsData)
    } catch (error) {
      console.error('Error loading airports:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadServices = async () => {
    if (!selectedAirport) return

    try {
      setServicesLoading(true)

      // Use the customer API endpoint for airport services
      const backendUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8000/api/customer/v1'
      const response = await fetch(`${backendUrl}/airports/${selectedAirport.iata_code}/services?service_type=${selectedServiceType}&passengers=${passengers}`, {
        headers: {
          'X-Tenant-ID': '37de875f-170d-47e5-8f38-8c36b2112475' // TODO: Get from environment
        }
      })

      if (response.ok) {
        const data = await response.json()
        setServices(data.data || [])
      } else {
        console.error('Error loading services:', response.statusText)
        setServices([])
      }
    } catch (error) {
      console.error('Error loading services:', error)
      setServices([])
    } finally {
      setServicesLoading(false)
    }
  }

  const handleAirportSelect = (airport: Airport) => {
    setSelectedAirport(airport)
    setSelectedService(null) // Reset service selection
  }

  const handleServiceTypeChange = (type: 'arrival' | 'departure') => {
    setSelectedServiceType(type)
    setSelectedService(null) // Reset service selection
  }

  const handleServiceSelect = (service: Service) => {
    setSelectedService(service)
  }

  const handleContinueToPayment = () => {
    if (!selectedAirport || !selectedService || !date || !time) {
      alert('Please fill in all required fields')
      return
    }

    // Navigate to payment with booking data
    const bookingData = {
      airport: `${selectedAirport.iata_code} - ${selectedAirport.name}`,
      airportId: selectedAirport.iata_code,
      service: selectedService.name,
      serviceId: selectedService.id,
      serviceType: selectedServiceType,
      date,
      time,
      passengers,
      flightNumber,
      totalAmount: selectedService.base_price * passengers,
      currency: selectedService.currency
    }

    // Store booking data and navigate to payment
    localStorage.setItem('bookingData', JSON.stringify(bookingData))
    window.location.href = '/book/payment'
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(price)
  }

  const getTotalPrice = () => {
    if (!selectedService) return 0
    return selectedService.base_price * passengers
  }

  return (
    <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-6">
        <h1 className="text-2xl font-bold text-white mb-2">Book Your Airport Service</h1>
        <p className="text-primary-100">Select your airport, service type, and preferred service in one simple step</p>
      </div>

      <div className="p-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Selection */}
          <div className="lg:col-span-2 space-y-6">
            {/* Airport Selection */}
            <div>
              <label className="block text-sm font-semibold text-gray-900 mb-3">
                <MapPin className="inline h-4 w-4 mr-2" />
                Select Airport
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {isLoading ? (
                  <div className="col-span-2 flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-primary-600" />
                    <span className="ml-2 text-gray-600">Loading airports...</span>
                  </div>
                ) : (
                  airports.map((airport) => (
                    <button
                      key={airport.id}
                      onClick={() => handleAirportSelect(airport)}
                      className={`p-4 border-2 rounded-lg text-left transition-all duration-200 hover:shadow-md ${
                        selectedAirport?.id === airport.id
                          ? 'border-primary-500 bg-primary-50 shadow-md'
                          : 'border-gray-200 hover:border-primary-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-semibold text-gray-900">{airport.iata_code}</div>
                        {selectedAirport?.id === airport.id && (
                          <CheckCircle className="h-5 w-5 text-primary-500" />
                        )}
                      </div>
                      <div className="text-sm text-gray-600">{airport.name}</div>
                      <div className="text-sm text-gray-500">{airport.city}, {airport.country}</div>
                      <div className="text-xs text-primary-600 mt-1">
                        {airport.available_services} services from {formatPrice(airport.starting_price, airport.currency)}
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>

            {/* Service Type Selection */}
            {selectedAirport && (
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  <Plane className="inline h-4 w-4 mr-2" />
                  Service Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => handleServiceTypeChange('arrival')}
                    className={`p-4 border-2 rounded-lg text-center transition-all duration-200 hover:shadow-md ${
                      selectedServiceType === 'arrival'
                        ? 'border-primary-500 bg-primary-50 shadow-md'
                        : 'border-gray-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="flex items-center justify-center mb-2">
                      <Plane className="h-5 w-5 text-primary-500 transform rotate-180" />
                      {selectedServiceType === 'arrival' && (
                        <CheckCircle className="h-4 w-4 text-primary-500 ml-2" />
                      )}
                    </div>
                    <div className="font-semibold text-gray-900">Arrival</div>
                    <div className="text-sm text-gray-600">Landing at {selectedAirport.iata_code}</div>
                  </button>
                  <button
                    onClick={() => handleServiceTypeChange('departure')}
                    className={`p-4 border-2 rounded-lg text-center transition-all duration-200 hover:shadow-md ${
                      selectedServiceType === 'departure'
                        ? 'border-primary-500 bg-primary-50 shadow-md'
                        : 'border-gray-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="flex items-center justify-center mb-2">
                      <Plane className="h-5 w-5 text-primary-500" />
                      {selectedServiceType === 'departure' && (
                        <CheckCircle className="h-4 w-4 text-primary-500 ml-2" />
                      )}
                    </div>
                    <div className="font-semibold text-gray-900">Departure</div>
                    <div className="text-sm text-gray-600">Flying from {selectedAirport.iata_code}</div>
                  </button>
                </div>
              </div>
            )}

            {/* Passengers Selection */}
            {selectedAirport && (
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  <Users className="inline h-4 w-4 mr-2" />
                  Number of Passengers
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setPassengers(Math.max(1, passengers - 1))}
                    className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="text-lg font-semibold w-8 text-center">{passengers}</span>
                  <button
                    onClick={() => setPassengers(Math.min(10, passengers + 1))}
                    className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
                  >
                    +
                  </button>
                  <span className="text-sm text-gray-600 ml-2">
                    {passengers === 1 ? 'passenger' : 'passengers'}
                  </span>
                </div>
              </div>
            )}

            {/* Available Services */}
            {selectedAirport && (
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  <Star className="inline h-4 w-4 mr-2" />
                  Available Services
                </label>
                <div className="space-y-3">
                  {servicesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin text-primary-600" />
                      <span className="ml-2 text-gray-600">Loading services...</span>
                    </div>
                  ) : services.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No services available for the selected criteria
                    </div>
                  ) : (
                    services.map((service) => (
                      <button
                        key={service.id}
                        onClick={() => handleServiceSelect(service)}
                        className={`w-full p-4 border-2 rounded-lg text-left transition-all duration-200 hover:shadow-md ${
                          selectedService?.id === service.id
                            ? 'border-primary-500 bg-primary-50 shadow-md'
                            : 'border-gray-200 hover:border-primary-300'
                        }`}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <div className="font-semibold text-gray-900">{service.name}</div>
                              {selectedService?.id === service.id && (
                                <CheckCircle className="h-5 w-5 text-primary-500 ml-2" />
                              )}
                            </div>
                            <div className="text-sm text-gray-600 mt-1">{service.description}</div>
                            <div className="text-xs text-gray-500 mt-2">
                              Duration: {service.duration_minutes} minutes • Up to {service.max_passengers} passengers
                            </div>
                            {service.inclusions && service.inclusions.length > 0 && (
                              <div className="text-xs text-gray-500 mt-1">
                                Includes: {service.inclusions.slice(0, 2).join(', ')}
                                {service.inclusions.length > 2 && ` +${service.inclusions.length - 2} more`}
                              </div>
                            )}
                          </div>
                          <div className="text-right ml-4">
                            <div className="text-lg font-bold text-primary-600">
                              {formatPrice(service.base_price, service.currency)}
                            </div>
                            <div className="text-xs text-gray-500">per person</div>
                          </div>
                        </div>
                      </button>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Booking Details */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking Details</h3>
              
              {/* Date & Time */}
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="inline h-4 w-4 mr-1" />
                    Date
                  </label>
                  <input
                    type="date"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="inline h-4 w-4 mr-1" />
                    Time
                  </label>
                  <input
                    type="time"
                    value={time}
                    onChange={(e) => setTime(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Plane className="inline h-4 w-4 mr-1" />
                    Flight Number (Optional)
                  </label>
                  <input
                    type="text"
                    value={flightNumber}
                    onChange={(e) => setFlightNumber(e.target.value)}
                    placeholder="e.g., AI101"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              {/* Summary */}
              {selectedAirport && selectedService && (
                <div className="border-t border-gray-200 pt-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Airport:</span>
                      <span className="font-medium">{selectedAirport.iata_code}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service:</span>
                      <span className="font-medium">{selectedService.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span className="font-medium capitalize">{selectedServiceType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Passengers:</span>
                      <span className="font-medium">{passengers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Price per person:</span>
                      <span className="font-medium">{formatPrice(selectedService.base_price, selectedService.currency)}</span>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-200 mt-4 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">Total:</span>
                      <span className="text-2xl font-bold text-primary-600">
                        {formatPrice(getTotalPrice(), selectedService.currency)}
                      </span>
                    </div>
                  </div>

                  <button
                    onClick={handleContinueToPayment}
                    disabled={!selectedAirport || !selectedService || !date || !time}
                    className="w-full mt-6 bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center"
                  >
                    Continue to Payment
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
