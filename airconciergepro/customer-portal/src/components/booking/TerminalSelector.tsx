'use client'

import { useState, useEffect } from 'react'
import { Building2, MapPin, Users, DollarSign } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { apiClient } from '@/lib/api'

interface Terminal {
  terminal_code: string
  terminal_name: string
  facilities: any
  gates: any[]
  service_count: number
  min_price: number
  currency: string
}

interface TerminalSelectorProps {
  airportCode: string
  serviceType: 'arrival' | 'departure'
  selectedTerminal: string
  onTerminalSelect: (terminal: string) => void
  passengers?: number
}

export default function TerminalSelector({
  airportCode,
  serviceType,
  selectedTerminal,
  onTerminalSelect,
  passengers = 1
}: TerminalSelectorProps) {
  const [terminals, setTerminals] = useState<Terminal[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchTerminals = async () => {
      if (!airportCode) return

      setIsLoading(true)
      setError('')

      try {
        const terminalsData = await apiClient.getTerminals(airportCode, {
          type: serviceType,
          passengers: passengers
        })

        setTerminals(terminalsData)
      } catch (error) {
        console.error('Error fetching terminals:', error)
        setError('Unable to load terminals. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchTerminals()
  }, [airportCode, serviceType, passengers])

  const getTerminalTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'international':
        return 'bg-blue-100 text-blue-800'
      case 'domestic':
        return 'bg-green-100 text-green-800'
      case 'cargo':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTerminalIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'international':
      case 'domestic':
        return Building2
      case 'cargo':
        return MapPin
      default:
        return Building2
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading terminals...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="text-primary-600 hover:text-primary-700 font-medium"
        >
          Try Again
        </button>
      </div>
    )
  }

  if (terminals.length === 0) {
    return (
      <div className="text-center py-8">
        <Building2 className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No terminals available</h3>
        <p className="mt-1 text-sm text-gray-500">
          No terminals found for {serviceType} services at {airportCode}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h4 className="text-lg font-medium text-gray-900">
          Select Terminal at {airportCode}
        </h4>
        <p className="text-sm text-gray-600">
          Choose the terminal for your {serviceType} service
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <AnimatePresence>
          {terminals.map((terminal) => {
            const IconComponent = getTerminalIcon('international') // Default to international
            const isSelected = selectedTerminal === terminal.terminal_code

            return (
              <motion.button
                key={terminal.terminal_code}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onTerminalSelect(terminal.terminal_code)}
                className={`relative p-6 border-2 rounded-xl text-left transition-all duration-200 ${
                  isSelected
                    ? 'border-primary-500 bg-primary-50 shadow-lg'
                    : 'border-gray-200 hover:border-primary-300 hover:shadow-md'
                }`}
                style={{ minHeight: '160px' }}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-3 right-3 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </motion.div>
                )}

                {/* Terminal icon and info */}
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg ${isSelected ? 'bg-primary-100' : 'bg-gray-100'}`}>
                    <IconComponent className={`h-6 w-6 ${isSelected ? 'text-primary-600' : 'text-gray-600'}`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {terminal.terminal_name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {terminal.terminal_code}
                    </p>

                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Terminal
                    </span>
                  </div>
                </div>

                {/* Terminal stats */}
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {terminal.service_count} services
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      from {terminal.currency} {terminal.min_price}
                    </span>
                  </div>
                </div>

                {/* Hover effect overlay */}
                <div className={`absolute inset-0 rounded-xl transition-opacity duration-200 ${
                  isSelected ? 'opacity-0' : 'opacity-0 hover:opacity-5 bg-primary-500'
                }`} />
              </motion.button>
            )
          })}
        </AnimatePresence>
      </div>

      {/* Additional info */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <MapPin className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900">Terminal Information</h4>
            <p className="text-sm text-blue-700 mt-1">
              Select the terminal where your {serviceType === 'arrival' ? 'flight will arrive' : 'flight will depart'}. 
              Our service representatives will meet you at the appropriate location within the selected terminal.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
