'use client'

import { useState } from 'react'
import { X, Download, Printer, QrCode, MapPin, Calendar, Clock, Users, Plane } from 'lucide-react'

interface BookingData {
  id: string
  reference: string
  airport: string
  airportId: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
  flightNumber?: string
  customerName: string
  customerEmail: string
  customerPhone: string
  status: 'confirmed' | 'pending' | 'cancelled'
  createdAt: string
  paymentIntentId: string
}

interface DigitalVoucherProps {
  booking: BookingData
  onClose: () => void
}

export default function DigitalVoucher({ booking, onClose }: DigitalVoucherProps) {
  const [isDownloading, setIsDownloading] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const handleDownload = async () => {
    setIsDownloading(true)
    
    try {
      // Generate PDF voucher
      const response = await fetch('/api/generate-voucher', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ booking }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `voucher-${booking.booking_reference}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Failed to download voucher:', error)
      alert('Failed to download voucher. Please try again.')
    } finally {
      setIsDownloading(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Digital Voucher</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Voucher Content */}
        <div className="p-6" id="voucher-content">
          {/* Company Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-primary-600 mb-2">AirConcierge Pro</h1>
            <p className="text-gray-600">Premium Airport Meet & Greet Services</p>
            <div className="w-24 h-1 bg-primary-600 mx-auto mt-4"></div>
          </div>

          {/* Voucher Title */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Service Voucher</h2>
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full font-medium">
              ✓ Confirmed
            </div>
          </div>

          {/* QR Code Placeholder */}
          <div className="flex justify-center mb-8">
            <div className="w-32 h-32 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              <QrCode className="h-16 w-16 text-gray-400" />
            </div>
          </div>

          {/* Booking Reference */}
          <div className="text-center mb-8">
            <div className="text-sm text-gray-500 mb-1">Booking Reference</div>
            <div className="text-2xl font-mono font-bold text-gray-900 tracking-wider">
              {booking.booking_reference}
            </div>
          </div>

          {/* Service Details */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary-600 mt-0.5" />
                <div>
                  <div className="text-sm text-gray-500">Airport</div>
                  <div className="font-medium text-gray-900">
                    {booking.service_type === 'arrival'
                      ? `${booking.arrival_airport} (from ${booking.departure_airport})`
                      : `${booking.departure_airport} (to ${booking.arrival_airport})`
                    }
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-primary-600 mt-0.5" />
                <div>
                  <div className="text-sm text-gray-500">Flight Date</div>
                  <div className="font-medium text-gray-900">{formatDate(booking.flight_date)}</div>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-primary-600 mt-0.5" />
                <div>
                  <div className="text-sm text-gray-500">Estimated Arrival</div>
                  <div className="font-medium text-gray-900">
                    {booking.estimated_arrival ? formatDate(booking.estimated_arrival) : 'Not specified'}
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-primary-600 mt-0.5" />
                <div>
                  <div className="text-sm text-gray-500">Passengers</div>
                  <div className="font-medium text-gray-900">
                    {booking.passenger_count} {booking.passenger_count === 1 ? 'person' : 'people'}
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Service Type</div>
                  <div className="font-medium text-gray-900 capitalize">
                    {booking.service_type} Assistance
                  </div>
                </div>
              </div>

              {booking.flight_number && (
                <div className="flex items-start space-x-3">
                  <Plane className="h-5 w-5 text-primary-600 mt-0.5" />
                  <div>
                    <div className="text-sm text-gray-500">Flight Number</div>
                    <div className="font-medium text-gray-900">{booking.flight_number}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Services */}
          <div className="mb-8">
            <h3 className="font-semibold text-gray-900 mb-3">Included Services</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                <span className="text-gray-700">{booking.service_name || 'Service details not available'}</span>
              </div>
              {booking.service_description && (
                <div className="ml-4 text-sm text-gray-600">
                  {booking.service_description}
                </div>
              )}
            </div>
          </div>

          {/* Customer Information */}
          <div className="mb-8">
            <h3 className="font-semibold text-gray-900 mb-3">Customer Information</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Name</div>
                  <div className="font-medium text-gray-900">
                    {booking.customer_first_name} {booking.customer_last_name}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Phone</div>
                  <div className="font-medium text-gray-900">{booking.customer_phone}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Total Amount */}
          <div className="text-center mb-8">
            <div className="text-sm text-gray-500 mb-1">Total Amount Paid</div>
            <div className="text-3xl font-bold text-primary-600">
              {formatCurrency(parseFloat(booking.total_price), booking.currency)}
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <h4 className="font-semibold text-blue-900 mb-2">Important Instructions</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Present this voucher to our representative at the airport</li>
              <li>• Arrive at the meeting point 15 minutes before scheduled time</li>
              <li>• Our representative will be holding a sign with your name</li>
              <li>• Keep your booking reference handy for identification</li>
            </ul>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-500">
            <p>AirConcierge Pro | Premium Airport Services</p>
            <p>24/7 Support: +1 (555) 123-4567 | <EMAIL></p>
            <p className="mt-2">Generated on {new Date().toLocaleDateString()}</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-center space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className="flex items-center space-x-2 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            <Download className="h-5 w-5" />
            <span>{isDownloading ? 'Downloading...' : 'Download PDF'}</span>
          </button>
          
          <button
            onClick={handlePrint}
            className="flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Printer className="h-5 w-5" />
            <span>Print</span>
          </button>
        </div>
      </div>
    </div>
  )
}
