'use client'

import { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, TrendingUp, TrendingDown } from 'lucide-react'
import { wsService, WebSocketMessage } from '@/lib/websocket'
import { formatPrice } from '@/lib/realtime-pricing'

interface Notification {
  id: string
  type: 'success' | 'warning' | 'info' | 'error'
  title: string
  message: string
  timestamp: Date
  autoClose?: boolean
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface RealTimeNotificationsProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  maxNotifications?: number
}

export default function RealTimeNotifications({
  position = 'top-right',
  maxNotifications = 5
}: RealTimeNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])

  useEffect(() => {
    // Subscribe to WebSocket messages for real-time notifications
    const unsubscribeAvailability = wsService.subscribe('availability_update', (data) => {
      addNotification({
        type: 'info',
        title: 'Availability Updated',
        message: `${data.availableSlots} slots now available for your selected service`,
        autoClose: true,
        duration: 5000
      })
    })

    const unsubscribePrice = wsService.subscribe('price_update', (data) => {
      const priceChange = data.dynamicPrice - data.basePrice
      const isIncrease = priceChange > 0
      
      addNotification({
        type: isIncrease ? 'warning' : 'success',
        title: 'Price Updated',
        message: `Price ${isIncrease ? 'increased' : 'decreased'} by ${formatPrice(Math.abs(priceChange), data.currency)}`,
        autoClose: true,
        duration: 7000
      })
    })

    const unsubscribeBooking = wsService.subscribe('booking_update', (data) => {
      let type: 'success' | 'warning' | 'info' | 'error' = 'info'
      
      switch (data.status) {
        case 'confirmed':
          type = 'success'
          break
        case 'cancelled':
          type = 'error'
          break
        case 'modified':
          type = 'warning'
          break
        default:
          type = 'info'
      }

      addNotification({
        type,
        title: 'Booking Update',
        message: data.message,
        autoClose: type !== 'error',
        duration: type === 'success' ? 5000 : 10000
      })
    })

    const unsubscribeSystem = wsService.subscribe('system_notification', (data) => {
      addNotification({
        type: data.level || 'info',
        title: data.title || 'System Notification',
        message: data.message,
        autoClose: data.autoClose !== false,
        duration: data.duration || 8000
      })
    })

    // Cleanup subscriptions
    return () => {
      unsubscribeAvailability()
      unsubscribePrice()
      unsubscribeBooking()
      unsubscribeSystem()
    }
  }, [])

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    }

    setNotifications(prev => {
      const updated = [newNotification, ...prev].slice(0, maxNotifications)
      return updated
    })

    // Auto-close notification if specified
    if (notification.autoClose) {
      setTimeout(() => {
        removeNotification(newNotification.id)
      }, notification.duration || 5000)
    }
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getNotificationColors = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      default:
        return 'top-4 right-4'
    }
  }

  if (notifications.length === 0) {
    return null
  }

  return (
    <div className={`fixed ${getPositionClasses()} z-50 space-y-3 max-w-sm w-full`}>
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`
            ${getNotificationColors(notification.type)}
            border rounded-lg p-4 shadow-lg
            transform transition-all duration-300 ease-in-out
            animate-in slide-in-from-right-full
          `}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {getNotificationIcon(notification.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold">
                  {notification.title}
                </h4>
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <p className="text-sm mt-1 opacity-90">
                {notification.message}
              </p>
              
              {notification.action && (
                <button
                  onClick={notification.action.onClick}
                  className="text-sm font-medium underline mt-2 hover:no-underline transition-all"
                >
                  {notification.action.label}
                </button>
              )}
              
              <div className="text-xs opacity-70 mt-2">
                {notification.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Hook for programmatically adding notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    }

    setNotifications(prev => [newNotification, ...prev])

    if (notification.autoClose) {
      setTimeout(() => {
        removeNotification(newNotification.id)
      }, notification.duration || 5000)
    }

    return newNotification.id
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications
  }
}

// Specific notification functions for common use cases
export const notificationHelpers = {
  priceIncrease: (amount: number, currency: string = 'USD') => ({
    type: 'warning' as const,
    title: 'Price Increase',
    message: `Service price increased by ${formatPrice(amount, currency)}`,
    autoClose: true,
    duration: 7000
  }),

  priceDecrease: (amount: number, currency: string = 'USD') => ({
    type: 'success' as const,
    title: 'Price Drop',
    message: `Service price decreased by ${formatPrice(amount, currency)}`,
    autoClose: true,
    duration: 7000
  }),

  availabilityChange: (available: boolean, slots?: number) => ({
    type: available ? 'success' as const : 'warning' as const,
    title: 'Availability Update',
    message: available 
      ? `${slots ? `${slots} slots` : 'Service'} now available`
      : 'Service no longer available for selected time',
    autoClose: true,
    duration: 6000
  }),

  bookingConfirmed: (reference: string) => ({
    type: 'success' as const,
    title: 'Booking Confirmed',
    message: `Your booking ${reference} has been confirmed`,
    autoClose: true,
    duration: 8000
  }),

  connectionStatus: (connected: boolean) => ({
    type: connected ? 'success' as const : 'warning' as const,
    title: connected ? 'Connected' : 'Connection Lost',
    message: connected 
      ? 'Real-time updates are active'
      : 'Real-time updates temporarily unavailable',
    autoClose: connected,
    duration: connected ? 3000 : undefined
  })
}
