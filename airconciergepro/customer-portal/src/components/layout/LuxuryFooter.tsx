'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  Plane, 
  Phone, 
  Mail, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Building,
  Star,
  Award,
  Shield,
  Clock,
  ArrowUp,
  CreditCard,
  Globe
} from 'lucide-react'
import { useTenantConfig, detectTenant } from '@/lib/branding'

export default function LuxuryFooter() {
  const [tenantConfig, setTenantConfig] = useState(useTenantConfig())
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const tenantId = detectTenant()
    if (tenantId) {
      setTenantConfig(useTenantConfig(tenantId))
    }

    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const footerLinks = {
    services: tenantConfig.type === 'B2B' 
      ? [
          { name: 'Enterprise Solutions', href: '/enterprise' },
          { name: 'Corporate Travel', href: '/corporate' },
          { name: 'Bulk Booking', href: '/bulk-booking' },
          { name: 'Account Management', href: '/account-management' },
          { name: 'Custom Reporting', href: '/reporting' },
        ]
      : [
          { name: 'Meet & Greet', href: '/services/meet-greet' },
          { name: 'VIP Lounge Access', href: '/services/vip-lounge' },
          { name: 'Fast Track Security', href: '/services/fast-track' },
          { name: 'Airport Transfer', href: '/services/transfer' },
          { name: 'Special Assistance', href: '/services/special-assistance' },
        ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Our Team', href: '/team' },
      { name: 'Careers', href: '/careers' },
      { name: 'Press', href: '/press' },
      { name: 'Partners', href: '/partners' },
    ],
    support: [
      { name: 'Help Center', href: '/help' },
      { name: 'Contact Us', href: '/contact' },
      { name: 'Booking Support', href: '/support' },
      { name: 'Cancellation Policy', href: '/cancellation' },
      { name: 'Terms of Service', href: '/terms' },
    ],
  }

  return (
    <>
      <footer className="bg-primary-500 text-white relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-gold-500 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-primary-500 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-3xl" />
        </div>

        <div className="container-max px-4 sm:px-6 lg:px-8 py-20 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            {/* Luxury Company Info */}
            <motion.div 
              className="space-y-6 lg:col-span-1"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-4">
                <motion.div
                  className="bg-gold-500 p-3 rounded-xl shadow-gold"
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {tenantConfig.type === 'B2B' ? (
                    <Building className="h-7 w-7 text-white" />
                  ) : (
                    <Plane className="h-7 w-7 text-white" />
                  )}
                </motion.div>
                <div>
                  <div className="text-2xl font-bold font-display">{tenantConfig.name.split(' ')[0]}</div>
                  <div className="text-gold-400 font-semibold">{tenantConfig.name.split(' ').slice(1).join(' ')}</div>
                </div>
              </div>
              
              <p className="text-gray-300 leading-relaxed">
                {tenantConfig.type === 'B2B' 
                  ? 'Enterprise travel solutions providing seamless corporate travel management across global destinations.'
                  : 'Premium airport concierge services providing seamless travel experiences across 150+ airports worldwide.'
                }
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <motion.div 
                  className="flex items-center space-x-3 text-gray-300"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Phone className="h-5 w-5 text-gold-400" />
                  <span>{tenantConfig.contact.phone}</span>
                </motion.div>
                <motion.div 
                  className="flex items-center space-x-3 text-gray-300"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Mail className="h-5 w-5 text-gold-400" />
                  <span>{tenantConfig.contact.email}</span>
                </motion.div>
                <motion.div 
                  className="flex items-center space-x-3 text-gray-300"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Clock className="h-5 w-5 text-gold-400" />
                  <span>{tenantConfig.contact.supportHours}</span>
                </motion.div>
              </div>

              {/* Social Links */}
              <div className="flex space-x-4">
                {[Facebook, Twitter, Instagram, Linkedin].map((Icon, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.2, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Link 
                      href="#" 
                      className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-300 hover:text-gold-400 hover:bg-white/20 transition-all duration-300"
                    >
                      <Icon className="h-5 w-5" />
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Services Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold font-display mb-6 text-gold-400">
                {tenantConfig.type === 'B2B' ? 'Solutions' : 'Services'}
              </h3>
              <ul className="space-y-3">
                {footerLinks.services.map((link, index) => (
                  <motion.li 
                    key={link.name}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    viewport={{ once: true }}
                  >
                    <Link 
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-300 hover:translate-x-1 inline-block"
                    >
                      {link.name}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Company Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold font-display mb-6 text-gold-400">Company</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link, index) => (
                  <motion.li 
                    key={link.name}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    viewport={{ once: true }}
                  >
                    <Link 
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-300 hover:translate-x-1 inline-block"
                    >
                      {link.name}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Support & Trust */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold font-display mb-6 text-gold-400">Support</h3>
              <ul className="space-y-3 mb-8">
                {footerLinks.support.map((link, index) => (
                  <motion.li 
                    key={link.name}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    viewport={{ once: true }}
                  >
                    <Link 
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-300 hover:translate-x-1 inline-block"
                    >
                      {link.name}
                    </Link>
                  </motion.li>
                ))}
              </ul>

              {/* Trust Badges */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white">Trusted & Secure</h4>
                <div className="flex flex-wrap gap-3">
                  <motion.div 
                    className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Shield className="h-4 w-4 text-green-400" />
                    <span className="text-sm text-gray-300">SSL Secured</span>
                  </motion.div>
                  <motion.div 
                    className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Award className="h-4 w-4 text-gold-400" />
                    <span className="text-sm text-gray-300">ISO Certified</span>
                  </motion.div>
                  <motion.div 
                    className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Globe className="h-4 w-4 text-blue-400" />
                    <span className="text-sm text-gray-300">Global Coverage</span>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom Section */}
          <motion.div 
            className="border-t border-white/20 mt-16 pt-8 flex flex-col md:flex-row justify-between items-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="text-gray-300 text-sm mb-4 md:mb-0">
              © 2024 {tenantConfig.name}. All rights reserved.
            </div>
            <div className="flex items-center space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-300 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-300 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-gray-300 hover:text-white transition-colors">
                Cookie Policy
              </Link>
            </div>
          </motion.div>
        </div>
      </footer>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <motion.button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 w-12 h-12 bg-gold-500 rounded-full shadow-luxury-lg text-white z-40 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          whileHover={{
            scale: 1.1,
            boxShadow: "0 20px 60px rgba(212, 175, 55, 0.4)"
          }}
          whileTap={{ scale: 0.9 }}
        >
          <ArrowUp className="w-6 h-6" />
        </motion.button>
      )}
    </>
  )
}
