'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Menu,
  X,
  Plane,
  Phone,
  Mail,
  MapPin,
  User,
  ShoppingBag,
  Building,
  Star
} from 'lucide-react'
import { useTenantConfig, detectTenant, TenantBrandingService } from '@/lib/branding'

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [tenantConfig, setTenantConfig] = useState(useTenantConfig())

  useEffect(() => {
    const tenantId = detectTenant()
    if (tenantId) {
      const config = useTenantConfig(tenantId)
      setTenantConfig(config)
      TenantBrandingService.getInstance().setCurrentTenant(config)
    }
  }, [])

  const navigation = tenantConfig.type === 'B2B'
    ? [
        { name: 'Home', href: '/' },
        { name: 'Solutions', href: '/solutions' },
        { name: 'Enterprise', href: '/enterprise' },
        { name: 'Support', href: '/support' },
        { name: 'Contact', href: '/contact' },
      ]
    : [
        { name: 'Home', href: '/' },
        { name: 'Services', href: '/services' },
        { name: 'Airports', href: '/airports' },
        { name: 'About', href: '/about' },
        { name: 'Contact', href: '/contact' },
      ]

  return (
    <header className="bg-white shadow-luxury sticky top-0 z-40">
      {/* Luxury Top bar */}
      <motion.div
        className={`${
          tenantConfig.type === 'B2B'
            ? 'bg-luxury-800'
            : 'bg-primary-500'
        } text-white py-3`}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container-max px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-8">
              <motion.div
                className="flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
              >
                <Phone className="h-4 w-4 text-gold-400" />
                <span className="font-medium">{tenantConfig.contact.phone}</span>
              </motion.div>
              <motion.div
                className="hidden sm:flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
              >
                <Mail className="h-4 w-4 text-gold-400" />
                <span className="font-medium">{tenantConfig.contact.email}</span>
              </motion.div>
            </div>
            <div className="flex items-center space-x-6">
              <motion.span
                className="hidden sm:inline font-medium"
                whileHover={{ scale: 1.05 }}
              >
                {tenantConfig.messaging.supportMessage}
              </motion.span>
              <motion.div
                className="flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
              >
                {tenantConfig.type === 'B2B' ? (
                  <Building className="h-4 w-4 text-gold-400" />
                ) : (
                  <MapPin className="h-4 w-4 text-gold-400" />
                )}
                <span className="font-medium">
                  {tenantConfig.type === 'B2B' ? 'Enterprise Solutions' : '150+ Airports Worldwide'}
                </span>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Luxury Main navigation */}
      <nav className="bg-white border-b border-luxury-200">
        <div className="container-max px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Luxury Logo */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Link href="/" className="flex items-center space-x-4 group">
                <motion.div
                  className={`${
                    tenantConfig.type === 'B2B'
                      ? 'bg-luxury-700'
                      : 'bg-gold-500'
                  } p-3 rounded-xl shadow-luxury group-hover:shadow-luxury-lg transition-all duration-300`}
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {tenantConfig.type === 'B2B' ? (
                    <Building className="h-7 w-7 text-white" />
                  ) : (
                    <Plane className="h-7 w-7 text-white" />
                  )}
                </motion.div>
                <div className="group-hover:scale-105 transition-transform duration-300">
                  <div className="text-2xl font-bold text-luxury-900 font-display">
                    {tenantConfig.name.split(' ')[0]}
                  </div>
                  <div className="text-sm text-gold-500 font-semibold">
                    {tenantConfig.name.split(' ').slice(1).join(' ')}
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* Luxury Desktop navigation */}
            <motion.div
              className="hidden lg:flex items-center space-x-10"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                >
                  <Link
                    href={item.href}
                    className="text-luxury-700 hover:text-luxury-900 font-semibold transition-all duration-300 relative group py-2"
                  >
                    {item.name}
                    <motion.span
                      className="absolute -bottom-1 left-0 h-0.5 bg-gold-500 transition-all duration-300"
                      initial={{ width: 0 }}
                      whileHover={{ width: '100%' }}
                    />
                    <motion.div
                      className="absolute inset-0 bg-luxury-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"
                      whileHover={{ scale: 1.05 }}
                    />
                  </Link>
                </motion.div>
              ))}
            </motion.div>

            {/* Luxury Desktop CTA buttons */}
            <motion.div
              className="hidden lg:flex items-center space-x-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }}>
                <Link
                  href="/my-bookings"
                  className="flex items-center space-x-2 text-luxury-700 hover:text-luxury-900 font-semibold transition-all duration-300 px-4 py-2 rounded-xl hover:bg-luxury-50"
                >
                  <User className="h-5 w-5" />
                  <span>{tenantConfig.type === 'B2B' ? 'Dashboard' : 'My Bookings'}</span>
                </Link>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/book"
                  className={`${
                    tenantConfig.type === 'B2B' ? 'btn-outline' : 'btn-gold'
                  } flex items-center space-x-2`}
                >
                  {tenantConfig.type === 'B2B' ? (
                    <Building className="h-5 w-5" />
                  ) : (
                    <Star className="h-5 w-5" />
                  )}
                  <span>{tenantConfig.messaging.ctaText}</span>
                </Link>
              </motion.div>
            </motion.div>

            {/* Luxury Mobile menu button */}
            <motion.button
              type="button"
              className="lg:hidden p-3 rounded-xl text-luxury-700 hover:text-luxury-900 hover:bg-luxury-50 transition-all duration-300 shadow-inner-luxury"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{ rotate: mobileMenuOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {mobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </motion.div>
            </motion.button>
          </div>
        </div>

        {/* Luxury Mobile menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="lg:hidden bg-white border-t border-luxury-200 shadow-luxury"
          >
            <div className="px-6 py-6 space-y-6">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="block text-luxury-700 hover:text-luxury-900 font-semibold py-3 px-4 rounded-xl hover:bg-luxury-100 transition-all duration-300 border-l-4 border-transparent hover:border-gold-500"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}

              <motion.div
                className="pt-6 border-t border-luxury-200 space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
              >
                <Link
                  href="/my-bookings"
                  className="flex items-center space-x-3 text-luxury-700 hover:text-luxury-900 font-semibold py-3 px-4 rounded-xl hover:bg-luxury-100 transition-all duration-300"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <User className="h-5 w-5" />
                  <span>{tenantConfig.type === 'B2B' ? 'Dashboard' : 'My Bookings'}</span>
                </Link>

                <motion.div whileTap={{ scale: 0.95 }}>
                  <Link
                    href="/book"
                    className={`block w-full text-center ${
                      tenantConfig.type === 'B2B' ? 'btn-outline' : 'btn-gold'
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <div className="flex items-center justify-center space-x-2">
                      {tenantConfig.type === 'B2B' ? (
                        <Building className="h-5 w-5" />
                      ) : (
                        <Star className="h-5 w-5" />
                      )}
                      <span>{tenantConfig.messaging.ctaText}</span>
                    </div>
                  </Link>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </nav>
    </header>
  )
}
