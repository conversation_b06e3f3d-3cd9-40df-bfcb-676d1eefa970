'use client'

import { useState } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { CreditCard, Lock, Shield, CheckCircle } from 'lucide-react'
import PaymentSelector, { PaymentProvider } from './PaymentSelector'
import RazorpayCheckout from './RazorpayCheckout'

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface BookingData {
  airport: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
}

interface CheckoutFormProps {
  bookingData: BookingData
  onPaymentSuccess: (paymentIntent: any) => void
  onPaymentError: (error: string) => void
}

interface PaymentFormProps extends CheckoutFormProps {}

const PaymentForm = ({ bookingData, onPaymentSuccess, onPaymentError }: PaymentFormProps) => {
  const stripe = useStripe()
  const elements = useElements()
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentError, setPaymentError] = useState<string | null>(null)

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsProcessing(true)
    setPaymentError(null)

    const cardElement = elements.getElement(CardElement)

    if (!cardElement) {
      setPaymentError('Card element not found')
      setIsProcessing(false)
      return
    }

    try {
      // Create payment method
      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: 'Customer Name', // This would come from form data
        },
      })

      if (paymentMethodError) {
        setPaymentError(paymentMethodError.message || 'Payment method creation failed')
        setIsProcessing(false)
        return
      }

      // Create payment intent on backend
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: bookingData.totalAmount * 100, // Convert to cents
          currency: bookingData.currency,
          payment_method: paymentMethod.id,
          booking_data: bookingData,
        }),
      })

      const { client_secret } = await response.json()

      // Confirm payment
      const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(client_secret)

      if (confirmError) {
        setPaymentError(confirmError.message || 'Payment confirmation failed')
        onPaymentError(confirmError.message || 'Payment failed')
      } else if (paymentIntent.status === 'succeeded') {
        // Generate booking reference and send confirmation email
        try {
          const bookingReference = `AC${Date.now().toString().slice(-8)}`

          // Send confirmation email
          const emailResponse = await fetch('/api/send-confirmation-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              booking_reference: bookingReference,
              payment_intent_id: paymentIntent.id,
              booking_data: bookingData,
              customer_email: '<EMAIL>', // TODO: Get from form
              customer_name: 'Customer Name' // TODO: Get from form
            }),
          })

          if (emailResponse.ok) {
            onPaymentSuccess(bookingReference)
          } else {
            console.error('Failed to send confirmation email')
            onPaymentSuccess(bookingReference) // Still proceed with booking reference
          }
        } catch (error) {
          console.error('Error processing payment success:', error)
          // Fallback: use payment intent ID as reference
          onPaymentSuccess(`AC${paymentIntent.id.slice(-8)}`)
        }
      }
    } catch (error) {
      setPaymentError('An unexpected error occurred')
      onPaymentError('Payment processing failed')
    } finally {
      setIsProcessing(false)
    }
  }

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: 'Inter, system-ui, sans-serif',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Order Summary */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Airport</span>
            <span className="font-medium">{bookingData.airport}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Service Type</span>
            <span className="font-medium capitalize">{bookingData.serviceType}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Services</span>
            <span className="font-medium">{bookingData.services.join(', ')}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Date & Time</span>
            <span className="font-medium">{bookingData.date} at {bookingData.time}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Passengers</span>
            <span className="font-medium">{bookingData.passengers}</span>
          </div>
          <div className="border-t pt-3 flex justify-between text-lg font-semibold">
            <span>Total</span>
            <span>{bookingData.currency} {bookingData.totalAmount.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Payment Details */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <CreditCard className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900">Payment Details</h3>
        </div>

        <div className="border border-gray-300 rounded-lg p-4">
          <CardElement options={cardElementOptions} />
        </div>

        {paymentError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800 text-sm">{paymentError}</div>
          </div>
        )}
      </div>

      {/* Security Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-800">
            <div className="font-medium mb-1">Secure Payment</div>
            <div>Your payment information is encrypted and secure. We use industry-standard SSL encryption to protect your data.</div>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className={`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200 ${
          isProcessing
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-primary-600 hover:bg-primary-700 text-white'
        }`}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>Processing Payment...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>Pay {bookingData.currency} {bookingData.totalAmount.toFixed(2)}</span>
          </div>
        )}
      </button>

      {/* Trust Indicators */}
      <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
        <div className="flex items-center space-x-1">
          <Shield className="h-4 w-4" />
          <span>SSL Secured</span>
        </div>
        <div className="flex items-center space-x-1">
          <CheckCircle className="h-4 w-4" />
          <span>PCI Compliant</span>
        </div>
        <div className="flex items-center space-x-1">
          <Lock className="h-4 w-4" />
          <span>256-bit Encryption</span>
        </div>
      </div>
    </form>
  )
}

export default function CheckoutForm({ bookingData, onPaymentSuccess, onPaymentError }: CheckoutFormProps) {
  const [selectedProvider, setSelectedProvider] = useState<PaymentProvider>('stripe')
  const [showPaymentForm, setShowPaymentForm] = useState(false)

  const handleProviderSelect = (provider: PaymentProvider) => {
    setSelectedProvider(provider)
    setShowPaymentForm(true)
  }

  const handleBackToSelection = () => {
    setShowPaymentForm(false)
  }

  if (!showPaymentForm) {
    return (
      <div className="space-y-6">
        <PaymentSelector
          selectedProvider={selectedProvider}
          onProviderSelect={handleProviderSelect}
          amount={bookingData.totalAmount}
          currency={bookingData.currency}
        />

        <button
          onClick={() => setShowPaymentForm(true)}
          className="w-full py-3 px-6 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors"
        >
          Continue to Payment
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <button
        onClick={handleBackToSelection}
        className="text-primary-600 hover:text-primary-700 font-medium"
      >
        ← Back to Payment Methods
      </button>

      {/* Payment Form based on selected provider */}
      {selectedProvider === 'stripe' ? (
        <Elements stripe={stripePromise}>
          <PaymentForm
            bookingData={bookingData}
            onPaymentSuccess={onPaymentSuccess}
            onPaymentError={onPaymentError}
          />
        </Elements>
      ) : (
        <RazorpayCheckout
          bookingData={bookingData}
          onPaymentSuccess={onPaymentSuccess}
          onPaymentError={onPaymentError}
        />
      )}
    </div>
  )
}
