'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, Download, Mail, Calendar, MapPin, Users, Clock, Phone } from 'lucide-react'

interface PaymentSuccessProps {
  paymentIntent: any
  bookingData: {
    airport: string
    serviceType: 'arrival' | 'departure'
    services: string[]
    selectedServices?: Array<{
      id: string
      name: string
      description: string
      base_price: string
    }>
    date: string
    time: string
    passengers: number
    totalAmount: number
    currency: string
    flightNumber?: string
  }
  bookingReference: string
}

// Service mapping for display names
const getServiceName = (serviceId: string): string => {
  const serviceMap: Record<string, string> = {
    '550e8400-e29b-41d4-a716-446655440010': 'Premium Meet & Greet - Arrival',
    '550e8400-e29b-41d4-a716-446655440011': 'Premium Meet & Greet - Departure',
    '550e8400-e29b-41d4-a716-446655440012': 'VIP Fast Track Security',
    '550e8400-e29b-41d4-a716-446655440013': 'Executive Lounge Access',
    '550e8400-e29b-41d4-a716-446655440014': 'Premium Airport Transfer',
    '550e8400-e29b-41d4-a716-446655440015': 'VIP Terminal Services',
    '1': 'Meet & Greet Service',
    '2': 'Fast Track Security',
    '3': 'VIP Lounge Access',
    '4': 'Airport Transfer',
    '5': 'Special Assistance',
    '6': 'Porter Service'
  }
  return serviceMap[serviceId] || `Premium Service`
}

export default function PaymentSuccess({
  paymentIntent,
  bookingData,
  bookingReference
}: PaymentSuccessProps) {
  const [emailSent, setEmailSent] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  useEffect(() => {
    // Simulate sending confirmation email
    const sendConfirmationEmail = async () => {
      try {
        await fetch('/api/send-confirmation-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            booking_reference: bookingReference,
            payment_intent_id: paymentIntent.id,
            booking_data: bookingData,
          }),
        })
        setEmailSent(true)
      } catch (error) {
        console.error('Failed to send confirmation email:', error)
      }
    }

    sendConfirmationEmail()
  }, [paymentIntent.id, bookingReference, bookingData])

  const handleDownloadVoucher = async () => {
    setIsDownloading(true)
    try {
      const response = await fetch(`/api/download-voucher/${bookingReference}`)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `AirConcierge-Voucher-${bookingReference}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download voucher:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-8">
      {/* Success Header */}
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Payment Successful!
        </h1>
        <p className="text-lg text-gray-600">
          Your booking has been confirmed and you'll receive a confirmation email shortly.
        </p>
      </div>

      {/* Booking Reference */}
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-6 text-center">
        <div className="text-sm text-primary-700 mb-1">Booking Reference</div>
        <div className="text-2xl font-bold text-primary-900 tracking-wider">
          {bookingReference}
        </div>
        <div className="text-sm text-primary-600 mt-2">
          Please save this reference number for your records
        </div>
      </div>

      {/* Booking Details */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Booking Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Airport</div>
                <div className="text-gray-600">{bookingData.airport}</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Date & Time</div>
                <div className="text-gray-600">
                  {formatDate(bookingData.date)} at {bookingData.time}
                </div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Users className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Passengers</div>
                <div className="text-gray-600">{bookingData.passengers} passenger{bookingData.passengers !== 1 ? 's' : ''}</div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Clock className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Service Type</div>
                <div className="text-gray-600 capitalize">{bookingData.serviceType}</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Services</div>
                <div className="text-gray-600 space-y-1">
                  {bookingData.selectedServices && bookingData.selectedServices.length > 0 ? (
                    bookingData.selectedServices.map((service, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span>• {service.name}</span>
                        <span className="text-sm font-medium text-primary-600">
                          ₹{parseInt(service.base_price).toLocaleString()}
                        </span>
                      </div>
                    ))
                  ) : (
                    bookingData.services.map((serviceId, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span>• {getServiceName(serviceId)}</span>
                        <span className="text-sm font-medium text-primary-600">
                          ₹{Math.round(bookingData.totalAmount / bookingData.services.length).toLocaleString()}
                        </span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {bookingData.flightNumber && (
              <div className="flex items-start space-x-3">
                <div className="h-5 w-5 text-gray-400 mt-0.5">✈️</div>
                <div>
                  <div className="font-medium text-gray-900">Flight Number</div>
                  <div className="text-gray-600">{bookingData.flightNumber}</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Payment Info */}
        <div className="border-t mt-6 pt-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div>
                <div className="font-medium text-gray-900 flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span>Payment Confirmed</span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Transaction ID: {paymentIntent.id || 'mock_payment_intent'}
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-700">
                  ₹{bookingData.totalAmount.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">
                  {bookingData.currency || 'INR'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          onClick={handleDownloadVoucher}
          disabled={isDownloading}
          className="flex items-center justify-center space-x-2 bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors disabled:opacity-50"
        >
          <Download className="h-5 w-5" />
          <span>{isDownloading ? 'Downloading...' : 'Download Voucher'}</span>
        </button>

        <button
          onClick={() => window.location.href = `/booking/confirmation/${bookingReference}`}
          className="flex items-center justify-center space-x-2 bg-gray-100 text-gray-900 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors"
        >
          <Calendar className="h-5 w-5" />
          <span>View Full Details</span>
        </button>
      </div>

      {/* Email Confirmation Status */}
      <div className={`rounded-lg p-4 ${emailSent ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
        <div className="flex items-center space-x-3">
          <Mail className={`h-5 w-5 ${emailSent ? 'text-green-600' : 'text-yellow-600'}`} />
          <div className={`text-sm ${emailSent ? 'text-green-800' : 'text-yellow-800'}`}>
            {emailSent 
              ? 'Confirmation email sent successfully!'
              : 'Sending confirmation email...'
            }
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-semibold text-blue-900 mb-3">What happens next?</h3>
        <ul className="space-y-2 text-sm text-blue-800">
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 font-bold">1.</span>
            <span>You'll receive a confirmation email with your digital voucher</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 font-bold">2.</span>
            <span>Our team will contact you 24 hours before your service</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 font-bold">3.</span>
            <span>Arrive at the designated meeting point 15 minutes early</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-blue-600 font-bold">4.</span>
            <span>Enjoy your premium airport experience!</span>
          </li>
        </ul>
      </div>

      {/* Support Contact */}
      <div className="text-center bg-gray-50 rounded-lg p-6">
        <div className="text-sm text-gray-600 mb-2">Need help or have questions?</div>
        <div className="flex items-center justify-center space-x-4 text-sm">
          <a href="tel:******-123-4567" className="flex items-center space-x-1 text-primary-600 hover:text-primary-700">
            <Phone className="h-4 w-4" />
            <span>******-123-4567</span>
          </a>
          <span className="text-gray-400">|</span>
          <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
            <EMAIL>
          </a>
        </div>
        <div className="text-xs text-gray-500 mt-2">24/7 Customer Support Available</div>
      </div>
      </div>
    </div>
  )
}
