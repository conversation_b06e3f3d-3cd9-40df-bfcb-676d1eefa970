'use client'

import { useState, useEffect } from 'react'
import { Lock, Shield, CheckCircle, AlertCircle } from 'lucide-react'

interface BookingData {
  airport: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
}

interface RazorpayCheckoutProps {
  bookingData: BookingData
  onPaymentSuccess: (paymentData: any) => void
  onPaymentError: (error: string) => void
}

declare global {
  interface Window {
    Razorpay: any;
  }
}

export default function RazorpayCheckout({
  bookingData,
  onPaymentSuccess,
  onPaymentError
}: RazorpayCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isScriptLoaded, setIsScriptLoaded] = useState(false)

  // Format currency based on locale
  const formatCurrency = (amount: number, currency: string) => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US'
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }
  const [paymentError, setPaymentError] = useState<string | null>(null)

  useEffect(() => {
    // Load Razorpay script
    const script = document.createElement('script')
    script.src = 'https://checkout.razorpay.com/v1/checkout.js'
    script.async = true
    script.onload = () => setIsScriptLoaded(true)
    script.onerror = () => {
      setPaymentError('Failed to load Razorpay. Please try again.')
    }
    document.body.appendChild(script)

    return () => {
      document.body.removeChild(script)
    }
  }, [])

  const handlePayment = async () => {
    if (!isScriptLoaded) {
      setPaymentError('Payment system is still loading. Please wait.')
      return
    }

    setIsLoading(true)
    setPaymentError(null)

    try {
      // Create payment intent on backend
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: bookingData.totalAmount,
          currency: bookingData.currency,
          provider: 'razorpay',
          booking_data: bookingData,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create payment')
      }

      // For Razorpay, the order data is directly in data.data
      const orderData = data.data

      // Configure Razorpay options
      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: Math.round(bookingData.totalAmount * 100), // Amount in paise
        currency: bookingData.currency.toUpperCase(),
        name: 'AirConcierge Pro',
        description: `${bookingData.serviceType} service at ${bookingData.airport}`,
        order_id: orderData.id,
        handler: async function (response: any) {
          try {
            // Verify payment on backend
            const verifyResponse = await fetch('/api/verify-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                payment_id: response.razorpay_order_id,
                provider: 'razorpay',
                verification_data: {
                  razorpay_order_id: response.razorpay_order_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              }),
            })

            const verifyData = await verifyResponse.json()

            if (verifyData.success) {
              onPaymentSuccess({
                ...response,
                order_id: orderData.id,
                amount: bookingData.totalAmount,
                currency: bookingData.currency,
              })
            } else {
              throw new Error('Payment verification failed')
            }
          } catch (error) {
            onPaymentError('Payment verification failed. Please contact support.')
          }
        },
        prefill: {
          name: 'Customer', // This would come from form data
          email: '<EMAIL>',
          contact: '+919999999999',
        },
        notes: {
          booking_airport: bookingData.airport,
          booking_service_type: bookingData.serviceType,
          booking_services: bookingData.services.join(','),
          booking_date: bookingData.date,
          booking_time: bookingData.time,
          booking_passengers: bookingData.passengers.toString(),
        },
        theme: {
          color: '#3B82F6', // Primary color
        },
        modal: {
          ondismiss: function () {
            setIsLoading(false)
            onPaymentError('Payment was cancelled')
          },
        },
      }

      const razorpay = new window.Razorpay(options)
      razorpay.open()

    } catch (error: any) {
      setPaymentError(error.message || 'Payment initialization failed')
      onPaymentError(error.message || 'Payment failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Order Summary */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Airport</span>
            <span className="font-medium">{bookingData.airport}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Service Type</span>
            <span className="font-medium capitalize">{bookingData.serviceType}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Services</span>
            <span className="font-medium">{bookingData.services.join(', ')}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Date & Time</span>
            <span className="font-medium">{bookingData.date} at {bookingData.time}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Passengers</span>
            <span className="font-medium">{bookingData.passengers}</span>
          </div>
          <div className="border-t pt-3 flex justify-between text-lg font-semibold">
            <span>Total Amount</span>
            <span className="text-primary-600">
              {bookingData.currency} {bookingData.totalAmount.toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Methods Info */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 mb-3">Available Payment Methods</h4>
        <div className="grid grid-cols-2 gap-4 text-sm text-blue-800">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <span>UPI (Google Pay, PhonePe, Paytm)</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <span>Credit/Debit Cards</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <span>Net Banking</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <span>Digital Wallets</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {paymentError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <div className="text-red-800 text-sm">{paymentError}</div>
          </div>
        </div>
      )}

      {/* Pay Button */}
      <button
        onClick={handlePayment}
        disabled={isLoading || !isScriptLoaded}
        className={`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200 ${
          isLoading || !isScriptLoaded
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>Processing...</span>
          </div>
        ) : !isScriptLoaded ? (
          <span>Loading Payment System...</span>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>Pay {formatCurrency(bookingData.totalAmount, bookingData.currency)}</span>
          </div>
        )}
      </button>

      {/* Trust Indicators */}
      <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
        <div className="flex items-center space-x-1">
          <Shield className="h-4 w-4" />
          <span>RBI Approved</span>
        </div>
        <div className="flex items-center space-x-1">
          <CheckCircle className="h-4 w-4" />
          <span>PCI DSS Compliant</span>
        </div>
        <div className="flex items-center space-x-1">
          <Lock className="h-4 w-4" />
          <span>256-bit SSL</span>
        </div>
      </div>
    </div>
  )
}
