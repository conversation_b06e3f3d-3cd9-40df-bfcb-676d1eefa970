'use client'

import { useState } from 'react'
import { CreditCard, Smartphone, Shield, CheckCircle } from 'lucide-react'

export type PaymentProvider = 'stripe' | 'razorpay'

interface PaymentSelectorProps {
  selectedProvider: PaymentProvider
  onProviderSelect: (provider: PaymentProvider) => void
  amount: number
  currency: string
}

const paymentMethods = [
  {
    id: 'stripe' as PaymentProvider,
    name: 'Credit/Debit Card',
    description: 'Visa, Mastercard, American Express',
    icon: CreditCard,
    features: ['Secure SSL encryption', 'International cards accepted', 'Instant processing'],
    regions: ['Global', 'US', 'Europe', 'Asia'],
  },
  {
    id: 'razorpay' as PaymentProvider,
    name: 'Razorpay',
    description: 'UPI, Cards, Net Banking, Wallets',
    icon: Smartphone,
    features: ['UPI payments', 'Net banking', 'Digital wallets', 'EMI options'],
    regions: ['India'],
  },
]

export default function PaymentSelector({ 
  selectedProvider, 
  onProviderSelect, 
  amount, 
  currency 
}: PaymentSelectorProps) {
  const [hoveredMethod, setHoveredMethod] = useState<PaymentProvider | null>(null)

  const formatAmount = (amount: number, currency: string) => {
    // Use Indian locale for INR formatting
    const locale = currency === 'INR' ? 'en-IN' : 'en-US'
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Payment Amount Display */}
      <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Amount</h3>
        <div className="text-3xl font-bold text-primary-600">
          {formatAmount(amount, currency)}
        </div>
      </div>

      {/* Payment Method Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Payment Method</h3>
        
        <div className="grid gap-4">
          {paymentMethods.map((method) => {
            const Icon = method.icon
            const isSelected = selectedProvider === method.id
            const isHovered = hoveredMethod === method.id

            return (
              <div
                key={method.id}
                className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'border-primary-500 bg-primary-50 shadow-md'
                    : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
                }`}
                onClick={() => onProviderSelect(method.id)}
                onMouseEnter={() => setHoveredMethod(method.id)}
                onMouseLeave={() => setHoveredMethod(null)}
              >
                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute top-4 right-4">
                    <CheckCircle className="h-6 w-6 text-primary-600" />
                  </div>
                )}

                <div className="flex items-start space-x-4">
                  {/* Icon */}
                  <div className={`p-3 rounded-lg ${
                    isSelected ? 'bg-primary-100' : 'bg-gray-100'
                  }`}>
                    <Icon className={`h-6 w-6 ${
                      isSelected ? 'text-primary-600' : 'text-gray-600'
                    }`} />
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">
                        {method.name}
                      </h4>
                      <div className="flex space-x-1">
                        {method.regions.map((region) => (
                          <span
                            key={region}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                          >
                            {region}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{method.description}</p>

                    {/* Features */}
                    {(isSelected || isHovered) && (
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          {method.features.map((feature, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <Shield className="h-5 w-5 text-green-600" />
          <span className="font-semibold text-gray-900">Secure Payment</span>
        </div>
        <p className="text-sm text-gray-600">
          Your payment information is encrypted and secure. We use industry-standard security 
          measures to protect your data and never store your payment details.
        </p>
      </div>

      {/* Provider-specific Information */}
      {selectedProvider === 'razorpay' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Razorpay Payment Options</h4>
          <div className="grid grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <strong>UPI:</strong> Google Pay, PhonePe, Paytm
            </div>
            <div>
              <strong>Cards:</strong> Visa, Mastercard, RuPay
            </div>
            <div>
              <strong>Net Banking:</strong> All major banks
            </div>
            <div>
              <strong>Wallets:</strong> Paytm, Mobikwik, Freecharge
            </div>
          </div>
        </div>
      )}

      {selectedProvider === 'stripe' && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h4 className="font-semibold text-purple-900 mb-2">International Payment</h4>
          <p className="text-sm text-purple-800">
            Stripe supports international credit and debit cards from over 135+ countries. 
            Your payment will be processed securely with 3D Secure authentication when required.
          </p>
        </div>
      )}
    </div>
  )
}
