// Tenant Branding Configuration System
export interface TenantConfig {
  id: string
  name: string
  type: 'B2C' | 'B2B'
  branding: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    logo?: string
    favicon?: string
    customDomain?: string
  }
  messaging: {
    heroTitle: string
    heroSubtitle: string
    tagline: string
    ctaText: string
    supportMessage: string
  }
  features: {
    showPricing: boolean
    allowGuestBooking: boolean
    requireApproval: boolean
    bulkBooking: boolean
    corporateRates: boolean
    customReporting: boolean
  }
  styling: {
    theme: 'luxury' | 'corporate' | 'minimal'
    fontFamily: string
    borderRadius: 'sharp' | 'rounded' | 'pill'
    shadowStyle: 'subtle' | 'prominent' | 'none'
  }
  contact: {
    phone: string
    email: string
    address?: string
    supportHours: string
  }
}

// Default B2C Configuration
export const defaultB2CConfig: TenantConfig = {
  id: 'default-b2c',
  name: 'AirConcierge Pro',
  type: 'B2C',
  branding: {
    primaryColor: '#1e3a8a', // Deep navy
    secondaryColor: '#f59e0b', // Gold
    accentColor: '#2563eb', // Premium blue
  },
  messaging: {
    heroTitle: 'Premium Airport Concierge',
    heroSubtitle: 'Experience luxury travel like never before',
    tagline: 'Your Personal Travel Companion',
    ctaText: 'Book Your Experience',
    supportMessage: '24/7 Premium Support'
  },
  features: {
    showPricing: true,
    allowGuestBooking: true,
    requireApproval: false,
    bulkBooking: false,
    corporateRates: false,
    customReporting: false
  },
  styling: {
    theme: 'luxury',
    fontFamily: 'Playfair Display',
    borderRadius: 'rounded',
    shadowStyle: 'prominent'
  },
  contact: {
    phone: '+****************',
    email: '<EMAIL>',
    supportHours: '24/7'
  }
}

// Default B2B Configuration
export const defaultB2BConfig: TenantConfig = {
  id: 'default-b2b',
  name: 'AirConcierge Business Solutions',
  type: 'B2B',
  branding: {
    primaryColor: '#1e293b', // Corporate dark
    secondaryColor: '#0ea5e9', // Professional blue
    accentColor: '#059669', // Success green
  },
  messaging: {
    heroTitle: 'Enterprise Travel Solutions',
    heroSubtitle: 'Streamline your corporate travel experience',
    tagline: 'Efficiency. Reliability. Excellence.',
    ctaText: 'Request Enterprise Demo',
    supportMessage: 'Dedicated Account Management'
  },
  features: {
    showPricing: false,
    allowGuestBooking: false,
    requireApproval: true,
    bulkBooking: true,
    corporateRates: true,
    customReporting: true
  },
  styling: {
    theme: 'corporate',
    fontFamily: 'Inter',
    borderRadius: 'sharp',
    shadowStyle: 'subtle'
  },
  contact: {
    phone: '+****************',
    email: '<EMAIL>',
    supportHours: 'Business Hours (9 AM - 6 PM EST)'
  }
}

// Sample tenant configurations
export const sampleTenants: Record<string, TenantConfig> = {
  'luxury-travel': {
    id: 'luxury-travel',
    name: 'Elite Travel Concierge',
    type: 'B2C',
    branding: {
      primaryColor: '#7c2d12', // Rich brown
      secondaryColor: '#d97706', // Amber
      accentColor: '#dc2626', // Red accent
    },
    messaging: {
      heroTitle: 'Elite Travel Experiences',
      heroSubtitle: 'Where luxury meets convenience',
      tagline: 'Exclusively Yours',
      ctaText: 'Begin Your Journey',
      supportMessage: 'Concierge Available 24/7'
    },
    features: {
      showPricing: true,
      allowGuestBooking: false,
      requireApproval: false,
      bulkBooking: false,
      corporateRates: false,
      customReporting: false
    },
    styling: {
      theme: 'luxury',
      fontFamily: 'Playfair Display',
      borderRadius: 'rounded',
      shadowStyle: 'prominent'
    },
    contact: {
      phone: '+1 (555) ELITE-01',
      email: '<EMAIL>',
      supportHours: '24/7 Concierge Service'
    }
  },
  'corporate-solutions': {
    id: 'corporate-solutions',
    name: 'BusinessJet Services',
    type: 'B2B',
    branding: {
      primaryColor: '#0f172a', // Very dark navy
      secondaryColor: '#3b82f6', // Blue
      accentColor: '#10b981', // Emerald
    },
    messaging: {
      heroTitle: 'Corporate Travel Management',
      heroSubtitle: 'Optimize your business travel operations',
      tagline: 'Professional. Efficient. Scalable.',
      ctaText: 'Schedule Consultation',
      supportMessage: 'Enterprise Support Team'
    },
    features: {
      showPricing: false,
      allowGuestBooking: false,
      requireApproval: true,
      bulkBooking: true,
      corporateRates: true,
      customReporting: true
    },
    styling: {
      theme: 'corporate',
      fontFamily: 'Inter',
      borderRadius: 'sharp',
      shadowStyle: 'subtle'
    },
    contact: {
      phone: '+1 (555) BIZ-JETS',
      email: '<EMAIL>',
      supportHours: 'Mon-Fri 8 AM - 8 PM EST'
    }
  }
}

// Utility functions for tenant management
export class TenantBrandingService {
  private static instance: TenantBrandingService
  private currentTenant: TenantConfig = defaultB2CConfig

  static getInstance(): TenantBrandingService {
    if (!TenantBrandingService.instance) {
      TenantBrandingService.instance = new TenantBrandingService()
    }
    return TenantBrandingService.instance
  }

  // Get tenant configuration by ID or domain
  getTenantConfig(identifier?: string): TenantConfig {
    if (!identifier) {
      return this.currentTenant
    }

    // Check if it's a tenant ID
    if (sampleTenants[identifier]) {
      return sampleTenants[identifier]
    }

    // Check if it's a custom domain
    const tenantByDomain = Object.values(sampleTenants).find(
      tenant => tenant.branding.customDomain === identifier
    )

    return tenantByDomain || this.currentTenant
  }

  // Set current tenant
  setCurrentTenant(config: TenantConfig): void {
    this.currentTenant = config
    this.applyTenantStyling(config)
  }

  // Apply tenant-specific CSS variables
  private applyTenantStyling(config: TenantConfig): void {
    const root = document.documentElement
    
    // Apply color scheme
    root.style.setProperty('--tenant-primary', config.branding.primaryColor)
    root.style.setProperty('--tenant-secondary', config.branding.secondaryColor)
    root.style.setProperty('--tenant-accent', config.branding.accentColor)
    
    // Apply font family
    root.style.setProperty('--tenant-font', config.styling.fontFamily)
    
    // Apply border radius
    const radiusMap = {
      sharp: '0px',
      rounded: '0.75rem',
      pill: '9999px'
    }
    root.style.setProperty('--tenant-radius', radiusMap[config.styling.borderRadius])
  }

  // Get theme-specific classes
  getThemeClasses(config: TenantConfig): string {
    const baseClasses = 'transition-all duration-300'
    
    switch (config.styling.theme) {
      case 'luxury':
        return `${baseClasses} luxury-theme`
      case 'corporate':
        return `${baseClasses} corporate-theme`
      case 'minimal':
        return `${baseClasses} minimal-theme`
      default:
        return baseClasses
    }
  }

  // Get messaging for current context
  getContextualMessaging(config: TenantConfig, context: 'hero' | 'cta' | 'support'): string {
    switch (context) {
      case 'hero':
        return config.type === 'B2B' 
          ? 'Streamline your corporate travel with our enterprise solutions'
          : 'Experience the ultimate in luxury travel services'
      case 'cta':
        return config.type === 'B2B' 
          ? 'Request Enterprise Quote'
          : 'Book Premium Service'
      case 'support':
        return config.type === 'B2B'
          ? 'Dedicated account management and priority support'
          : '24/7 concierge service for all your travel needs'
      default:
        return config.messaging.tagline
    }
  }

  // Check if feature is enabled for current tenant
  isFeatureEnabled(config: TenantConfig, feature: keyof TenantConfig['features']): boolean {
    return config.features[feature]
  }

  // Get pricing display format
  getPricingFormat(config: TenantConfig): 'show' | 'hide' | 'request_quote' {
    if (config.type === 'B2B') {
      return config.features.showPricing ? 'show' : 'request_quote'
    }
    return config.features.showPricing ? 'show' : 'hide'
  }

  // Get booking flow configuration
  getBookingFlowConfig(config: TenantConfig) {
    return {
      allowGuestBooking: config.features.allowGuestBooking,
      requireApproval: config.features.requireApproval,
      showBulkOptions: config.features.bulkBooking,
      useCorporateRates: config.features.corporateRates,
      collectCompanyInfo: config.type === 'B2B',
      showAdvancedOptions: config.type === 'B2B'
    }
  }
}

// Hook for using tenant configuration in React components
export function useTenantConfig(tenantId?: string): TenantConfig {
  const brandingService = TenantBrandingService.getInstance()
  return brandingService.getTenantConfig(tenantId)
}

// Utility to detect tenant from URL or headers
export function detectTenant(): string | null {
  if (typeof window === 'undefined') return null
  
  // Check for tenant in URL params
  const urlParams = new URLSearchParams(window.location.search)
  const tenantParam = urlParams.get('tenant')
  if (tenantParam) return tenantParam
  
  // Check for tenant in subdomain
  const hostname = window.location.hostname
  const subdomain = hostname.split('.')[0]
  if (subdomain && subdomain !== 'www' && subdomain !== 'localhost') {
    return subdomain
  }
  
  // Check for custom domain mapping
  if (sampleTenants) {
    const tenantByDomain = Object.values(sampleTenants).find(
      tenant => tenant.branding.customDomain === hostname
    )
    return tenantByDomain?.id || null
  }
  
  return null
}
