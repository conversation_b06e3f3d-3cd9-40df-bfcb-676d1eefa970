'use client'

import { io, Socket } from 'socket.io-client'

// WebSocket service for real-time updates
export interface WebSocketMessage {
  type: 'availability_update' | 'price_update' | 'booking_update' | 'system_notification'
  data: any
  timestamp: string
}

export interface AvailabilityUpdate {
  airportId: string
  serviceId: string
  date: string
  availableSlots: number
  totalSlots: number
  nextAvailableTime?: string
}

export interface PriceUpdate {
  serviceId: string
  airportId: string
  date: string
  basePrice: number
  dynamicPrice: number
  adjustments: Array<{
    type: string
    amount: number
    reason: string
  }>
  currency: string
}

export interface BookingUpdate {
  bookingId: string
  status: 'confirmed' | 'cancelled' | 'modified' | 'completed'
  message: string
  details?: any
}

export class WebSocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private listeners: Map<string, Set<(data: any) => void>> = new Map()
  private isConnecting = false

  constructor(private url: string = process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:8000') {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve()
        return
      }

      if (this.isConnecting) {
        return
      }

      this.isConnecting = true

      try {
        this.socket = io(this.url, {
          transports: ['websocket', 'polling'],
          timeout: 20000,
          forceNew: true
        })

        this.socket.on('connect', () => {
          console.log('Socket.IO connected')
          this.isConnecting = false
          this.reconnectAttempts = 0
          resolve()
        })

        this.socket.on('availability_update', (data) => {
          this.handleMessage({ type: 'availability_update', data, timestamp: new Date().toISOString() })
        })

        this.socket.on('price_update', (data) => {
          this.handleMessage({ type: 'price_update', data, timestamp: new Date().toISOString() })
        })

        this.socket.on('booking_update', (data) => {
          this.handleMessage({ type: 'booking_update', data, timestamp: new Date().toISOString() })
        })

        this.socket.on('system_notification', (data) => {
          this.handleMessage({ type: 'system_notification', data, timestamp: new Date().toISOString() })
        })

        this.socket.on('disconnect', (reason) => {
          console.log('Socket.IO disconnected:', reason)
          this.isConnecting = false

          if (reason === 'io server disconnect') {
            // Server initiated disconnect, reconnect manually
            this.scheduleReconnect()
          }
        })

        this.socket.on('connect_error', (error) => {
          console.error('Socket.IO connection error:', error)
          this.isConnecting = false
          reject(error)
        })

      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error)
      })
    }, delay)
  }

  private handleMessage(message: WebSocketMessage) {
    const listeners = this.listeners.get(message.type)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(message.data)
        } catch (error) {
          console.error('Error in WebSocket message handler:', error)
        }
      })
    }

    // Handle global listeners
    const globalListeners = this.listeners.get('*')
    if (globalListeners) {
      globalListeners.forEach(callback => {
        try {
          callback(message)
        } catch (error) {
          console.error('Error in global WebSocket message handler:', error)
        }
      })
    }
  }

  subscribe(messageType: string, callback: (data: any) => void) {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, new Set())
    }
    this.listeners.get(messageType)!.add(callback)

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(messageType)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          this.listeners.delete(messageType)
        }
      }
    }
  }

  // Subscribe to availability updates for specific airport/service
  subscribeToAvailability(airportId: string, serviceId: string, callback: (update: AvailabilityUpdate) => void) {
    return this.subscribe('availability_update', (data: AvailabilityUpdate) => {
      if (data.airportId === airportId && data.serviceId === serviceId) {
        callback(data)
      }
    })
  }

  // Subscribe to price updates for specific service
  subscribeToPricing(serviceId: string, callback: (update: PriceUpdate) => void) {
    return this.subscribe('price_update', (data: PriceUpdate) => {
      if (data.serviceId === serviceId) {
        callback(data)
      }
    })
  }

  // Subscribe to booking updates for specific booking
  subscribeToBooking(bookingId: string, callback: (update: BookingUpdate) => void) {
    return this.subscribe('booking_update', (data: BookingUpdate) => {
      if (data.bookingId === bookingId) {
        callback(data)
      }
    })
  }

  // Send message to server
  send(eventName: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(eventName, data)
    } else {
      console.warn('Socket.IO is not connected. Message not sent:', { eventName, data })
    }
  }

  // Request real-time updates for specific data
  requestAvailabilityUpdates(airportId: string, serviceId: string, date: string) {
    this.send('subscribe_availability', { airportId, serviceId, date })
  }

  requestPriceUpdates(serviceId: string, airportId: string, date: string, passengerCount: number) {
    this.send('subscribe_pricing', { serviceId, airportId, date, passengerCount })
  }

  requestBookingUpdates(bookingId: string) {
    this.send('subscribe_booking', { bookingId })
  }

  // Get connection status
  get isConnected(): boolean {
    return this.socket?.connected || false
  }

  get connectionState(): string {
    if (!this.socket) return 'disconnected'

    if (this.socket.connected) {
      return 'connected'
    } else if (this.isConnecting) {
      return 'connecting'
    } else {
      return 'disconnected'
    }
  }
}

// Create singleton instance
export const wsService = new WebSocketService()

// Auto-connect when the module is imported (only in browser)
if (typeof window !== 'undefined') {
  wsService.connect().catch(error => {
    console.warn('Failed to establish WebSocket connection:', error)
  })
}

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    wsService.disconnect()
  })
}
