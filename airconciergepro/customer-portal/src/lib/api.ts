// API configuration and utilities for connecting to the backend
const API_BASE_URL = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8000/api/customer/v1'

// Types for API responses
export interface Airport {
  id?: string
  iata_code: string
  code: string // For backward compatibility
  name: string
  city: string
  country: string
  timezone?: string
  terminals?: Terminal[]
  available_services?: number
  starting_price?: string
  currency?: string
}

export interface Terminal {
  terminal_code: string
  terminal_name: string
  facilities: any
  gates: any[]
  service_count: number
  min_price: number
  currency: string
}

export interface Service {
  id: string
  name: string
  description: string
  base_price: string
  currency: string
  duration_minutes: number
  category: string
  type: string
  max_passengers: number
  inclusions: string[]
  requirements: string[]
}

export interface BookingRequest {
  airport_id: string
  service_ids: string[]
  service_type: 'arrival' | 'departure'
  scheduled_datetime: string
  passenger_count: number
  flight_number?: string
  special_requirements?: string[]
  customer_info: {
    first_name: string
    last_name: string
    email: string
    phone: string
  }
}

export interface BookingResponse {
  id: string
  reference: string
  status: string
  total_amount: number
  currency: string
  meeting_point: string
  contact_number: string
}

// API client class
class ApiClient {
  private baseUrl: string
  private defaultHeaders: HeadersInit

  constructor() {
    this.baseUrl = API_BASE_URL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Tenant-ID': process.env.NEXT_PUBLIC_TENANT_ID || 'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff', // Use environment variable or fallback
    }
  }

  private getAuthHeaders(): HeadersInit {
    // Customer API endpoints are public, no authentication required
    return this.defaultHeaders
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const config: RequestInit = {
      headers: this.getAuthHeaders(),
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error)
      throw error
    }
  }

  // Airport endpoints - Updated for dynamic filtering
  async getAvailableAirports(params?: {
    category?: string;
    type?: string;
    passengers?: number;
  }): Promise<Airport[]> {
    const searchParams = new URLSearchParams()
    if (params?.category) searchParams.append('category', params.category)
    if (params?.type) searchParams.append('type', params.type)
    if (params?.passengers) searchParams.append('passengers', params.passengers.toString())

    const response = await this.request<{success: boolean, data: any[]}>(`/airports/available?${searchParams.toString()}`)

    // Map backend response to frontend Airport interface
    return response.data.map((airport: any) => ({
      id: airport.id,
      iata_code: airport.iata_code,
      code: airport.iata_code, // For backward compatibility
      name: airport.name,
      city: airport.city,
      country: airport.country,
      timezone: airport.timezone,
      available_services: airport.available_services,
      starting_price: airport.starting_price,
      currency: airport.currency
    }))
  }

  async getAirport(id: string): Promise<Airport> {
    const response = await this.request<{success: boolean, data: Airport}>(`/airports/${id}`)
    return response.data
  }

  async searchAirports(query: string): Promise<Airport[]> {
    // Use available airports endpoint with search functionality
    const response = await this.request<{success: boolean, data: Airport[]}>(`/airports/available`)
    const airports = response.data

    // Filter client-side for now (can be optimized later)
    return airports.filter(airport =>
      airport.name.toLowerCase().includes(query.toLowerCase()) ||
      airport.code.toLowerCase().includes(query.toLowerCase()) ||
      airport.city.toLowerCase().includes(query.toLowerCase())
    )
  }

  async getPopularAirports(): Promise<Airport[]> {
    // Use available airports endpoint to get only airports with active services
    const response = await this.request<{success: boolean, data: Airport[]}>('/airports/available')
    return response.data.slice(0, 8) // Return top 8 airports
  }

  // Terminal endpoints
  async getTerminals(airportCode: string, params?: {
    type?: string;
    passengers?: number;
  }): Promise<Terminal[]> {
    try {
      const searchParams = new URLSearchParams()
      if (params?.type) searchParams.append('type', params.type)
      if (params?.passengers) searchParams.append('passengers', params.passengers.toString())

      const response = await this.request<{success: boolean, data: Terminal[]}>(`/airports/${airportCode}/terminals?${searchParams.toString()}`)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch terminals for airport ${airportCode}:`, error)
      return []
    }
  }

  // Service endpoints - Updated for dynamic filtering
  async getServices(airportCode: string, params?: {
    category?: string;
    type?: string;
    passengers?: number;
    terminal?: string;
  }): Promise<Service[]> {
    const searchParams = new URLSearchParams()
    if (params?.category) searchParams.append('category', params.category)
    if (params?.type) searchParams.append('type', params.type)
    if (params?.passengers) searchParams.append('passengers', params.passengers.toString())

    // Use terminal-specific endpoint if terminal is provided
    const endpoint = params?.terminal
      ? `/airports/${airportCode}/terminals/${params.terminal}/services?${searchParams.toString()}`
      : `/airports/${airportCode}/services?${searchParams.toString()}`

    const response = await this.request<{success: boolean, data: Service[]}>(endpoint)
    return response.data
  }

  async getService(id: string): Promise<Service> {
    const response = await this.request<{success: boolean, data: Service}>(`/services/${id}`)
    return response.data
  }

  async getAvailableServices(params?: {
    airport?: string;
    type?: string;
    passengers?: number;
    category?: string;
  }): Promise<Service[]> {
    try {
      const searchParams = new URLSearchParams()
      if (params?.airport) searchParams.append('airport', params.airport)
      if (params?.type) searchParams.append('type', params.type)
      if (params?.passengers) searchParams.append('passengers', params.passengers.toString())
      if (params?.category) searchParams.append('category', params.category)

      const response = await this.request<{success: boolean, data: Service[]}>(`/services/available?${searchParams.toString()}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch available services:', error)
      return []
    }
  }

  // Booking endpoints
  async createBooking(bookingData: BookingRequest): Promise<BookingResponse> {
    return this.request<BookingResponse>('/bookings', {
      method: 'POST',
      body: JSON.stringify(bookingData),
    })
  }

  async getBookings(): Promise<BookingResponse[]> {
    return this.request<BookingResponse[]>('/bookings')
  }

  async getBooking(id: string): Promise<BookingResponse> {
    return this.request<BookingResponse>(`/bookings/${id}`)
  }

  async updateBooking(id: string, updates: Partial<BookingRequest>): Promise<BookingResponse> {
    return this.request<BookingResponse>(`/bookings/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    })
  }

  async cancelBooking(id: string): Promise<{ success: boolean }> {
    return this.request<{ success: boolean }>(`/bookings/${id}/cancel`, {
      method: 'POST',
    })
  }

  // Availability endpoints
  async checkAvailability(
    airportId: string,
    serviceIds: string[],
    datetime: string
  ): Promise<{
    available: boolean;
    totalCapacity: number;
    availableCapacity: number;
    timeSlots: Array<{
      time: string;
      available: boolean;
      capacity: number;
      booked: number;
      waitlist: number;
    }>;
    nextAvailableDate?: string;
    alternatives?: string[];
    lastUpdated: string;
    services: Array<{
      id: string;
      name: string;
      airport: string;
      airport_code: string;
    }>;
  }> {
    const response = await this.request<{
      success: boolean;
      data: {
        available: boolean;
        totalCapacity: number;
        availableCapacity: number;
        timeSlots: Array<{
          time: string;
          available: boolean;
          capacity: number;
          booked: number;
          waitlist: number;
        }>;
        nextAvailableDate?: string;
        lastUpdated: string;
        services: Array<{
          id: string;
          name: string;
          airport: string;
          airport_code: string;
        }>;
      };
    }>('/availability/check', {
      method: 'POST',
      body: JSON.stringify({
        airport_id: airportId,
        service_ids: serviceIds,
        datetime,
      }),
    });

    return response.data;
  }

  // Pricing endpoints
  async calculatePrice(
    serviceIds: string[],
    passengerCount: number,
    datetime: string
  ): Promise<{ total: number; currency: string; breakdown: any[] }> {
    return this.request<{ total: number; currency: string; breakdown: any[] }>('/pricing/calculate', {
      method: 'POST',
      body: JSON.stringify({
        service_ids: serviceIds,
        passenger_count: passengerCount,
        datetime,
      }),
    })
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Utility functions for common operations
export const airportService = {
  async searchAirports(query: string, filters?: {
    category?: string;
    type?: string;
    passengers?: number;
  }): Promise<Airport[]> {
    if (!query || query.length < 2) return []

    try {
      return await apiClient.searchAirports(query)
    } catch (error) {
      console.error('Airport search failed:', error)
      return []
    }
  },

  async getAvailableAirports(filters?: {
    category?: string;
    type?: string;
    passengers?: number;
  }): Promise<Airport[]> {
    try {
      return await apiClient.getAvailableAirports(filters)
    } catch (error) {
      console.error('Failed to fetch available airports:', error)
      return []
    }
  },

  async getPopularAirports(): Promise<Airport[]> {
    try {
      return await apiClient.getPopularAirports()
    } catch (error) {
      console.error('Failed to fetch popular airports:', error)
      return []
    }
  }
}

export const serviceService = {
  async getServicesForAirport(airportCode: string, filters?: {
    category?: string;
    type?: string;
    passengers?: number;
    terminal?: string;
  }): Promise<Service[]> {
    try {
      return await apiClient.getServices(airportCode, filters)
    } catch (error) {
      console.error('Failed to fetch services:', error)
      return []
    }
  }
}

export const flightService = {
  async lookupFlight(flightNumber: string, flightDate: string, serviceType: 'arrival' | 'departure'): Promise<{
    flight: any;
    time_slot_suggestions: any[];
  } | null> {
    try {
      const response = await apiClient.request<{
        success: boolean;
        data: {
          flight: any;
          time_slot_suggestions: any[];
        };
      }>('/flights/lookup', {
        method: 'POST',
        body: JSON.stringify({
          flight_number: flightNumber,
          flight_date: flightDate,
          service_type: serviceType
        })
      });

      return response.data;
    } catch (error) {
      console.error('Failed to lookup flight:', error);
      return null;
    }
  },

  async getFlightStatus(flightNumber: string, date?: string): Promise<any | null> {
    try {
      const params = new URLSearchParams();
      if (date) params.append('date', date);

      const response = await apiClient.request<{
        success: boolean;
        data: any;
      }>(`/flights/${flightNumber}/status?${params.toString()}`);

      return response.data;
    } catch (error) {
      console.error('Failed to get flight status:', error);
      return null;
    }
  }
}

export const bookingService = {
  async createBooking(bookingData: BookingRequest): Promise<BookingResponse> {
    try {
      return await apiClient.createBooking(bookingData)
    } catch (error) {
      console.error('Booking creation failed:', error)
      throw error
    }
  },

  async getUserBookings(): Promise<BookingResponse[]> {
    try {
      return await apiClient.getBookings()
    } catch (error) {
      console.error('Failed to fetch user bookings:', error)
      return []
    }
  }
}

// Mock data fallbacks
function getMockAirports(): Airport[] {
  return [
    { id: '1', code: 'JFK', name: 'John F. Kennedy International Airport', city: 'New York', country: 'USA' },
    { id: '2', code: 'LAX', name: 'Los Angeles International Airport', city: 'Los Angeles', country: 'USA' },
    { id: '3', code: 'LHR', name: 'London Heathrow Airport', city: 'London', country: 'UK' },
    { id: '4', code: 'CDG', name: 'Charles de Gaulle Airport', city: 'Paris', country: 'France' },
    { id: '5', code: 'DXB', name: 'Dubai International Airport', city: 'Dubai', country: 'UAE' },
    { id: '6', code: 'NRT', name: 'Narita International Airport', city: 'Tokyo', country: 'Japan' },
    { id: '7', code: 'SIN', name: 'Singapore Changi Airport', city: 'Singapore', country: 'Singapore' },
    { id: '8', code: 'FRA', name: 'Frankfurt Airport', city: 'Frankfurt', country: 'Germany' }
  ]
}

function getMockServices(): Service[] {
  return [
    {
      id: '1',
      name: 'Meet & Greet',
      description: 'Personal assistance from arrival to departure',
      base_price: '4150',
      currency: 'INR',
      duration_minutes: 60,
      category: 'assistance',
      type: 'arrival',
      max_passengers: 4,
      inclusions: ['Personal assistant', 'Baggage assistance', 'Navigation help'],
      requirements: ['Valid ID required']
    },
    {
      id: '2',
      name: 'Fast Track Security',
      description: 'Skip the regular security lines',
      base_price: '3320',
      currency: 'INR',
      duration_minutes: 15,
      category: 'security',
      type: 'departure',
      max_passengers: 2,
      inclusions: ['Priority security screening', 'Dedicated lanes', 'Time saving'],
      requirements: ['Valid boarding pass required']
    },
    {
      id: '3',
      name: 'VIP Lounge Access',
      description: 'Access to premium airport lounges',
      base_price: '6225',
      currency: 'INR',
      duration_minutes: 180,
      category: 'lounge',
      type: 'arrival',
      max_passengers: 6,
      inclusions: ['Premium lounge access', 'Food & beverages', 'WiFi & charging'],
      requirements: ['Valid boarding pass required']
    }
  ]
}
