// AviationStack API Integration
// Documentation: https://aviationstack.com/documentation

const AVIATIONSTACK_BASE_URL = 'https://api.aviationstack.com/v1'
const API_KEY = process.env.AVIATIONSTACK_API_KEY

export interface Airport {
  airport_name: string
  iata_code: string
  icao_code: string
  country_name: string
  country_iso2: string
  city_name: string
  timezone: string
  gmt: string
  latitude: number
  longitude: number
}

export interface Flight {
  status: string
  type: string
  departure: {
    iataCode: string
    icaoCode: string
    terminal: string
    gate: string
    delay: string | null
    scheduledTime: string
    estimatedTime: string | null
    actualTime: string | null
    estimatedRunway: string | null
    actualRunway: string | null
    baggage: string | null
  }
  arrival: {
    iataCode: string
    icaoCode: string
    terminal: string | null
    gate: string | null
    baggage: string | null
    delay: string | null
    scheduledTime: string
    estimatedTime: string | null
    actualTime: string | null
    estimatedRunway: string | null
    actualRunway: string | null
  }
  airline: {
    name: string
    iataCode: string
    icaoCode: string
  }
  flight: {
    number: string
    iataNumber: string
    icaoNumber: string
  }
  codeshared?: {
    airline: {
      iataCode: string
      icaoCode: string
      name: string
    }
    flight: {
      number: string
      iataNumber: string
      icaoNumber: string
    }
  }
  aircraft?: {
    registration: string
    iataCode: string
    icaoCode: string
  }
}

export interface AviationStackResponse<T> {
  pagination: {
    limit: number
    offset: number
    count: number
    total: number
  }
  data: T[]
}

class AviationStackAPI {
  private apiKey: string

  constructor() {
    this.apiKey = API_KEY || ''
    if (!this.apiKey) {
      console.warn('AviationStack API key not found. Flight data will be limited.')
    }
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, string> = {}): Promise<AviationStackResponse<T>> {
    if (!this.apiKey) {
      throw new Error('AviationStack API key not configured')
    }

    const url = new URL(`${AVIATIONSTACK_BASE_URL}/${endpoint}`)
    url.searchParams.append('access_key', this.apiKey)
    
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value)
    })

    try {
      const response = await fetch(url.toString())
      
      if (!response.ok) {
        throw new Error(`AviationStack API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(`AviationStack API error: ${data.error.message}`)
      }

      return data
    } catch (error) {
      console.error('AviationStack API request failed:', error)
      throw error
    }
  }

  // Get airports by search query
  async searchAirports(query: string, limit: number = 10): Promise<Airport[]> {
    try {
      const response = await this.makeRequest<Airport>('airports', {
        search: query,
        limit: limit.toString()
      })
      return response.data
    } catch (error) {
      console.error('Failed to search airports:', error)
      return []
    }
  }

  // Get airport by IATA code
  async getAirportByIATA(iataCode: string): Promise<Airport | null> {
    try {
      const response = await this.makeRequest<Airport>('airports', {
        iata_code: iataCode
      })
      return response.data[0] || null
    } catch (error) {
      console.error('Failed to get airport by IATA:', error)
      return null
    }
  }

  // Get flights by airport using timetable endpoint with comprehensive filtering
  async getFlightsByAirport(
    iataCode: string,
    type: 'departure' | 'arrival' = 'departure',
    options: {
      limit?: number
      status?: 'landed' | 'scheduled' | 'cancelled' | 'active' | 'incident' | 'diverted' | 'redirected' | 'unknown'
      airline_iata?: string
      flight_iata?: string
      flight_num?: string
      dep_schTime?: string
      arr_schTime?: string
    } = {}
  ): Promise<Flight[]> {
    try {
      const params: Record<string, string> = {
        iataCode: iataCode,
        type: type
      }

      // Add optional parameters
      if (options.status) params.status = options.status
      if (options.airline_iata) params.airline_iata = options.airline_iata
      if (options.flight_iata) params.flight_iata = options.flight_iata
      if (options.flight_num) params.flight_num = options.flight_num
      if (options.dep_schTime) params.dep_schTime = options.dep_schTime
      if (options.arr_schTime) params.arr_schTime = options.arr_schTime

      const response = await this.makeRequest<Flight>('timetable', params)
      const limit = options.limit || 20
      return response.data.slice(0, limit) // Limit results client-side
    } catch (error) {
      console.error('Failed to get flights by airport:', error)
      throw error // Re-throw to handle in API route
    }
  }

  // Search flights by flight number using timetable endpoint
  async searchFlightByNumber(
    flightNumber: string,
    iataCode?: string,
    type: 'departure' | 'arrival' = 'departure'
  ): Promise<Flight[]> {
    try {
      // If no airport specified, try major airports
      const airports = iataCode ? [iataCode] : ['DXB', 'DEL', 'BOM', 'BLR', 'SIN']
      const allFlights: Flight[] = []

      for (const airport of airports) {
        try {
          const flights = await this.getFlightsByAirport(airport, type, {
            flight_iata: flightNumber,
            limit: 10
          })
          allFlights.push(...flights)
        } catch (error) {
          console.log(`No flights found for ${flightNumber} at ${airport}`)
        }
      }

      return allFlights
    } catch (error) {
      console.error('Failed to search flight by number:', error)
      throw error
    }
  }

  // Get flights by route (departure and arrival airports)
  async getFlightsByRoute(
    departureIATA: string,
    arrivalIATA: string,
    date?: string,
    limit: number = 20
  ): Promise<Flight[]> {
    try {
      // First get departures from departure airport
      const departureFlights = await this.getFlightsByAirport(departureIATA, 'departure', limit)

      // Filter flights that go to the arrival airport
      const routeFlights = departureFlights.filter(flight =>
        flight.arrival && flight.arrival.iata === arrivalIATA
      )

      return routeFlights
    } catch (error) {
      console.error('Failed to get flights by route:', error)
      return []
    }
  }

  // Get live flights by airline
  async getFlightsByAirline(airlineIATA: string, limit: number = 20): Promise<Flight[]> {
    try {
      const response = await this.makeRequest<Flight>('flights', {
        airline_iata: airlineIATA,
        limit: limit.toString()
      })
      return response.data
    } catch (error) {
      console.error('Failed to get flights by airline:', error)
      return []
    }
  }

  // Get flight by flight number
  async getFlightByNumber(flightNumber: string, date?: string): Promise<Flight[]> {
    try {
      const params: Record<string, string> = {
        flight_iata: flightNumber
      }

      if (date) {
        params.flight_date = date
      }

      const response = await this.makeRequest<Flight>('flights', params)
      return response.data
    } catch (error) {
      console.error('Failed to get flight by number:', error)
      return []
    }
  }


}

export const aviationStackAPI = new AviationStackAPI()
export default aviationStackAPI
