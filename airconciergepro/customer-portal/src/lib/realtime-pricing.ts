'use client'

import { apiClient } from './api'
import { wsService, PriceUpdate } from './websocket'

export interface PricingContext {
  serviceIds: string[]
  airportId: string
  date: string
  time: string
  passengerCount: number
  customerGroupId?: string
}

export interface PriceBreakdown {
  serviceId: string
  serviceName: string
  basePrice: number
  adjustments: Array<{
    type: 'surge' | 'discount' | 'group' | 'time_based' | 'demand_based'
    name: string
    amount: number
    percentage?: number
    reason: string
  }>
  finalPrice: number
}

export interface PricingResult {
  subtotal: number
  adjustments: Array<{
    type: string
    name: string
    amount: number
    reason: string
  }>
  total: number
  currency: string
  breakdown: PriceBreakdown[]
  validUntil: string
  lastUpdated: string
}

export class RealTimePricingService {
  private priceCache = new Map<string, PricingResult>()
  private subscriptions = new Map<string, () => void>()
  private updateCallbacks = new Map<string, Set<(result: PricingResult) => void>>()

  constructor() {
    // Subscribe to price updates from WebSocket
    wsService.subscribe('price_update', this.handlePriceUpdate.bind(this))
  }

  private getCacheKey(context: PricingContext): string {
    return `${context.airportId}-${context.serviceIds.join(',')}-${context.date}-${context.time}-${context.passengerCount}`
  }

  private handlePriceUpdate(update: PriceUpdate) {
    // Find relevant cached prices and update them
    const entries = Array.from(this.priceCache.entries())
    for (const [key, cachedResult] of entries) {
      if (key.includes(update.serviceId) && key.includes(update.airportId)) {
        // Update the cached result with new pricing
        const updatedResult = this.applyPriceUpdate(cachedResult, update)
        this.priceCache.set(key, updatedResult)
        
        // Notify subscribers
        const callbacks = this.updateCallbacks.get(key)
        if (callbacks) {
          callbacks.forEach(callback => callback(updatedResult))
        }
      }
    }
  }

  private applyPriceUpdate(cachedResult: PricingResult, update: PriceUpdate): PricingResult {
    const updatedBreakdown = cachedResult.breakdown.map(item => {
      if (item.serviceId === update.serviceId) {
        return {
          ...item,
          finalPrice: update.dynamicPrice,
          adjustments: update.adjustments.map(adj => ({
            type: adj.type as any,
            name: adj.reason,
            amount: adj.amount,
            reason: adj.reason
          }))
        }
      }
      return item
    })

    const newSubtotal = updatedBreakdown.reduce((sum, item) => sum + item.basePrice, 0)
    const newTotal = updatedBreakdown.reduce((sum, item) => sum + item.finalPrice, 0)

    return {
      ...cachedResult,
      subtotal: newSubtotal,
      total: newTotal,
      breakdown: updatedBreakdown,
      lastUpdated: new Date().toISOString()
    }
  }

  async calculatePrice(context: PricingContext): Promise<PricingResult> {
    const cacheKey = this.getCacheKey(context)
    
    // Check cache first
    const cached = this.priceCache.get(cacheKey)
    if (cached && this.isCacheValid(cached)) {
      return cached
    }

    try {
      // Call API for initial pricing
      const response = await apiClient.calculatePrice(
        context.serviceIds,
        context.passengerCount,
        `${context.date}T${context.time}`
      )

      // Transform API response to our format
      const result: PricingResult = {
        subtotal: response.total,
        adjustments: [],
        total: response.total,
        currency: response.currency,
        breakdown: response.breakdown.map((item: any) => ({
          serviceId: item.serviceId,
          serviceName: item.serviceName,
          basePrice: item.basePrice,
          adjustments: item.adjustments || [],
          finalPrice: item.finalPrice
        })),
        validUntil: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // Valid for 5 minutes
        lastUpdated: new Date().toISOString()
      }

      // Cache the result
      this.priceCache.set(cacheKey, result)

      // Subscribe to real-time updates for this pricing context
      this.subscribeToUpdates(context)

      return result
    } catch (error) {
      console.error('Failed to calculate price:', error)
      
      // Return fallback pricing if API fails
      return this.getFallbackPricing(context)
    }
  }

  private isCacheValid(result: PricingResult): boolean {
    return new Date(result.validUntil) > new Date()
  }

  private subscribeToUpdates(context: PricingContext) {
    const cacheKey = this.getCacheKey(context)
    
    // Don't subscribe if already subscribed
    if (this.subscriptions.has(cacheKey)) {
      return
    }

    // Subscribe to price updates for each service
    context.serviceIds.forEach(serviceId => {
      wsService.requestPriceUpdates(serviceId, context.airportId, context.date, context.passengerCount)
    })

    // Mark as subscribed
    this.subscriptions.set(cacheKey, () => {
      // Cleanup function if needed
    })
  }

  private getFallbackPricing(context: PricingContext): PricingResult {
    // Fallback pricing when API is unavailable
    const basePrice = 50 // Default base price per service
    const breakdown: PriceBreakdown[] = context.serviceIds.map((serviceId, index) => ({
      serviceId,
      serviceName: `Service ${index + 1}`,
      basePrice,
      adjustments: [],
      finalPrice: basePrice
    }))

    const subtotal = breakdown.reduce((sum, item) => sum + item.basePrice, 0)
    const total = subtotal * context.passengerCount

    return {
      subtotal,
      adjustments: [],
      total,
      currency: 'USD',
      breakdown,
      validUntil: new Date(Date.now() + 2 * 60 * 1000).toISOString(), // Valid for 2 minutes
      lastUpdated: new Date().toISOString()
    }
  }

  // Subscribe to price updates for a specific context
  subscribeToPriceUpdates(context: PricingContext, callback: (result: PricingResult) => void): () => void {
    const cacheKey = this.getCacheKey(context)
    
    if (!this.updateCallbacks.has(cacheKey)) {
      this.updateCallbacks.set(cacheKey, new Set())
    }
    
    this.updateCallbacks.get(cacheKey)!.add(callback)

    // Return unsubscribe function
    return () => {
      const callbacks = this.updateCallbacks.get(cacheKey)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.updateCallbacks.delete(cacheKey)
          // Also cleanup subscription
          const unsubscribe = this.subscriptions.get(cacheKey)
          if (unsubscribe) {
            unsubscribe()
            this.subscriptions.delete(cacheKey)
          }
        }
      }
    }
  }

  // Get cached price if available
  getCachedPrice(context: PricingContext): PricingResult | null {
    const cacheKey = this.getCacheKey(context)
    const cached = this.priceCache.get(cacheKey)
    
    if (cached && this.isCacheValid(cached)) {
      return cached
    }
    
    return null
  }

  // Clear cache for specific context
  clearCache(context?: PricingContext) {
    if (context) {
      const cacheKey = this.getCacheKey(context)
      this.priceCache.delete(cacheKey)
      
      // Cleanup subscriptions
      const unsubscribe = this.subscriptions.get(cacheKey)
      if (unsubscribe) {
        unsubscribe()
        this.subscriptions.delete(cacheKey)
      }
      
      this.updateCallbacks.delete(cacheKey)
    } else {
      // Clear all cache
      this.priceCache.clear()
      this.subscriptions.forEach(unsubscribe => unsubscribe())
      this.subscriptions.clear()
      this.updateCallbacks.clear()
    }
  }

  // Get pricing statistics
  getPricingStats() {
    return {
      cacheSize: this.priceCache.size,
      activeSubscriptions: this.subscriptions.size,
      activeCallbacks: Array.from(this.updateCallbacks.values()).reduce((sum, set) => sum + set.size, 0)
    }
  }
}

// Create singleton instance
export const realTimePricingService = new RealTimePricingService()

// Utility function for formatting prices
export function formatPrice(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount)
}

// Utility function for calculating price difference
export function calculatePriceDifference(oldPrice: number, newPrice: number): {
  amount: number
  percentage: number
  direction: 'increase' | 'decrease' | 'same'
} {
  const amount = newPrice - oldPrice
  const percentage = oldPrice > 0 ? (amount / oldPrice) * 100 : 0
  
  let direction: 'increase' | 'decrease' | 'same'
  if (amount > 0) direction = 'increase'
  else if (amount < 0) direction = 'decrease'
  else direction = 'same'

  return { amount, percentage, direction }
}
