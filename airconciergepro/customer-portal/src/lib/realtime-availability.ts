'use client'

import { apiClient } from './api'
import { wsService, AvailabilityUpdate } from './websocket'

export interface AvailabilityContext {
  airportId: string
  serviceIds: string[]
  date: string
  passengerCount: number
}

export interface TimeSlot {
  time: string
  available: boolean
  capacity: number
  booked: number
  waitlist: number
  price?: number
}

export interface AvailabilityResult {
  date: string
  available: boolean
  totalCapacity: number
  availableCapacity: number
  timeSlots: TimeSlot[]
  nextAvailableDate?: string
  nextAvailableTime?: string
  restrictions?: string[]
  lastUpdated: string
}

export class RealTimeAvailabilityService {
  private availabilityCache = new Map<string, AvailabilityResult>()
  private subscriptions = new Map<string, () => void>()
  private updateCallbacks = new Map<string, Set<(result: AvailabilityResult) => void>>()
  private pollingIntervals = new Map<string, NodeJS.Timeout>()

  constructor() {
    // Subscribe to availability updates from WebSocket
    wsService.subscribe('availability_update', this.handleAvailabilityUpdate.bind(this))
  }

  private getCacheKey(context: AvailabilityContext): string {
    return `${context.airportId}-${context.serviceIds.join(',')}-${context.date}-${context.passengerCount}`
  }

  private handleAvailabilityUpdate(update: AvailabilityUpdate) {
    // Find relevant cached availability and update it
    const entries = Array.from(this.availabilityCache.entries())
    for (const [key, cachedResult] of entries) {
      if (key.includes(update.airportId) && key.includes(update.serviceId) && key.includes(update.date)) {
        const updatedResult = this.applyAvailabilityUpdate(cachedResult, update)
        this.availabilityCache.set(key, updatedResult)

        // Notify subscribers
        const callbacks = this.updateCallbacks.get(key)
        if (callbacks) {
          callbacks.forEach(callback => callback(updatedResult))
        }
      }
    }
  }

  private applyAvailabilityUpdate(cachedResult: AvailabilityResult, update: AvailabilityUpdate): AvailabilityResult {
    return {
      ...cachedResult,
      availableCapacity: update.availableSlots,
      totalCapacity: update.totalSlots,
      available: update.availableSlots > 0,
      nextAvailableTime: update.nextAvailableTime,
      lastUpdated: new Date().toISOString(),
      timeSlots: cachedResult.timeSlots.map(slot => {
        // Update time slots based on the update
        // This is a simplified update - in reality, you'd have more specific slot data
        return {
          ...slot,
          available: slot.available && update.availableSlots > 0
        }
      })
    }
  }

  async checkAvailability(context: AvailabilityContext): Promise<AvailabilityResult> {
    const cacheKey = this.getCacheKey(context)
    
    // Check cache first (availability data is valid for shorter time)
    const cached = this.availabilityCache.get(cacheKey)
    if (cached && this.isCacheValid(cached)) {
      return cached
    }

    try {
      // Call API for availability check
      const response = await apiClient.checkAvailability(
        context.airportId,
        context.serviceIds,
        context.date
      )

      const result: AvailabilityResult = {
        date: context.date,
        available: response.available,
        totalCapacity: response.totalCapacity,
        availableCapacity: response.availableCapacity,
        timeSlots: response.timeSlots,
        nextAvailableDate: response.nextAvailableDate,
        lastUpdated: response.lastUpdated
      }

      // Cache the result (shorter cache time for availability)
      this.availabilityCache.set(cacheKey, result)

      // Subscribe to real-time updates
      this.subscribeToUpdates(context)

      return result
    } catch (error) {
      console.error('Failed to check availability:', error)
      return this.getFallbackAvailability(context)
    }
  }

  private isCacheValid(result: AvailabilityResult): boolean {
    const cacheAge = Date.now() - new Date(result.lastUpdated).getTime()
    return cacheAge < 2 * 60 * 1000 // Cache valid for 2 minutes
  }

  private generateTimeSlots(date: string): TimeSlot[] {
    const slots: TimeSlot[] = []
    const startHour = 6
    const endHour = 22
    
    for (let hour = startHour; hour <= endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        
        // Mock availability logic
        const capacity = 10
        const booked = Math.floor(Math.random() * capacity)
        const available = booked < capacity
        
        slots.push({
          time,
          available,
          capacity,
          booked,
          waitlist: available ? 0 : Math.floor(Math.random() * 5)
        })
      }
    }
    
    return slots
  }

  private subscribeToUpdates(context: AvailabilityContext) {
    const cacheKey = this.getCacheKey(context)
    
    // Don't subscribe if already subscribed
    if (this.subscriptions.has(cacheKey)) {
      return
    }

    // Subscribe to availability updates for each service
    context.serviceIds.forEach(serviceId => {
      wsService.requestAvailabilityUpdates(context.airportId, serviceId, context.date)
    })

    // Start polling for updates (fallback if WebSocket fails)
    const pollingInterval = setInterval(() => {
      this.refreshAvailability(context)
    }, 30000) // Poll every 30 seconds

    this.pollingIntervals.set(cacheKey, pollingInterval)

    // Mark as subscribed
    this.subscriptions.set(cacheKey, () => {
      const interval = this.pollingIntervals.get(cacheKey)
      if (interval) {
        clearInterval(interval)
        this.pollingIntervals.delete(cacheKey)
      }
    })
  }

  private async refreshAvailability(context: AvailabilityContext) {
    try {
      const cacheKey = this.getCacheKey(context)
      const response = await apiClient.checkAvailability(
        context.airportId,
        context.serviceIds,
        context.date
      )

      const updated: AvailabilityResult = {
        date: context.date,
        available: response.available,
        totalCapacity: response.totalCapacity,
        availableCapacity: response.availableCapacity,
        timeSlots: response.timeSlots,
        nextAvailableDate: response.nextAvailableDate,
        lastUpdated: response.lastUpdated
      }

      this.availabilityCache.set(cacheKey, updated)

      // Notify subscribers
      const callbacks = this.updateCallbacks.get(cacheKey)
      if (callbacks) {
        callbacks.forEach(callback => callback(updated))
      }
    } catch (error) {
      console.error('Failed to refresh availability:', error)
    }
  }

  private getFallbackAvailability(context: AvailabilityContext): AvailabilityResult {
    const timeSlots = this.generateTimeSlots(context.date)
    const availableSlots = timeSlots.filter(slot => slot.available).length
    
    return {
      date: context.date,
      available: availableSlots > 0,
      totalCapacity: timeSlots.length * 10,
      availableCapacity: availableSlots * 5,
      timeSlots,
      lastUpdated: new Date().toISOString()
    }
  }

  // Subscribe to availability updates for a specific context
  subscribeToAvailabilityUpdates(
    context: AvailabilityContext, 
    callback: (result: AvailabilityResult) => void
  ): () => void {
    const cacheKey = this.getCacheKey(context)
    
    if (!this.updateCallbacks.has(cacheKey)) {
      this.updateCallbacks.set(cacheKey, new Set())
    }
    
    this.updateCallbacks.get(cacheKey)!.add(callback)

    // Return unsubscribe function
    return () => {
      const callbacks = this.updateCallbacks.get(cacheKey)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.updateCallbacks.delete(cacheKey)
          // Also cleanup subscription
          const unsubscribe = this.subscriptions.get(cacheKey)
          if (unsubscribe) {
            unsubscribe()
            this.subscriptions.delete(cacheKey)
          }
        }
      }
    }
  }

  // Get cached availability if available
  getCachedAvailability(context: AvailabilityContext): AvailabilityResult | null {
    const cacheKey = this.getCacheKey(context)
    const cached = this.availabilityCache.get(cacheKey)
    
    if (cached && this.isCacheValid(cached)) {
      return cached
    }
    
    return null
  }

  // Get available time slots for a specific date
  getAvailableTimeSlots(context: AvailabilityContext): Promise<TimeSlot[]> {
    return this.checkAvailability(context).then(result => 
      result.timeSlots.filter(slot => slot.available)
    )
  }

  // Check if a specific time slot is available
  async isTimeSlotAvailable(
    context: AvailabilityContext, 
    time: string
  ): Promise<boolean> {
    const result = await this.checkAvailability(context)
    const slot = result.timeSlots.find(s => s.time === time)
    return slot?.available || false
  }

  // Get next available time slot
  async getNextAvailableSlot(context: AvailabilityContext): Promise<TimeSlot | null> {
    const result = await this.checkAvailability(context)
    return result.timeSlots.find(slot => slot.available) || null
  }

  // Clear cache for specific context
  clearCache(context?: AvailabilityContext) {
    if (context) {
      const cacheKey = this.getCacheKey(context)
      this.availabilityCache.delete(cacheKey)
      
      // Cleanup subscriptions
      const unsubscribe = this.subscriptions.get(cacheKey)
      if (unsubscribe) {
        unsubscribe()
        this.subscriptions.delete(cacheKey)
      }
      
      this.updateCallbacks.delete(cacheKey)
    } else {
      // Clear all cache
      this.availabilityCache.clear()
      this.subscriptions.forEach(unsubscribe => unsubscribe())
      this.subscriptions.clear()
      this.updateCallbacks.clear()
      this.pollingIntervals.forEach(interval => clearInterval(interval))
      this.pollingIntervals.clear()
    }
  }

  // Get availability statistics
  getAvailabilityStats() {
    return {
      cacheSize: this.availabilityCache.size,
      activeSubscriptions: this.subscriptions.size,
      activeCallbacks: Array.from(this.updateCallbacks.values()).reduce((sum, set) => sum + set.size, 0),
      pollingIntervals: this.pollingIntervals.size
    }
  }
}

// Create singleton instance
export const realTimeAvailabilityService = new RealTimeAvailabilityService()

// Utility function for formatting availability status
export function formatAvailabilityStatus(result: AvailabilityResult): string {
  if (result.available) {
    return `${result.availableCapacity} of ${result.totalCapacity} slots available`
  } else if (result.nextAvailableDate) {
    return `Next available: ${result.nextAvailableDate}`
  } else {
    return 'No availability'
  }
}
