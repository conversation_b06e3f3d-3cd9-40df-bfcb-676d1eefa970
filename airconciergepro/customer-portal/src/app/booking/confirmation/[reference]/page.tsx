'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { CheckCircle, Download, Mail, Calendar, MapPin, Users, Clock, Phone, ArrowLeft, Share2 } from 'lucide-react'
import Link from 'next/link'
import DigitalVoucher from '@/components/booking/DigitalVoucher'
import BookingDetails from '@/components/booking/BookingDetails'

interface BookingData {
  id: string
  reference: string
  airport: string
  airportId: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
  flightNumber?: string
  customerName: string
  customerEmail: string
  customerPhone: string
  status: 'confirmed' | 'pending' | 'cancelled'
  createdAt: string
  paymentIntentId: string
}

export default function BookingConfirmationPage() {
  const params = useParams()
  const reference = params?.reference as string
  
  const [booking, setBooking] = useState<BookingData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [emailResent, setEmailResent] = useState(false)
  const [showVoucher, setShowVoucher] = useState(false)

  useEffect(() => {
    const fetchBooking = async () => {
      try {
        const response = await fetch(`/api/bookings/${reference}`, {
          headers: {
            'x-tenant-id': '37de875f-170d-47e5-8f38-8c36b2112475' // TODO: Get from environment or context
          }
        })
        if (!response.ok) {
          throw new Error('Booking not found')
        }
        const bookingData = await response.json()
        console.log('Booking API response:', bookingData)

        // Extract booking from the API response structure
        if (bookingData.success && bookingData.data && bookingData.data.booking) {
          setBooking(bookingData.data.booking)
        } else {
          throw new Error('Invalid booking data structure')
        }
      } catch (error) {
        console.error('Failed to fetch booking:', error)
        setError('Failed to load booking details. Please check your booking reference.')
      } finally {
        setLoading(false)
      }
    }

    if (reference) {
      fetchBooking()
    }
  }, [reference])

  const handleResendEmail = async () => {
    if (!booking) return

    try {
      const response = await fetch('/api/send-confirmation-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_reference: booking.reference,
          payment_intent_id: booking.paymentIntentId,
          booking_data: {
            airport: booking.airport,
            serviceType: booking.serviceType,
            services: booking.services,
            date: booking.date,
            time: booking.time,
            passengers: booking.passengers,
            totalAmount: booking.totalAmount,
            currency: booking.currency,
            flightNumber: booking.flightNumber,
          },
          customer_email: booking.customerEmail,
          customer_name: booking.customerName,
        }),
      })

      if (response.ok) {
        setEmailResent(true)
        setTimeout(() => setEmailResent(false), 3000)
      }
    } catch (error) {
      console.error('Failed to resend email:', error)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Booking Confirmation - ${reference}`,
          text: `My AirConcierge Pro booking confirmation for ${booking?.airport}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading booking details...</div>
        </div>
      </div>
    )
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Booking Not Found</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/"
            className="inline-flex items-center space-x-2 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Home</span>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Booking Confirmed!
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            Your booking reference is <span className="font-mono font-bold text-primary-600">{booking.reference}</span>
          </p>
          <div className="flex items-center justify-center space-x-4">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              booking.status === 'confirmed' 
                ? 'bg-green-100 text-green-800'
                : booking.status === 'pending'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {booking.status ? booking.status.charAt(0).toUpperCase() + booking.status.slice(1) : 'Unknown'}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap items-center justify-center gap-4 mb-8">
          <button
            onClick={() => setShowVoucher(true)}
            className="flex items-center space-x-2 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
          >
            <Download className="h-5 w-5" />
            <span>Download Voucher</span>
          </button>
          
          <button
            onClick={handleResendEmail}
            className="flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Mail className="h-5 w-5" />
            <span>{emailResent ? 'Email Sent!' : 'Resend Email'}</span>
          </button>

          <button
            onClick={handleShare}
            className="flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Share2 className="h-5 w-5" />
            <span>Share</span>
          </button>

          <Link
            href="/my-account"
            className="flex items-center space-x-2 bg-gray-100 text-gray-900 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Calendar className="h-5 w-5" />
            <span>My Bookings</span>
          </Link>
        </div>

        {/* Booking Details */}
        <BookingDetails booking={booking} />

        {/* Digital Voucher Modal */}
        {showVoucher && (
          <DigitalVoucher
            booking={booking}
            onClose={() => setShowVoucher(false)}
          />
        )}
      </div>
    </div>
  )
}
