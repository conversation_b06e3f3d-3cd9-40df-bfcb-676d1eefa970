import { NextResponse } from 'next/server'
import { aviationStackAPI } from '@/lib/aviationstack'

export async function GET() {
  try {
    // Try to get airports from API first
    const majorAirportCodes = ['DEL', 'BOM', 'BLR', 'DXB', 'SIN', 'LHR', 'JFK', 'LAX']
    const airports = []

    for (const code of majorAirportCodes) {
      try {
        const airport = await aviationStackAPI.getAirportByIATA(code)
        if (airport) {
          airports.push(airport)
        }
      } catch (error) {
        console.log(`Failed to fetch airport ${code}:`, error.message)
      }
    }

    if (airports.length > 0) {
      return NextResponse.json({
        success: true,
        data: airports,
        pagination: {
          limit: airports.length,
          count: airports.length
        }
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Airport data service temporarily unavailable. Please try again later.'
        },
        { status: 503 }
      )
    }
  } catch (error) {
    console.error('Popular airports error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Airport data service temporarily unavailable. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 503 }
    )
  }
}
