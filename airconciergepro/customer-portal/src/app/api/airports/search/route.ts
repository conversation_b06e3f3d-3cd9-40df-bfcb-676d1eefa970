import { NextRequest, NextResponse } from 'next/server'
import { aviationStackAPI } from '@/lib/aviationstack'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Search query is required' },
        { status: 400 }
      )
    }

    // Search airports using AviationStack API
    const airports = await aviationStackAPI.searchAirports(query, limit)

    return NextResponse.json({
      success: true,
      data: airports,
      pagination: {
        limit,
        count: airports.length
      }
    })
  } catch (error) {
    console.error('Airport search API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Airport search service temporarily unavailable. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 503 }
    )
  }
}
