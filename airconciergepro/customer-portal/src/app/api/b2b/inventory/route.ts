import { NextRequest, NextResponse } from 'next/server'

const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000'

export async function GET(request: NextRequest) {
  try {
    const apiKey = request.headers.get('X-API-Key')

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const airport = searchParams.get('airport')
    const type = searchParams.get('type')
    const date = searchParams.get('date')
    const passengers = searchParams.get('passengers')

    // Build query string for backend API
    const queryParams = new URLSearchParams()
    if (airport) queryParams.append('airport', airport)
    if (type) queryParams.append('type', type)
    if (date) queryParams.append('date', date)
    if (passengers) queryParams.append('passengers', passengers)

    // Call the backend partner inventory endpoint
    const response = await fetch(`${BACKEND_API_URL}/api/partner/v1/inventory?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json',
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to fetch inventory' },
        { status: response.status }
      )
    }

    // Return the inventory data
    return NextResponse.json({
      success: true,
      services: data.data || []
    })

  } catch (error) {
    console.error('B2B inventory error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
