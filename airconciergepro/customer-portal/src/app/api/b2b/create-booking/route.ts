import { NextRequest, NextResponse } from 'next/server'

const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000'

export async function POST(request: NextRequest) {
  try {
    const apiKey = request.headers.get('X-API-Key')

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 401 }
      )
    }

    const bookingData = await request.json()

    // Generate a unique partner booking ID
    const partnerBookingId = `PB-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Get primary passenger for customer details
    const primaryPassenger = bookingData.passengers[0]

    // Transform the data to match the backend API format
    const backendPayload = {
      partnerBookingId: partnerBookingId,
      customerDetails: {
        email: `${primaryPassenger.firstName.toLowerCase()}.${primaryPassenger.lastName.toLowerCase()}@partner.booking`,
        firstName: primaryPassenger.firstName,
        lastName: primaryPassenger.lastName,
        phone: primaryPassenger.contactNumber || '******-000-0000'
      },
      serviceId: bookingData.serviceId,
      flightNumber: bookingData.flightNumber,
      airline: bookingData.airline,
      departureAirport: bookingData.departureAirport,
      arrivalAirport: bookingData.arrivalAirport,
      flightDate: new Date(bookingData.flightDate).toISOString(),
      estimatedArrival: bookingData.estimatedArrival ? new Date(bookingData.estimatedArrival).toISOString() : undefined,
      serviceType: bookingData.serviceType,
      passengerCount: bookingData.passengerCount,
      passengers: bookingData.passengers.map((passenger: any) => ({
        firstName: passenger.firstName,
        lastName: passenger.lastName,
        age: passenger.age,
        specialRequirements: passenger.specialRequirements || []
      })),
      specialRequirements: bookingData.specialRequirements || [],
      meetingPoint: bookingData.meetingPoint
    }

    // Call the backend partner booking endpoint
    const response = await fetch(`${BACKEND_API_URL}/api/partner/v1/book`, {
      method: 'POST',
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(backendPayload)
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to create booking' },
        { status: response.status }
      )
    }

    // Return the booking data
    return NextResponse.json({
      success: true,
      booking_reference: data.data.bookingReference,
      booking_id: data.data.bookingId,
      partner_booking_id: data.data.partnerBookingId,
      status: data.data.status,
      total_price: data.data.totalPrice,
      currency: data.data.currency
    })

  } catch (error) {
    console.error('B2B create booking error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
