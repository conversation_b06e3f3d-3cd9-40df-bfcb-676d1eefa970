import { NextRequest, NextResponse } from 'next/server'

const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000'

export async function GET(request: NextRequest) {
  try {
    const apiKey = request.headers.get('X-API-Key')

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '20'
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    // Build query string for backend API
    const queryParams = new URLSearchParams()
    queryParams.append('page', page)
    queryParams.append('limit', limit)
    if (status) queryParams.append('status', status)
    if (search) queryParams.append('search', search)

    // Call the backend partner bookings endpoint
    const response = await fetch(`${BACKEND_API_URL}/api/partner/v1/bookings?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json',
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to fetch bookings' },
        { status: response.status }
      )
    }

    // Return the bookings data
    return NextResponse.json({
      success: true,
      bookings: data.data || [],
      pagination: data.pagination || {}
    })

  } catch (error) {
    console.error('B2B bookings error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
