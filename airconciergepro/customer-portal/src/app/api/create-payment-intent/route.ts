import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'

// Check if Stripe secret key is available
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('STRIPE_SECRET_KEY environment variable is not set')
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder', {
  apiVersion: '2023-10-16',
})

export async function POST(request: NextRequest) {
  try {
    const { amount, currency, provider = 'stripe', payment_method, booking_data } = await request.json()

    // Validate required fields
    if (!amount || !currency || !booking_data) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate provider
    if (!['stripe', 'razorpay'].includes(provider)) {
      return NextResponse.json(
        { error: 'Invalid payment provider' },
        { status: 400 }
      )
    }

    // Handle different providers
    if (provider === 'stripe') {
      // Check if Stripe is properly configured
      if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === 'sk_test_placeholder') {
        return NextResponse.json(
          { error: 'Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.' },
          { status: 500 }
        )
      }

      // Create Stripe payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount), // Ensure amount is an integer
        currency: currency.toLowerCase(),
        payment_method: payment_method,
        confirmation_method: payment_method ? 'manual' : 'automatic',
        confirm: !!payment_method,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/booking/success`,
        metadata: {
          booking_airport: booking_data.airport,
          booking_service_type: booking_data.serviceType,
          booking_services: booking_data.services.join(','),
          booking_date: booking_data.date,
          booking_time: booking_data.time,
          booking_passengers: booking_data.passengers.toString(),
          booking_flight_number: booking_data.flightNumber || '',
        },
      })

      return handleStripeResponse(paymentIntent)
    } else if (provider === 'razorpay') {
      // For Razorpay, we'll proxy to the backend
      const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_CUSTOMER_API_URL}/payments/create-payment-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': 'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff', // Demo tenant
        },
        body: JSON.stringify({
          amount: amount, // Razorpay uses main currency unit directly
          currency,
          provider: 'razorpay',
          booking_data,
        }),
      })

      const backendData = await backendResponse.json()

      if (!backendResponse.ok) {
        throw new Error(backendData.error || 'Backend payment creation failed')
      }

      return NextResponse.json(backendData)
    }

    throw new Error('Unsupported payment provider')

  } catch (error: any) {
    console.error('Payment intent creation error:', error)
    
    return NextResponse.json(
      { 
        error: error.message || 'An error occurred while processing payment',
        type: error.type || 'api_error'
      },
      { status: 500 }
    )
  }
}

// Helper function to handle Stripe response
function handleStripeResponse(paymentIntent: any) {
  if (paymentIntent.status === 'requires_action' && paymentIntent.next_action?.type === 'use_stripe_sdk') {
    return NextResponse.json({
      requires_action: true,
      data: {
        payment_intent: {
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret,
        },
      },
    })
  } else if (paymentIntent.status === 'succeeded') {
    return NextResponse.json({
      success: true,
      data: {
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
        },
      },
    })
  } else if (paymentIntent.status === 'requires_payment_method') {
    return NextResponse.json({
      success: true,
      data: {
        payment_intent: {
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret,
          status: paymentIntent.status,
        },
      },
    })
  } else {
    return NextResponse.json({
      error: 'Payment failed',
      data: {
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
        },
      },
    }, { status: 400 })
  }
}

// Handle payment intent confirmation
export async function PUT(request: NextRequest) {
  try {
    const { payment_intent_id } = await request.json()

    if (!payment_intent_id) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      )
    }

    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id)

    return NextResponse.json({
      payment_intent: {
        id: paymentIntent.id,
        status: paymentIntent.status,
      },
    })

  } catch (error: any) {
    console.error('Payment intent confirmation error:', error)
    
    return NextResponse.json(
      { 
        error: error.message || 'An error occurred while confirming payment',
        type: error.type || 'api_error'
      },
      { status: 500 }
    )
  }
}
