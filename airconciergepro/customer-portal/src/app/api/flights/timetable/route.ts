import { NextRequest, NextResponse } from 'next/server'
import { aviationStackAPI } from '@/lib/aviationstack'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const iataCode = searchParams.get('iataCode')
    const type = searchParams.get('type') as 'departure' | 'arrival' || 'departure'
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') as any
    const airline = searchParams.get('airline')
    const flightNumber = searchParams.get('flight_number')
    const date = searchParams.get('date')

    if (!iataCode) {
      return NextResponse.json(
        { success: false, error: 'Airport IATA code is required' },
        { status: 400 }
      )
    }

    // Build options for the API call
    const options: any = {
      limit,
      status: status || undefined,
      airline_iata: airline || undefined,
      flight_iata: flightNumber || undefined
    }

    // Add date filtering based on type
    if (date) {
      if (type === 'departure') {
        options.dep_schTime = date
      } else {
        options.arr_schTime = date
      }
    }

    // Get flights from airport timetable with filters
    const flights = await aviationStackAPI.getFlightsByAirport(iataCode, type, options)

    return NextResponse.json({
      success: true,
      data: flights,
      pagination: {
        limit,
        count: flights.length
      }
    })
  } catch (error) {
    console.error('Flight timetable API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Flight timetable service temporarily unavailable. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 503 }
    )
  }
}
