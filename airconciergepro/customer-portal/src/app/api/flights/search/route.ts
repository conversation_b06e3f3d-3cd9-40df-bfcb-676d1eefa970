import { NextRequest, NextResponse } from 'next/server'
import { aviationStackAPI } from '@/lib/aviationstack'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const departure = searchParams.get('departure')
    const arrival = searchParams.get('arrival')
    const date = searchParams.get('date')
    const flightNumber = searchParams.get('flight_number')
    const airline = searchParams.get('airline')
    const status = searchParams.get('status') as any
    const limit = parseInt(searchParams.get('limit') || '20')

    // Search by flight number
    if (flightNumber) {
      const flights = await aviationStackAPI.searchFlightByNumber(
        flightNumber,
        departure || undefined,
        'departure'
      )
      return NextResponse.json({
        success: true,
        data: flights,
        pagination: {
          limit,
          count: flights.length
        }
      })
    }

    // Search by single airport with filters
    if (departure && !arrival) {
      const flights = await aviationStackAPI.getFlightsByAirport(departure, 'departure', {
        limit,
        status,
        airline_iata: airline || undefined,
        dep_schTime: date || undefined
      })

      return NextResponse.json({
        success: true,
        data: flights,
        pagination: {
          limit,
          count: flights.length
        }
      })
    }

    // Search by arrival airport
    if (arrival && !departure) {
      const flights = await aviationStackAPI.getFlightsByAirport(arrival, 'arrival', {
        limit,
        status,
        airline_iata: airline || undefined,
        arr_schTime: date || undefined
      })

      return NextResponse.json({
        success: true,
        data: flights,
        pagination: {
          limit,
          count: flights.length
        }
      })
    }

    // Search by route (departure to arrival)
    if (departure && arrival) {
      const departureFlights = await aviationStackAPI.getFlightsByAirport(departure, 'departure', {
        limit: limit * 2, // Get more to filter
        status,
        airline_iata: airline || undefined,
        dep_schTime: date || undefined
      })

      // Filter flights that go to the arrival airport
      const routeFlights = departureFlights.filter(flight =>
        flight.arrival && flight.arrival.iataCode === arrival
      ).slice(0, limit)

      return NextResponse.json({
        success: true,
        data: routeFlights,
        pagination: {
          limit,
          count: routeFlights.length
        }
      })
    }

    return NextResponse.json(
      { success: false, error: 'At least one of: flight_number, departure, or arrival is required' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Flight search API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Flight search service temporarily unavailable. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 503 }
    )
  }
}
