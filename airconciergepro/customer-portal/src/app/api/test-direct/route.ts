import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const API_KEY = process.env.AVIATIONSTACK_API_KEY
    const testUrl = `https://api.aviationstack.com/v1/timetable?iataCode=DXB&type=departure&access_key=${API_KEY}`
    
    console.log('Testing direct API call to:', testUrl.replace(API_KEY || '', 'HIDDEN_KEY'))
    
    const response = await fetch(testUrl)
    const data = await response.json()
    
    console.log('Response status:', response.status)
    console.log('Response data keys:', Object.keys(data))
    
    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: `API returned ${response.status}: ${response.statusText}`,
        data: data,
        apiKeyConfigured: !!API_KEY
      })
    }
    
    return NextResponse.json({
      success: true,
      data: {
        totalFlights: data.data?.length || 0,
        sampleFlight: data.data?.[0] || null,
        pagination: data.pagination || null
      },
      apiKeyConfigured: !!API_KEY
    })
  } catch (error) {
    console.error('Direct API test error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      apiKeyConfigured: !!process.env.AVIATIONSTACK_API_KEY
    })
  }
}
