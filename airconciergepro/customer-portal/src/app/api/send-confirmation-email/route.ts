import { NextRequest, NextResponse } from 'next/server'

// This would typically integrate with an email service like SendGrid, Mailgun, or AWS SES
// For now, we'll simulate the email sending process

interface BookingData {
  airport: string
  serviceType: 'arrival' | 'departure'
  services: string[]
  date: string
  time: string
  passengers: number
  totalAmount: number
  currency: string
  flightNumber?: string
}

interface EmailData {
  booking_reference: string
  payment_intent_id: string
  booking_data: BookingData
  customer_email?: string
  customer_name?: string
}

export async function POST(request: NextRequest) {
  try {
    const emailData: EmailData = await request.json()

    // Validate required fields
    if (!emailData.booking_reference || !emailData.payment_intent_id || !emailData.booking_data) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Generate email content
    const emailContent = generateConfirmationEmail(emailData)

    // In a real implementation, you would send the email here
    // Example with SendGrid:
    /*
    const sgMail = require('@sendgrid/mail')
    sgMail.setApiKey(process.env.SENDGRID_API_KEY)

    const msg = {
      to: emailData.customer_email || '<EMAIL>',
      from: '<EMAIL>',
      subject: `Booking Confirmation - ${emailData.booking_reference}`,
      html: emailContent.html,
      text: emailContent.text,
    }

    await sgMail.send(msg)
    */

    // For now, we'll just log the email content and return success
    console.log('Email would be sent with content:', emailContent)

    // Store booking in real database via backend API
    try {
      const backendApiUrl = process.env.BACKEND_API_URL || 'http://localhost:8000/api/v1'
      const tenantId = '37de875f-170d-47e5-8f38-8c36b2112475' // TODO: Get from environment or context

      // Use the customer booking schema format
      const primaryServiceId = emailData.booking_data.services[0] // Take the first service
      const airportCode = emailData.booking_data.airport.split(' - ')[0] // Extract airport code (e.g., "DEL")

      const bookingPayload = {
        serviceId: primaryServiceId,
        flightNumber: emailData.booking_data.flightNumber || 'WEB001',
        airline: 'Customer Portal',
        departureAirport: emailData.booking_data.serviceType === 'departure' ? airportCode : 'WEB',
        arrivalAirport: emailData.booking_data.serviceType === 'arrival' ? airportCode : 'WEB',
        flightDate: `${emailData.booking_data.date}T${emailData.booking_data.time}:00.000Z`,
        estimatedArrival: `${emailData.booking_data.date}T${emailData.booking_data.time}:00.000Z`,
        serviceType: emailData.booking_data.serviceType,
        passengerCount: emailData.booking_data.passengers,
        passengers: [{
          firstName: emailData.customer_name?.split(' ')[0] || 'Customer',
          lastName: emailData.customer_name?.split(' ').slice(1).join(' ') || '',
          age: 30,
          specialRequirements: []
        }],
        specialRequirements: [],
        meetingPoint: emailData.booking_data.terminal ? `${emailData.booking_data.terminal} - Meeting Point` : 'Terminal - Meeting Point'
      }

      const response = await fetch(`${backendApiUrl.replace('/api/v1', '')}/api/customer/v1/bookings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': tenantId
        },
        body: JSON.stringify(bookingPayload)
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Failed to save booking to backend:', errorData)
        // Continue anyway - email was sent successfully
      } else {
        const savedBooking = await response.json()
        console.log('Booking saved to backend successfully:', savedBooking)
      }
    } catch (error) {
      console.error('Error saving booking to backend:', error)
      // Continue anyway - email was sent successfully
    }

    const bookingRecord = {
      id: emailData.booking_reference,
      payment_intent_id: emailData.payment_intent_id,
      ...emailData.booking_data,
      status: 'confirmed',
      created_at: new Date().toISOString(),
    }

    console.log('Booking record created:', bookingRecord)

    return NextResponse.json({
      success: true,
      message: 'Confirmation email sent successfully',
      booking_reference: emailData.booking_reference,
    })

  } catch (error: any) {
    console.error('Email sending error:', error)
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to send confirmation email',
        success: false
      },
      { status: 500 }
    )
  }
}

function generateConfirmationEmail(emailData: EmailData) {
  const { booking_reference, booking_data } = emailData
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Booking Confirmation - AirConcierge Pro</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1e40af; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .booking-details { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .reference { background: #dbeafe; padding: 15px; text-align: center; margin: 20px 0; border-radius: 8px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>AirConcierge Pro</h1>
          <h2>Booking Confirmation</h2>
        </div>
        
        <div class="content">
          <p>Dear Valued Customer,</p>
          <p>Thank you for choosing AirConcierge Pro! Your booking has been confirmed and payment processed successfully.</p>
          
          <div class="reference">
            <h3>Booking Reference</h3>
            <h2 style="color: #1e40af; margin: 0;">${booking_reference}</h2>
            <p>Please save this reference number for your records</p>
          </div>
          
          <div class="booking-details">
            <h3>Booking Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Airport:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${booking_data.airport}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Service Type:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee; text-transform: capitalize;">${booking_data.serviceType}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Services:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${booking_data.services.join(', ')}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Date & Time:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${formatDate(booking_data.date)} at ${booking_data.time}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Passengers:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${booking_data.passengers}</td>
              </tr>
              ${booking_data.flightNumber ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Flight Number:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${booking_data.flightNumber}</td>
              </tr>
              ` : ''}
              <tr>
                <td style="padding: 8px 0; font-weight: bold;"><strong>Total Paid:</strong></td>
                <td style="padding: 8px 0; font-weight: bold;">${booking_data.currency} ${booking_data.totalAmount.toFixed(2)}</td>
              </tr>
            </table>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/my-bookings" class="button">View My Bookings</a>
          </div>
          
          <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin-top: 0;">What happens next?</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Our team will contact you 24 hours before your service</li>
              <li>Arrive at the designated meeting point 15 minutes early</li>
              <li>Present this confirmation or your booking reference</li>
              <li>Enjoy your premium airport experience!</li>
            </ul>
          </div>
          
          <p>If you need to make any changes or have questions, please contact our 24/7 customer support:</p>
          <p>
            <strong>Phone:</strong> ******-123-4567<br>
            <strong>Email:</strong> <EMAIL>
          </p>
        </div>
        
        <div class="footer">
          <p>Thank you for choosing AirConcierge Pro</p>
          <p>© 2024 AirConcierge Pro. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    AirConcierge Pro - Booking Confirmation
    
    Dear Valued Customer,
    
    Thank you for choosing AirConcierge Pro! Your booking has been confirmed and payment processed successfully.
    
    Booking Reference: ${booking_reference}
    
    Booking Details:
    - Airport: ${booking_data.airport}
    - Service Type: ${booking_data.serviceType}
    - Services: ${booking_data.services.join(', ')}
    - Date & Time: ${formatDate(booking_data.date)} at ${booking_data.time}
    - Passengers: ${booking_data.passengers}
    ${booking_data.flightNumber ? `- Flight Number: ${booking_data.flightNumber}` : ''}
    - Total Paid: ${booking_data.currency} ${booking_data.totalAmount.toFixed(2)}
    
    What happens next?
    1. Our team will contact you 24 hours before your service
    2. Arrive at the designated meeting point 15 minutes early
    3. Present this confirmation or your booking reference
    4. Enjoy your premium airport experience!
    
    For support: ******-123-4567 or <EMAIL>
    
    Thank you for choosing AirConcierge Pro
  `

  return { html, text }
}
