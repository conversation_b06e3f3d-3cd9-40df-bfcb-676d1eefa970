import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { payment_id, provider, verification_data } = await request.json()

    // Validate required fields
    if (!payment_id || !provider) {
      return NextResponse.json(
        { error: 'Payment ID and provider are required' },
        { status: 400 }
      )
    }

    // Validate provider
    if (!['stripe', 'razorpay'].includes(provider)) {
      return NextResponse.json(
        { error: 'Invalid payment provider' },
        { status: 400 }
      )
    }

    // Proxy to backend for verification
    const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_CUSTOMER_API_URL}/payments/verify-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Tenant-ID': 'fb9b92e3-7d23-4a8d-832a-9ddfebcf25ff', // Demo tenant
      },
      body: JSON.stringify({
        payment_id,
        provider,
        verification_data,
      }),
    })

    const backendData = await backendResponse.json()
    
    if (!backendResponse.ok) {
      return NextResponse.json(
        { 
          success: false,
          error: backendData.error || 'Payment verification failed',
        },
        { status: backendResponse.status }
      )
    }

    return NextResponse.json(backendData)

  } catch (error: any) {
    console.error('Payment verification error:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'An error occurred while verifying payment',
      },
      { status: 500 }
    )
  }
}
