import { NextResponse } from 'next/server'
import { aviationStackAPI } from '@/lib/aviationstack'

export async function GET() {
  try {
    console.log('Testing AviationStack API integration...')
    
    // Test popular airports
    const popularAirports = aviationStackAPI.getPopularAirports()
    console.log('Popular airports loaded:', popularAirports.length)

    // Test airport search (this will use the API if key is available)
    let searchResults = []
    try {
      searchResults = await aviationStackAPI.searchAirports('Delhi', 5)
      console.log('Airport search results:', searchResults.length)
    } catch (error) {
      console.log('Airport search failed (expected if no API key):', error.message)
    }

    // Test flight timetable (this will use the API if key is available)
    let flightResults = []
    try {
      flightResults = await aviationStackAPI.getFlightsByAirport('DXB', 'departure', 5)
      console.log('Flight timetable results:', flightResults.length)
    } catch (error) {
      console.log('Flight timetable failed:', error.message)
    }

    return NextResponse.json({
      success: true,
      data: {
        popularAirports: popularAirports.length,
        searchResults: searchResults.length,
        flightResults: flightResults.length,
        apiKeyConfigured: !!process.env.AVIATIONSTACK_API_KEY,
        testResults: {
          popularAirports: popularAirports.slice(0, 2), // Show first 2 for testing
          searchResults: searchResults.slice(0, 2),
          flightResults: flightResults.slice(0, 1)
        }
      }
    })
  } catch (error) {
    console.error('AviationStack test error:', error)
    
    return NextResponse.json({
      success: false,
      error: error.message,
      apiKeyConfigured: !!process.env.AVIATIONSTACK_API_KEY
    })
  }
}
