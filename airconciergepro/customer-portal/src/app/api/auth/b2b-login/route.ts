import { NextRequest, NextResponse } from 'next/server'

const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000'

export async function POST(request: NextRequest) {
  try {
    const { tenantId, apiSecret } = await request.json()

    if (!tenantId || !apiSecret) {
      return NextResponse.json(
        { error: 'Tenant ID and API secret are required' },
        { status: 400 }
      )
    }

    // Call the backend partner authentication endpoint
    const response = await fetch(`${BACKEND_API_URL}/api/partner/v1/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tenantId,
        apiSecret
      }),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Authentication failed' },
        { status: response.status }
      )
    }

    // Return the API key and tenant information
    return NextResponse.json({
      success: true,
      apiKey: data.data.apiKey,
      tenant: data.data.tenant,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      expiresIn: data.data.expiresIn
    })

  } catch (error) {
    console.error('B2B authentication error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
