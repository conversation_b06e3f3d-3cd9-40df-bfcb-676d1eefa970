import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/Providers'
import { Toaster } from 'react-hot-toast'
import RealTimeNotifications from '@/components/notifications/RealTimeNotifications'
import ConnectionStatus from '@/components/ui/ConnectionStatus'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AirConcierge Pro - Premium Airport Meet & Greet Services',
  description: 'Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.',
  keywords: 'airport meet and greet, VIP services, airport assistance, travel concierge, airport transfer',
  authors: [{ name: 'AirConcierge Pro' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'AirConcierge Pro - Premium Airport Meet & Greet Services',
    description: 'Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AirConcierge Pro - Premium Airport Meet & Greet Services',
    description: 'Book premium airport meet and greet services worldwide. Professional assistance, VIP treatment, and seamless travel experience.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
          {/* Real-time notifications */}
          <RealTimeNotifications position="top-right" maxNotifications={3} />
          {/* Connection status indicator */}
          <ConnectionStatus position="fixed" className="bottom-4 left-4" />
        </Providers>
      </body>
    </html>
  )
}
