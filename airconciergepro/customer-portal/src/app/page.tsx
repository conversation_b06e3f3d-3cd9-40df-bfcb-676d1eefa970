'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  Plane,
  Users,
  Shield,
  Clock,
  Star,
  MapPin,
  Phone,
  Mail,
  CheckCircle,
  ArrowRight,
  Award,
  Globe,
  Heart
} from 'lucide-react'
import Header from '@/components/layout/Header'
import LuxuryFooter from '@/components/layout/LuxuryFooter'
import LuxuryHero from '@/components/ui/LuxuryHero'
import ServiceCard from '@/components/ui/ServiceCard'
import TestimonialCard from '@/components/ui/TestimonialCard'
import { LuxuryNotificationContainer } from '@/components/ui/LuxuryNotification'

const services = [
  {
    id: 'meet-greet',
    title: 'Meet & Greet',
    description: 'Personal assistance from arrival to departure with professional greeting service.',
    icon: Users,
    price: 'From ₹3,750',
    features: ['Personal greeter', 'Baggage assistance', 'Fast-track service', 'Terminal guidance'],
    popular: true
  },
  {
    id: 'vip-lounge',
    title: 'VIP Lounge Access',
    description: 'Relax in premium lounges with complimentary refreshments and Wi-Fi.',
    icon: Star,
    price: 'From ₹5,400',
    features: ['Premium lounge access', 'Complimentary food & drinks', 'Business facilities', 'Quiet environment'],
    premium: true
  },
  {
    id: 'fast-track',
    title: 'Fast Track Security',
    description: 'Skip the queues with priority security and immigration clearance.',
    icon: Clock,
    price: 'From ₹2,900',
    features: ['Priority security', 'Immigration fast-track', 'Dedicated lanes', 'Time-saving service']
  },
  {
    id: 'transfer',
    title: 'Airport Transfer',
    description: 'Luxury vehicle transfers between terminals or to your destination.',
    icon: Plane,
    price: 'From ₹4,600',
    features: ['Luxury vehicles', 'Professional drivers', 'Flight monitoring', 'Door-to-door service']
  }
]

const testimonials = [
  {
    name: 'Sarah Johnson',
    role: 'Business Traveler',
    content: 'Exceptional service! The meet and greet team made my connection so smooth. Highly recommended for frequent travelers.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    name: 'Michael Chen',
    role: 'Family Traveler',
    content: 'Traveling with kids became stress-free. The team helped us through every step and even entertained the children.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    name: 'Emma Williams',
    role: 'Senior Traveler',
    content: 'The wheelchair assistance and personal care exceeded my expectations. Professional and caring service.',
    rating: 5,
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
  }
]

const stats = [
  { number: '500K+', label: 'Happy Customers' },
  { number: '150+', label: 'Airports Worldwide' },
  { number: '99.8%', label: 'Success Rate' },
  { number: '24/7', label: 'Customer Support' }
]

export default function HomePage() {

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Luxury Hero Section */}
      <LuxuryHero />

      {/* Stats Section */}
      <section className="bg-gray-50 py-16">
        <div className="container-max px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl font-bold text-primary-600 mb-2">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Luxury Services Section */}
      <section className="section-padding bg-gradient-to-b from-white to-luxury-50 relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gold-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary-500/10 rounded-full blur-3xl" />

        <div className="container-max relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-gold-100 to-primary-100 rounded-full px-6 py-3 mb-8"
            >
              <Star className="h-5 w-5 text-gold-600" />
              <span className="text-luxury-700 font-semibold">Premium Services</span>
            </motion.div>

            <h2 className="heading-lg mb-6 text-luxury-gradient">
              Luxury Airport Experiences
            </h2>
            <p className="text-xl text-luxury-600 max-w-4xl mx-auto leading-relaxed">
              Discover our curated collection of premium airport services, each designed to transform
              your travel experience into a journey of comfort, convenience, and luxury.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <ServiceCard
                key={service.id}
                service={service}
                index={index}
                onSelect={() => window.location.href = '/book'}
              />
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <Link href="/book">
              <motion.button
                className="btn-gold text-lg px-10 py-4 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Explore All Services</span>
                <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="bg-gray-50 section-padding">
        <div className="container-max">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-responsive-lg font-bold text-gray-900 mb-8">
                Why Choose AirConcierge Pro?
              </h2>
              <div className="space-y-6">
                {[
                  {
                    icon: Shield,
                    title: 'Trusted & Secure',
                    description: 'Licensed professionals with background checks and insurance coverage.'
                  },
                  {
                    icon: Clock,
                    title: '24/7 Support',
                    description: 'Round-the-clock customer service and real-time flight monitoring.'
                  },
                  {
                    icon: Star,
                    title: 'Premium Quality',
                    description: 'Consistently rated 5-star service by thousands of satisfied customers.'
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-4"
                  >
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <feature.icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                      <p className="text-gray-600">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-1556388158-158ea5ccacbd?w=600&h=400&fit=crop"
                alt="Professional airport service"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-8 w-8 text-success-600" />
                  <div>
                    <div className="font-semibold text-gray-900">99.8% Success Rate</div>
                    <div className="text-sm text-gray-600">On-time service delivery</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="section-padding">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-responsive-lg font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Don't just take our word for it. Here's what our satisfied customers have to say about their experience.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <TestimonialCard testimonial={testimonial} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 text-white section-padding">
        <div className="container-max text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-responsive-lg font-bold mb-6">
              Ready for a Stress-Free Travel Experience?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-primary-100">
              Join thousands of satisfied customers who trust AirConcierge Pro for their airport service needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setShowBookingWidget(true)}
                className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Book Your Service Now
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-4 px-8 rounded-lg transition-all duration-200">
                Contact Us
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      <LuxuryFooter />



      {/* Luxury Notification System */}
      <LuxuryNotificationContainer />
    </div>
  )
}
