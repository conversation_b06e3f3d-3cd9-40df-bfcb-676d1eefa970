@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile-first optimizations */
@media (max-width: 768px) {
  /* Ensure minimum touch targets */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve form field spacing on mobile */
  input,
  select,
  textarea {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 12px 16px;
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 16px;
  }

  /* Reduce excessive margins on mobile */
  .container-max {
    padding-left: 16px;
    padding-right: 16px;
  }

  /* Optimize scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Improve touch interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  /* Better use of screen real estate */
  .container-max {
    max-width: 1400px;
  }

  /* Improved hover states for desktop */
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

/* Very small screens optimization */
@media (max-width: 375px) {
  .container-max {
    padding-left: 12px;
    padding-right: 12px;
  }

  /* Ensure content doesn't overflow on very small screens */
  .booking-widget {
    margin: 0 -4px;
  }
}

/* Utility classes for responsive design */
.responsive-text {
  font-size: 14px;
}

@media (min-width: 768px) {
  .responsive-text {
    font-size: 16px;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 18px;
  }
}

/* Sticky button and safe area support */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Ensure sticky button doesn't interfere with content */
.sticky-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Smooth transitions for sticky button */
.sticky-button-container {
  transition: transform 0.3s ease-in-out;
}

/* Hide sticky button when keyboard is open on mobile */
@media (max-width: 768px) {
  .sticky-button-container.keyboard-open {
    transform: translateY(100%);
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Luxury Components */
.btn-primary {
  @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 shadow-luxury hover:shadow-luxury-lg transform hover:-translate-y-1 hover:scale-105;
}

.btn-secondary {
  @apply bg-white hover:bg-luxury-50 text-primary-500 font-semibold py-4 px-8 rounded-xl border-2 border-primary-500 transition-all duration-300 shadow-luxury hover:shadow-luxury-lg transform hover:-translate-y-1 hover:scale-105;
}

.btn-gold {
  @apply bg-gold-500 hover:bg-gold-600 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 shadow-gold hover:shadow-gold-lg transform hover:-translate-y-1 hover:scale-105;
}

.btn-outline {
  @apply bg-transparent hover:bg-primary-50 text-primary-600 font-semibold py-4 px-8 rounded-xl border-2 border-primary-600 transition-all duration-300 hover:shadow-luxury;
}

.card {
  @apply bg-white rounded-2xl shadow-luxury border border-luxury-200 p-8 transition-all duration-300 hover:shadow-luxury-lg hover:-translate-y-1;
}

.card-luxury {
  @apply bg-white rounded-2xl shadow-luxury-lg border border-luxury-200 p-8 transition-all duration-300 hover:shadow-luxury-xl hover:-translate-y-2 relative overflow-hidden;
}

.card-luxury::before {
  content: '';
  @apply absolute top-0 left-0 w-full h-1 bg-gold-500;
}

.input-field {
  @apply w-full px-6 py-4 border-2 border-luxury-300 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-gold-500 transition-all duration-300 bg-white hover:border-luxury-400 shadow-inner-luxury;
}

.input-luxury {
  @apply w-full px-6 py-4 border-2 border-luxury-300 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-gold-500 transition-all duration-300 bg-gradient-to-r from-white to-luxury-50 hover:border-luxury-400 shadow-inner-luxury placeholder-luxury-500;
}

.section-padding {
  @apply py-16 px-4 sm:px-6 lg:px-8;
}

.container-max {
  @apply max-w-7xl mx-auto;
}

/* Hero gradient background */
.hero-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Service card hover effects */
.service-card {
  @apply transform transition-all duration-300 hover:scale-105 hover:shadow-2xl;
}

/* Loading spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form validation styles */
.form-error {
  @apply text-error-600 text-sm mt-1;
}

.form-success {
  @apply text-success-600 text-sm mt-1;
}

/* Responsive text sizes */
.text-responsive-xl {
  @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
}

.text-responsive-lg {
  @apply text-xl sm:text-2xl lg:text-3xl;
}

.text-responsive-md {
  @apply text-lg sm:text-xl lg:text-2xl;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Booking wizard styles */
.wizard-step {
  @apply flex items-center justify-center w-10 h-10 rounded-full border-2 font-semibold transition-all duration-200;
}

.wizard-step.active {
  @apply bg-primary-600 border-primary-600 text-white;
}

.wizard-step.completed {
  @apply bg-success-600 border-success-600 text-white;
}

.wizard-step.inactive {
  @apply bg-gray-100 border-gray-300 text-gray-500;
}

/* Airport search dropdown */
.airport-dropdown {
  @apply absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto;
}

.airport-option {
  @apply px-4 py-3 hover:bg-primary-50 cursor-pointer border-b border-gray-100 last:border-b-0;
}

.airport-option.selected {
  @apply bg-primary-100 text-primary-800;
}

/* Luxury Typography */
.heading-luxury {
  @apply font-display font-bold text-luxury-900 leading-tight;
}

.heading-xl {
  @apply text-5xl sm:text-6xl lg:text-7xl heading-luxury;
}

.heading-lg {
  @apply text-4xl sm:text-5xl lg:text-6xl heading-luxury;
}

.heading-md {
  @apply text-3xl sm:text-4xl lg:text-5xl heading-luxury;
}

.heading-sm {
  @apply text-2xl sm:text-3xl lg:text-4xl heading-luxury;
}

.text-luxury-gradient {
  @apply text-primary-500;
}

.text-gold-gradient {
  @apply text-gold-500;
}

/* Luxury Backgrounds */
.bg-luxury-gradient {
  @apply bg-primary-500;
}

.bg-gold-gradient {
  @apply bg-gold-500;
}

.bg-premium-gradient {
  @apply bg-primary-500;
}

/* Glass Morphism Effects */
.glass {
  @apply bg-white/10 backdrop-blur-luxury border border-white/20;
}

.glass-dark {
  @apply bg-black/10 backdrop-blur-luxury border border-black/20;
}

/* Luxury Hover Effects */
.hover-lift {
  @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-luxury-lg;
}

.hover-glow {
  @apply transition-all duration-300 hover:shadow-gold-lg;
}

.hover-scale {
  @apply transition-all duration-300 hover:scale-105;
}

/* Premium Service Cards */
.service-card-luxury {
  @apply card-luxury transform transition-all duration-500 hover:scale-105 hover:shadow-luxury-xl hover:-translate-y-3 cursor-pointer;
}

.service-card-luxury::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-gold-500/10 to-primary-600/10 opacity-0 transition-opacity duration-500 rounded-2xl;
}

.service-card-luxury:hover::after {
  @apply opacity-100;
}

/* Luxury Progress Indicators */
.progress-luxury {
  @apply bg-gold-500 h-2 rounded-full transition-all duration-500;
}

/* Premium Animations */
.animate-luxury-fade-in {
  animation: luxuryFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-luxury-slide-up {
  animation: luxurySlideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-luxury-scale {
  animation: luxuryScale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Shimmer Effect for Loading */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s linear infinite;
}

/* Mobile-First Luxury Responsive Design */
@media (max-width: 640px) {
  .heading-xl {
    @apply text-3xl sm:text-4xl;
  }

  .heading-lg {
    @apply text-2xl sm:text-3xl;
  }

  .heading-md {
    @apply text-xl sm:text-2xl;
  }

  .card-luxury {
    @apply p-6;
  }

  .btn-primary, .btn-gold, .btn-secondary {
    @apply py-3 px-6 text-base;
  }

  .service-card-luxury {
    @apply hover:scale-105;
  }

  .glass {
    @apply p-6;
  }
}

/* Touch-Optimized Interactions */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover {
    transform: none;
  }

  .hover-scale:hover {
    transform: none;
  }

  .service-card-luxury:hover {
    transform: none;
  }

  .btn-primary:hover,
  .btn-gold:hover,
  .btn-secondary:hover {
    transform: none;
  }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .shadow-luxury {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .shadow-luxury-lg {
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18);
  }

  .shadow-luxury-xl {
    box-shadow: 0 24px 64px rgba(0, 0, 0, 0.24);
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  .luxury-theme {
    --luxury-50: #1a1a1a;
    --luxury-100: #2a2a2a;
    --luxury-900: #f5f5f5;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-luxury-fade-in,
  .animate-luxury-slide-up,
  .animate-luxury-scale,
  .animate-float,
  .animate-glow {
    animation: none;
  }

  .hover-lift,
  .hover-scale,
  .service-card-luxury {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .shadow-luxury,
  .shadow-luxury-lg,
  .shadow-luxury-xl {
    box-shadow: none;
  }

  .bg-luxury-gradient,
  .bg-gold-gradient,
  .bg-premium-gradient {
    background: #ffffff;
    color: #000000;
  }
}
