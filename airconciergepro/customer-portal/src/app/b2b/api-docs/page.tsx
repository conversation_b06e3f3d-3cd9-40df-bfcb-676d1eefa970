'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  ArrowLeft, 
  Code, 
  Copy, 
  CheckCircle,
  Book,
  Globe,
  Key,
  Calendar,
  DollarSign
} from 'lucide-react'

export default function APIDocsPage() {
  const router = useRouter()
  const [apiKey, setApiKey] = useState<string>('')
  const [copiedEndpoint, setCopiedEndpoint] = useState<string>('')

  useEffect(() => {
    const storedApiKey = localStorage.getItem('b2b_api_key')
    if (!storedApiKey) {
      router.push('/auth/b2b-login')
      return
    }
    setApiKey(storedApiKey)
  }, [router])

  const copyToClipboard = (text: string, endpoint: string) => {
    navigator.clipboard.writeText(text)
    setCopiedEndpoint(endpoint)
    setTimeout(() => setCopiedEndpoint(''), 2000)
  }

  const endpoints = [
    {
      id: 'inventory',
      title: 'Get Available Inventory',
      method: 'GET',
      url: '/api/partner/v1/inventory',
      description: 'Retrieve available services and pricing for specific criteria',
      parameters: [
        { name: 'airport', type: 'string', required: false, description: 'Airport IATA code (e.g., DEL)' },
        { name: 'type', type: 'string', required: false, description: 'Service type (arrival/departure)' },
        { name: 'date', type: 'string', required: false, description: 'Date in YYYY-MM-DD format' },
        { name: 'passengers', type: 'number', required: false, description: 'Number of passengers' }
      ],
      example: `curl -X GET "https://api.airconciergepro.com/api/partner/v1/inventory?airport=DEL&type=arrival&passengers=2" \\
  -H "X-API-Key: ${apiKey.substring(0, 20)}..."`
    },
    {
      id: 'quote',
      title: 'Get Service Quote',
      method: 'POST',
      url: '/api/partner/v1/quote',
      description: 'Get pricing quote for specific service and requirements',
      parameters: [
        { name: 'serviceId', type: 'string', required: true, description: 'Service UUID' },
        { name: 'passengerCount', type: 'number', required: true, description: 'Number of passengers' },
        { name: 'date', type: 'string', required: true, description: 'Service date' }
      ],
      example: `curl -X POST "https://api.airconciergepro.com/api/partner/v1/quote" \\
  -H "X-API-Key: ${apiKey.substring(0, 20)}..." \\
  -H "Content-Type: application/json" \\
  -d '{
    "serviceId": "ba1fa529-1188-4538-878c-67753b26ad85",
    "passengerCount": 2,
    "date": "2025-07-20"
  }'`
    },
    {
      id: 'book',
      title: 'Create Booking',
      method: 'POST',
      url: '/api/partner/v1/book',
      description: 'Create a new booking through the partner API',
      parameters: [
        { name: 'serviceId', type: 'string', required: true, description: 'Service UUID' },
        { name: 'flightNumber', type: 'string', required: true, description: 'Flight number' },
        { name: 'airline', type: 'string', required: true, description: 'Airline name' },
        { name: 'departureAirport', type: 'string', required: true, description: 'Departure airport IATA code' },
        { name: 'arrivalAirport', type: 'string', required: true, description: 'Arrival airport IATA code' },
        { name: 'flightDate', type: 'string', required: true, description: 'Flight date (ISO format)' },
        { name: 'serviceType', type: 'string', required: true, description: 'arrival/departure/transit' },
        { name: 'passengerCount', type: 'number', required: true, description: 'Number of passengers' },
        { name: 'passengers', type: 'array', required: true, description: 'Passenger details array' },
        { name: 'meetingPoint', type: 'string', required: true, description: 'Meeting point description' }
      ],
      example: `curl -X POST "https://api.airconciergepro.com/api/partner/v1/book" \\
  -H "X-API-Key: ${apiKey.substring(0, 20)}..." \\
  -H "Content-Type: application/json" \\
  -d '{
    "serviceId": "ba1fa529-1188-4538-878c-67753b26ad85",
    "flightNumber": "AI101",
    "airline": "Air India",
    "departureAirport": "BOM",
    "arrivalAirport": "DEL",
    "flightDate": "2025-07-20T12:00:00.000Z",
    "serviceType": "arrival",
    "passengerCount": 1,
    "passengers": [{
      "firstName": "John",
      "lastName": "Doe",
      "age": 30,
      "contactNumber": "+1-555-123-4567"
    }],
    "meetingPoint": "Terminal 3 - Arrival Hall"
  }'`
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-1" />
              Back
            </button>
            <div className="flex items-center">
              <Book className="h-6 w-6 text-primary-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Partner API Documentation</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Getting Started</h2>
          <p className="text-gray-600 mb-4">
            The AirConcierge Pro Partner API allows you to integrate our airport services directly into your platform. 
            All API requests require authentication using your API key.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h3 className="font-semibold text-blue-900 mb-2">Authentication</h3>
            <p className="text-blue-800 text-sm mb-2">
              Include your API key in the request header:
            </p>
            <code className="block bg-blue-100 px-3 py-2 rounded text-sm font-mono text-blue-900">
              X-API-Key: {apiKey.substring(0, 20)}...
            </code>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-900 mb-2">Base URL</h3>
            <code className="block bg-green-100 px-3 py-2 rounded text-sm font-mono text-green-900">
              https://api.airconciergepro.com
            </code>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="space-y-8">
          {endpoints.map((endpoint) => (
            <div key={endpoint.id} className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <span className={`px-2 py-1 rounded text-xs font-medium mr-3 ${
                    endpoint.method === 'GET' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {endpoint.method}
                  </span>
                  <h3 className="text-lg font-semibold text-gray-900">{endpoint.title}</h3>
                </div>
                <button
                  onClick={() => copyToClipboard(endpoint.example, endpoint.id)}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-900"
                >
                  {copiedEndpoint === endpoint.id ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <Copy className="h-4 w-4 mr-1" />
                  )}
                  Copy Example
                </button>
              </div>

              <p className="text-gray-600 mb-4">{endpoint.description}</p>

              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Endpoint</h4>
                <code className="block bg-gray-100 px-3 py-2 rounded text-sm font-mono text-gray-800">
                  {endpoint.method} {endpoint.url}
                </code>
              </div>

              {endpoint.parameters.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Parameters</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Required</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {endpoint.parameters.map((param, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm font-mono text-gray-900">{param.name}</td>
                            <td className="px-4 py-2 text-sm text-gray-600">{param.type}</td>
                            <td className="px-4 py-2 text-sm">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                param.required 
                                  ? 'bg-red-100 text-red-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {param.required ? 'Required' : 'Optional'}
                              </span>
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-600">{param.description}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Example Request</h4>
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto whitespace-pre-wrap">
                  <code>{endpoint.example}</code>
                </pre>
              </div>
            </div>
          ))}
        </div>

        {/* Support */}
        <div className="bg-white rounded-lg shadow-sm p-6 mt-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h2>
          <p className="text-gray-600 mb-4">
            If you have questions about the API or need technical support, please contact our developer support team.
          </p>
          <div className="flex space-x-4">
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Email Support
            </a>
            <a 
              href="/b2b/dashboard"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
