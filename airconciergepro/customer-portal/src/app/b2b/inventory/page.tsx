'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  ArrowLeft, 
  Search, 
  Filter,
  MapPin,
  Clock,
  Users,
  DollarSign,
  Plane,
  RefreshCw
} from 'lucide-react'

interface Service {
  id: string
  name: string
  description: string
  category: string
  type: string
  base_price: string
  currency: string
  duration_minutes: number
  max_passengers: number
  available_airports: string[]
  inclusions: string[]
}

export default function InventoryPage() {
  const router = useRouter()
  const [services, setServices] = useState<Service[]>([])
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [apiKey, setApiKey] = useState<string>('')
  const [filters, setFilters] = useState({
    airport: '',
    type: '',
    category: '',
    passengers: ''
  })

  useEffect(() => {
    const storedApiKey = localStorage.getItem('b2b_api_key')
    if (!storedApiKey) {
      router.push('/auth/b2b-login')
      return
    }
    setApiKey(storedApiKey)
    loadInventory(storedApiKey)
  }, [router])

  const loadInventory = async (apiKey: string) => {
    try {
      setIsLoading(true)
      
      // Call the partner inventory API
      const response = await fetch('/api/b2b/inventory', {
        headers: {
          'X-API-Key': apiKey
        }
      })

      if (response.ok) {
        const data = await response.json()
        setServices(data.services || [])
        setFilteredServices(data.services || [])
      } else {
        console.error('Failed to load inventory')
      }
    } catch (error) {
      console.error('Error loading inventory:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = services

    if (filters.airport) {
      filtered = filtered.filter(service => 
        service.available_airports.includes(filters.airport.toUpperCase())
      )
    }

    if (filters.type) {
      filtered = filtered.filter(service => service.type === filters.type)
    }

    if (filters.category) {
      filtered = filtered.filter(service => service.category === filters.category)
    }

    if (filters.passengers) {
      filtered = filtered.filter(service => 
        service.max_passengers >= parseInt(filters.passengers)
      )
    }

    setFilteredServices(filtered)
  }

  useEffect(() => {
    applyFilters()
  }, [filters, services])

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const formatPrice = (price: string, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(parseFloat(price))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back
              </button>
              <div className="flex items-center">
                <MapPin className="h-6 w-6 text-primary-600 mr-3" />
                <h1 className="text-xl font-semibold text-gray-900">Service Inventory</h1>
              </div>
            </div>
            <button
              onClick={() => loadInventory(apiKey)}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Filter className="h-5 w-5 text-primary-600 mr-2" />
            Filters
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Airport</label>
              <input
                type="text"
                placeholder="e.g., DEL, BOM"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={filters.airport}
                onChange={(e) => handleFilterChange('airport', e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Service Type</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
              >
                <option value="">All Types</option>
                <option value="arrival">Arrival</option>
                <option value="departure">Departure</option>
                <option value="transit">Transit</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
              >
                <option value="">All Categories</option>
                <option value="meet_greet">Meet & Greet</option>
                <option value="fast_track">Fast Track</option>
                <option value="lounge_access">Lounge Access</option>
                <option value="porter_service">Porter Service</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Min Passengers</label>
              <input
                type="number"
                placeholder="1"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={filters.passengers}
                onChange={(e) => handleFilterChange('passengers', e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Services Grid */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-2 text-gray-600">Loading inventory...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <div key={service.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{service.name}</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {service.category.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary-600">
                      {formatPrice(service.base_price, service.currency)}
                    </div>
                    <div className="text-sm text-gray-500">starting from</div>
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4">{service.description}</p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    {service.duration_minutes} minutes
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    Up to {service.max_passengers} passengers
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Plane className="h-4 w-4 mr-2" />
                    {service.type.charAt(0).toUpperCase() + service.type.slice(1)} service
                  </div>
                </div>

                {service.inclusions && service.inclusions.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Inclusions:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {service.inclusions.slice(0, 3).map((inclusion, index) => (
                        <li key={index} className="flex items-center">
                          <div className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-2"></div>
                          {inclusion}
                        </li>
                      ))}
                      {service.inclusions.length > 3 && (
                        <li className="text-gray-500">+{service.inclusions.length - 3} more</li>
                      )}
                    </ul>
                  </div>
                )}

                <div className="pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Available at:</span> {service.available_airports.join(', ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {!isLoading && filteredServices.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
            <p className="text-gray-600">Try adjusting your filters to see more results.</p>
          </div>
        )}
      </div>
    </div>
  )
}
