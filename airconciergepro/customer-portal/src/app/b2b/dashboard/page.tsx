'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Building2, 
  Key, 
  BarChart3, 
  Calendar, 
  Users, 
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  LogOut,
  Code,
  BookOpen,
  Settings
} from 'lucide-react'

interface TenantInfo {
  id: string
  name: string
  status: string
}

interface DashboardStats {
  totalBookings: number
  totalRevenue: number
  activeBookings: number
  apiCalls: number
}

export default function B2BDashboard() {
  const router = useRouter()
  const [tenant, setTenant] = useState<TenantInfo | null>(null)
  const [apiKey, setApiKey] = useState<string>('')
  const [stats, setStats] = useState<DashboardStats>({
    totalBookings: 0,
    totalRevenue: 0,
    activeBookings: 0,
    apiCalls: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is authenticated
    const storedApiKey = localStorage.getItem('b2b_api_key')
    const storedTenant = localStorage.getItem('b2b_tenant')
    const expiresAt = localStorage.getItem('b2b_expires_at')

    if (!storedApiKey || !storedTenant || !expiresAt) {
      router.push('/auth/b2b-login')
      return
    }

    // Check if token is expired
    if (new Date() > new Date(expiresAt)) {
      localStorage.removeItem('b2b_api_key')
      localStorage.removeItem('b2b_tenant')
      localStorage.removeItem('b2b_expires_at')
      router.push('/auth/b2b-login')
      return
    }

    setApiKey(storedApiKey)
    setTenant(JSON.parse(storedTenant))
    
    // Load dashboard data
    loadDashboardData(storedApiKey)
  }, [router])

  const loadDashboardData = async (apiKey: string) => {
    try {
      // Mock data for now - in real implementation, call partner API endpoints
      setStats({
        totalBookings: 156,
        totalRevenue: 45230,
        activeBookings: 12,
        apiCalls: 2847
      })
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('b2b_api_key')
    localStorage.removeItem('b2b_tenant')
    localStorage.removeItem('b2b_expires_at')
    router.push('/auth/b2b-login')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Partner Dashboard</h1>
                <p className="text-sm text-gray-500">{tenant?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-500">
                <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                Connected
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center text-sm text-gray-700 hover:text-gray-900 transition-colors duration-200"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalBookings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">₹{stats.totalRevenue.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeBookings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">API Calls</p>
                <p className="text-2xl font-bold text-gray-900">{stats.apiCalls}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* API Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Key className="h-5 w-5 text-primary-600 mr-2" />
              API Information
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                <div className="flex items-center">
                  <code className="flex-1 bg-gray-100 px-3 py-2 rounded-md text-sm font-mono text-gray-800 truncate">
                    {apiKey.substring(0, 20)}...
                  </code>
                  <button 
                    onClick={() => navigator.clipboard.writeText(apiKey)}
                    className="ml-2 px-3 py-2 text-sm bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors duration-200"
                  >
                    Copy
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Base URL</label>
                <code className="block bg-gray-100 px-3 py-2 rounded-md text-sm font-mono text-gray-800">
                  https://api.airconciergepro.com/api/partner/v1
                </code>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings className="h-5 w-5 text-primary-600 mr-2" />
              Quick Actions
            </h2>
            
            <div className="space-y-3">
              <button
                onClick={() => router.push('/b2b/create-booking')}
                className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <Code className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Create Booking</span>
                </div>
                <span className="text-gray-400">→</span>
              </button>

              <button
                onClick={() => router.push('/b2b/bookings')}
                className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Manage Bookings</span>
                </div>
                <span className="text-gray-400">→</span>
              </button>

              <button
                onClick={() => router.push('/b2b/api-docs')}
                className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">API Documentation</span>
                </div>
                <span className="text-gray-400">→</span>
              </button>

              <button
                onClick={() => router.push('/b2b/inventory')}
                className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">View Inventory</span>
                </div>
                <span className="text-gray-400">→</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
