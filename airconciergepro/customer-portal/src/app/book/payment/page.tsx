'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, CreditCard, Shield, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import Header from '@/components/layout/Header'
import LuxuryFooter from '@/components/layout/LuxuryFooter'
import CheckoutForm from '@/components/payment/CheckoutForm'

interface BookingData {
  airport: string
  airportId: string
  service: string
  serviceId: string
  serviceType: 'arrival' | 'departure'
  date: string
  time: string
  passengers: number
  flightNumber?: string
  totalAmount: number
  currency: string
}

export default function PaymentPage() {
  const [bookingData, setBookingData] = useState<BookingData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Load booking data from localStorage
    const storedData = localStorage.getItem('bookingData')
    if (storedData) {
      try {
        const data = JSON.parse(storedData)
        setBookingData(data)
      } catch (error) {
        console.error('Error parsing booking data:', error)
        router.push('/book')
      }
    } else {
      router.push('/book')
    }
    setIsLoading(false)
  }, [router])

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(price)
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeStr: string) => {
    const [hours, minutes] = timeStr.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No booking data found. Redirecting...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <section className="py-8">
        {/* Back button */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 mb-6">
          <Link
            href="/book"
            className="inline-flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200 text-gray-600 hover:text-gray-800 hover:border-gray-300 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm font-medium">Back to Booking</span>
          </Link>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-6xl mx-auto"
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Booking Summary */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6">Booking Summary</h2>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Service:</span>
                      <span className="font-medium text-right">{bookingData.service}</span>
                    </div>
                    
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Airport:</span>
                      <span className="font-medium text-right">{bookingData.airport}</span>
                    </div>
                    
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Type:</span>
                      <span className="font-medium text-right capitalize">{bookingData.serviceType}</span>
                    </div>
                    
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Date:</span>
                      <span className="font-medium text-right">{formatDate(bookingData.date)}</span>
                    </div>
                    
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Time:</span>
                      <span className="font-medium text-right">{formatTime(bookingData.time)}</span>
                    </div>
                    
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Passengers:</span>
                      <span className="font-medium text-right">{bookingData.passengers}</span>
                    </div>
                    
                    {bookingData.flightNumber && (
                      <div className="flex justify-between items-start">
                        <span className="text-gray-600">Flight:</span>
                        <span className="font-medium text-right">{bookingData.flightNumber}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="border-t border-gray-200 mt-6 pt-6">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">Total Amount:</span>
                      <span className="text-2xl font-bold text-primary-600">
                        {formatPrice(bookingData.totalAmount, bookingData.currency)}
                      </span>
                    </div>
                  </div>

                  {/* Security Features */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span>Secure SSL encryption</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Instant confirmation</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <CreditCard className="h-4 w-4 text-green-500" />
                      <span>Multiple payment options</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-lg p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Payment Details</h2>
                  
                  <CheckoutForm
                    bookingData={{
                      airport: bookingData.airport,
                      terminal: 'T1', // Default terminal
                      serviceType: bookingData.serviceType,
                      services: [bookingData.serviceId],
                      date: bookingData.date,
                      time: bookingData.time,
                      passengers: bookingData.passengers,
                      totalAmount: bookingData.totalAmount,
                      currency: bookingData.currency,
                      flightNumber: bookingData.flightNumber
                    }}
                    onPaymentSuccess={(reference) => {
                      // Clear booking data and redirect to confirmation
                      localStorage.removeItem('bookingData')
                      router.push(`/booking/confirmation/${reference}`)
                    }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <LuxuryFooter />
    </div>
  )
}
