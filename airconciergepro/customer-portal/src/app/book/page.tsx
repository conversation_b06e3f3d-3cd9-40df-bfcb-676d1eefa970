'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import LuxuryFooter from '@/components/layout/LuxuryFooter'
import OptimizedBookingWidget from '@/components/booking/OptimizedBookingWidget'
import { LuxuryNotificationContainer } from '@/components/ui/LuxuryNotification'

export default function BookingPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />



      {/* Booking Widget Section - Full height */}
      <section className="min-h-screen bg-gray-50 pt-4 lg:pt-8 relative">
        {/* Small back button */}
        <div className="absolute top-4 left-4 z-10">
          <Link
            href="/"
            className="inline-flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200 text-gray-600 hover:text-gray-800 hover:border-gray-300 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm font-medium">Home</span>
          </Link>
        </div>

        <div className="container-max px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-7xl mx-auto"
          >
            <OptimizedBookingWidget />
          </motion.div>
        </div>
      </section>



      <LuxuryFooter />
      <LuxuryNotificationContainer />
    </div>
  )
}
