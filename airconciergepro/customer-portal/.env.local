# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_CUSTOMER_API_URL=http://localhost:8000/api/customer/v1

# WebSocket Configuration
NEXT_PUBLIC_WS_URL=http://localhost:8000

# Payment Gateway Configuration (Test Keys)
# Stripe - Get from: https://dashboard.stripe.com/test/apikeys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_STRIPE_TEST_KEY_HERE
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_STRIPE_SECRET_KEY_HERE

# Razorpay - Get from: https://dashboard.razorpay.com/app/keys
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_Q3fn9oLi8a9o5l
RAZORPAY_KEY_SECRET=YOUR_ACTUAL_RAZORPAY_SECRET_KEY_HERE

# Application Configuration
NEXT_PUBLIC_APP_NAME=AirConcierge Pro
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NEXT_PUBLIC_SUPPORT_PHONE=******-123-4567

# Tenant Configuration
NEXT_PUBLIC_TENANT_ID=37de875f-170d-47e5-8f38-8c36b2112475

# Currency Configuration
NEXT_PUBLIC_DEFAULT_CURRENCY=INR

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_CHAT_SUPPORT=true
NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE=false

# External Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here
AVIATIONSTACK_API_KEY=********************************

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true
