{"name": "airconciergepro-customer-portal", "version": "1.0.0", "description": "AirConcierge Pro Customer Booking Portal (B2C)", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^2.4.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "14.0.4", "postcss": "^8.4.32", "razorpay": "^2.9.6", "react": "^18.2.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-select": "^5.8.0", "socket.io-client": "^4.8.1", "stripe": "^14.9.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.56.0", "eslint-config-next": "14.0.4"}}