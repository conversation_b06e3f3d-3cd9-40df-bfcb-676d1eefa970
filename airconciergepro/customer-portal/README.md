# AirConcierge Pro - Customer Portal

The customer-facing booking portal for AirConcierge Pro's premium airport meet and greet services.

## Features

### Core Functionality
- **Multi-step Booking Wizard**: Intuitive 4-step booking process
- **Airport Search**: Search and select from 150+ airports worldwide
- **Service Selection**: Choose from meet & greet, VIP lounge, fast track, and transfer services
- **Real-time Availability**: Check service availability in real-time
- **Secure Payments**: Integrated Stripe payment processing
- **Instant Confirmation**: Digital vouchers and booking confirmations

### User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **Accessibility**: WCAG 2.1 compliant with screen reader support
- **Multi-language Support**: Available in 10+ languages
- **Progressive Web App**: Installable with offline capabilities

### Services Offered
1. **Meet & Greet** - Personal assistance and baggage help
2. **VIP Lounge Access** - Premium lounge with refreshments
3. **Fast Track Security** - Priority security and immigration
4. **Airport Transfer** - Luxury vehicle transfers

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Query + Zustand
- **Animations**: Framer Motion
- **Forms**: React Hook Form + Zod validation
- **Payments**: Stripe
- **Icons**: Lucide React

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Backend API running on port 3000

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
# Edit .env.local with your configuration
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3001](http://localhost:3001) in your browser

### Environment Variables

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_CUSTOMER_API_URL=http://localhost:3000/api/customer/v1

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Application Configuration
NEXT_PUBLIC_APP_NAME=AirConcierge Pro
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── globals.css        # Global styles
│   └── ...
├── components/            # React components
│   ├── booking/          # Booking wizard components
│   ├── layout/           # Layout components (Header, Footer)
│   ├── ui/               # Reusable UI components
│   └── ...
├── lib/                  # Utility functions
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── styles/               # Additional styles
```

## Key Components

### BookingWidget
The main booking component with 4 steps:
1. Airport selection with arrival/departure toggle
2. Service selection with pricing
3. Date/time selection with flight info
4. Passenger details with special requirements

### AirportSearch
- Searchable airport database
- Popular airports quick selection
- Real-time filtering by name, city, or IATA code

### ServiceSelector
- Visual service cards with features and pricing
- Service combinations and discounts
- Dynamic pricing based on airport and date

## API Integration

The portal integrates with the backend API for:
- Airport data and availability
- Service pricing and options
- Booking creation and management
- Payment processing
- Customer account management

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Deployment
```bash
docker build -t airconcierge-customer-portal .
docker run -p 3001:3001 airconcierge-customer-portal
```

### Environment-specific Builds
- **Development**: `npm run dev`
- **Staging**: `npm run build:staging`
- **Production**: `npm run build:production`

## Performance Optimization

- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Next.js Image component
- **Caching**: React Query for API caching
- **Bundle Analysis**: `npm run analyze`

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

## Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for all new components
3. Add proper error handling and loading states
4. Test on multiple devices and browsers
5. Update documentation for new features

## Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [docs.airconciergepro.com](https://docs.airconciergepro.com)
- Issues: Create a GitHub issue

## License

Proprietary - AirConcierge Pro. All rights reserved.
