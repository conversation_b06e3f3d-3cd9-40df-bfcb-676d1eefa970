// Simple WebSocket server for testing real-time features
// Run with: node websocket-server.js

const WebSocket = require('ws');

const wss = new WebSocket.Server({ port: 5000 });

console.log('WebSocket server started on port 5000');

// Store connected clients
const clients = new Set();

// Mock data generators
const generateAvailabilityUpdate = () => ({
  type: 'availability_update',
  data: {
    airportId: 'JFK',
    serviceId: 'meet-greet-basic',
    date: new Date().toISOString().split('T')[0],
    availableSlots: Math.floor(Math.random() * 20) + 5,
    totalSlots: 25,
    nextAvailableTime: Math.random() > 0.5 ? '14:30' : undefined
  },
  timestamp: new Date().toISOString()
});

const generatePriceUpdate = () => {
  const basePrice = 50;
  const adjustment = (Math.random() - 0.5) * 20; // -10 to +10
  return {
    type: 'price_update',
    data: {
      serviceId: 'meet-greet-basic',
      airportId: 'JFK',
      date: new Date().toISOString().split('T')[0],
      basePrice,
      dynamicPrice: basePrice + adjustment,
      adjustments: [
        {
          type: adjustment > 0 ? 'surge' : 'discount',
          amount: adjustment,
          reason: adjustment > 0 ? 'High demand' : 'Off-peak discount'
        }
      ],
      currency: 'USD'
    },
    timestamp: new Date().toISOString()
  };
};

const generateBookingUpdate = () => ({
  type: 'booking_update',
  data: {
    bookingId: '**********',
    status: ['confirmed', 'modified', 'cancelled'][Math.floor(Math.random() * 3)],
    message: 'Your booking status has been updated',
    details: {
      reference: '**********',
      service: 'Meet & Greet Basic',
      airport: 'JFK'
    }
  },
  timestamp: new Date().toISOString()
});

const generateSystemNotification = () => ({
  type: 'system_notification',
  data: {
    level: ['info', 'warning', 'success'][Math.floor(Math.random() * 3)],
    title: 'System Update',
    message: 'Service maintenance scheduled for tonight 2-4 AM EST',
    autoClose: true,
    duration: 8000
  },
  timestamp: new Date().toISOString()
});

// Handle client connections
wss.on('connection', (ws) => {
  console.log('Client connected');
  clients.add(ws);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'system_notification',
    data: {
      level: 'success',
      title: 'Connected',
      message: 'Real-time updates are now active',
      autoClose: true,
      duration: 3000
    },
    timestamp: new Date().toISOString()
  }));

  // Handle incoming messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('Received:', data);

      // Handle ping/pong
      if (data.type === 'ping') {
        ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
        return;
      }

      // Handle subscription requests
      if (data.type === 'subscribe_availability') {
        console.log('Client subscribed to availability updates for:', data.data);
        // Send immediate availability update
        setTimeout(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(generateAvailabilityUpdate()));
          }
        }, 1000);
      }

      if (data.type === 'subscribe_pricing') {
        console.log('Client subscribed to pricing updates for:', data.data);
        // Send immediate price update
        setTimeout(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(generatePriceUpdate()));
          }
        }, 1500);
      }

      if (data.type === 'subscribe_booking') {
        console.log('Client subscribed to booking updates for:', data.data);
        // Send immediate booking update
        setTimeout(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(generateBookingUpdate()));
          }
        }, 2000);
      }

    } catch (error) {
      console.error('Error parsing message:', error);
    }
  });

  // Handle client disconnect
  ws.on('close', () => {
    console.log('Client disconnected');
    clients.delete(ws);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
});

// Broadcast updates to all connected clients periodically
const broadcastUpdates = () => {
  if (clients.size === 0) return;

  const updates = [
    generateAvailabilityUpdate,
    generatePriceUpdate,
    generateBookingUpdate,
    generateSystemNotification
  ];

  // Send random update to all clients
  const randomUpdate = updates[Math.floor(Math.random() * updates.length)]();
  
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(randomUpdate));
    }
  });

  console.log(`Broadcasted ${randomUpdate.type} to ${clients.size} clients`);
};

// Send updates every 10-30 seconds
const scheduleNextUpdate = () => {
  const delay = Math.random() * 20000 + 10000; // 10-30 seconds
  setTimeout(() => {
    broadcastUpdates();
    scheduleNextUpdate();
  }, delay);
};

// Start broadcasting after 5 seconds
setTimeout(() => {
  console.log('Starting periodic updates...');
  scheduleNextUpdate();
}, 5000);

// Handle server shutdown gracefully
process.on('SIGINT', () => {
  console.log('\nShutting down WebSocket server...');
  
  // Close all client connections
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        type: 'system_notification',
        data: {
          level: 'warning',
          title: 'Server Shutdown',
          message: 'WebSocket server is shutting down',
          autoClose: false
        },
        timestamp: new Date().toISOString()
      }));
      client.close();
    }
  });

  // Close server
  wss.close(() => {
    console.log('WebSocket server closed');
    process.exit(0);
  });
});

// Error handling
wss.on('error', (error) => {
  console.error('WebSocket server error:', error);
});

console.log('WebSocket server is ready to accept connections');
console.log('Connect to: ws://localhost:5000');
console.log('Press Ctrl+C to stop the server');
