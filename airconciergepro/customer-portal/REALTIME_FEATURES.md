# Real-time Features Implementation

This document describes the real-time features implemented in the AirConcierge Pro Customer Portal.

## Overview

The real-time features provide live updates for:
- **Availability checking** - Real-time service availability and capacity updates
- **Dynamic pricing** - Live price updates based on demand, time, and other factors
- **Booking updates** - Real-time booking status changes and notifications
- **System notifications** - Live system alerts and maintenance notifications

## Architecture

### WebSocket Service (`/lib/websocket.ts`)
- Manages WebSocket connections with automatic reconnection
- Handles message routing and subscription management
- Provides heartbeat mechanism for connection health
- Supports multiple message types with typed interfaces

### Real-time Pricing Service (`/lib/realtime-pricing.ts`)
- Calculates dynamic pricing with real-time updates
- Caches pricing results with automatic invalidation
- Subscribes to price change notifications
- Provides fallback pricing when API is unavailable

### Real-time Availability Service (`/lib/realtime-availability.ts`)
- Checks service availability with live updates
- Generates time slot availability data
- Polls for updates as fallback to WebSocket
- Provides availability forecasting

## Components

### Enhanced ServiceSelector
- Shows real-time pricing with change indicators
- Displays price adjustments (surge, discounts, etc.)
- Animated price change notifications
- Live pricing updates with trend arrows

### Enhanced DateTimeSelector
- Real-time availability checking for selected dates
- Live availability indicators on time slots
- Compact availability status for quick selection
- Availability forecasting for alternative times

### AvailabilityIndicator
- Comprehensive availability status display
- Real-time capacity monitoring
- Next available slot suggestions
- Restriction and requirement notifications

### RealTimeNotifications
- Toast-style notifications for live updates
- Price change alerts with trend indicators
- Availability change notifications
- Booking status update alerts
- System maintenance notifications

### ConnectionStatus
- Live WebSocket connection status
- Automatic reconnection attempts
- Connection health monitoring
- Offline mode indicators

## Usage

### Starting the WebSocket Server

For development, start the mock WebSocket server:

```bash
cd airconciergepro/customer-portal
node websocket-server.js
```

The server will start on `ws://localhost:5000` and provide mock real-time updates.

### Environment Configuration

Add to your `.env.local`:

```env
NEXT_PUBLIC_WS_URL=ws://localhost:5000
```

### Component Integration

#### ServiceSelector with Real-time Pricing

```tsx
<ServiceSelector
  selectedService={selectedService}
  onServiceSelect={setSelectedService}
  airportId={airportId}
  serviceType={serviceType}
  date={date}
  time={time}
  passengerCount={passengerCount}
/>
```

#### DateTimeSelector with Availability

```tsx
<DateTimeSelector
  date={date}
  time={time}
  onDateChange={setDate}
  onTimeChange={setTime}
  serviceType={serviceType}
  airportId={airportId}
  serviceIds={[selectedService]}
  passengerCount={passengerCount}
/>
```

#### Availability Indicator

```tsx
<AvailabilityIndicator
  airportId={airportId}
  serviceIds={[serviceId]}
  date={date}
  time={time}
  passengerCount={passengerCount}
/>
```

## Real-time Data Flow

1. **Connection Establishment**
   - WebSocket connects on app load
   - Connection status displayed to user
   - Automatic reconnection on failure

2. **Subscription Management**
   - Components subscribe to relevant data updates
   - Subscriptions cleaned up on unmount
   - Multiple components can share subscriptions

3. **Live Updates**
   - Server pushes updates via WebSocket
   - Components receive and process updates
   - UI updates with smooth animations
   - Notifications shown for important changes

4. **Fallback Mechanisms**
   - Polling fallback when WebSocket fails
   - Cached data used when offline
   - Graceful degradation to static pricing

## Message Types

### Availability Update
```typescript
{
  type: 'availability_update',
  data: {
    airportId: string,
    serviceId: string,
    date: string,
    availableSlots: number,
    totalSlots: number,
    nextAvailableTime?: string
  }
}
```

### Price Update
```typescript
{
  type: 'price_update',
  data: {
    serviceId: string,
    airportId: string,
    date: string,
    basePrice: number,
    dynamicPrice: number,
    adjustments: Array<{
      type: string,
      amount: number,
      reason: string
    }>,
    currency: string
  }
}
```

### Booking Update
```typescript
{
  type: 'booking_update',
  data: {
    bookingId: string,
    status: 'confirmed' | 'cancelled' | 'modified' | 'completed',
    message: string,
    details?: any
  }
}
```

## Performance Considerations

- **Caching**: Pricing and availability data cached with TTL
- **Debouncing**: Updates debounced to prevent UI thrashing
- **Subscription Management**: Automatic cleanup prevents memory leaks
- **Fallback Polling**: Limited frequency to reduce server load
- **Connection Pooling**: Single WebSocket connection shared across components

## Testing

### Mock WebSocket Server
The included `websocket-server.js` provides:
- Random availability updates every 10-30 seconds
- Dynamic price changes with surge/discount simulation
- Booking status updates
- System notifications
- Ping/pong heartbeat handling

### Manual Testing
1. Start the WebSocket server
2. Open the customer portal
3. Navigate through booking flow
4. Observe real-time updates in:
   - Service pricing
   - Availability indicators
   - Notifications
   - Connection status

## Production Deployment

### WebSocket Server Requirements
- Scalable WebSocket server (e.g., Socket.io cluster)
- Redis for message broadcasting across instances
- Load balancer with sticky sessions
- SSL/TLS for secure connections (wss://)

### Environment Variables
```env
NEXT_PUBLIC_WS_URL=wss://api.airconciergepro.com/ws
```

### Monitoring
- WebSocket connection metrics
- Message delivery rates
- Client subscription counts
- Error rates and reconnection attempts

## Future Enhancements

- **Real-time Chat Support**: Live customer service chat
- **Live Tracking**: Real-time service provider location tracking
- **Push Notifications**: Browser push notifications for important updates
- **Collaborative Booking**: Multiple users booking together with live sync
- **Real-time Analytics**: Live booking conversion and user behavior tracking
