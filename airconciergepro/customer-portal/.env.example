# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Payment Gateway Configuration
# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Razorpay
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_your_key_id

# WebSocket Configuration
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:8000

# JWT Configuration (for client-side token handling)
NEXT_PUBLIC_JWT_SECRET=48656A9F-F24A-4B31-AD51-75D3D69A4224

# Application Configuration
NEXT_PUBLIC_APP_NAME=AirConcierge Pro
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Currency Configuration
NEXT_PUBLIC_DEFAULT_CURRENCY=INR

# Development/Production Mode
NODE_ENV=development
